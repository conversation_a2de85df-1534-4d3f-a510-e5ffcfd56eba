import{c as G}from"./chunk-5VBIWGCV.js";import{a as S}from"./chunk-AASME2FE.js";import{Fe as P,Sd as c}from"./chunk-K5H3SJL5.js";import{A as F,Bc as D,Cc as R,Da as w,Eb as m,Ib as E,Jb as I,Lb as a,Oa as M,Qa as j,Wb as b,Xb as g,Yb as A,_c as y,ba as n,cd as r,f as h,fc as C,ga as p,gc as v,h as u,ha as N,ia as B,kc as V,lc as O,mc as T,na as l,ob as z,pb as o,sa as f,ta as $}from"./chunk-YK6FMNSY.js";var Q=["*"],Z=["inputElement"],_=["nz-radio",""],k=(()=>{class s{constructor(){this.selected$=new u(1),this.touched$=new h,this.disabled$=new u(1),this.name$=new u(1)}touch(){this.touched$.next()}select(e){this.selected$.next(e)}setDisabled(e){this.disabled$.next(e)}setName(e){this.name$.next(e)}static{this.\u0275fac=function(t){return new(t||s)}}static{this.\u0275prov=N({token:s,factory:s.\u0275fac})}}return s})(),ae=(()=>{class s{constructor(e,t,i){this.cdr=e,this.nzRadioService=t,this.directionality=i,this.value=null,this.destroy$=new h,this.isNzDisableFirstChange=!0,this.onChange=()=>{},this.onTouched=()=>{},this.nzDisabled=!1,this.nzButtonStyle="outline",this.nzSize="default",this.nzName=null,this.dir="ltr"}ngOnInit(){this.nzRadioService.selected$.pipe(n(this.destroy$)).subscribe(e=>{this.value!==e&&(this.value=e,this.onChange(this.value))}),this.nzRadioService.touched$.pipe(n(this.destroy$)).subscribe(()=>{Promise.resolve().then(()=>this.onTouched())}),this.directionality.change?.pipe(n(this.destroy$)).subscribe(e=>{this.dir=e,this.cdr.detectChanges()}),this.dir=this.directionality.value}ngOnChanges(e){let{nzDisabled:t,nzName:i}=e;t&&this.nzRadioService.setDisabled(this.nzDisabled),i&&this.nzRadioService.setName(this.nzName)}ngOnDestroy(){this.destroy$.next(!0),this.destroy$.complete()}writeValue(e){this.value=e,this.nzRadioService.select(e),this.cdr.markForCheck()}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.nzDisabled=this.isNzDisableFirstChange&&this.nzDisabled||e,this.isNzDisableFirstChange=!1,this.nzRadioService.setDisabled(this.nzDisabled),this.cdr.markForCheck()}static{this.\u0275fac=function(t){return new(t||s)(o(y),o(k),o(c))}}static{this.\u0275cmp=f({type:s,selectors:[["nz-radio-group"]],hostAttrs:[1,"ant-radio-group"],hostVars:8,hostBindings:function(t,i){t&2&&a("ant-radio-group-large",i.nzSize==="large")("ant-radio-group-small",i.nzSize==="small")("ant-radio-group-solid",i.nzButtonStyle==="solid")("ant-radio-group-rtl",i.dir==="rtl")},inputs:{nzDisabled:[2,"nzDisabled","nzDisabled",r],nzButtonStyle:"nzButtonStyle",nzSize:"nzSize",nzName:"nzName"},exportAs:["nzRadioGroup"],standalone:!0,features:[D([k,{provide:S,useExisting:p(()=>s),multi:!0}]),m,w,R],ngContentSelectors:Q,decls:1,vars:0,template:function(t,i){t&1&&(C(),v(0))},encapsulation:2,changeDetection:0})}}return s})(),re=(()=>{class s{focus(){this.focusMonitor.focusVia(this.inputElement,"keyboard")}blur(){this.inputElement.nativeElement.blur()}constructor(e,t,i,d){this.ngZone=e,this.elementRef=t,this.cdr=i,this.focusMonitor=d,this.isNgModel=!1,this.destroy$=new h,this.isNzDisableFirstChange=!0,this.directionality=l(c),this.nzRadioService=l(k,{optional:!0}),this.nzFormStatusService=l(G,{optional:!0}),this.isChecked=!1,this.name=null,this.onChange=()=>{},this.onTouched=()=>{},this.nzValue=null,this.nzDisabled=!1,this.nzAutoFocus=!1,this.isRadioButton=!1,this.dir="ltr"}setDisabledState(e){this.nzDisabled=this.isNzDisableFirstChange&&this.nzDisabled||e,this.isNzDisableFirstChange=!1,this.cdr.markForCheck()}writeValue(e){this.isChecked=e,this.cdr.markForCheck()}registerOnChange(e){this.isNgModel=!0,this.onChange=e}registerOnTouched(e){this.onTouched=e}ngOnInit(){this.nzRadioService&&(this.nzRadioService.name$.pipe(n(this.destroy$)).subscribe(e=>{this.name=e,this.cdr.markForCheck()}),this.nzRadioService.disabled$.pipe(n(this.destroy$)).subscribe(e=>{this.nzDisabled=this.isNzDisableFirstChange&&this.nzDisabled||e,this.isNzDisableFirstChange=!1,this.cdr.markForCheck()}),this.nzRadioService.selected$.pipe(n(this.destroy$)).subscribe(e=>{let t=this.isChecked;this.isChecked=this.nzValue===e,this.isNgModel&&t!==this.isChecked&&this.isChecked===!1&&this.onChange(!1),this.cdr.markForCheck()})),this.focusMonitor.monitor(this.elementRef,!0).pipe(n(this.destroy$)).subscribe(e=>{e||(Promise.resolve().then(()=>this.onTouched()),this.nzRadioService&&this.nzRadioService.touch())}),this.directionality.change.pipe(n(this.destroy$)).subscribe(e=>{this.dir=e,this.cdr.detectChanges()}),this.dir=this.directionality.value,this.setupClickListener()}ngAfterViewInit(){this.nzAutoFocus&&this.focus()}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete(),this.focusMonitor.stopMonitoring(this.elementRef)}setupClickListener(){this.ngZone.runOutsideAngular(()=>{F(this.elementRef.nativeElement,"click").pipe(n(this.destroy$)).subscribe(e=>{e.stopPropagation(),e.preventDefault(),!(this.nzDisabled||this.isChecked)&&this.ngZone.run(()=>{this.focus(),this.nzRadioService?.select(this.nzValue),this.isNgModel&&(this.isChecked=!0,this.onChange(!0)),this.cdr.markForCheck()})})})}static{this.\u0275fac=function(t){return new(t||s)(o(M),o(j),o(y),o(P))}}static{this.\u0275cmp=f({type:s,selectors:[["","nz-radio",""],["","nz-radio-button",""]],viewQuery:function(t,i){if(t&1&&V(Z,7),t&2){let d;O(d=T())&&(i.inputElement=d.first)}},hostVars:18,hostBindings:function(t,i){t&2&&a("ant-radio-wrapper-in-form-item",!!i.nzFormStatusService)("ant-radio-wrapper",!i.isRadioButton)("ant-radio-button-wrapper",i.isRadioButton)("ant-radio-wrapper-checked",i.isChecked&&!i.isRadioButton)("ant-radio-button-wrapper-checked",i.isChecked&&i.isRadioButton)("ant-radio-wrapper-disabled",i.nzDisabled&&!i.isRadioButton)("ant-radio-button-wrapper-disabled",i.nzDisabled&&i.isRadioButton)("ant-radio-wrapper-rtl",!i.isRadioButton&&i.dir==="rtl")("ant-radio-button-wrapper-rtl",i.isRadioButton&&i.dir==="rtl")},inputs:{nzValue:"nzValue",nzDisabled:[2,"nzDisabled","nzDisabled",r],nzAutoFocus:[2,"nzAutoFocus","nzAutoFocus",r],isRadioButton:[2,"nz-radio-button","isRadioButton",r]},exportAs:["nzRadio"],standalone:!0,features:[D([{provide:S,useExisting:p(()=>s),multi:!0}]),m,R],attrs:_,ngContentSelectors:Q,decls:6,vars:24,consts:[["inputElement",""],["type","radio",3,"disabled","checked"]],template:function(t,i){t&1&&(C(),b(0,"span"),A(1,"input",1,0)(3,"span"),g(),b(4,"span"),v(5),g()),t&2&&(a("ant-radio",!i.isRadioButton)("ant-radio-checked",i.isChecked&&!i.isRadioButton)("ant-radio-disabled",i.nzDisabled&&!i.isRadioButton)("ant-radio-button",i.isRadioButton)("ant-radio-button-checked",i.isChecked&&i.isRadioButton)("ant-radio-button-disabled",i.nzDisabled&&i.isRadioButton),z(),a("ant-radio-input",!i.isRadioButton)("ant-radio-button-input",i.isRadioButton),I("disabled",i.nzDisabled)("checked",i.isChecked),E("autofocus",i.nzAutoFocus?"autofocus":null)("name",i.name),z(2),a("ant-radio-inner",!i.isRadioButton)("ant-radio-button-inner",i.isRadioButton))},encapsulation:2,changeDetection:0})}}return s})(),de=(()=>{class s{static{this.\u0275fac=function(t){return new(t||s)}}static{this.\u0275mod=$({type:s})}static{this.\u0275inj=B({})}}return s})();export{ae as a,re as b,de as c};
