import{a as M}from"./chunk-GEIXMSZ2.js";import{a as T}from"./chunk-SOAH2WLW.js";import{o as a}from"./chunk-5VBIWGCV.js";import{a as D,w as N}from"./chunk-AASME2FE.js";import{Pe as E,Z as U,jf as b,ud as k}from"./chunk-K5H3SJL5.js";import{A as p,Bc as v,Cb as h,Cc as P,Ea as c,F as m,Fa as d,Hb as w,Jb as n,La as f,Qc as S,Wb as y,Xb as g,Yb as I,ac as x,cc as C,ec as r,ga as l,na as i,nc as _,sa as u}from"./chunk-YK6FMNSY.js";function z(o,A){if(o&1){let e=x();y(0,"button",2),C("click",function(){c(e);let t=r();return d(t.handleShowHidePassword())}),g()}if(o&2){let e=r();n("mute",!0)("iconOnly",!0)("size",e.UI.ButtonSize.Md)("prefixIcon",e.iconEye)("color",e.UI.ButtonColor.Text)}}var Y=(()=>{class o extends a{formControlName="";type=U.Password;showPassword=!1;iconEye=this.showPassword?"assets/media/icons/outline/eye-hide.svg":"assets/media/icons/outline/eye.svg";destroyRef=i(f);UI=i(b);constructor(){super()}handleShowHidePassword(){this.showPassword=!this.showPassword,this.iconEye=this.showPassword?"assets/media/icons/outline/eye-hide.svg":"assets/media/icons/outline/eye.svg",this.type=this.showPassword?this.UI.InputType.Text:this.UI.InputType.Password}ngOnInit(){super.ngOnInit(),p(window,"keydown").pipe(m(e=>e.ctrlKey&&e.key==="m"),k(this.destroyRef)).subscribe(e=>{e.preventDefault(),this.handleShowHidePassword()})}static \u0275fac=function(s){return new(s||o)};static \u0275cmp=u({type:o,selectors:[["app-input-password"]],inputs:{formControlName:"formControlName"},standalone:!0,features:[v([{provide:D,useExisting:l(()=>o),multi:!0}]),h,P],decls:3,vars:9,consts:[["passSuffix",""],["appNoneUnikey","","appNoneSpace","",3,"size","placeholder","type","prefixIcon","suffix","maxLength","autocomplete","focusAfterClear","errorMessages"],["app-button","","aria-label","hide/show","type","button","tabindex","-1",3,"click","mute","iconOnly","size","prefixIcon","color"]],template:function(s,t){if(s&1&&(I(0,"app-input",1),w(1,z,1,5,"ng-template",null,0,S)),s&2){let R=_(2);n("size",t.size)("placeholder",t.placeholder)("type",t.type)("prefixIcon","assets/media/icons/doutone/icon-he-thong/ht-lock.svg")("suffix",R)("maxLength",t.maxLength)("autocomplete",t.autocomplete)("focusAfterClear",t.focusAfterClear)("errorMessages",t.errorMessages)}},dependencies:[E,N,a,T,M]})}return o})();export{Y as a};
