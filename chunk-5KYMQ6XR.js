import{a as dt}from"./chunk-CLYMFTGQ.js";import{a as mt}from"./chunk-6NBO3RRK.js";import{a as _t}from"./chunk-EJBNJL2X.js";import{a as st}from"./chunk-TI2OG4JD.js";import{a as tt}from"./chunk-P2H32HMS.js";import{a as ot}from"./chunk-UVPCVSBB.js";import{a as it}from"./chunk-IDHI5ISK.js";import{a as at}from"./chunk-IZVSQRWS.js";import{a as ut}from"./chunk-24JQYWUX.js";import{a as rt}from"./chunk-5VYTNFSL.js";import{o as nt}from"./chunk-5VBIWGCV.js";import{g as je,h as Qe,j as qe,l as Ye,n as Xe,q as Je,u as We,v as Ze,w as et}from"./chunk-AASME2FE.js";import{$ as pt,P as lt,Q as ct,c as Ke}from"./chunk-AX4DCRSD.js";import{Ad as Re,Af as Pe,Bd as ae,Cd as le,Dc as Ve,H as we,Jc as ne,Kc as O,Pb as Ue,Pd as ce,Pe as pe,Ra as Fe,Ye as Me,gb as Ne,hb as Be,jf as se,mf as Oe,o as be,of as $e,q as Ee,r as Le,td as re,ud as j,v as Ae,w as ie,wd as De,xf as ze,y as k,yd as oe,zf as Ge}from"./chunk-K5H3SJL5.js";import{a as He}from"./chunk-2WPD26RB.js";import{$b as te,Ac as F,Cb as xe,Cc as R,Ea as C,Ec as Se,Fa as v,Hb as f,Ib as ee,Jb as s,Jd as Te,K as fe,Kd as Ie,La as z,Lb as D,Lc as d,Mc as S,Na as W,Oc as X,Od as ke,Pc as U,Qc as K,Qd as M,Rb as g,Sb as N,Ub as L,Vb as A,Wb as o,Xb as c,Yb as _,_c as ye,ac as x,cc as h,ec as p,ha as Ce,hb as ve,kc as Q,lc as q,ma as _e,mc as Y,na as u,nc as G,ob as r,oc as m,pb as Z,pc as B,qc as T,rc as P,sa as V,yc as I,zc as w}from"./chunk-YK6FMNSY.js";import{e as vt,f as xt}from"./chunk-TSRGIXR5.js";var Ct=vt((he,ge)=>{"use strict";(function(t,l){typeof he=="object"&&typeof ge<"u"?ge.exports=l():typeof define=="function"&&define.amd?define(l):(t=typeof globalThis<"u"?globalThis:t||self).dayjs_plugin_isBetween=l()})(he,function(){"use strict";return function(t,l,e){l.prototype.isBetween=function(i,n,a,y){var b=e(i),E=e(n),H=(y=y||"()")[0]==="(",$=y[1]===")";return(H?this.isAfter(b,a):!this.isBefore(b,a))&&($?this.isBefore(E,a):!this.isAfter(E,a))||(H?this.isBefore(b,a):!this.isAfter(b,a))&&($?this.isAfter(E,a):!this.isBefore(E,a))}}})});var hi=(()=>{class t{storeService;router;constructor(e,i){this.storeService=e,this.router=i}canActivate(e,i){let{userInfo:n,isAuthenticated:a}=this.storeService;return!a||n.loginType!==Ne.dang_nhap_binh_thuong?!0:(this.router.navigate([""]),!1)}canActivateChild(e,i){if(e.data&&e.data.step===Be.nhap_otp)if(this.router.getCurrentNavigation()?.extras.state){if(!(this.router.getCurrentNavigation()?.extras.state).authToken)return this.router.navigate(["quen-mat-khau"]),!1}else return this.router.navigate(["quen-mat-khau"]),!1;return!0}static \u0275fac=function(i){return new(i||t)(_e(O),_e(Ee))};static \u0275prov=Ce({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();var de=(()=>{class t{toggle=!0;change=new W;lang=k.Vi;languages=Fe;otherLanguage;storeService=u(O);commonService=u(re);destroyRef=u(z);ngOnInit(){this.commonService.getLanguage().pipe(j(this.destroyRef)).subscribe(e=>{e&&(this.lang=e,this.updateOtherLanguage())})}handleChange(){switch(this.lang){case k.En:this.lang=k.Vi;break;default:this.lang=k.En;break}this.change.emit(this.lang),this.storeService.language=this.lang,this.commonService.setLanguage(this.lang)}updateOtherLanguage(){this.otherLanguage=this.languages[this.lang===k.Vi?k.En:k.Vi]}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=V({type:t,selectors:[["app-toggle-language"]],hostVars:2,hostBindings:function(i,n){i&2&&D("toggle-language",n.toggle)},outputs:{change:"change"},standalone:!0,features:[R],decls:4,vars:2,consts:[[1,"toggle-language-inner",3,"click"],["appVvip","","alt","icon flag",1,"language-flag",3,"src"],[1,"language-name"]],template:function(i,n){i&1&&(o(0,"div",0),h("click",function(){return n.handleChange()}),_(1,"img",1),o(2,"p",2),m(3),c()()),i&2&&(r(),s("src",n.otherLanguage.icon,ve),r(2),T(" ",n.otherLanguage.label," "))},dependencies:[M,De]})}return t})();var Hi=(()=>{class t{activatedRoute;storeService;translate;header=!0;language;store$;tick;timeSpace=1e3;returnUrl="";Language=k;crypto=u(ne);constructor(e,i,n){this.activatedRoute=e,this.storeService=i,this.translate=n,this.returnUrl=this.activatedRoute.snapshot.queryParams.returnUrl||"",this.store$=this.storeService.changes.subscribe(a=>{a.key===ie.LANGUAGE&&(this.translate.use(a.value),this.language=a.value)})}ngOnInit(){this.language=this.storeService.language,this.tick=setInterval(()=>{this.checkStateApp()},this.timeSpace)}changeLang(){this.language=this.language===k.En||!this.language?k.Vi:k.En}checkStateApp(){try{let e=document.querySelector("[data-tabid]"),i=e&&e.getAttribute("data-tabid"),n=this.storeService.appEvent,a=n?JSON.parse(this.crypto.decryptText(n)):void 0;if(a&&a.id!==i&&a.type===oe.Login&&a?.data){let{userInfo:y,accessKey:b,sessionTimeout:E}=this.crypto.decryptText(a.data)||{};b&&(this.storeService.accessKey=b),E&&(this.storeService.sessionTimeout=E),y&&(this.storeService.userInfo=y),location.href="/",this.storeService.appEvent=null}}catch(e){console.log("Error checkStateApp",e)}}ngOnDestroy(){this.tick&&clearInterval(this.tick)}static \u0275fac=function(i){return new(i||t)(Z(be),Z(O),Z(Re))};static \u0275cmp=V({type:t,selectors:[["app-non-login-header"],["","app-non-login-header",""]],hostVars:2,hostBindings:function(i,n){i&2&&D("nonlogin-header",n.header)},exportAs:["app-non-login-header"],standalone:!0,features:[R],decls:3,vars:0,consts:[[1,"nonlogin-header--inner"],["src","media/logo/bidv.svg","alt","",1,"logo"],[3,"change"]],template:function(i,n){i&1&&(o(0,"div",0),_(1,"img",1),o(2,"app-toggle-language",2),h("change",function(){return n.changeLang()}),c()())},dependencies:[M,de]})}return t})();var yt=["searchInput"],ft=(t,l)=>l.id,Tt=t=>({result:t});function It(t,l){if(t&1){let e=x();o(0,"div",10),_(1,"div",11),o(2,"form",12),_(3,"app-input",13),d(4,"translate"),c(),o(5,"button",14),h("click",function(){C(e);let n=p();return v(n.close())}),c()()}if(t&2){let e=p();r(2),s("formGroup",e.form),r(),s("maxLength",e.MAX_LENGTH_SEARCH)("shape",e.UI.InputShape.Around)("size",e.UI.InputSize.Medium)("placeholder",S(4,10,"search_full.search"))("prefixIcon","assets/media/icons/doutone/icon-he-thong/ht-search.svg"),r(2),s("color",e.UI.ButtonColor.Grey)("iconOnly",!0)("size",e.UI.ButtonSize.Md)("prefixIcon","assets/media/icons/outline/close.svg")}}function kt(t,l){if(t&1&&(o(0,"app-card-empty",15)(1,"p"),m(2),d(3,"translate"),c()()),t&2){let e=p();s("type",e.UI.CardEmptyType.Search),r(2),B(S(3,2,"search_full.search_no_data"))}}function bt(t,l){if(t&1){let e=x();o(0,"button",21),h("click",function(n){C(e);let a=p().$index,y=p(3);return v(y.removeItem(n,a))}),c()}if(t&2){let e=p(4);s("iconOnly",!0)("mute",!0)("size",e.UI.ButtonSize.Xs)("iconColorChange",!1)("prefixIcon","assets/media/icons/solid/cancel.svg")}}function Et(t,l){if(t&1){let e=x();o(0,"div",20),h("click",function(){let n=C(e).$implicit,a=p(3);return v(a.handlePatchValue(n))}),m(1),f(2,bt,1,5,"ng-template",null,4,K),c()}if(t&2){let e=l.$implicit,i=G(3),n=p(3);s("size",n.UI.PillSize.Lg)("color",n.UI.PillColor.OutlineGrey)("suffix",i),r(),T(" ",e," ")}}function Lt(t,l){if(t&1&&(o(0,"div",16)(1,"p",17),m(2),d(3,"translate"),c(),o(4,"div",18),L(5,Et,4,4,"div",19,N),c()()),t&2){let e=p(2);r(2),T(" ",S(3,1,"search_full.search_history")," "),r(3),A(e.searchHistories)}}function At(t,l){if(t&1){let e=x();o(0,"app-card-category",24),h("click",function(){let n=C(e).$implicit,a=p(3);return v(a.handleClickSearch(n))}),o(1,"p",25),m(2),d(3,"localizeField"),c()()}if(t&2){let e=l.$implicit,i=p(3);s("prefixIcon",e.iconUrl||i.MEDIA_DEFAULT_URL.CARD_CATEGORY_ICON)("typeIcon","bank-icon")("type",i.UI.CardCategoryType.OutlineGrey)("cardLink",!1),r(2),T(" ",X(3,5,e,"functionName","functionNameEn")," ")}}function wt(t,l){if(t&1&&(o(0,"div",16)(1,"p",17),m(2),d(3,"translate"),c(),o(4,"div",22),L(5,At,4,9,"app-card-category",23,N),c()()),t&2){p();let e=F(1);r(2),T(" ",S(3,1,"search_full.suggestion_feature")," "),r(3),A(e)}}function Ft(t,l){if(t&1){let e=x();o(0,"div",27),h("click",function(){let n=C(e).$implicit,a=p(3);return v(a.handlePatchValue(n))}),m(1),c()}if(t&2){let e=l.$implicit,i=p(3);s("size",i.UI.PillSize.Lg)("color",i.UI.PillColor.OutlineGrey),r(),T(" ",e," ")}}function Nt(t,l){if(t&1&&(o(0,"div",16)(1,"p",17),m(2),d(3,"translate"),c(),o(4,"div",18),L(5,Ft,2,3,"div",26,N),c()()),t&2){p();let e=F(4);r(2),T(" ",S(3,1,"search_full.popular_search")," "),r(3),A(e)}}function Bt(t,l){if(t&1&&(f(0,Lt,7,3,"div",16),I(1),d(2,"async"),f(3,wt,7,3,"div",16),I(4),d(5,"async"),f(6,Nt,7,3,"div",16)),t&2){let e=p();g(e.searchHistories.length?0:-1),r();let i=w(S(2,3,e.suggestedFeatures$));r(2),g(i!=null&&i.length?3:-1),r();let n=w(S(5,6,e.popularSearchData$));r(2),g(n!=null&&n.length?6:-1)}}function Ut(t,l){t&1&&te(0)}function Vt(t,l){if(t&1&&f(0,Ut,1,0,"ng-container",29),t&2){p(2);let e=G(3);s("ngTemplateOutlet",e)}}function Dt(t,l){if(t&1){let e=x();o(0,"div",31),h("click",function(){let n=C(e).$implicit,a=p(3);return v(a.handleClickSearch(n))}),o(1,"p",25),m(2),d(3,"localizeField"),c()()}if(t&2){let e=l.$implicit,i=p(3);s("prefixIcon",e.iconUrl||i.MEDIA_DEFAULT_URL.CARD_CATEGORY_ICON)("typeIcon","bank-icon")("type",i.UI.CardCategoryType.OutlineGrey)("cardLink",!1),r(2),T(" ",X(3,5,e,"functionName","functionNameEn")," ")}}function Rt(t,l){if(t&1&&(o(0,"div",28),d(1,"translate"),o(2,"div",22),L(3,Dt,4,9,"div",30,N),c()()),t&2){p();let e=F(0),i=p();s("header",S(1,3,"search_full.feature"))("type",i.UI.AccordionType.UnBox)("isActive",!0),r(3),A(e)}}function Mt(t,l){t&1&&_(0,"app-svg",37),t&2&&s("src","media/icons/solid/star.svg")("size",5)}function Ot(t,l){if(t&1){let e=x();I(0),o(1,"div",34),h("click",function(){let n=C(e).$implicit,a=p(3);return v(a.handleClickSearch(n))}),o(2,"div",35)(3,"p",36),m(4),c(),f(5,Mt,1,2,"app-svg",37),c(),o(6,"p",38),m(7),c()()}if(t&2){let e=l.$implicit,i=p(3),n=(e==null?null:e.urlLogo)||(i.serviceCodesNoiBo.includes(e.serviceCode)?i.DEFAULT_BANK_TARGET.urlLogo:i.IMAGE_DEFAULT.IC);r(),s("cardLink",!1)("type",i.UI.CardCategoryType.OutlineGrey)("prefixIcon",n)("typeIcon","bank-icon"),ee("data-code-bank",e==null?null:e.bankCode),r(3),T(" ",(e==null?null:e.beneName)||(e==null?null:e.toAccName)," "),r(),g(e!=null&&e.isFavourite?5:-1),r(2),P(" ",(e==null?null:e.virtualAccount)||(e==null?null:e.toAcc)||(e==null?null:e.toCard)," | ",(e==null?null:e.sortName)||(e==null?null:e.bankName)," ")}}function Ht(t,l){if(t&1&&(o(0,"p",40),m(1),d(2,"number"),c()),t&2){let e=p().$implicit;r(),P(" ",X(2,2,e==null?null:e.amount,"","en")," ",e==null?null:e.ccy," ")}}function $t(t,l){if(t&1){let e=x();I(0),o(1,"div",39),h("click",function(){let n=C(e).$implicit,a=p(3);return v(a.handleClickSearch(n))}),o(2,"p",36),m(3),c(),o(4,"p",38),m(5),c(),f(6,Ht,3,6,"p",40),c()}if(t&2){let e=l.$implicit,i=p(3),n=(e==null?null:e.urlLogo)||(i.serviceCodesNoiBo.includes(e.serviceCode)?i.DEFAULT_BANK_TARGET.urlLogo:i.IMAGE_DEFAULT.IC);r(),s("cardLink",!1)("type",i.UI.CardCategoryType.OutlineGrey)("prefixIcon",n)("typeIcon","bank-icon"),ee("data-code-bank",e==null?null:e.bankCode),r(2),T(" ",(e==null?null:e.templateName)||(e==null?null:e.toAccName)," "),r(2),P(" ",(e==null?null:e.virtualAccount)||(e==null?null:e.toAcc)||(e==null?null:e.toCard)," | ",(e==null?null:e.sortName)||(e==null?null:e.bankName)," "),r(),g(e!=null&&e.amount?6:-1)}}function zt(t,l){if(t&1&&(o(0,"p",40),m(1),d(2,"number"),c()),t&2){let e=p().$implicit;r(),P(" ",X(2,2,e==null?null:e.amount,"","en")," ",e==null?null:e.ccy," ")}}function Gt(t,l){if(t&1){let e=x();I(0),o(1,"div",39),h("click",function(){let n=C(e).$implicit,a=p(3);return v(a.handleClickSearch(n))}),o(2,"p",36),m(3),c(),o(4,"p",38),m(5),c(),f(6,zt,3,6,"p",40),c()}if(t&2){let e=l.$implicit,i=p(3),n=(e==null?null:e.urlLogo)||(i.serviceCodesNoiBo.includes(e.serviceCode)?i.DEFAULT_BANK_TARGET.urlLogo:i.IMAGE_DEFAULT.IC);r(),s("cardLink",!1)("type",i.UI.CardCategoryType.OutlineGrey)("prefixIcon",n)("typeIcon","bank-icon"),ee("data-code-bank",e==null?null:e.bankCode),r(2),B(e==null?null:e.toAccName),r(2),P(" ",(e==null?null:e.virtualAccount)||(e==null?null:e.toAcc)||(e==null?null:e.toCard)," | ",(e==null?null:e.sortName)||(e==null?null:e.bankName)," "),r(),g(e!=null&&e.amount?6:-1)}}function Pt(t,l){if(t&1&&(o(0,"div",28),d(1,"translate"),o(2,"div",22),L(3,Ot,8,9,"div",32,N),L(5,$t,7,9,"div",33,ft),L(7,Gt,7,9,"div",33,ft),c()()),t&2){p();let e=F(2),i=F(4),n=F(6),a=p();s("header",S(1,3,"search_full.beneficiary_directory"))("type",a.UI.AccordionType.UnBox)("isActive",!0),r(3),A(e),r(2),A(i),r(2),A(n)}}function Kt(t,l){if(t&1){let e=x();o(0,"div",31),h("click",function(){let n=C(e).$implicit,a=p(3);return v(a.handleClickSearch(n,a.TransformSearchDataKey.TEMPLATES))}),o(1,"p",36),m(2),c(),o(3,"p",38),m(4),c(),o(5,"p",38),m(6),c()()}if(t&2){let e=l.$implicit,i=p(3);s("prefixIcon",e.icon)("typeIcon","bank-icon")("type",i.UI.CardCategoryType.OutlineGrey)("cardLink",!1),r(2),B(e.templateName),r(2),B(e.invoice),r(2),B(e.serviceName)}}function jt(t,l){if(t&1&&(o(0,"div",28),d(1,"translate"),o(2,"div",22),L(3,Kt,7,7,"div",30,N),c()()),t&2){p();let e=F(8),i=p();s("header",S(1,3,"search_full.payment_template"))("type",i.UI.AccordionType.UnBox)("isActive",!0),r(3),A(e)}}function Qt(t,l){if(t&1){let e=x();o(0,"div",31),h("click",function(){let n=C(e).$implicit,a=p(3);return v(a.handleClickSearch(n))}),o(1,"div",41)(2,"p",36),m(3),c(),_(4,"app-svg",42),c(),o(5,"p",38),m(6),c()()}if(t&2){let e=l.$implicit,i=p(3);s("prefixIcon",e.urlLogo)("typeIcon","bank-icon")("type",i.UI.CardCategoryType.OutlineGrey)("cardLink",!1),r(3),B(e==null?null:e.sortName),r(),s("src","assets/media/icons/solid/ht-timer.svg")("colorChange",!1)("size",5),r(2),T(" ",e==null?null:e.bankName," ")}}function qt(t,l){if(t&1&&(o(0,"div",28),d(1,"translate"),o(2,"div",22),L(3,Qt,7,9,"div",30,N),c()()),t&2){p();let e=F(10),i=p();s("header",S(1,3,"search_full.bank"))("type",i.UI.AccordionType.UnBox)("isActive",!0),r(3),A(e)}}function Yt(t,l){if(t&1){let e=x();o(0,"div",31),h("click",function(){let n=C(e).$implicit,a=p(3);return v(a.handleClickSearch(n,a.TransformSearchDataKey.BILL_FEATURES))}),o(1,"p",36),m(2),c(),o(3,"p",38),m(4),c()()}if(t&2){let e=l.$implicit,i=p(3);s("prefixIcon",e.icon)("typeIcon","bank-icon")("type",i.UI.CardCategoryType.OutlineGrey)("cardLink",!1),r(2),B(e==null?null:e.serviceTypeName),r(2),T(" ",e==null?null:e.serviceName," ")}}function Xt(t,l){if(t&1&&(o(0,"div",28),d(1,"translate"),o(2,"div",22),L(3,Yt,5,6,"div",30,N),c()()),t&2){p();let e=F(12),i=p();s("header",S(1,3,"search_full.invoice_payment_service"))("type",i.UI.AccordionType.UnBox)("isActive",!0),r(3),A(e)}}function Jt(t,l){if(t&1){let e=x();o(0,"div",31),h("click",function(){let n=C(e).$implicit,a=p(3);return v(a.handleClickSearch(n))}),o(1,"p",36),m(2),c(),o(3,"p",38),m(4),c()()}if(t&2){let e=l.$implicit,i=p(3);s("prefixIcon","assets/media/icons/doutone/icon-chuc-nang/cn-nap-tien-dien-thoai.svg")("typeIcon","bank-icon")("type",i.UI.CardCategoryType.OutlineGrey)("cardLink",!1),r(2),B(e.beneName),r(2),B(e.mobileNo)}}function Wt(t,l){if(t&1&&(o(0,"div",28),d(1,"translate"),o(2,"div",22),L(3,Jt,5,6,"div",30,N),c()()),t&2){p();let e=F(14),i=p();s("header",S(1,3,"search_full.recent_top_up"))("type",i.UI.AccordionType.UnBox)("isActive",!0),r(3),A(e)}}function Zt(t,l){if(t&1&&(I(0),d(1,"searchFilter"),I(2),d(3,"searchFilter"),I(4),d(5,"searchFilter"),I(6),d(7,"searchFilter"),I(8),d(9,"searchFilter"),I(10),d(11,"searchFilter"),I(12),d(13,"searchFilter"),I(14),d(15,"searchFilter"),f(16,Vt,1,1,"ng-container")(17,Rt,5,5,"div",28)(18,Pt,9,5,"div",28)(19,jt,5,5,"div",28)(20,qt,5,5,"div",28)(21,Xt,5,5,"div",28)(22,Wt,5,5,"div",28)),t&2){let e=l.result,i=p(),n=w(U(1,7,e.features,i.searchTerm,i.featureSearchFields,i.ModeSearch.ADVANCE));r(2);let a=w(U(3,13,e.beneficiaries,i.searchTerm,i.beneficiarySearchFields,i.ModeSearch.ADVANCE));r(2);let y=w(U(5,19,e.transferTemplates,i.searchTerm,i.transferTemplateSearchFields,i.ModeSearch.ADVANCE));r(2);let b=w(U(7,25,e.recentlyTransaction,i.searchTerm,i.recentTransactionSearchFields,i.ModeSearch.ADVANCE));r(2);let E=w(U(9,31,e.templates,i.searchTerm,i.templateSearchFields,i.ModeSearch.ADVANCE));r(2);let H=w(U(11,37,e.banks,i.searchTerm,i.bankSearchFields,i.ModeSearch.ADVANCE));r(2);let $=w(U(13,43,e.billFeatures,i.searchTerm,i.billSearchFields,i.ModeSearch.ADVANCE));r(2);let J=w(U(15,49,e.topUps,i.searchTerm,i.topUpSearchFields,i.ModeSearch.ADVANCE));r(2),g(!(n!=null&&n.length)&&!(a!=null&&a.length)&&!(y!=null&&y.length)&&!(b!=null&&b.length)&&!(E!=null&&E.length)&&!(H!=null&&H.length)&&!($!=null&&$.length)&&!(J!=null&&J.length)?16:-1),r(),g(n!=null&&n.length?17:-1),r(),g(a!=null&&a.length||y!=null&&y.length||b!=null&&b.length?18:-1),r(),g(E!=null&&E.length?19:-1),r(),g(H!=null&&H.length?20:-1),r(),g($!=null&&$.length?21:-1),r(),g(J!=null&&J.length?22:-1)}}function ei(t,l){if(t&1&&te(0,43),t&2){p();let e=F(0);p();let i=G(7);s("ngTemplateOutlet",i)("ngTemplateOutletContext",Se(2,Tt,e))}}function ti(t,l){t&1&&(o(0,"div",44)(1,"div",45),_(2,"div",46)(3,"div",47)(4,"div",48),c(),o(5,"div",49)(6,"div",50),_(7,"div",51),o(8,"div",52),_(9,"div",53)(10,"div",54)(11,"div",55),c()(),o(12,"div",56),_(13,"div",51),o(14,"div",52),_(15,"div",53)(16,"div",54),c()(),o(17,"div",56),_(18,"div",51),o(19,"div",52),_(20,"div",53),c()()(),o(21,"div",45),_(22,"div",46)(23,"div",47)(24,"div",48),c()())}function ii(t,l){if(t&1&&(I(0),d(1,"async"),f(2,ei,1,4,"ng-container",43)(3,ti,25,0,"div",44)),t&2){let e=w(S(1,1,p().searchData$));r(2),g(e?2:3)}}function ni(t,l){t&1&&te(0)}function ri(t,l){if(t&1&&f(0,ni,1,0,"ng-container",29),t&2){p();let e=G(5);s("ngTemplateOutlet",e)}}var ue=(()=>{class t{search=!0;searchInput;form;searchData$;suggestedFeatures$;popularSearchData$;searchTerm="";searchHistories=[];MAX_LENGTH_SEARCH=200;commonFields=["bankName","toAccName","beneName","toCard","toAcc","mobileNo","sortName","templateName","amount","bankCode","bankCode247","serviceName"];featureSearchFields=[];beneficiarySearchFields=this.commonFields;transferTemplateSearchFields=this.commonFields.filter(e=>e!=="serviceName");recentTransactionSearchFields=[...this.commonFields,"remark"];templateSearchFields=["templateName","invoice","serviceTypeName"];bankSearchFields=["bankName","sortName","bankCode","bankCode247"];billSearchFields=["serviceName","serviceTypeName","providerName"];topUpSearchFields=["mobileNo","beneName"];ModeSearch=ct;TransformSearchDataKey=lt;MEDIA_DEFAULT_URL=Ve;IMAGE_DEFAULT=_t;serviceCodesNoiBo=Me;DEFAULT_BANK_TARGET=Ue;UI=u(se);fb=u(We);destroyRef=u(z);drawerRef=u(ze);searchService=u(mt);cdr=u(ye);commonService=u(re);constructor(){this.searchData$=this.searchService.getSearchData(),this.popularSearchData$=this.searchService.getPopularSearchData(),this.suggestedFeatures$=this.searchService.getSuggestedFeatures()}ngOnInit(){this.commonService.getLanguage().pipe(j(this.destroyRef)).subscribe(e=>{let i=e===k.En;this.featureSearchFields=["tagFilter",i?"functionNameEn":"functionName"]}),this.searchService.fetchSearchData(),this.form=this.fb.group({search:new qe}),this.searchControl.valueChanges.pipe(j(this.destroyRef),fe(500)).subscribe(e=>{e&&this.searchService.addHHistory(e),this.searchTerm=e,this.cdr.detectChanges()}),this.searchService.getSearchHistory().pipe(j(this.destroyRef)).subscribe(e=>{this.searchHistories=e})}handlePatchValue(e){this.searchControl.markAsTouched(),this.searchControl.markAsDirty(),this.searchControl.setValue(e)}removeItem(e,i){this.searchHistories.splice(i,1),this.searchService.setSearchHistory(this.searchHistories),e.stopPropagation()}close(){this.drawerRef.close()}handleClickSearch(e,i){this.drawerRef.close(),this.searchService.processSelectResult(e,i)}get searchControl(){return this.form.get("search")}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=V({type:t,selectors:[["bidv-omni-search-full"]],viewQuery:function(i,n){if(i&1&&Q(yt,5),i&2){let a;q(a=Y())&&(n.searchInput=a.first)}},hostVars:2,hostBindings:function(i,n){i&2&&D("page-search",n.search)},standalone:!0,features:[R],decls:15,vars:3,consts:[["headerTpl",""],["searchEmptyTpl",""],["historyTpl",""],["searchResultTpl",""],["pillHistorySuffixTpl",""],[3,"title","headerTpl"],[1,"search-content-wrap"],[1,"fake","fake-logo"],[1,"search-content"],[1,"fake","fake-btn"],[1,"head"],[1,"logo-main"],[1,"content","form-search",3,"formGroup"],["formControlName","search",1,"input-search","flex-1",3,"maxLength","shape","size","placeholder","prefixIcon"],["app-button","","aria-label","close","type","button",1,"btn-close-drawer",3,"click","color","iconOnly","size","prefixIcon"],[3,"type"],[1,"content-group"],[1,"content-group--title"],[1,"search-history"],["app-pill","",3,"size","color","suffix"],["app-pill","",3,"click","size","color","suffix"],["app-button","","aria-label","icon clear","type","button",1,"btn-remove",3,"click","iconOnly","mute","size","iconColorChange","prefixIcon"],[1,"content-group-items"],[3,"prefixIcon","typeIcon","type","cardLink"],[3,"click","prefixIcon","typeIcon","type","cardLink"],[1,"item-title","item-title--full"],["app-pill","",3,"size","color"],["app-pill","",3,"click","size","color"],["app-accordion","",3,"header","type","isActive"],[4,"ngTemplateOutlet"],["app-card-category","",3,"prefixIcon","typeIcon","type","cardLink"],["app-card-category","",3,"click","prefixIcon","typeIcon","type","cardLink"],["app-card-category","",1,"card-category-directory",3,"cardLink","type","prefixIcon","typeIcon"],["app-card-category","",3,"cardLink","type","prefixIcon","typeIcon"],["app-card-category","",1,"card-category-directory",3,"click","cardLink","type","prefixIcon","typeIcon"],[1,"flex","gap-3","items-center"],[1,"item-title"],[1,"card-category-directory--favourite-icon",3,"src","size"],[1,"item-subtext"],["app-card-category","",3,"click","cardLink","type","prefixIcon","typeIcon"],[1,"item-money"],[1,"flex","gap-1","items-center"],[3,"src","colorChange","size"],[3,"ngTemplateOutlet","ngTemplateOutletContext"],[1,"space-y-2","md:space-y-4"],[1,"flex","flex-wrap","gap-3"],[1,"skeleton-light","h-8","w-1/3","max-w-24"],[1,"skeleton-light","h-8","w-2/4","max-w-36"],[1,"skeleton-light","h-8","w-2/5","max-w-32"],[1,"grid","gap-x-4","gap-y-3","sm:grid-cols-2","md:grid-cols-3"],[1,"skeleton","w-full","h-24","flex","gap-3","p-4","items-center"],[1,"skeleton-light","size-10"],[1,"space-y-1","flex-1"],[1,"skeleton-light","w-full","h-7"],[1,"skeleton-light","w-2/3","h-5"],[1,"skeleton-light","w-1/3","min-w-20","h-4"],[1,"skeleton","w-full","h-20","flex","gap-3","p-4","items-center"]],template:function(i,n){if(i&1&&(f(0,It,6,12,"ng-template",null,0,K)(2,kt,4,4,"ng-template",null,1,K)(4,Bt,7,9,"ng-template",null,2,K)(6,Zt,23,55,"ng-template",null,3,K),o(8,"app-drawer",5)(9,"div",6),_(10,"div",7),o(11,"div",8),f(12,ii,4,4)(13,ri,1,1,"ng-container"),c(),_(14,"div",9),c()()),i&2){let a=G(1);r(8),s("title","search_full.transaction_history")("headerTpl",a),r(4),g(n.searchTerm?12:13)}},dependencies:[M,Te,Ie,ke,le,ae,et,Ye,je,Qe,Xe,Je,it,nt,pe,rt,ot,at,tt,ce,dt,st],changeDetection:0})}return t})();var Vn=xt(Ct());var oi=["searchbox"],ai=["bidv-omni-header",""];function li(t,l){if(t&1){let e=x();o(0,"button",8),h("click",function(){C(e);let n=p();return v(n.handleExpand(!0))}),c()}if(t&2){let e=p();s("iconOnly",!0)("color",e.UI.ButtonColor.Text)("prefixIcon","media/icons/outline/menu-bar.svg")("size",e.UI.ButtonSize.Sm)("iconSize",6)}}function ci(t,l){if(t&1&&_(0,"div",3),t&2){let e=p();s("routerLink",e.MainRoute.Home)}}function pi(t,l){t&1&&_(0,"app-breadcrumb")}function si(t,l){t&1&&_(0,"app-svg",6),t&2&&s("src","media/icons/outline/search.svg")}function mi(t,l){t&1&&(_(0,"app-svg",9),o(1,"p",10),m(2),d(3,"translate"),c()),t&2&&(s("src","media/icons/doutone/icon-he-thong/ht-search.svg")("colorChange",!1),r(2),T(" ",S(3,3,"common.tim_kiem")," "))}function di(t,l){if(t&1){let e=x();o(0,"app-toggle-language",12),h("change",function(){C(e);let n=p(2);return v(n.changeLang())}),c()}}function _i(t,l){if(t&1){let e=x();f(0,di,1,0,"app-toggle-language"),o(1,"button",11),h("click",function(){C(e);let n=p();return v(n.handleLogout())}),c()}if(t&2){let e=p();g(e.language?0:-1),r(),s("iconOnly",!0)("size",e.UI.ButtonSize.Md)("color",e.UI.ButtonColor.White)("prefixIcon","assets/media/icons/outline/logout.svg")}}var Xn=(()=>{class t extends pt{searchBox;appSearch;header=!0;allowShowBreadcrumb=!0;allowShowSidebar=!0;expandSidebarEvent=new W;MainRoute=He;timeSpace=1e3;isNoticed=!1;tick;tabid="";language;linkAtm="";SYSTEM_MENU=Ke;UI=u(se);storageService=u(O);modalService=u(Oe);apiService=u(Pe);processApi=u($e);destroyRef=u(z);cryptoService=u(ne);drawerService=u(Ge);constructor(){super();let e=this.SYSTEM_MENU.find(i=>i.name==="menu.tim_kiem_atm");if(e){let i=e.link,n=e.linkEN,a=this.storeService.language;this.language=a,a==="vi"?this.linkAtm=i:this.linkAtm=n||"",this.store$=this.storeService.changes.subscribe(y=>{y.key===ie.LANGUAGE&&(y.value==="vi"?this.linkAtm=i||"":this.linkAtm=n||"")})}this.onLoopTime()}ngOnInit(){this.userInfo=this.storageService.userInfo;let e=document.querySelector("[data-tabid]");this.tabid=e&&e.getAttribute("data-tabid")||""}onLoopTime(){this.tick=setInterval(()=>{this.onCheckSession()},this.timeSpace)}onCheckSession(){let e=this.storageService.appEvent,i=e?JSON.parse(this.cryptoService.decryptText(e)):void 0;i&&i.id!==this.tabid&&i.type===oe.Logout&&!this.isNoticed&&(this.isNoticed=!0,this.apiService.expiredSession()),!this.storageService.isAuthenticated&&!this.isNoticed&&this.idleMessage()}idleMessage(){this.apiService.expiredSession(void 0,this.processApi.isFocusInApp),this.isNoticed=!0}handleLogout(){this.modalService.confirm({title:this.translate.instant("heading.dang_xuat"),message:this.translate.instant("message.xac_nhan_dang_xuat"),btnConfirm:{title:this.translate.instant("btn.accept"),color:we.Primary},confirm:()=>{this.apiService.logout()}})}handelSearch(){this.drawerService.open(ue,{placement:"top",class:"drawer-body-white drawer-md-full"})}handleExpand(e){this.expandSidebarEvent.emit(e)}ngOnDestroy(){super.ngOnDestroy(),clearInterval(this.tick)}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=V({type:t,selectors:[["","bidv-omni-header",""]],viewQuery:function(i,n){if(i&1&&(Q(oi,5),Q(ue,5)),i&2){let a;q(a=Y())&&(n.searchBox=a.first),q(a=Y())&&(n.appSearch=a.first)}},hostVars:2,hostBindings:function(i,n){i&2&&D("inlogin-header",n.header)},inputs:{allowShowBreadcrumb:"allowShowBreadcrumb",allowShowSidebar:"allowShowSidebar"},outputs:{expandSidebarEvent:"expandSidebarEvent"},standalone:!0,features:[xe,R],attrs:ai,decls:10,vars:4,consts:[[1,"header-inner"],["app-button","","aria-label","btn expand",1,"header--btn-expand-sidebar",3,"iconOnly","color","prefixIcon","size","iconSize"],[1,"header-left"],[1,"header--logo-main",3,"routerLink"],[1,"header-right"],[1,"header-right--search-box",3,"click"],[1,"header-right--search-box--icon",3,"src"],["app-button","","aria-label","logout out",1,"btn-logout",3,"iconOnly","size","color","prefixIcon"],["app-button","","aria-label","btn expand",1,"header--btn-expand-sidebar",3,"click","iconOnly","color","prefixIcon","size","iconSize"],[1,"header-right--search-box--icon",3,"src","colorChange"],[1,"header-right--search-box--content"],["app-button","","aria-label","logout out",1,"btn-logout",3,"click","iconOnly","size","color","prefixIcon"],[3,"change"]],template:function(i,n){i&1&&(o(0,"div",0),f(1,li,1,5,"button",1),o(2,"div",2),f(3,ci,1,1,"div",3)(4,pi,1,0,"app-breadcrumb"),c(),o(5,"div",4)(6,"div",5),h("click",function(){return n.handelSearch()}),f(7,si,1,1,"app-svg",6)(8,mi,4,5),c(),f(9,_i,2,5,"button",7),c()()),i&2&&(r(),g(n.allowShowSidebar?-1:1),r(2),g(n.allowShowSidebar?n.allowShowBreadcrumb?4:-1:3),r(4),g(n.allowShowSidebar?8:7),r(2),g(n.allowShowSidebar?9:-1))},dependencies:[M,Ae,Le,le,ae,Ze,pe,de,ut,ce]})}return t})();export{de as a,Hi as b,hi as c,Ct as d,Xn as e};
