import{Pd as S,da as z,ea as y}from"./chunk-K5H3SJL5.js";import{$b as u,Cc as g,Ed as C,Hb as l,Jb as t,Jd as v,Nb as d,Qd as _,Wb as p,Xb as a,Yb as c,ec as f,fc as x,gc as I,ob as s,sa as m}from"./chunk-YK6FMNSY.js";var h=["*"];function P(i,r){if(i&1&&(p(0,"div",3),c(1,"app-svg",4),a()),i&2){let e=f();s(),t("src",e.prefixIcon)("size",e.sizeIcon||e.SIZE_ICON[e.size])}}function B(i,r){if(i&1&&c(0,"app-svg",8),i&2){let e=f(2);t("size",e.sizeIcon||e.SIZE_ICON[e.size])("src",e.suffixIcon)}}function T(i,r){if(i&1&&(p(0,"div",5),l(1,B,1,2,"app-svg",6),u(2,7),a()),i&2){let e=f();s(),t("ngIf",e.suffixIcon),s(),t("ngTemplateOutlet",e.suffix||null)}}var j=(()=>{class i{color=z.Primary;size=y.Md;suffix;prefixIcon;sizeIcon;suffixIcon;data;labelClick=!1;isBadge=!1;get hostClass(){return this.initClass()}initClass(){let e=["app-pill","app-pill--"+this.size,"app-pill--"+this.color];return this.isBadge&&e.push("app-pill--badge"),e.join(" ")}SIZE_ICON={large:5,medium:4,small:4};static \u0275fac=function(n){return new(n||i)};static \u0275cmp=m({type:i,selectors:[["app-pill"],["","app-pill",""]],hostVars:2,hostBindings:function(n,o){n&2&&d(o.hostClass)},inputs:{color:"color",size:"size",suffix:"suffix",prefixIcon:"prefixIcon",sizeIcon:"sizeIcon",suffixIcon:"suffixIcon",data:"data",labelClick:"labelClick",isBadge:"isBadge"},standalone:!0,features:[g],ngContentSelectors:h,decls:4,vars:2,consts:[["class","prefix",4,"ngIf"],[1,"title"],["class","suffix",4,"ngIf"],[1,"prefix"],[3,"src","size"],[1,"suffix"],["class","suffix-icon",3,"size","src",4,"ngIf"],[3,"ngTemplateOutlet"],[1,"suffix-icon",3,"size","src"]],template:function(n,o){n&1&&(x(),l(0,P,2,2,"div",0),p(1,"div",1),I(2),a(),l(3,T,3,2,"div",2)),n&2&&(t("ngIf",o.prefixIcon),s(3),t("ngIf",o.suffixIcon||o.suffix))},dependencies:[_,C,v,S]})}return i})();export{j as a};
