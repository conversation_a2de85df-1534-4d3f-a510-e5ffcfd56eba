import{f}from"./chunk-AASME2FE.js";import{fa as h}from"./chunk-AX4DCRSD.js";import{A as o,Ka as a,Qa as p,ba as l,f as s,na as i,ua as c}from"./chunk-YK6FMNSY.js";var w=(()=>{class r{allow;blockString="[^a-zA-Z0-9\\u0080-\\u9fff ALLOW_STRING]";elementRef=i(p);injector=i(a);control=i(f);helperService=i(h);destroy$=new s;ngOnInit(){if(this.allow){let t=this.allow.split("").map(e=>e&&"\\"+e);this.blockString=this.blockString.replace("ALLOW_STRING",t.join(""))}else this.blockString=this.blockString.replace("ALLOW_STRING","");o(this.elementRef.nativeElement,"keydown").pipe(l(this.destroy$)).subscribe(t=>{this.helperService.allowMetaKey(t)||t.key.search(new RegExp(this.blockString,"gi"))>=0&&t.preventDefault()}),o(this.elementRef.nativeElement,"paste").pipe(l(this.destroy$)).subscribe(t=>{setTimeout(()=>{let e=this.control.value;if(e&&typeof e=="string"){e=e?.replace(new RegExp(this.blockString,"gi"),"");let n=e?.trim();this.control&&this.control.control?.setValue(n)}})}),o(this.elementRef.nativeElement,"blur").pipe(l(this.destroy$)).subscribe(t=>{let e=this.control.value;if(e&&typeof e=="string"){e=e?.replace(new RegExp(this.blockString.replace("\\u0080-\\u9fff","\\\\u0080-\\\\u9fff"),"gi"),"");let n=e?.trim();this.control&&this.control.control?.setValue(n)}})}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}static \u0275fac=function(e){return new(e||r)};static \u0275dir=c({type:r,selectors:[["","appNoneSpecial",""]],inputs:{allow:"allow"},standalone:!0})}return r})();export{w as a};
