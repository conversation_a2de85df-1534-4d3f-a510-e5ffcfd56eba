import{Pe as b,jf as O,xf as H}from"./chunk-K5H3SJL5.js";import{$b as m,Cc as I,Ea as u,Ed as E,Fa as _,Hb as c,Jb as o,Jd as B,Lb as w,Qc as y,Qd as S,Wb as i,Xb as a,ac as x,cc as T,ec as p,fc as g,gc as v,na as s,nc as C,ob as r,oc as h,pc as D,sa as f}from"./chunk-YK6FMNSY.js";var z=["*"];function R(e,d){e&1&&m(0)}function U(e,d){if(e&1&&(i(0,"div",7),m(1,8),a()),e&2){let t=p(2);r(),o("ngTemplateOutlet",t.headerExtendBotTpl)}}function V(e,d){if(e&1&&(i(0,"div",4),c(1,R,1,0,"ng-container",5)(2,U,2,1,"div",6),a()),e&2){let t=p(),n=C(5);r(),o("ngTemplateOutlet",t.headerTpl||n),r(),o("ngIf",t.headerExtendBotTpl)}}function j(e,d){if(e&1&&(i(0,"div",9),m(1,8),a()),e&2){let t=p();r(),o("ngTemplateOutlet",t.footerTpl)}}function k(e,d){if(e&1){let t=x();i(0,"div",10)(1,"p",11),h(2),a(),i(3,"button",12),T("click",function(){u(t);let l=p();return _(l.close())}),a()()}if(e&2){let t=p();r(2),D(t.title),r(),o("iconOnly",!0)("mute",!0)("size",t.UI.ButtonSize.Lg)("color",t.UI.ButtonColor.Text)("prefixIcon","assets/media/icons/outline/close.svg")}}var Q=(()=>{class e{drawer=!0;title="Draw title";showHeader=!0;headerTpl;headerExtendBotTpl;footerTpl;drawerRef=s(H);UI=s(O);close(){this.drawerRef.close()}static \u0275fac=function(n){return new(n||e)};static \u0275cmp=f({type:e,selectors:[["app-drawer"]],hostVars:2,hostBindings:function(n,l){n&2&&w("app-drawer",l.drawer)},inputs:{title:"title",showHeader:"showHeader",headerTpl:"headerTpl",headerExtendBotTpl:"headerExtendBotTpl",footerTpl:"footerTpl"},standalone:!0,features:[I],ngContentSelectors:z,decls:6,vars:2,consts:[["headerDefaultTpl",""],["class","drawer-header",4,"ngIf"],[1,"drawer-body"],["class","drawer-footer",4,"ngIf"],[1,"drawer-header"],[4,"ngTemplateOutlet"],["class","drawer-header-bottom",4,"ngIf"],[1,"drawer-header-bottom"],[3,"ngTemplateOutlet"],[1,"drawer-footer"],[1,"drawer-title-wrap"],[1,"drawer-title"],["app-button","","aria-label","close",1,"btn-close-drawer",3,"click","iconOnly","mute","size","color","prefixIcon"]],template:function(n,l){n&1&&(g(),c(0,V,3,2,"div",1),i(1,"div",2),v(2),a(),c(3,j,2,1,"div",3)(4,k,4,6,"ng-template",null,0,y)),n&2&&(o("ngIf",l.showHeader),r(3),o("ngIf",l.footerTpl))},dependencies:[b,S,E,B]})}return e})();export{Q as a};
