import{td as $t}from"./chunk-K5H3SJL5.js";import{Cc as Mt,Da as Ct,Lb as St,Yb as xt,kc as At,lc as qt,mc as Ot,na as yt,sa as bt}from"./chunk-YK6FMNSY.js";import{e as kt,f as Bt,g as rt}from"./chunk-TSRGIXR5.js";var Dt=kt((ct,_t)=>{"use strict";(function(et,nt){typeof ct=="object"&&typeof _t=="object"?_t.exports=nt():typeof define=="function"&&define.amd?define([],nt):typeof ct=="object"?ct.QRCodeStyling=nt():et.QRCodeStyling=nt()})(ct,()=>(()=>{var et={873:(P,I)=>{var H,gt,ot=function(){var Y=function(f,l){var a=f,e=tt[l],t=null,i=0,n=null,r=[],d={},g=function(o,h){t=function(s){for(var u=new Array(s),c=0;c<s;c+=1){u[c]=new Array(s);for(var b=0;b<s;b+=1)u[c][b]=null}return u}(i=4*a+17),p(0,0),p(i-7,0),p(0,i-7),v(),w(),C(o,h),a>=7&&S(o),n==null&&(n=y(a,e,r)),_(n,h)},p=function(o,h){for(var s=-1;s<=7;s+=1)if(!(o+s<=-1||i<=o+s))for(var u=-1;u<=7;u+=1)h+u<=-1||i<=h+u||(t[o+s][h+u]=0<=s&&s<=6&&(u==0||u==6)||0<=u&&u<=6&&(s==0||s==6)||2<=s&&s<=4&&2<=u&&u<=4)},w=function(){for(var o=8;o<i-8;o+=1)t[o][6]==null&&(t[o][6]=o%2==0);for(var h=8;h<i-8;h+=1)t[6][h]==null&&(t[6][h]=h%2==0)},v=function(){for(var o=T.getPatternPosition(a),h=0;h<o.length;h+=1)for(var s=0;s<o.length;s+=1){var u=o[h],c=o[s];if(t[u][c]==null)for(var b=-2;b<=2;b+=1)for(var O=-2;O<=2;O+=1)t[u+b][c+O]=b==-2||b==2||O==-2||O==2||b==0&&O==0}},S=function(o){for(var h=T.getBCHTypeNumber(a),s=0;s<18;s+=1){var u=!o&&(h>>s&1)==1;t[Math.floor(s/3)][s%3+i-8-3]=u}for(s=0;s<18;s+=1)u=!o&&(h>>s&1)==1,t[s%3+i-8-3][Math.floor(s/3)]=u},C=function(o,h){for(var s=e<<3|h,u=T.getBCHTypeInfo(s),c=0;c<15;c+=1){var b=!o&&(u>>c&1)==1;c<6?t[c][8]=b:c<8?t[c+1][8]=b:t[i-15+c][8]=b}for(c=0;c<15;c+=1)b=!o&&(u>>c&1)==1,c<8?t[8][i-c-1]=b:c<9?t[8][15-c-1+1]=b:t[8][15-c-1]=b;t[i-8][8]=!o},_=function(o,h){for(var s=-1,u=i-1,c=7,b=0,O=T.getMaskFunction(h),A=i-1;A>0;A-=2)for(A==6&&(A-=1);;){for(var M=0;M<2;M+=1)if(t[u][A-M]==null){var $=!1;b<o.length&&($=(o[b]>>>c&1)==1),O(u,A-M)&&($=!$),t[u][A-M]=$,(c-=1)==-1&&(b+=1,c=7)}if((u+=s)<0||i<=u){u-=s,s=-s;break}}},y=function(o,h,s){for(var u=lt.getRSBlocks(o,h),c=at(),b=0;b<s.length;b+=1){var O=s[b];c.put(O.getMode(),4),c.put(O.getLength(),T.getLengthInBits(O.getMode(),o)),O.write(c)}var A=0;for(b=0;b<u.length;b+=1)A+=u[b].dataCount;if(c.getLengthInBits()>8*A)throw"code length overflow. ("+c.getLengthInBits()+">"+8*A+")";for(c.getLengthInBits()+4<=8*A&&c.put(0,4);c.getLengthInBits()%8!=0;)c.putBit(!1);for(;!(c.getLengthInBits()>=8*A||(c.put(236,8),c.getLengthInBits()>=8*A));)c.put(17,8);return function(M,$){for(var k=0,R=0,E=0,B=new Array($.length),D=new Array($.length),x=0;x<$.length;x+=1){var N=$[x].dataCount,j=$[x].totalCount-N;R=Math.max(R,N),E=Math.max(E,j),B[x]=new Array(N);for(var q=0;q<B[x].length;q+=1)B[x][q]=255&M.getBuffer()[q+k];k+=N;var L=T.getErrorCorrectPolynomial(j),U=st(B[x],L.getLength()-1).mod(L);for(D[x]=new Array(L.getLength()-1),q=0;q<D[x].length;q+=1){var G=q+U.getLength()-D[x].length;D[x][q]=G>=0?U.getAt(G):0}}var Z=0;for(q=0;q<$.length;q+=1)Z+=$[q].totalCount;var ut=new Array(Z),K=0;for(q=0;q<R;q+=1)for(x=0;x<$.length;x+=1)q<B[x].length&&(ut[K]=B[x][q],K+=1);for(q=0;q<E;q+=1)for(x=0;x<$.length;x+=1)q<D[x].length&&(ut[K]=D[x][q],K+=1);return ut}(c,u)};d.addData=function(o,h){var s=null;switch(h=h||"Byte"){case"Numeric":s=ft(o);break;case"Alphanumeric":s=pt(o);break;case"Byte":s=ht(o);break;case"Kanji":s=wt(o);break;default:throw"mode:"+h}r.push(s),n=null},d.isDark=function(o,h){if(o<0||i<=o||h<0||i<=h)throw o+","+h;return t[o][h]},d.getModuleCount=function(){return i},d.make=function(){if(a<1){for(var o=1;o<40;o++){for(var h=lt.getRSBlocks(o,e),s=at(),u=0;u<r.length;u++){var c=r[u];s.put(c.getMode(),4),s.put(c.getLength(),T.getLengthInBits(c.getMode(),o)),c.write(s)}var b=0;for(u=0;u<h.length;u++)b+=h[u].dataCount;if(s.getLengthInBits()<=8*b)break}a=o}g(!1,function(){for(var O=0,A=0,M=0;M<8;M+=1){g(!0,M);var $=T.getLostPoint(d);(M==0||O>$)&&(O=$,A=M)}return A}())},d.createTableTag=function(o,h){o=o||2;var s="";s+='<table style="',s+=" border-width: 0px; border-style: none;",s+=" border-collapse: collapse;",s+=" padding: 0px; margin: "+(h=h===void 0?4*o:h)+"px;",s+='">',s+="<tbody>";for(var u=0;u<d.getModuleCount();u+=1){s+="<tr>";for(var c=0;c<d.getModuleCount();c+=1)s+='<td style="',s+=" border-width: 0px; border-style: none;",s+=" border-collapse: collapse;",s+=" padding: 0px; margin: 0px;",s+=" width: "+o+"px;",s+=" height: "+o+"px;",s+=" background-color: ",s+=d.isDark(u,c)?"#000000":"#ffffff",s+=";",s+='"/>';s+="</tr>"}return(s+="</tbody>")+"</table>"},d.createSvgTag=function(o,h,s,u){var c={};typeof arguments[0]=="object"&&(o=(c=arguments[0]).cellSize,h=c.margin,s=c.alt,u=c.title),o=o||2,h=h===void 0?4*o:h,(s=typeof s=="string"?{text:s}:s||{}).text=s.text||null,s.id=s.text?s.id||"qrcode-description":null,(u=typeof u=="string"?{text:u}:u||{}).text=u.text||null,u.id=u.text?u.id||"qrcode-title":null;var b,O,A,M,$=d.getModuleCount()*o+2*h,k="";for(M="l"+o+",0 0,"+o+" -"+o+",0 0,-"+o+"z ",k+='<svg version="1.1" xmlns="http://www.w3.org/2000/svg"',k+=c.scalable?"":' width="'+$+'px" height="'+$+'px"',k+=' viewBox="0 0 '+$+" "+$+'" ',k+=' preserveAspectRatio="xMinYMin meet"',k+=u.text||s.text?' role="img" aria-labelledby="'+m([u.id,s.id].join(" ").trim())+'"':"",k+=">",k+=u.text?'<title id="'+m(u.id)+'">'+m(u.text)+"</title>":"",k+=s.text?'<description id="'+m(s.id)+'">'+m(s.text)+"</description>":"",k+='<rect width="100%" height="100%" fill="white" cx="0" cy="0"/>',k+='<path d="',O=0;O<d.getModuleCount();O+=1)for(A=O*o+h,b=0;b<d.getModuleCount();b+=1)d.isDark(O,b)&&(k+="M"+(b*o+h)+","+A+M);return(k+='" stroke="transparent" fill="black"/>')+"</svg>"},d.createDataURL=function(o,h){o=o||2,h=h===void 0?4*o:h;var s=d.getModuleCount()*o+2*h,u=h,c=s-h;return dt(s,s,function(b,O){if(u<=b&&b<c&&u<=O&&O<c){var A=Math.floor((b-u)/o),M=Math.floor((O-u)/o);return d.isDark(M,A)?0:1}return 1})},d.createImgTag=function(o,h,s){o=o||2,h=h===void 0?4*o:h;var u=d.getModuleCount()*o+2*h,c="";return c+="<img",c+=' src="',c+=d.createDataURL(o,h),c+='"',c+=' width="',c+=u,c+='"',c+=' height="',c+=u,c+='"',s&&(c+=' alt="',c+=m(s),c+='"'),c+"/>"};var m=function(o){for(var h="",s=0;s<o.length;s+=1){var u=o.charAt(s);switch(u){case"<":h+="&lt;";break;case">":h+="&gt;";break;case"&":h+="&amp;";break;case'"':h+="&quot;";break;default:h+=u}}return h};return d.createASCII=function(o,h){if((o=o||1)<2)return function(B){B=B===void 0?2:B;var D,x,N,j,q,L=1*d.getModuleCount()+2*B,U=B,G=L-B,Z={"\u2588\u2588":"\u2588","\u2588 ":"\u2580"," \u2588":"\u2584","  ":" "},ut={"\u2588\u2588":"\u2580","\u2588 ":"\u2580"," \u2588":" ","  ":" "},K="";for(D=0;D<L;D+=2){for(N=Math.floor((D-U)/1),j=Math.floor((D+1-U)/1),x=0;x<L;x+=1)q="\u2588",U<=x&&x<G&&U<=D&&D<G&&d.isDark(N,Math.floor((x-U)/1))&&(q=" "),U<=x&&x<G&&U<=D+1&&D+1<G&&d.isDark(j,Math.floor((x-U)/1))?q+=" ":q+="\u2588",K+=B<1&&D+1>=G?ut[q]:Z[q];K+=`
`}return L%2&&B>0?K.substring(0,K.length-L-1)+Array(L+1).join("\u2580"):K.substring(0,K.length-1)}(h);o-=1,h=h===void 0?2*o:h;var s,u,c,b,O=d.getModuleCount()*o+2*h,A=h,M=O-h,$=Array(o+1).join("\u2588\u2588"),k=Array(o+1).join("  "),R="",E="";for(s=0;s<O;s+=1){for(c=Math.floor((s-A)/o),E="",u=0;u<O;u+=1)b=1,A<=u&&u<M&&A<=s&&s<M&&d.isDark(c,Math.floor((u-A)/o))&&(b=0),E+=b?$:k;for(c=0;c<o;c+=1)R+=E+`
`}return R.substring(0,R.length-1)},d.renderTo2dContext=function(o,h){h=h||2;for(var s=d.getModuleCount(),u=0;u<s;u++)for(var c=0;c<s;c++)o.fillStyle=d.isDark(u,c)?"black":"white",o.fillRect(u*h,c*h,h,h)},d};Y.stringToBytes=(Y.stringToBytesFuncs={default:function(f){for(var l=[],a=0;a<f.length;a+=1){var e=f.charCodeAt(a);l.push(255&e)}return l}}).default,Y.createStringToBytes=function(f,l){var a=function(){for(var t=mt(f),i=function(){var w=t.read();if(w==-1)throw"eof";return w},n=0,r={};;){var d=t.read();if(d==-1)break;var g=i(),p=i()<<8|i();r[String.fromCharCode(d<<8|g)]=p,n+=1}if(n!=l)throw n+" != "+l;return r}(),e=63;return function(t){for(var i=[],n=0;n<t.length;n+=1){var r=t.charCodeAt(n);if(r<128)i.push(r);else{var d=a[t.charAt(n)];typeof d=="number"?(255&d)==d?i.push(d):(i.push(d>>>8),i.push(255&d)):i.push(e)}}return i}};var it,V,X,z,J,tt={L:1,M:0,Q:3,H:2},T=(it=[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],V=1335,X=7973,J=function(f){for(var l=0;f!=0;)l+=1,f>>>=1;return l},(z={}).getBCHTypeInfo=function(f){for(var l=f<<10;J(l)-J(V)>=0;)l^=V<<J(l)-J(V);return 21522^(f<<10|l)},z.getBCHTypeNumber=function(f){for(var l=f<<12;J(l)-J(X)>=0;)l^=X<<J(l)-J(X);return f<<12|l},z.getPatternPosition=function(f){return it[f-1]},z.getMaskFunction=function(f){switch(f){case 0:return function(l,a){return(l+a)%2==0};case 1:return function(l,a){return l%2==0};case 2:return function(l,a){return a%3==0};case 3:return function(l,a){return(l+a)%3==0};case 4:return function(l,a){return(Math.floor(l/2)+Math.floor(a/3))%2==0};case 5:return function(l,a){return l*a%2+l*a%3==0};case 6:return function(l,a){return(l*a%2+l*a%3)%2==0};case 7:return function(l,a){return(l*a%3+(l+a)%2)%2==0};default:throw"bad maskPattern:"+f}},z.getErrorCorrectPolynomial=function(f){for(var l=st([1],0),a=0;a<f;a+=1)l=l.multiply(st([1,F.gexp(a)],0));return l},z.getLengthInBits=function(f,l){if(1<=l&&l<10)switch(f){case 1:return 10;case 2:return 9;case 4:case 8:return 8;default:throw"mode:"+f}else if(l<27)switch(f){case 1:return 12;case 2:return 11;case 4:return 16;case 8:return 10;default:throw"mode:"+f}else{if(!(l<41))throw"type:"+l;switch(f){case 1:return 14;case 2:return 13;case 4:return 16;case 8:return 12;default:throw"mode:"+f}}},z.getLostPoint=function(f){for(var l=f.getModuleCount(),a=0,e=0;e<l;e+=1)for(var t=0;t<l;t+=1){for(var i=0,n=f.isDark(e,t),r=-1;r<=1;r+=1)if(!(e+r<0||l<=e+r))for(var d=-1;d<=1;d+=1)t+d<0||l<=t+d||r==0&&d==0||n==f.isDark(e+r,t+d)&&(i+=1);i>5&&(a+=3+i-5)}for(e=0;e<l-1;e+=1)for(t=0;t<l-1;t+=1){var g=0;f.isDark(e,t)&&(g+=1),f.isDark(e+1,t)&&(g+=1),f.isDark(e,t+1)&&(g+=1),f.isDark(e+1,t+1)&&(g+=1),g!=0&&g!=4||(a+=3)}for(e=0;e<l;e+=1)for(t=0;t<l-6;t+=1)f.isDark(e,t)&&!f.isDark(e,t+1)&&f.isDark(e,t+2)&&f.isDark(e,t+3)&&f.isDark(e,t+4)&&!f.isDark(e,t+5)&&f.isDark(e,t+6)&&(a+=40);for(t=0;t<l;t+=1)for(e=0;e<l-6;e+=1)f.isDark(e,t)&&!f.isDark(e+1,t)&&f.isDark(e+2,t)&&f.isDark(e+3,t)&&f.isDark(e+4,t)&&!f.isDark(e+5,t)&&f.isDark(e+6,t)&&(a+=40);var p=0;for(t=0;t<l;t+=1)for(e=0;e<l;e+=1)f.isDark(e,t)&&(p+=1);return a+Math.abs(100*p/l/l-50)/5*10},z),F=function(){for(var f=new Array(256),l=new Array(256),a=0;a<8;a+=1)f[a]=1<<a;for(a=8;a<256;a+=1)f[a]=f[a-4]^f[a-5]^f[a-6]^f[a-8];for(a=0;a<255;a+=1)l[f[a]]=a;return{glog:function(e){if(e<1)throw"glog("+e+")";return l[e]},gexp:function(e){for(;e<0;)e+=255;for(;e>=256;)e-=255;return f[e]}}}();function st(f,l){if(f.length===void 0)throw f.length+"/"+l;var a=function(){for(var t=0;t<f.length&&f[t]==0;)t+=1;for(var i=new Array(f.length-t+l),n=0;n<f.length-t;n+=1)i[n]=f[n+t];return i}(),e={getAt:function(t){return a[t]},getLength:function(){return a.length},multiply:function(t){for(var i=new Array(e.getLength()+t.getLength()-1),n=0;n<e.getLength();n+=1)for(var r=0;r<t.getLength();r+=1)i[n+r]^=F.gexp(F.glog(e.getAt(n))+F.glog(t.getAt(r)));return st(i,0)},mod:function(t){if(e.getLength()-t.getLength()<0)return e;for(var i=F.glog(e.getAt(0))-F.glog(t.getAt(0)),n=new Array(e.getLength()),r=0;r<e.getLength();r+=1)n[r]=e.getAt(r);for(r=0;r<t.getLength();r+=1)n[r]^=F.gexp(F.glog(t.getAt(r))+i);return st(n,0).mod(t)}};return e}var lt=function(){var f=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12,7,37,13],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],l=function(e,t){var i={};return i.totalCount=e,i.dataCount=t,i},a={getRSBlocks:function(e,t){var i=function(S,C){switch(C){case tt.L:return f[4*(S-1)+0];case tt.M:return f[4*(S-1)+1];case tt.Q:return f[4*(S-1)+2];case tt.H:return f[4*(S-1)+3];default:return}}(e,t);if(i===void 0)throw"bad rs block @ typeNumber:"+e+"/errorCorrectionLevel:"+t;for(var n=i.length/3,r=[],d=0;d<n;d+=1)for(var g=i[3*d+0],p=i[3*d+1],w=i[3*d+2],v=0;v<g;v+=1)r.push(l(p,w));return r}};return a}(),at=function(){var f=[],l=0,a={getBuffer:function(){return f},getAt:function(e){var t=Math.floor(e/8);return(f[t]>>>7-e%8&1)==1},put:function(e,t){for(var i=0;i<t;i+=1)a.putBit((e>>>t-i-1&1)==1)},getLengthInBits:function(){return l},putBit:function(e){var t=Math.floor(l/8);f.length<=t&&f.push(0),e&&(f[t]|=128>>>l%8),l+=1}};return a},ft=function(f){var l=f,a={getMode:function(){return 1},getLength:function(i){return l.length},write:function(i){for(var n=l,r=0;r+2<n.length;)i.put(e(n.substring(r,r+3)),10),r+=3;r<n.length&&(n.length-r==1?i.put(e(n.substring(r,r+1)),4):n.length-r==2&&i.put(e(n.substring(r,r+2)),7))}},e=function(i){for(var n=0,r=0;r<i.length;r+=1)n=10*n+t(i.charAt(r));return n},t=function(i){if("0"<=i&&i<="9")return i.charCodeAt(0)-48;throw"illegal char :"+i};return a},pt=function(f){var l=f,a={getMode:function(){return 2},getLength:function(t){return l.length},write:function(t){for(var i=l,n=0;n+1<i.length;)t.put(45*e(i.charAt(n))+e(i.charAt(n+1)),11),n+=2;n<i.length&&t.put(e(i.charAt(n)),6)}},e=function(t){if("0"<=t&&t<="9")return t.charCodeAt(0)-48;if("A"<=t&&t<="Z")return t.charCodeAt(0)-65+10;switch(t){case" ":return 36;case"$":return 37;case"%":return 38;case"*":return 39;case"+":return 40;case"-":return 41;case".":return 42;case"/":return 43;case":":return 44;default:throw"illegal char :"+t}};return a},ht=function(f){var l=Y.stringToBytes(f);return{getMode:function(){return 4},getLength:function(a){return l.length},write:function(a){for(var e=0;e<l.length;e+=1)a.put(l[e],8)}}},wt=function(f){var l=Y.stringToBytesFuncs.SJIS;if(!l)throw"sjis not supported.";(function(){var t=l("\u53CB");if(t.length!=2||(t[0]<<8|t[1])!=38726)throw"sjis not supported."})();var a=l(f),e={getMode:function(){return 8},getLength:function(t){return~~(a.length/2)},write:function(t){for(var i=a,n=0;n+1<i.length;){var r=(255&i[n])<<8|255&i[n+1];if(33088<=r&&r<=40956)r-=33088;else{if(!(57408<=r&&r<=60351))throw"illegal char at "+(n+1)+"/"+r;r-=49472}r=192*(r>>>8&255)+(255&r),t.put(r,13),n+=2}if(n<i.length)throw"illegal char at "+(n+1)}};return e},vt=function(){var f=[],l={writeByte:function(a){f.push(255&a)},writeShort:function(a){l.writeByte(a),l.writeByte(a>>>8)},writeBytes:function(a,e,t){e=e||0,t=t||a.length;for(var i=0;i<t;i+=1)l.writeByte(a[i+e])},writeString:function(a){for(var e=0;e<a.length;e+=1)l.writeByte(a.charCodeAt(e))},toByteArray:function(){return f},toString:function(){var a="";a+="[";for(var e=0;e<f.length;e+=1)e>0&&(a+=","),a+=f[e];return a+"]"}};return l},mt=function(f){var l=f,a=0,e=0,t=0,i={read:function(){for(;t<8;){if(a>=l.length){if(t==0)return-1;throw"unexpected end of file./"+t}var r=l.charAt(a);if(a+=1,r=="=")return t=0,-1;r.match(/^\s$/)||(e=e<<6|n(r.charCodeAt(0)),t+=6)}var d=e>>>t-8&255;return t-=8,d}},n=function(r){if(65<=r&&r<=90)return r-65;if(97<=r&&r<=122)return r-97+26;if(48<=r&&r<=57)return r-48+52;if(r==43)return 62;if(r==47)return 63;throw"c:"+r};return i},dt=function(f,l,a){for(var e=function(p,w){var v=p,S=w,C=new Array(p*w),_={setPixel:function(o,h,s){C[h*v+o]=s},write:function(o){o.writeString("GIF87a"),o.writeShort(v),o.writeShort(S),o.writeByte(128),o.writeByte(0),o.writeByte(0),o.writeByte(0),o.writeByte(0),o.writeByte(0),o.writeByte(255),o.writeByte(255),o.writeByte(255),o.writeString(","),o.writeShort(0),o.writeShort(0),o.writeShort(v),o.writeShort(S),o.writeByte(0);var h=y(2);o.writeByte(2);for(var s=0;h.length-s>255;)o.writeByte(255),o.writeBytes(h,s,255),s+=255;o.writeByte(h.length-s),o.writeBytes(h,s,h.length-s),o.writeByte(0),o.writeString(";")}},y=function(o){for(var h=1<<o,s=1+(1<<o),u=o+1,c=m(),b=0;b<h;b+=1)c.add(String.fromCharCode(b));c.add(String.fromCharCode(h)),c.add(String.fromCharCode(s));var O,A,M,$=vt(),k=(O=$,A=0,M=0,{write:function(D,x){if(D>>>x)throw"length over";for(;A+x>=8;)O.writeByte(255&(D<<A|M)),x-=8-A,D>>>=8-A,M=0,A=0;M|=D<<A,A+=x},flush:function(){A>0&&O.writeByte(M)}});k.write(h,u);var R=0,E=String.fromCharCode(C[R]);for(R+=1;R<C.length;){var B=String.fromCharCode(C[R]);R+=1,c.contains(E+B)?E+=B:(k.write(c.indexOf(E),u),c.size()<4095&&(c.size()==1<<u&&(u+=1),c.add(E+B)),E=B)}return k.write(c.indexOf(E),u),k.write(s,u),k.flush(),$.toByteArray()},m=function(){var o={},h=0,s={add:function(u){if(s.contains(u))throw"dup key:"+u;o[u]=h,h+=1},size:function(){return h},indexOf:function(u){return o[u]},contains:function(u){return o[u]!==void 0}};return s};return _}(f,l),t=0;t<l;t+=1)for(var i=0;i<f;i+=1)e.setPixel(i,t,a(i,t));var n=vt();e.write(n);for(var r=function(){var p=0,w=0,v=0,S="",C={},_=function(m){S+=String.fromCharCode(y(63&m))},y=function(m){if(!(m<0)){if(m<26)return 65+m;if(m<52)return m-26+97;if(m<62)return m-52+48;if(m==62)return 43;if(m==63)return 47}throw"n:"+m};return C.writeByte=function(m){for(p=p<<8|255&m,w+=8,v+=1;w>=6;)_(p>>>w-6),w-=6},C.flush=function(){if(w>0&&(_(p<<6-w),p=0,w=0),v%3!=0)for(var m=3-v%3,o=0;o<m;o+=1)S+="="},C.toString=function(){return S},C}(),d=n.toByteArray(),g=0;g<d.length;g+=1)r.writeByte(d[g]);return r.flush(),"data:image/gif;base64,"+r};return Y}();ot.stringToBytesFuncs["UTF-8"]=function(Y){return function(it){for(var V=[],X=0;X<it.length;X++){var z=it.charCodeAt(X);z<128?V.push(z):z<2048?V.push(192|z>>6,128|63&z):z<55296||z>=57344?V.push(224|z>>12,128|z>>6&63,128|63&z):(X++,z=65536+((1023&z)<<10|1023&it.charCodeAt(X)),V.push(240|z>>18,128|z>>12&63,128|z>>6&63,128|63&z))}return V}(Y)},(gt=typeof(H=function(){return ot})=="function"?H.apply(I,[]):H)===void 0||(P.exports=gt)}},nt={};function Q(P){var I=nt[P];if(I!==void 0)return I.exports;var H=nt[P]={exports:{}};return et[P](H,H.exports,Q),H.exports}Q.n=P=>{var I=P&&P.__esModule?()=>P.default:()=>P;return Q.d(I,{a:I}),I},Q.d=(P,I)=>{for(var H in I)Q.o(I,H)&&!Q.o(P,H)&&Object.defineProperty(P,H,{enumerable:!0,get:I[H]})},Q.o=(P,I)=>Object.prototype.hasOwnProperty.call(P,I);var W={};return(()=>{"use strict";Q.d(W,{default:()=>l});let P=a=>!!a&&typeof a=="object"&&!Array.isArray(a);function I(a,...e){if(!e.length)return a;let t=e.shift();return t!==void 0&&P(a)&&P(t)?(a=Object.assign({},a),Object.keys(t).forEach(i=>{let n=a[i],r=t[i];Array.isArray(n)&&Array.isArray(r)?a[i]=r:P(n)&&P(r)?a[i]=I(Object.assign({},n),r):a[i]=r}),I(a,...e)):a}function H(a,e){let t=document.createElement("a");t.download=e,t.href=a,document.body.appendChild(t),t.click(),document.body.removeChild(t)}let gt={L:.07,M:.15,Q:.25,H:.3};class ot{constructor({svg:e,type:t,window:i}){this._svg=e,this._type=t,this._window=i}draw(e,t,i,n){let r;switch(this._type){case"dots":r=this._drawDot;break;case"classy":r=this._drawClassy;break;case"classy-rounded":r=this._drawClassyRounded;break;case"rounded":r=this._drawRounded;break;case"extra-rounded":r=this._drawExtraRounded;break;default:r=this._drawSquare}r.call(this,{x:e,y:t,size:i,getNeighbor:n})}_rotateFigure({x:e,y:t,size:i,rotation:n=0,draw:r}){var d;let g=e+i/2,p=t+i/2;r(),(d=this._element)===null||d===void 0||d.setAttribute("transform",`rotate(${180*n/Math.PI},${g},${p})`)}_basicDot(e){let{size:t,x:i,y:n}=e;this._rotateFigure(Object.assign(Object.assign({},e),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","circle"),this._element.setAttribute("cx",String(i+t/2)),this._element.setAttribute("cy",String(n+t/2)),this._element.setAttribute("r",String(t/2))}}))}_basicSquare(e){let{size:t,x:i,y:n}=e;this._rotateFigure(Object.assign(Object.assign({},e),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","rect"),this._element.setAttribute("x",String(i)),this._element.setAttribute("y",String(n)),this._element.setAttribute("width",String(t)),this._element.setAttribute("height",String(t))}}))}_basicSideRounded(e){let{size:t,x:i,y:n}=e;this._rotateFigure(Object.assign(Object.assign({},e),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","path"),this._element.setAttribute("d",`M ${i} ${n}v ${t}h `+t/2+`a ${t/2} ${t/2}, 0, 0, 0, 0 ${-t}`)}}))}_basicCornerRounded(e){let{size:t,x:i,y:n}=e;this._rotateFigure(Object.assign(Object.assign({},e),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","path"),this._element.setAttribute("d",`M ${i} ${n}v ${t}h ${t}v `+-t/2+`a ${t/2} ${t/2}, 0, 0, 0, ${-t/2} ${-t/2}`)}}))}_basicCornerExtraRounded(e){let{size:t,x:i,y:n}=e;this._rotateFigure(Object.assign(Object.assign({},e),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","path"),this._element.setAttribute("d",`M ${i} ${n}v ${t}h ${t}a ${t} ${t}, 0, 0, 0, ${-t} ${-t}`)}}))}_basicCornersRounded(e){let{size:t,x:i,y:n}=e;this._rotateFigure(Object.assign(Object.assign({},e),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","path"),this._element.setAttribute("d",`M ${i} ${n}v `+t/2+`a ${t/2} ${t/2}, 0, 0, 0, ${t/2} ${t/2}h `+t/2+"v "+-t/2+`a ${t/2} ${t/2}, 0, 0, 0, ${-t/2} ${-t/2}`)}}))}_drawDot({x:e,y:t,size:i}){this._basicDot({x:e,y:t,size:i,rotation:0})}_drawSquare({x:e,y:t,size:i}){this._basicSquare({x:e,y:t,size:i,rotation:0})}_drawRounded({x:e,y:t,size:i,getNeighbor:n}){let r=n?+n(-1,0):0,d=n?+n(1,0):0,g=n?+n(0,-1):0,p=n?+n(0,1):0,w=r+d+g+p;if(w!==0)if(w>2||r&&d||g&&p)this._basicSquare({x:e,y:t,size:i,rotation:0});else{if(w===2){let v=0;return r&&g?v=Math.PI/2:g&&d?v=Math.PI:d&&p&&(v=-Math.PI/2),void this._basicCornerRounded({x:e,y:t,size:i,rotation:v})}if(w===1){let v=0;return g?v=Math.PI/2:d?v=Math.PI:p&&(v=-Math.PI/2),void this._basicSideRounded({x:e,y:t,size:i,rotation:v})}}else this._basicDot({x:e,y:t,size:i,rotation:0})}_drawExtraRounded({x:e,y:t,size:i,getNeighbor:n}){let r=n?+n(-1,0):0,d=n?+n(1,0):0,g=n?+n(0,-1):0,p=n?+n(0,1):0,w=r+d+g+p;if(w!==0)if(w>2||r&&d||g&&p)this._basicSquare({x:e,y:t,size:i,rotation:0});else{if(w===2){let v=0;return r&&g?v=Math.PI/2:g&&d?v=Math.PI:d&&p&&(v=-Math.PI/2),void this._basicCornerExtraRounded({x:e,y:t,size:i,rotation:v})}if(w===1){let v=0;return g?v=Math.PI/2:d?v=Math.PI:p&&(v=-Math.PI/2),void this._basicSideRounded({x:e,y:t,size:i,rotation:v})}}else this._basicDot({x:e,y:t,size:i,rotation:0})}_drawClassy({x:e,y:t,size:i,getNeighbor:n}){let r=n?+n(-1,0):0,d=n?+n(1,0):0,g=n?+n(0,-1):0,p=n?+n(0,1):0;r+d+g+p!==0?r||g?d||p?this._basicSquare({x:e,y:t,size:i,rotation:0}):this._basicCornerRounded({x:e,y:t,size:i,rotation:Math.PI/2}):this._basicCornerRounded({x:e,y:t,size:i,rotation:-Math.PI/2}):this._basicCornersRounded({x:e,y:t,size:i,rotation:Math.PI/2})}_drawClassyRounded({x:e,y:t,size:i,getNeighbor:n}){let r=n?+n(-1,0):0,d=n?+n(1,0):0,g=n?+n(0,-1):0,p=n?+n(0,1):0;r+d+g+p!==0?r||g?d||p?this._basicSquare({x:e,y:t,size:i,rotation:0}):this._basicCornerExtraRounded({x:e,y:t,size:i,rotation:Math.PI/2}):this._basicCornerExtraRounded({x:e,y:t,size:i,rotation:-Math.PI/2}):this._basicCornersRounded({x:e,y:t,size:i,rotation:Math.PI/2})}}let Y={dot:"dot",square:"square",extraRounded:"extra-rounded"},it=Object.values(Y);class V{constructor({svg:e,type:t,window:i}){this._svg=e,this._type=t,this._window=i}draw(e,t,i,n){let r;switch(this._type){case Y.square:r=this._drawSquare;break;case Y.extraRounded:r=this._drawExtraRounded;break;default:r=this._drawDot}r.call(this,{x:e,y:t,size:i,rotation:n})}_rotateFigure({x:e,y:t,size:i,rotation:n=0,draw:r}){var d;let g=e+i/2,p=t+i/2;r(),(d=this._element)===null||d===void 0||d.setAttribute("transform",`rotate(${180*n/Math.PI},${g},${p})`)}_basicDot(e){let{size:t,x:i,y:n}=e,r=t/7;this._rotateFigure(Object.assign(Object.assign({},e),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","path"),this._element.setAttribute("clip-rule","evenodd"),this._element.setAttribute("d",`M ${i+t/2} ${n}a ${t/2} ${t/2} 0 1 0 0.1 0zm 0 ${r}a ${t/2-r} ${t/2-r} 0 1 1 -0.1 0Z`)}}))}_basicSquare(e){let{size:t,x:i,y:n}=e,r=t/7;this._rotateFigure(Object.assign(Object.assign({},e),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","path"),this._element.setAttribute("clip-rule","evenodd"),this._element.setAttribute("d",`M ${i} ${n}v ${t}h ${t}v `+-t+`zM ${i+r} ${n+r}h `+(t-2*r)+"v "+(t-2*r)+"h "+(2*r-t)+"z")}}))}_basicExtraRounded(e){let{size:t,x:i,y:n}=e,r=t/7;this._rotateFigure(Object.assign(Object.assign({},e),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","path"),this._element.setAttribute("clip-rule","evenodd"),this._element.setAttribute("d",`M ${i} ${n+2.5*r}v `+2*r+`a ${2.5*r} ${2.5*r}, 0, 0, 0, ${2.5*r} ${2.5*r}h `+2*r+`a ${2.5*r} ${2.5*r}, 0, 0, 0, ${2.5*r} ${2.5*-r}v `+-2*r+`a ${2.5*r} ${2.5*r}, 0, 0, 0, ${2.5*-r} ${2.5*-r}h `+-2*r+`a ${2.5*r} ${2.5*r}, 0, 0, 0, ${2.5*-r} ${2.5*r}M ${i+2.5*r} ${n+r}h `+2*r+`a ${1.5*r} ${1.5*r}, 0, 0, 1, ${1.5*r} ${1.5*r}v `+2*r+`a ${1.5*r} ${1.5*r}, 0, 0, 1, ${1.5*-r} ${1.5*r}h `+-2*r+`a ${1.5*r} ${1.5*r}, 0, 0, 1, ${1.5*-r} ${1.5*-r}v `+-2*r+`a ${1.5*r} ${1.5*r}, 0, 0, 1, ${1.5*r} ${1.5*-r}`)}}))}_drawDot({x:e,y:t,size:i,rotation:n}){this._basicDot({x:e,y:t,size:i,rotation:n})}_drawSquare({x:e,y:t,size:i,rotation:n}){this._basicSquare({x:e,y:t,size:i,rotation:n})}_drawExtraRounded({x:e,y:t,size:i,rotation:n}){this._basicExtraRounded({x:e,y:t,size:i,rotation:n})}}let X={dot:"dot",square:"square"},z=Object.values(X);class J{constructor({svg:e,type:t,window:i}){this._svg=e,this._type=t,this._window=i}draw(e,t,i,n){let r;r=this._type===X.square?this._drawSquare:this._drawDot,r.call(this,{x:e,y:t,size:i,rotation:n})}_rotateFigure({x:e,y:t,size:i,rotation:n=0,draw:r}){var d;let g=e+i/2,p=t+i/2;r(),(d=this._element)===null||d===void 0||d.setAttribute("transform",`rotate(${180*n/Math.PI},${g},${p})`)}_basicDot(e){let{size:t,x:i,y:n}=e;this._rotateFigure(Object.assign(Object.assign({},e),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","circle"),this._element.setAttribute("cx",String(i+t/2)),this._element.setAttribute("cy",String(n+t/2)),this._element.setAttribute("r",String(t/2))}}))}_basicSquare(e){let{size:t,x:i,y:n}=e;this._rotateFigure(Object.assign(Object.assign({},e),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","rect"),this._element.setAttribute("x",String(i)),this._element.setAttribute("y",String(n)),this._element.setAttribute("width",String(t)),this._element.setAttribute("height",String(t))}}))}_drawDot({x:e,y:t,size:i,rotation:n}){this._basicDot({x:e,y:t,size:i,rotation:n})}_drawSquare({x:e,y:t,size:i,rotation:n}){this._basicSquare({x:e,y:t,size:i,rotation:n})}}let tt="circle",T=[[1,1,1,1,1,1,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,1,1,1,1,1,1]],F=[[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,1,1,1,0,0],[0,0,1,1,1,0,0],[0,0,1,1,1,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]],lt=(()=>{class a{constructor(t,i){this._roundSize=n=>this._options.dotsOptions.roundSize?Math.floor(n):n,this._window=i,this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","svg"),this._element.setAttribute("width",String(t.width)),this._element.setAttribute("height",String(t.height)),this._element.setAttribute("xmlns:xlink","http://www.w3.org/1999/xlink"),t.dotsOptions.roundSize||this._element.setAttribute("shape-rendering","crispEdges"),this._element.setAttribute("viewBox",`0 0 ${t.width} ${t.height}`),this._defs=this._window.document.createElementNS("http://www.w3.org/2000/svg","defs"),this._element.appendChild(this._defs),this._imageUri=t.image,this._instanceId=a.instanceCount++,this._options=t}get width(){return this._options.width}get height(){return this._options.height}getElement(){return this._element}drawQR(t){return rt(this,null,function*(){let i=t.getModuleCount(),n=Math.min(this._options.width,this._options.height)-2*this._options.margin,r=this._options.shape===tt?n/Math.sqrt(2):n,d=this._roundSize(r/i),g={hideXDots:0,hideYDots:0,width:0,height:0};if(this._qr=t,this._options.image){if(yield this.loadImage(),!this._image)return;let{imageOptions:p,qrOptions:w}=this._options,v=p.imageSize*gt[w.errorCorrectionLevel],S=Math.floor(v*i*i);g=function({originalHeight:C,originalWidth:_,maxHiddenDots:y,maxHiddenAxisDots:m,dotSize:o}){let h={x:0,y:0},s={x:0,y:0};if(C<=0||_<=0||y<=0||o<=0)return{height:0,width:0,hideYDots:0,hideXDots:0};let u=C/_;return h.x=Math.floor(Math.sqrt(y/u)),h.x<=0&&(h.x=1),m&&m<h.x&&(h.x=m),h.x%2==0&&h.x--,s.x=h.x*o,h.y=1+2*Math.ceil((h.x*u-1)/2),s.y=Math.round(s.x*u),(h.y*h.x>y||m&&m<h.y)&&(m&&m<h.y?(h.y=m,h.y%2==0&&h.x--):h.y-=2,s.y=h.y*o,h.x=1+2*Math.ceil((h.y/u-1)/2),s.x=Math.round(s.y/u)),{height:s.y,width:s.x,hideYDots:h.y,hideXDots:h.x}}({originalWidth:this._image.width,originalHeight:this._image.height,maxHiddenDots:S,maxHiddenAxisDots:i-14,dotSize:d})}this.drawBackground(),this.drawDots((p,w)=>{var v,S,C,_,y,m;return!(this._options.imageOptions.hideBackgroundDots&&p>=(i-g.hideYDots)/2&&p<(i+g.hideYDots)/2&&w>=(i-g.hideXDots)/2&&w<(i+g.hideXDots)/2||!((v=T[p])===null||v===void 0)&&v[w]||!((S=T[p-i+7])===null||S===void 0)&&S[w]||!((C=T[p])===null||C===void 0)&&C[w-i+7]||!((_=F[p])===null||_===void 0)&&_[w]||!((y=F[p-i+7])===null||y===void 0)&&y[w]||!((m=F[p])===null||m===void 0)&&m[w-i+7])}),this.drawCorners(),this._options.image&&(yield this.drawImage({width:g.width,height:g.height,count:i,dotSize:d}))})}drawBackground(){var t,i,n;let r=this._element,d=this._options;if(r){let g=(t=d.backgroundOptions)===null||t===void 0?void 0:t.gradient,p=(i=d.backgroundOptions)===null||i===void 0?void 0:i.color,w=d.height,v=d.width;if(g||p){let S=this._window.document.createElementNS("http://www.w3.org/2000/svg","rect");this._backgroundClipPath=this._window.document.createElementNS("http://www.w3.org/2000/svg","clipPath"),this._backgroundClipPath.setAttribute("id",`clip-path-background-color-${this._instanceId}`),this._defs.appendChild(this._backgroundClipPath),!((n=d.backgroundOptions)===null||n===void 0)&&n.round&&(w=v=Math.min(d.width,d.height),S.setAttribute("rx",String(w/2*d.backgroundOptions.round))),S.setAttribute("x",String(this._roundSize((d.width-v)/2))),S.setAttribute("y",String(this._roundSize((d.height-w)/2))),S.setAttribute("width",String(v)),S.setAttribute("height",String(w)),this._backgroundClipPath.appendChild(S),this._createColor({options:g,color:p,additionalRotation:0,x:0,y:0,height:d.height,width:d.width,name:`background-color-${this._instanceId}`})}}}drawDots(t){var i,n;if(!this._qr)throw"QR code is not defined";let r=this._options,d=this._qr.getModuleCount();if(d>r.width||d>r.height)throw"The canvas is too small.";let g=Math.min(r.width,r.height)-2*r.margin,p=r.shape===tt?g/Math.sqrt(2):g,w=this._roundSize(p/d),v=this._roundSize((r.width-d*w)/2),S=this._roundSize((r.height-d*w)/2),C=new ot({svg:this._element,type:r.dotsOptions.type,window:this._window});this._dotsClipPath=this._window.document.createElementNS("http://www.w3.org/2000/svg","clipPath"),this._dotsClipPath.setAttribute("id",`clip-path-dot-color-${this._instanceId}`),this._defs.appendChild(this._dotsClipPath),this._createColor({options:(i=r.dotsOptions)===null||i===void 0?void 0:i.gradient,color:r.dotsOptions.color,additionalRotation:0,x:0,y:0,height:r.height,width:r.width,name:`dot-color-${this._instanceId}`});for(let _=0;_<d;_++)for(let y=0;y<d;y++)t&&!t(_,y)||!((n=this._qr)===null||n===void 0)&&n.isDark(_,y)&&(C.draw(v+y*w,S+_*w,w,(m,o)=>!(y+m<0||_+o<0||y+m>=d||_+o>=d)&&!(t&&!t(_+o,y+m))&&!!this._qr&&this._qr.isDark(_+o,y+m)),C._element&&this._dotsClipPath&&this._dotsClipPath.appendChild(C._element));if(r.shape===tt){let _=this._roundSize((g/w-d)/2),y=d+2*_,m=v-_*w,o=S-_*w,h=[],s=this._roundSize(y/2);for(let u=0;u<y;u++){h[u]=[];for(let c=0;c<y;c++)u>=_-1&&u<=y-_&&c>=_-1&&c<=y-_||Math.sqrt((u-s)*(u-s)+(c-s)*(c-s))>s?h[u][c]=0:h[u][c]=this._qr.isDark(c-2*_<0?c:c>=d?c-2*_:c-_,u-2*_<0?u:u>=d?u-2*_:u-_)?1:0}for(let u=0;u<y;u++)for(let c=0;c<y;c++)h[u][c]&&(C.draw(m+c*w,o+u*w,w,(b,O)=>{var A;return!!(!((A=h[u+O])===null||A===void 0)&&A[c+b])}),C._element&&this._dotsClipPath&&this._dotsClipPath.appendChild(C._element))}}drawCorners(){if(!this._qr)throw"QR code is not defined";let t=this._element,i=this._options;if(!t)throw"Element code is not defined";let n=this._qr.getModuleCount(),r=Math.min(i.width,i.height)-2*i.margin,d=i.shape===tt?r/Math.sqrt(2):r,g=this._roundSize(d/n),p=7*g,w=3*g,v=this._roundSize((i.width-n*g)/2),S=this._roundSize((i.height-n*g)/2);[[0,0,0],[1,0,Math.PI/2],[0,1,-Math.PI/2]].forEach(([C,_,y])=>{var m,o,h,s,u,c,b,O,A,M,$,k,R,E;let B=v+C*g*(n-7),D=S+_*g*(n-7),x=this._dotsClipPath,N=this._dotsClipPath;if((!((m=i.cornersSquareOptions)===null||m===void 0)&&m.gradient||!((o=i.cornersSquareOptions)===null||o===void 0)&&o.color)&&(x=this._window.document.createElementNS("http://www.w3.org/2000/svg","clipPath"),x.setAttribute("id",`clip-path-corners-square-color-${C}-${_}-${this._instanceId}`),this._defs.appendChild(x),this._cornersSquareClipPath=this._cornersDotClipPath=N=x,this._createColor({options:(h=i.cornersSquareOptions)===null||h===void 0?void 0:h.gradient,color:(s=i.cornersSquareOptions)===null||s===void 0?void 0:s.color,additionalRotation:y,x:B,y:D,height:p,width:p,name:`corners-square-color-${C}-${_}-${this._instanceId}`})),((u=i.cornersSquareOptions)===null||u===void 0?void 0:u.type)&&it.includes(i.cornersSquareOptions.type)){let j=new V({svg:this._element,type:i.cornersSquareOptions.type,window:this._window});j.draw(B,D,p,y),j._element&&x&&x.appendChild(j._element)}else{let j=new ot({svg:this._element,type:((c=i.cornersSquareOptions)===null||c===void 0?void 0:c.type)||i.dotsOptions.type,window:this._window});for(let q=0;q<T.length;q++)for(let L=0;L<T[q].length;L++)!((b=T[q])===null||b===void 0)&&b[L]&&(j.draw(B+L*g,D+q*g,g,(U,G)=>{var Z;return!!(!((Z=T[q+G])===null||Z===void 0)&&Z[L+U])}),j._element&&x&&x.appendChild(j._element))}if((!((O=i.cornersDotOptions)===null||O===void 0)&&O.gradient||!((A=i.cornersDotOptions)===null||A===void 0)&&A.color)&&(N=this._window.document.createElementNS("http://www.w3.org/2000/svg","clipPath"),N.setAttribute("id",`clip-path-corners-dot-color-${C}-${_}-${this._instanceId}`),this._defs.appendChild(N),this._cornersDotClipPath=N,this._createColor({options:(M=i.cornersDotOptions)===null||M===void 0?void 0:M.gradient,color:($=i.cornersDotOptions)===null||$===void 0?void 0:$.color,additionalRotation:y,x:B+2*g,y:D+2*g,height:w,width:w,name:`corners-dot-color-${C}-${_}-${this._instanceId}`})),((k=i.cornersDotOptions)===null||k===void 0?void 0:k.type)&&z.includes(i.cornersDotOptions.type)){let j=new J({svg:this._element,type:i.cornersDotOptions.type,window:this._window});j.draw(B+2*g,D+2*g,w,y),j._element&&N&&N.appendChild(j._element)}else{let j=new ot({svg:this._element,type:((R=i.cornersDotOptions)===null||R===void 0?void 0:R.type)||i.dotsOptions.type,window:this._window});for(let q=0;q<F.length;q++)for(let L=0;L<F[q].length;L++)!((E=F[q])===null||E===void 0)&&E[L]&&(j.draw(B+L*g,D+q*g,g,(U,G)=>{var Z;return!!(!((Z=F[q+G])===null||Z===void 0)&&Z[L+U])}),j._element&&N&&N.appendChild(j._element))}})}loadImage(){return new Promise((t,i)=>{var n;let r=this._options;if(!r.image)return i("Image is not defined");if(!((n=r.nodeCanvas)===null||n===void 0)&&n.loadImage)r.nodeCanvas.loadImage(r.image).then(d=>{var g,p;if(this._image=d,this._options.imageOptions.saveAsBlob){let w=(g=r.nodeCanvas)===null||g===void 0?void 0:g.createCanvas(this._image.width,this._image.height);(p=w?.getContext("2d"))===null||p===void 0||p.drawImage(d,0,0),this._imageUri=w?.toDataURL()}t()}).catch(i);else{let d=new this._window.Image;typeof r.imageOptions.crossOrigin=="string"&&(d.crossOrigin=r.imageOptions.crossOrigin),this._image=d,d.onload=()=>rt(this,null,function*(){this._options.imageOptions.saveAsBlob&&(this._imageUri=yield function(g,p){return rt(this,null,function*(){return new Promise(w=>{let v=new p.XMLHttpRequest;v.onload=function(){let S=new p.FileReader;S.onloadend=function(){w(S.result)},S.readAsDataURL(v.response)},v.open("GET",g),v.responseType="blob",v.send()})})}(r.image||"",this._window)),t()}),d.src=r.image}})}drawImage(d){return rt(this,arguments,function*({width:t,height:i,count:n,dotSize:r}){let g=this._options,p=this._roundSize((g.width-n*r)/2),w=this._roundSize((g.height-n*r)/2),v=p+this._roundSize(g.imageOptions.margin+(n*r-t)/2),S=w+this._roundSize(g.imageOptions.margin+(n*r-i)/2),C=t-2*g.imageOptions.margin,_=i-2*g.imageOptions.margin,y=this._window.document.createElementNS("http://www.w3.org/2000/svg","image");y.setAttribute("href",this._imageUri||""),y.setAttribute("xlink:href",this._imageUri||""),y.setAttribute("x",String(v)),y.setAttribute("y",String(S)),y.setAttribute("width",`${C}px`),y.setAttribute("height",`${_}px`),this._element.appendChild(y)})}_createColor({options:t,color:i,additionalRotation:n,x:r,y:d,height:g,width:p,name:w}){let v=p>g?p:g,S=this._window.document.createElementNS("http://www.w3.org/2000/svg","rect");if(S.setAttribute("x",String(r)),S.setAttribute("y",String(d)),S.setAttribute("height",String(g)),S.setAttribute("width",String(p)),S.setAttribute("clip-path",`url('#clip-path-${w}')`),t){let C;if(t.type==="radial")C=this._window.document.createElementNS("http://www.w3.org/2000/svg","radialGradient"),C.setAttribute("id",w),C.setAttribute("gradientUnits","userSpaceOnUse"),C.setAttribute("fx",String(r+p/2)),C.setAttribute("fy",String(d+g/2)),C.setAttribute("cx",String(r+p/2)),C.setAttribute("cy",String(d+g/2)),C.setAttribute("r",String(v/2));else{let _=((t.rotation||0)+n)%(2*Math.PI),y=(_+2*Math.PI)%(2*Math.PI),m=r+p/2,o=d+g/2,h=r+p/2,s=d+g/2;y>=0&&y<=.25*Math.PI||y>1.75*Math.PI&&y<=2*Math.PI?(m-=p/2,o-=g/2*Math.tan(_),h+=p/2,s+=g/2*Math.tan(_)):y>.25*Math.PI&&y<=.75*Math.PI?(o-=g/2,m-=p/2/Math.tan(_),s+=g/2,h+=p/2/Math.tan(_)):y>.75*Math.PI&&y<=1.25*Math.PI?(m+=p/2,o+=g/2*Math.tan(_),h-=p/2,s-=g/2*Math.tan(_)):y>1.25*Math.PI&&y<=1.75*Math.PI&&(o+=g/2,m+=p/2/Math.tan(_),s-=g/2,h-=p/2/Math.tan(_)),C=this._window.document.createElementNS("http://www.w3.org/2000/svg","linearGradient"),C.setAttribute("id",w),C.setAttribute("gradientUnits","userSpaceOnUse"),C.setAttribute("x1",String(Math.round(m))),C.setAttribute("y1",String(Math.round(o))),C.setAttribute("x2",String(Math.round(h))),C.setAttribute("y2",String(Math.round(s)))}t.colorStops.forEach(({offset:_,color:y})=>{let m=this._window.document.createElementNS("http://www.w3.org/2000/svg","stop");m.setAttribute("offset",100*_+"%"),m.setAttribute("stop-color",y),C.appendChild(m)}),S.setAttribute("fill",`url('#${w}')`),this._defs.appendChild(C)}else i&&S.setAttribute("fill",i);this._element.appendChild(S)}}return a.instanceCount=0,a})(),at="canvas",ft={};for(let a=0;a<=40;a++)ft[a]=a;let pt={type:at,shape:"square",width:300,height:300,data:"",margin:0,qrOptions:{typeNumber:ft[0],mode:void 0,errorCorrectionLevel:"Q"},imageOptions:{saveAsBlob:!0,hideBackgroundDots:!0,imageSize:.4,crossOrigin:void 0,margin:0},dotsOptions:{type:"square",color:"#000",roundSize:!0},backgroundOptions:{round:0,color:"#fff"}};function ht(a){let e=Object.assign({},a);if(!e.colorStops||!e.colorStops.length)throw"Field 'colorStops' is required in gradient";return e.rotation?e.rotation=Number(e.rotation):e.rotation=0,e.colorStops=e.colorStops.map(t=>Object.assign(Object.assign({},t),{offset:Number(t.offset)})),e}function wt(a){let e=Object.assign({},a);return e.width=Number(e.width),e.height=Number(e.height),e.margin=Number(e.margin),e.imageOptions=Object.assign(Object.assign({},e.imageOptions),{hideBackgroundDots:!!e.imageOptions.hideBackgroundDots,imageSize:Number(e.imageOptions.imageSize),margin:Number(e.imageOptions.margin)}),e.margin>Math.min(e.width,e.height)&&(e.margin=Math.min(e.width,e.height)),e.dotsOptions=Object.assign({},e.dotsOptions),e.dotsOptions.gradient&&(e.dotsOptions.gradient=ht(e.dotsOptions.gradient)),e.cornersSquareOptions&&(e.cornersSquareOptions=Object.assign({},e.cornersSquareOptions),e.cornersSquareOptions.gradient&&(e.cornersSquareOptions.gradient=ht(e.cornersSquareOptions.gradient))),e.cornersDotOptions&&(e.cornersDotOptions=Object.assign({},e.cornersDotOptions),e.cornersDotOptions.gradient&&(e.cornersDotOptions.gradient=ht(e.cornersDotOptions.gradient))),e.backgroundOptions&&(e.backgroundOptions=Object.assign({},e.backgroundOptions),e.backgroundOptions.gradient&&(e.backgroundOptions.gradient=ht(e.backgroundOptions.gradient))),e}var vt=Q(873),mt=Q.n(vt);function dt(a){if(!a)throw new Error("Extension must be defined");a[0]==="."&&(a=a.substring(1));let e={bmp:"image/bmp",gif:"image/gif",ico:"image/vnd.microsoft.icon",jpeg:"image/jpeg",jpg:"image/jpeg",png:"image/png",svg:"image/svg+xml",tif:"image/tiff",tiff:"image/tiff",webp:"image/webp",pdf:"application/pdf"}[a.toLowerCase()];if(!e)throw new Error(`Extension "${a}" is not supported`);return e}class f{constructor(e){e?.jsdom?this._window=new e.jsdom("",{resources:"usable"}).window:this._window=window,this._options=e?wt(I(pt,e)):pt,this.update()}static _clearContainer(e){e&&(e.innerHTML="")}_setupSvg(){if(!this._qr)return;let e=new lt(this._options,this._window);this._svg=e.getElement(),this._svgDrawingPromise=e.drawQR(this._qr).then(()=>{var t;this._svg&&((t=this._extension)===null||t===void 0||t.call(this,e.getElement(),this._options))})}_setupCanvas(){var e,t;this._qr&&(!((e=this._options.nodeCanvas)===null||e===void 0)&&e.createCanvas?(this._nodeCanvas=this._options.nodeCanvas.createCanvas(this._options.width,this._options.height),this._nodeCanvas.width=this._options.width,this._nodeCanvas.height=this._options.height):(this._domCanvas=document.createElement("canvas"),this._domCanvas.width=this._options.width,this._domCanvas.height=this._options.height),this._setupSvg(),this._canvasDrawingPromise=(t=this._svgDrawingPromise)===null||t===void 0?void 0:t.then(()=>{var i;if(!this._svg)return;let n=this._svg,r=new this._window.XMLSerializer().serializeToString(n),d=btoa(r),g=`data:${dt("svg")};base64,${d}`;if(!((i=this._options.nodeCanvas)===null||i===void 0)&&i.loadImage)return this._options.nodeCanvas.loadImage(g).then(p=>{var w,v;p.width=this._options.width,p.height=this._options.height,(v=(w=this._nodeCanvas)===null||w===void 0?void 0:w.getContext("2d"))===null||v===void 0||v.drawImage(p,0,0)});{let p=new this._window.Image;return new Promise(w=>{p.onload=()=>{var v,S;(S=(v=this._domCanvas)===null||v===void 0?void 0:v.getContext("2d"))===null||S===void 0||S.drawImage(p,0,0),w()},p.src=g})}}))}_getElement(e="png"){return rt(this,null,function*(){if(!this._qr)throw"QR code is empty";return e.toLowerCase()==="svg"?(this._svg&&this._svgDrawingPromise||this._setupSvg(),yield this._svgDrawingPromise,this._svg):((this._domCanvas||this._nodeCanvas)&&this._canvasDrawingPromise||this._setupCanvas(),yield this._canvasDrawingPromise,this._domCanvas||this._nodeCanvas)})}update(e){f._clearContainer(this._container),this._options=e?wt(I(this._options,e)):this._options,this._options.data&&(this._qr=mt()(this._options.qrOptions.typeNumber,this._options.qrOptions.errorCorrectionLevel),this._qr.addData(this._options.data,this._options.qrOptions.mode||function(t){switch(!0){case/^[0-9]*$/.test(t):return"Numeric";case/^[0-9A-Z $%*+\-./:]*$/.test(t):return"Alphanumeric";default:return"Byte"}}(this._options.data)),this._qr.make(),this._options.type===at?this._setupCanvas():this._setupSvg(),this.append(this._container))}append(e){if(e){if(typeof e.appendChild!="function")throw"Container should be a single DOM node";this._options.type===at?this._domCanvas&&e.appendChild(this._domCanvas):this._svg&&e.appendChild(this._svg),this._container=e}}applyExtension(e){if(!e)throw"Extension function should be defined.";this._extension=e,this.update()}deleteExtension(){this._extension=void 0,this.update()}getRawData(e="png"){return rt(this,null,function*(){if(!this._qr)throw"QR code is empty";let t=yield this._getElement(e),i=dt(e);if(!t)return null;if(e.toLowerCase()==="svg"){let n=`<?xml version="1.0" standalone="no"?>\r
${new this._window.XMLSerializer().serializeToString(t)}`;return typeof Blob>"u"||this._options.jsdom?Buffer.from(n):new Blob([n],{type:i})}return new Promise(n=>{let r=t;if("toBuffer"in r)if(i==="image/png")n(r.toBuffer(i));else if(i==="image/jpeg")n(r.toBuffer(i));else{if(i!=="application/pdf")throw Error("Unsupported extension");n(r.toBuffer(i))}else"toBlob"in r&&r.toBlob(n,i,1)})})}download(e){return rt(this,null,function*(){if(!this._qr)throw"QR code is empty";if(typeof Blob>"u")throw"Cannot download in Node.js, call getRawData instead.";let t="png",i="qr";typeof e=="string"?(t=e,console.warn("Extension is deprecated as argument for 'download' method, please pass object { name: '...', extension: '...' } as argument")):typeof e=="object"&&e!==null&&(e.name&&(i=e.name),e.extension&&(t=e.extension));let n=yield this._getElement(t);if(n)if(t.toLowerCase()==="svg"){let r=new XMLSerializer().serializeToString(n);r=`<?xml version="1.0" standalone="no"?>\r
`+r,H(`data:${dt(t)};charset=utf-8,${encodeURIComponent(r)}`,`${i}.svg`)}else H(n.toDataURL(dt(t)),`${i}.${t}`)})}}let l=f})(),W.default})())});var zt=Bt(Dt());var Pt=["smartQrContainer"],It=["app-smart-qr",""],Ht=(()=>{class et{smartqr=!0;smartQrContainer;size=200;qrDotShape="dots";qrCornersShape="dot";qrDotColor="--verify-smartqr-dot-color";qrBackgroundColor="--verify-smartqr-bg";qrLogo="media/logo/bidv-flower.svg";qrLogoSize=.4;qrLogoPadding=1;qrPadding=4;qrType="svg";qrData="";fileName="qr";qrCodeImage;commonService=yt($t);ngOnChanges(){if(!this.qrData)return;let Q={data:this.qrData,width:this.size,height:this.size,type:this.qrType,margin:this.qrPadding,image:this.qrLogo,dotsOptions:{color:this.commonService.cssVariable(this.qrDotColor),type:this.qrDotShape},cornersSquareOptions:{color:this.commonService.cssVariable(this.qrDotColor),type:this.qrCornersShape},cornersDotOptions:{type:this.qrCornersShape},backgroundOptions:{color:this.commonService.cssVariable(this.qrBackgroundColor)},imageOptions:{crossOrigin:"anonymous",margin:this.qrLogoPadding,imageSize:this.qrLogoSize},qrOptions:{errorCorrectionLevel:"Q"}};this.qrCodeImage=new zt.default(Q),this.qrCodeImage.append(this.smartQrContainer.nativeElement)}handleSaveQr(){this.qrCodeImage?.download({name:this.fileName,extension:"png"})}static \u0275fac=function(W){return new(W||et)};static \u0275cmp=bt({type:et,selectors:[["","app-smart-qr",""]],viewQuery:function(W,P){if(W&1&&At(Pt,7),W&2){let I;qt(I=Ot())&&(P.smartQrContainer=I.first)}},hostVars:2,hostBindings:function(W,P){W&2&&St("auth-smart-qr",P.smartqr)},inputs:{size:"size",qrDotShape:"qrDotShape",qrCornersShape:"qrCornersShape",qrDotColor:"qrDotColor",qrBackgroundColor:"qrBackgroundColor",qrLogo:"qrLogo",qrLogoSize:"qrLogoSize",qrLogoPadding:"qrLogoPadding",qrPadding:"qrPadding",qrType:"qrType",qrData:"qrData",fileName:"fileName"},standalone:!0,features:[Ct,Mt],attrs:It,decls:2,vars:0,consts:[["smartQrContainer",""]],template:function(W,P){W&1&&xt(0,"div",null,0)}})}return et})();export{Dt as a,Ht as b};
