import{Bf as h}from"./chunk-K5H3SJL5.js";import{Cc as m,Da as a,Hb as d,Lb as l,Qd as f,Rb as r,Wb as g,Xb as c,Yb as p,_c as u,pb as t,sa as s}from"./chunk-YK6FMNSY.js";function b(n,v){n&1&&(g(0,"div",0),p(1,"div",1),c())}var w=(()=>{class n{loadingService;cd;spiner=!1;inBox;loadingSub$;showLoading=!1;loadingVisible;constructor(i,e){this.loadingService=i,this.cd=e}ngOnInit(){this.loadingSub$=this.loadingService.loading$.subscribe(i=>{i&&(this.loadingVisible&&clearTimeout(this.loadingVisible),this.showLoading=i.visible,this.spiner=i.visible,i.timeout&&(this.loadingVisible=setTimeout(()=>{this.showLoading=!1,this.spiner=!1,clearTimeout(this.loadingVisible)},i.timeout)),this.cd.detectChanges())},i=>{console.log(i)})}ngAfterViewChecked(){}ngOnChanges(){}ngOnDestroy(){this.loadingSub$&&this.loadingSub$.unsubscribe()}static \u0275fac=function(e){return new(e||n)(t(h),t(u))};static \u0275cmp=s({type:n,selectors:[["app-loading"]],hostVars:4,hostBindings:function(e,o){e&2&&l("spiner",o.spiner)("inbox",o.inBox)},inputs:{inBox:"inBox"},standalone:!0,features:[a,m],decls:1,vars:1,consts:[[1,"spiner-wrap"],[1,"spiner-inner"]],template:function(e,o){e&1&&d(0,b,2,0,"div",0),e&2&&r(o.showLoading?0:-1)},dependencies:[f]})}return n})();export{w as a};
