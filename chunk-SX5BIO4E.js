import{a as rt,b as pt,c as st}from"./chunk-SGPU43K3.js";import{a as at}from"./chunk-5VYTNFSL.js";import{b as it,j as ot,k as nt,l as lt}from"./chunk-5VBIWGCV.js";import{a as Y,g as Z,m as tt,w as et}from"./chunk-AASME2FE.js";import{Bd as K,Cd as Q,Pd as W,W as L,ea as J,fa as T,jf as X}from"./chunk-K5H3SJL5.js";import{$b as V,Bc as P,Cb as S,Cc as q,Cd as A,Da as y,Ea as I,Ec as H,Fa as w,Fc as $,Gc as U,Hb as c,Ia as b,Jb as d,Jd as G,Lb as E,Lc as u,Mc as C,Na as z,Nb as _,Qc as x,Qd as j,Rb as s,Sb as k,Ub as F,Vb as M,Wb as r,Xb as p,Yb as f,ac as B,cc as O,ec as l,ga as v,na as g,nc as D,ob as a,oc as m,pc as h,qc as N,sa as R}from"./chunk-YK6FMNSY.js";var dt=(e,o)=>({"radio-group":!0,"radio-group-pill":e,"radio-group-card":o}),mt=(e,o,t)=>({"radio-item":!0,"radio-item-pill":e,"radio-item-card":o,"radio-item-checked":t}),ut=e=>({item:e});function Ct(e,o){e&1&&(r(0,"div",6),m(1,"This is a tooltip"),p(),r(2,"div",7),m(3," Tooltips are used to describe or identify an element. In most scenarios, tooltips help the user understand the meaning, function or alt-text of an element. "),p())}function _t(e,o){if(e&1&&(r(0,"p",9),m(1),u(2,"translate"),p()),e&2){let t=l().item;a(),h(C(2,1,t==null?null:t.title))}}function ft(e,o){if(e&1&&(r(0,"p",10),m(1),u(2,"translate"),p()),e&2){let t=l().item;a(),h(C(2,1,t==null?null:t.subtitle))}}function ht(e,o){if(e&1&&(r(0,"div",8),c(1,_t,3,3,"p",9)(2,ft,3,3,"p",10),p()),e&2){let t=o.item;a(),s(t!=null&&t.title?1:-1),a(),s(t!=null&&t.subtitle?2:-1)}}function xt(e,o){if(e&1&&f(0,"div",2),e&2){let t=l();d("label",t.label)("allowShowRequired",t.required&&t.showRequired)("allowShowTooltip",t.showTooltip)("tooltipIcon",t.tooltipIcon)("tooltip",t.tooltip)("tooltipTpl",t.tooltipTpl)}}function Tt(e,o){if(e&1&&V(0,13),e&2){let t=l(2).$implicit,n=l(),i=D(3);d("ngTemplateOutlet",n.contentTpl||i)("ngTemplateOutletContext",H(2,ut,t))}}function vt(e,o){if(e&1&&(r(0,"app-pill",15),m(1),u(2,"translate"),p()),e&2){let t=l(2).$implicit,n=l();E("active",t.value===n.control.value),d("color",n.UI.PillColor.OutlineGrey)("size",n.size),a(),N(" ",C(2,5,t==null?null:t.title)," ")}}function gt(e,o){if(e&1){let t=B();r(0,"label",12),O("click",function(){I(t);let i=l().$implicit,ct=l();return w(ct.handleClickChoose(i.value))}),c(1,Tt,1,4,"ng-container",13)(2,vt,3,7,"app-pill",14),p()}if(e&2){let t,n=l().$implicit,i=l();_("radio-item-"+i.type+"-"+i.size),d("nzValue",n.value)("ngClass",U(5,mt,i.type===i.RadioType.Pill,i.type===i.RadioType.Card,i.hideChecked?!i.hideChecked:n.value===i.control.value)),a(),s((t=i.type)===i.RadioType.Card?1:2)}}function Rt(e,o){if(e&1&&c(0,gt,3,9,"label",11),e&2){let t=o.$implicit,n=l();s(t!=null&&t.title||t!=null&&t.subtitle||n.contentTpl?0:-1)}}function yt(e,o){if(e&1&&f(0,"app-svg",18),e&2){let t=l(3);d("size",5)("src",t.hintIcon)}}function It(e,o){if(e&1&&(r(0,"div",16),c(1,yt,1,2,"app-svg",18),r(2,"p"),m(3),u(4,"translate"),p()()),e&2){let t=l(2);a(),s(t.showHintIcon?1:-1),a(2),h(C(4,2,t.hint))}}function wt(e,o){if(e&1&&f(0,"app-validate-error",17),e&2){let t=l(2);d("errors",t.control.errors)("errorMessages",t.errorMessages)}}function bt(e,o){if(e&1&&(r(0,"div",5),c(1,It,5,4,"div",16)(2,wt,1,2,"app-validate-error",17),p()),e&2){let t=l();a(),s(t.hint&&!(t.control.invalid&&t.control.touched)?1:-1),a(),s(t.control.invalid&&t.control.touched?2:-1)}}var Qt=(()=>{class e extends ot{data;label;contentTpl;direction=L.Horizontal;tooltipTpl;tooltipIcon="media/icons/outline/alert-information.svg";showTooltip=!1;showRequired=!1;type=T.Pill;size=J.Md;tooltip;hint;hintIcon="media/icons/solid/alert-information.svg";showHintIcon=!1;extendClass;errorMessages;hideChecked=!1;clickChoose=new z;get hostClass(){return this.initClass()}UI=g(X);RadioType=T;initClass(){return["app-radio-group",this.direction.toString(),"app-radio-group-"+this.type].join(" ")}ngOnChanges(){this.hideChecked&&this.control?.setValue("")}handleClickChoose(t){this.clickChoose.emit(t)}static \u0275fac=(()=>{let t;return function(i){return(t||(t=b(e)))(i||e)}})();static \u0275cmp=R({type:e,selectors:[["app-radio"]],hostVars:2,hostBindings:function(n,i){n&2&&_(i.hostClass)},inputs:{data:"data",label:"label",contentTpl:"contentTpl",direction:"direction",tooltipTpl:"tooltipTpl",tooltipIcon:"tooltipIcon",showTooltip:"showTooltip",showRequired:"showRequired",type:"type",size:"size",tooltip:"tooltip",hint:"hint",hintIcon:"hintIcon",showHintIcon:"showHintIcon",extendClass:"extendClass",errorMessages:"errorMessages",hideChecked:"hideChecked"},outputs:{clickChoose:"clickChoose"},standalone:!0,features:[P([{provide:Y,useExisting:v(()=>e),multi:!0}]),S,y,q],decls:10,vars:9,consts:[["tooltipDefaultTpl",""],["contentDefaultTpl",""],["app-label-input","",3,"label","allowShowRequired","allowShowTooltip","tooltipIcon","tooltip","tooltipTpl"],[1,"app-root"],[3,"formControl","ngClass"],[1,"explain"],[1,"body-medium-thick","mb-1"],[1,"body-medium-thin"],[1,"content"],[1,"title"],[1,"subtitle"],["nz-radio-button","",3,"nzValue","class","ngClass"],["nz-radio-button","",3,"click","nzValue","ngClass"],[3,"ngTemplateOutlet","ngTemplateOutletContext"],[3,"color","size","active"],[3,"color","size"],[1,"hint"],[3,"errors","errorMessages"],[1,"hint-icon",3,"size","src"]],template:function(n,i){n&1&&(c(0,Ct,4,0,"ng-template",null,0,x)(2,ht,3,2,"ng-template",null,1,x)(4,xt,1,6,"div",2),r(5,"div",3)(6,"nz-radio-group",4),F(7,Rt,1,1,null,null,k),p(),c(9,bt,3,2,"div",5),p()),n&2&&(a(4),s(i.label?4:-1),a(2),_(i.extendClass),d("formControl",i.control)("ngClass",$(6,dt,i.type===i.RadioType.Pill,i.type===i.RadioType.Card)),a(),M(i.data),a(2),s(i.control.invalid&&i.control.touched||i.hint?9:-1))},dependencies:[j,A,G,it,st,pt,rt,et,Z,tt,nt,W,at,lt,Q,K]})}return e})();export{Qt as a};
