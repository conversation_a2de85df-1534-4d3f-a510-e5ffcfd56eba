import{Ad as T,Af as g,Bf as A,Ff as n,Gc as h,H as m,Kc as C,Sf as I,Tf as S,db as _,f,mf as d,nf as r}from"./chunk-K5H3SJL5.js";import{g as l,ha as p,na as s}from"./chunk-YK6FMNSY.js";import{a as c,b as u}from"./chunk-TSRGIXR5.js";var F=(()=>{class i{loginResponse$=new l(null);loadingService=s(A);apiService=s(g);modalService=s(d);storageService=s(C);httpClient=s(f);translate=s(T);apiUrl=`${h.apiUrl}/auth`;loginType;userName;oldPassword;getLoginResponse(){return this.loginResponse$.asObservable()}setLoginResponse(e){this.loginResponse$.next(e??null)}request(e,t){return this.httpClient.post(this.apiUrl,u(c({},e),{mid:t}))}login(e){return this.request(e,r.DANG_NHAP)}loginFirst(e){return this.request(e,r.KICH_HOAT_LAN_DAU)}activeAccountInOtherDevice(e){return this.request(e,r.KICH_HOAT_LAI_TREN_THIET_BI_KHAC)}trustBrowsers(e){return this.request(e,r.TRUST_TRINH_DUYET_IB)}changePassExpried(e){return this.request(e,r.MAT_KHAU_HET_HAN)}verifyLogin(e){return this.request(e,r.XAC_THUC_CMND)}forgotPassword(e){return this.request(e,r.QUEN_MAT_KHAU)}forgotPasswordOtp(e){return this.request(e,r.QUEN_MAT_KHAU_OTP)}sendOTPConvertUser(e){return this.request(e,r.GUI_OTP_CHUYEN_DOI_IB)}confirmOTPConvertUser(e){return this.request(e,r.XAC_THUC_CHUYEN_DOI_OTP)}crossLogin(e){return this.request(e,r.CROSS_LOGIN)}resendOTP(e){return e.token||(e.token=this.storageService.tokenOTP),this.request(e,r.GUI_LAI_OTP)}updateCifCoreBank(e){return this.apiService.requestProcess(e,r.CAP_NHAT_CIF_COREBANK)}processUpdateCifCoreBank(){this.loadingService.showLoading();let{sessionId:e,user:t}=this.storageService.userInfo,o={updateCifType:S,updateCifTypeValue:n.Yes,sessionId:e,user:t};this.updateCifCoreBank(o).subscribe()}checkPersonalDataDecree(e){let t=this.loginResponse$.getValue(),{statement13Status:o}=t||{};o===n.No&&this.modalService.info({message:e,btnConfirm:{title:this.translate.instant("xac_nhan"),color:m.Primary},confirm:()=>this.processUpdateCifCoreBank(),maskClosable:!1})}processAfterLoginSuccess(){let e=this.loginResponse$.getValue();if(!e)return;if(e?.codeFull===_.AUTOACTIVESOTP_00&&e?.showDes&&e?.des&&this.modalService.warning({message:e?.des}),Array.isArray(e.messages)&&e?.messages?.length){let o=e.messages?.find(a=>I.includes(a?.code)&&a?.des);o&&this.checkPersonalDataDecree(o?.des)}this.setLoginResponse()}static \u0275fac=function(t){return new(t||i)};static \u0275prov=p({token:i,factory:i.\u0275fac,providedIn:"root"})}return i})();export{F as a};
