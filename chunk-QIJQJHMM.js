import{b as L,c as s}from"./chunk-5KYMQ6XR.js";import"./chunk-CLYMFTGQ.js";import"./chunk-6NBO3RRK.js";import"./chunk-LAZOTBTB.js";import{a as R}from"./chunk-5YY5BVQL.js";import"./chunk-EJBNJL2X.js";import"./chunk-7BPXJPVY.js";import"./chunk-YJZSDC7G.js";import"./chunk-VFBBYLZU.js";import"./chunk-6KSU3ZJ7.js";import"./chunk-SD7XBMX3.js";import"./chunk-LSYDHUWU.js";import"./chunk-SG3W3EJ6.js";import"./chunk-SU5NX4RM.js";import"./chunk-TI2OG4JD.js";import"./chunk-P2H32HMS.js";import"./chunk-UVPCVSBB.js";import"./chunk-IDHI5ISK.js";import"./chunk-IZVSQRWS.js";import"./chunk-24JQYWUX.js";import"./chunk-54E4CCOA.js";import"./chunk-YNRPULUE.js";import"./chunk-5VYTNFSL.js";import"./chunk-SOAH2WLW.js";import"./chunk-5VBIWGCV.js";import"./chunk-AASME2FE.js";import"./chunk-AX4DCRSD.js";import{gb as i,p as A,q as T,td as E,ud as O,zc as I}from"./chunk-K5H3SJL5.js";import{a as n}from"./chunk-2WPD26RB.js";import{Cc as y,Hb as _,La as f,Lb as v,Qd as S,Rb as C,Wb as p,Xb as d,Yb as l,ha as h,ma as m,na as c,ob as g,sa as u}from"./chunk-YK6FMNSY.js";import"./chunk-TSRGIXR5.js";var a=(()=>{class t{authService;router;constructor(e,o){this.authService=e,this.router=o}canActivate(e,o){return e.data&&e.data.loginType&&(!Array.isArray(e.data.loginType)&&e.data.loginType===this.authService.loginType||Array.isArray(e.data.loginType)&&e.data.loginType.includes(this.authService.loginType))?!0:(this.router.navigate([n.Login]),!1)}static \u0275fac=function(o){return new(o||t)(m(R),m(T))};static \u0275prov=h({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function M(t,W){t&1&&(p(0,"div",2),l(1,"div",4),d())}var N=(()=>{class t{page=!0;commonService=c(E);destroyRef=c(f);SCREEN_SIZE_IS_SHOW_HEADER_NOTLOGIN=I;screenSize;ngOnInit(){this.commonService.getCurrentScreenSize().pipe(O(this.destroyRef)).subscribe(e=>{this.screenSize=e})}static \u0275fac=function(o){return new(o||t)};static \u0275cmp=u({type:t,selectors:[["app-authentication"]],hostVars:2,hostBindings:function(o,r){o&2&&v("nonlogin-page",r.page)},standalone:!0,features:[y],decls:5,vars:1,consts:[[1,"nonlogin-page--inner"],[1,"nonlogin-page--form"],[1,"nonlogin-page--form-head"],[1,"nonlogin-page--form-content"],["app-non-login-header",""]],template:function(o,r){o&1&&(p(0,"div",0)(1,"div",1),_(2,M,2,0,"div",2),p(3,"div",3),l(4,"router-outlet"),d()()()),o&2&&(g(2),C(r.SCREEN_SIZE_IS_SHOW_HEADER_NOTLOGIN.includes(r.screenSize)?-1:2))},dependencies:[S,A,L]})}return t})();var tt=[{path:"",component:N,canActivate:[s],data:{preload:!1},children:[{path:"",loadComponent:()=>import("./chunk-DO5GFN2Z.js").then(t=>t.LoginComponent),canActivate:[s],data:{preload:!1}},{path:n.Active,canActivate:[a],loadComponent:()=>import("./chunk-DP5GUWBA.js").then(t=>t.ActiveComponent),data:{preload:!1,loginType:i.kich_hoat_lan_dau}},{path:n.ExpiredPassword,canActivate:[a],loadComponent:()=>import("./chunk-XVMW5DLK.js").then(t=>t.ExpiredPasswordComponent),data:{preload:!1,loginType:i.mat_khau_het_han}},{path:n.WarningLoginWithId,canActivate:[a],loadComponent:()=>import("./chunk-WIX46JRY.js").then(t=>t.IdCardLoginWarningComponent),data:{preload:!1,loginType:i.xac_thuc_dang_nhap_cmnd}},{path:n.WarningLoginWithOTP,canActivate:[a],loadComponent:()=>import("./chunk-V7XLE5HT.js").then(t=>t.OtpLoginWarningComponent),data:{preload:!1,loginType:i.xac_thuc_dang_nhap_OTP}},{path:n.AuthByMB,loadComponent:()=>import("./chunk-RAQNFC7F.js").then(t=>t.AuthMbComponent),canActivate:[a],data:{preload:!1,loginType:i.xac_thuc_MB}},{path:n.TransformIB,canActivate:[a],loadComponent:()=>import("./chunk-PVW6PNV3.js").then(t=>t.TransformIbComponent),data:{preload:!1,loginType:i.chuyen_doi_IB}},{path:n.ConfirmTransformWithOTP,canActivate:[a],loadComponent:()=>import("./chunk-VAGAEZYD.js").then(t=>t.AuthTransformOtpComponent),data:{preload:!0,loginType:[i.chuyen_doi_IB,i.kich_hoat_lai]}},{path:"**",redirectTo:n.Login,pathMatch:"full"}]}];export{tt as authenticationRoutes};
