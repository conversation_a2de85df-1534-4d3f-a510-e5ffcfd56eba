import{Pd as I}from"./chunk-K5H3SJL5.js";import{Cc as g,Cd as u,Ed as w,Hb as a,Jb as t,Lb as d,Qd as v,Wb as r,Xb as s,Yb as m,ec as l,fc as C,gc as f,ob as p,sa as c}from"./chunk-YK6FMNSY.js";var S=["*"];function h(o,D){if(o&1&&(r(0,"div",2),m(1,"app-svg",3),s()),o&2){let n=l();p(),t("src",n.icon)("size",n.iconSize)("colorChange",n.iconColorChange)("ngClass",n.iconClass)}}var B=(()=>{class o{dropdown=!0;icon;iconSize=6;iconClass;iconColorChange=!0;static \u0275fac=function(e){return new(e||o)};static \u0275cmp=c({type:o,selectors:[["app-dropdown-item"]],hostVars:2,hostBindings:function(e,i){e&2&&d("custom-dropdown-item",i.dropdown)},inputs:{icon:"icon",iconSize:"iconSize",iconClass:"iconClass",iconColorChange:"iconColorChange"},standalone:!0,features:[g],ngContentSelectors:S,decls:3,vars:1,consts:[["class","icon-wrap-dropdown",4,"ngIf"],[1,"dropdown-label"],[1,"icon-wrap-dropdown"],[3,"src","size","colorChange","ngClass"]],template:function(e,i){e&1&&(C(),a(0,h,2,4,"div",0),r(1,"div",1),f(2),s()),e&2&&t("ngIf",i.icon)},dependencies:[v,u,w,I]})}return o})();export{B as a};
