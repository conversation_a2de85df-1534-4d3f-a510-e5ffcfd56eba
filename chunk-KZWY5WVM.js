import{Af as A,nf as _}from"./chunk-K5H3SJL5.js";import{ha as T,na as H,t as N}from"./chunk-YK6FMNSY.js";var I=(()=>{class r{apiService=H(A);checkConditionsOverseasRemittance(e){return this.apiService.requestProcess(e,_.CHUYEN_TIEN_QUOC_TE.KIEM_TRA_DIEU_KIEN_CHUYEN_TIEN_QUOC_TE)}getListPurposeOfRemittance(e){return this.apiService.requestProcess(e,_.CHUYEN_TIEN_QUOC_TE.DANH_SACH_MUC_DICH_CHUYEN_TIEN)}getListCurrency(e){return this.apiService.requestProcess(e,_.CHUYEN_TIEN_QUOC_TE.DANH_SACH_TIEN_TE)}getListPaymentBase(e){return this.apiService.requestProcess(e,_.CHUYEN_TIEN_QUOC_TE.LAY_DANH_CAN_CU_THANH_TOAN)}createPaymentBase(e){return this.apiService.requestProcess(e,_.CHUYEN_TIEN_QUOC_TE.TAO_CAN_CU_THANH_TOAN)}getListCountry(e){return this.apiService.requestProcess(e,_.CHUYEN_TIEN_QUOC_TE.DANH_SACH_QUOC_GIA)}getListBeneficiaryBank(e){return this.apiService.requestProcess(e,_.CHUYEN_TIEN_QUOC_TE.DANH_SACH_NGAN_HANG_THU_HUONG)}getExchangeRate(e){return this.apiService.requestProcess(e,_.CHUYEN_TIEN_QUOC_TE.LAY_TY_GIA_GIAO_DICH)}checkIBanInfo(e){return this.apiService.requestProcess(e,_.CHUYEN_TIEN_QUOC_TE.KIEM_TRA_THONG_TIN_IBAN)}getTransactionFee(e){return this.apiService.requestProcess(e,_.CHUYEN_TIEN_QUOC_TE.LAY_PHI_GIAO_DICH)}getListBranch(e){return this.apiService.requestProcess(e,_.MO_TAI_KHOAN_TICH_LUY.DANH_SACH_CHI_NHANH).pipe(N(t=>(t&&t.lst&&(t.lst=t.lst.map((i,c)=>(i.index=c.toString(),i))),t)))}initOverseasRemittance(e){return this.apiService.requestProcess(e,_.CHUYEN_TIEN_QUOC_TE.KHOI_TAO)}confirmOverseasRemittance(e){return this.apiService.requestProcess(e,_.CHUYEN_TIEN_QUOC_TE.XAC_NHAN)}getListTransaction(e){return this.apiService.requestProcess(e,_.CHUYEN_TIEN_QUOC_TE.LAY_DANH_SACH_GIAO_DICH)}cancelTransaction(e){return this.apiService.requestProcess(e,_.CHUYEN_TIEN_QUOC_TE.HUY_GIAO_DICH)}createClaimRequest(e){return this.apiService.requestProcess(e,_.CHUYEN_TIEN_QUOC_TE.TAO_TRA_SOAT)}confirmCreateClaimRequest(e){return this.apiService.requestProcess(e,_.CHUYEN_TIEN_QUOC_TE.XAC_NHAN_TRA_SOAT)}inquiryBeneficiaryStatus(e){return this.apiService.requestProcess(e,_.CHUYEN_TIEN_QUOC_TE.VAN_TIN_TRANG_THAI_NGUOI_HUONG)}inquiryWireTransfer(e){return this.apiService.requestProcess(e,_.CHUYEN_TIEN_QUOC_TE.VAN_TIN_DIEN_CHUYEN_TIEN)}updatePaymentBasisFile(e){return this.apiService.requestProcess(e,_.CHUYEN_TIEN_QUOC_TE.CAP_NHAT_HO_SO_CAN_CU_THANH_TOAN)}getExchangeRateUpdate(e){return this.apiService.requestProcess(e,_.CHUYEN_TIEN_QUOC_TE.LAY_CAP_NHAT_TY_GIA)}initExchangeRateUpdate(e){return this.apiService.requestProcess(e,_.CHUYEN_TIEN_QUOC_TE.KHOI_TAO_CAP_NHAT_TY_GIA)}confirmExchangeRateUpdate(e){return this.apiService.requestProcess(e,_.CHUYEN_TIEN_QUOC_TE.XAC_NHAN_CAP_NHAT_TY_GIA)}getListOfBusinesses(e){return this.apiService.requestProcess(e,_.CHUYEN_TIEN_QUOC_TE.DANH_SACH_DOANH_NGHIEP_DK_DO_LUONG)}getListSchool(e){return this.apiService.requestProcess(e,_.THANH_TOAN_HOA_DON_QUOC_TE.LAY_DANH_SACH_TRUONG_HOC)}getTuitionInfo(e){return this.apiService.requestProcess(e,_.THANH_TOAN_HOA_DON_QUOC_TE.LAY_THONG_TIN_HOC_PHI)}getFeeInfo(e){return this.apiService.requestProcess(e,_.THANH_TOAN_HOA_DON_QUOC_TE.LAY_THONG_TIN_PHI)}initInternationalPayment(e){return this.apiService.requestProcess(e,_.THANH_TOAN_HOA_DON_QUOC_TE.KHOI_TAO_THANH_TOAN_HOA_DON)}confirmInternationalPayment(e){return this.apiService.requestProcess(e,_.THANH_TOAN_HOA_DON_QUOC_TE.XAC_NHAH_THANH_TOAN_HOA_DON)}static \u0275fac=function(s){return new(s||r)};static \u0275prov=T({token:r,factory:r.\u0275fac,providedIn:"root"})}return r})();export{I as a};
