import{a as Ne}from"./chunk-ZRFTKJ6W.js";import{a as xe}from"./chunk-EQVFOYC5.js";import{a as Me}from"./chunk-5YY5BVQL.js";import{a as Fe}from"./chunk-YJZSDC7G.js";import{a as Ue}from"./chunk-WWVXSLWY.js";import"./chunk-GEIXMSZ2.js";import{a as ke}from"./chunk-SOAH2WLW.js";import{o as G}from"./chunk-5VBIWGCV.js";import{a as Te,e as v,g as N,h as P,l as R,n as z,q as V,u as B,v as Ie,w as A}from"./chunk-AASME2FE.js";import{ea as Ee,fa as Le}from"./chunk-AX4DCRSD.js";import{Ad as Se,Bd as k,Bf as we,Cd as M,Dd as Be,Ea as ge,Gc as fe,Kc as ve,Na as de,Pe as U,Y as ue,gb as f,jf as x,mf as be,na as he,o as le,q as me,td as _e,ud as $,vd as Ce,wd as ye}from"./chunk-K5H3SJL5.js";import{a as s}from"./chunk-2WPD26RB.js";import{Bc as pe,Cc as E,Da as J,Dc as L,Ea as g,F as Y,Fa as d,Hb as T,Jb as c,La as Z,Lc as u,M as K,Mc as h,Na as ee,Ob as ie,Qc as F,Qd as ce,Wb as l,Xb as m,Yb as _,ac as S,cc as C,ec as q,ga as X,hb as te,kc as re,lc as oe,mc as ne,na as o,nc as ae,ob as a,oc as b,qc as I,sa as w,sc as se}from"./chunk-YK6FMNSY.js";import{a as O,b as D,e as Ve,f as H,g as Q}from"./chunk-TSRGIXR5.js";var Pe=Ve(j=>{"use strict";j.__esModule=!0;var Ae=function(){function t(n){if(!n)throw new TypeError("Invalid argument; `value` has no value.");this.value=t.EMPTY,n&&t.isGuid(n)&&(this.value=n)}return t.isGuid=function(n){var i=n.toString();return n&&(n instanceof t||t.validator.test(i))},t.create=function(){return new t([t.gen(2),t.gen(1),t.gen(1),t.gen(1),t.gen(3)].join("-"))},t.createEmpty=function(){return new t("emptyguid")},t.parse=function(n){return new t(n)},t.raw=function(){return[t.gen(2),t.gen(1),t.gen(1),t.gen(1),t.gen(3)].join("-")},t.gen=function(n){for(var i="",e=0;e<n;e++)i+=((1+Math.random())*65536|0).toString(16).substring(1);return i},t.prototype.equals=function(n){return t.isGuid(n)&&this.value===n.toString()},t.prototype.isEmpty=function(){return this.value===t.EMPTY},t.prototype.toString=function(){return this.value},t.prototype.toJSON=function(){return{value:this.value}},t.validator=new RegExp("^[a-z0-9]{8}-[a-z0-9]{4}-[a-z0-9]{4}-[a-z0-9]{4}-[a-z0-9]{12}$","i"),t.EMPTY="00000000-0000-0000-0000-000000000000",t}();j.Guid=Ae});var Re=H(Be());var ze=H(Pe());function qe(t,n){if(t&1){let i=S();l(0,"button",5),C("click",function(){g(i);let r=q();return d(r.resetCaptcha())}),m()}if(t&2){let i=q();c("mute",!0)("iconOnly",!0)("size",i.UI.ButtonSize.Md)("prefixIcon","./assets/media/icons/outline/reload.svg")("color",i.UI.ButtonColor.Text)}}var $e=120,W=(()=>{class t{onRefresh;maxlength=5;showErrors=!1;focusAfterClear=!1;errorMessages;baseUrl=fe.apiUrl;captchaGuid;captchaImageUrl;UI=o(x);size=ue.Large;form;pointTimeGet;fb=o(B);ngOnInit(){this.initForm(),this.resetCaptcha(),this.onRefresh&&this.onRefresh.subscribe(i=>{i&&this.resetCaptcha()})}ngOnChanges(i){}initForm(){this.form=this.fb.group({captcha:["",[v.required,v.maxLength(this.maxlength)]]}),this.form.get("captcha")?.valueChanges.subscribe({next:i=>{this.updateChanges(i)}})}onChange=i=>{};onTouched=()=>{};updateChanges(i){if(i){let e={value:i,captchaGuid:this.captchaGuid};this.onChange(e)}else this.onChange(null)}writeValue(){}registerOnChange(i){this.onChange=i}registerOnTouched(i){this.onTouched=i}resetCaptcha(){this.captchaGuid=ze.Guid.create().toString(),this.pointTimeGet=Date.now(),this.captchaImageUrl=`${this.baseUrl}/captcha/${this.captchaGuid}`,this.onTouched(),this.form.get("captcha")?.setValue("")}onClick(){(0,Re.default)().diff(this.pointTimeGet,"seconds")>=$e&&this.resetCaptcha()}onFocus(){this.onClick()}onBlur(){this.onTouched()}static \u0275fac=function(e){return new(e||t)};static \u0275cmp=w({type:t,selectors:[["app-captcha"]],inputs:{onRefresh:"onRefresh",maxlength:"maxlength",showErrors:"showErrors",focusAfterClear:"focusAfterClear",errorMessages:"errorMessages"},standalone:!0,features:[pe([{provide:Te,useExisting:X(()=>t),multi:!0}]),J,E],decls:9,vars:13,consts:[["switchKey",""],[3,"formGroup"],[1,"capcha-block"],["formControlName","captcha","appNoneUnikey","","appNoneSpecial","",3,"clickEvent","blurEvent","focusEvent","size","placeholder","suffix","maxLength","focusAfterClear","errorMessages"],["appVvip","","alt","",1,"captcha--img",3,"src"],["app-button","","type","button","aria-label","reload security code",3,"click","mute","iconOnly","size","prefixIcon","color"]],template:function(e,r){if(e&1){let p=S();l(0,"form",1)(1,"div",2),T(2,qe,1,5,"ng-template",null,0,F),l(4,"app-input",3),u(5,"translate"),C("clickEvent",function(){return g(p),d(r.onClick())})("blurEvent",function(){return g(p),d(r.onBlur())})("focusEvent",function(){return g(p),d(r.onFocus())}),m(),l(6,"div")(7,"div"),_(8,"img",4),m()()()()}if(e&2){let p=ae(3);c("formGroup",r.form),a(4),c("size",r.UI.InputSize.Large)("placeholder",h(5,11,"ma_kiem_tra"))("suffix",p)("maxLength",r.maxlength)("focusAfterClear",r.focusAfterClear)("errorMessages",r.errorMessages),a(3),ie("captcha captcha-",r.size,""),a(),c("src",r.captchaImageUrl,te)}},dependencies:[M,k,Ie,R,N,P,Fe,ke,G,U,A,z,V,ye],encapsulation:2})}return t})();var je=()=>({required:"errors_dang_nhap.bo_trong_so_dien_thoai",pattern:"errors_dang_nhap.so_dien_thoai_khong_dung_dinh_dang"}),We=()=>({required:"errors.bo_trong_mk",minlength:"errors_dang_nhap.mk_nho_hon_8"}),He=()=>({required:"errors.bo_trong_ma_kiem_tra"});function Qe(t,n){t&1&&(l(0,"app-message",9),b(1),u(2,"translate"),m()),t&2&&(c("iconLess",!0),a(),I(" ",h(2,2,"voi_khach_hang_da_co_tk")," "))}var At=(()=>{class t{captchaComponent;loginForm;refreshCaptcha=new ee;isLoading=!1;isShowError=!1;returnUrl="/";formSub$;activatedRoute=o(le);helperService=o(Le);loadingService=o(we);storeService=o(ve);translate=o(Se);router=o(me);fb=o(B);authService=o(Me);modalService=o(be);destroyRef=o(Z);themeService=o(Ce);commonService=o(_e);homeService=o(Ee);UI=o(x);constructor(){this.returnUrl=this.activatedRoute.snapshot.queryParams.returnUrl||"/"}get f(){return this.loginForm.controls}ngOnInit(){this.initForm(),this.themeService.setTheme(he.Mass)}login(){if(this.isLoading=!0,this.loginForm.markAllAsTouched(),this.captchaComponent?.form?.markAllAsTouched(),this.loginForm.invalid){this.isLoading=!1;return}this.loadingService.showLoading();let i={user:this.loginForm.value.phoneNumber,pin:this.loginForm.value.password,captchaToken:this.loginForm.value.captcha.captchaGuid,captchaValue:this.loginForm.value.captcha.value};this.authService.userName=this.loginForm.value.phoneNumber,this.authService.oldPassword=this.loginForm.value.password,this.authService.login(i).pipe($(this.destroyRef)).subscribe({next:e=>Q(this,null,function*(){if(e&&e.loginType)switch(this.authService.loginType=e.loginType,this.storeService.userInfo=D(O({},this.storeService.userInfo),{isSpecial:e.isSpecial,isUpper:e.isUpper,lastTimeLogin:e.lastLogin,receivePass:e.receivePass,cusIdnumber:e.cusIdnumber,address:e.address,issueDate:e.issueDate,issuePlace:e.issuePlace,nationality:e.nationality,cusResident:e.cusResident,defaultAcc:e.defaultAcc}),e.loginType){case f.kich_hoat_lai:let r=this.commonService.getLocation();this.router.navigate([s.Login,s.ConfirmTransformWithOTP],{state:{user:this.loginForm.value.phoneNumber,location:r||"",token:e.token}});break;case f.dang_nhap_binh_thuong:this.router.navigateByUrl("");break;case f.kich_hoat_lan_dau:this.router.navigate([s.Login,s.Active]);break;case f.xac_thuc_MB:let p={state:{user:this.loginForm.value.phoneNumber,token:e.token||null,crossLoginExpire:e.crossLoginExpire?Number(e.crossLoginExpire):ge}};this.router.navigate([s.Login,s.AuthByMB],p);break;case f.chuyen_doi_IB:this.router.navigate([s.Login,s.TransformIB],{state:{user:this.loginForm.value.phoneNumber,mobiles:e.mobiles,requiredChangePass:e.requiredChangePass,oldPassword:this.loginForm.value.password}});break}}),error:e=>{if(e&&e.error)if(e.error.loginType){this.authService.loginType=e.error.loginType;let r={state:{message:e?.error?.des,user:this.loginForm.value.phoneNumber}};switch(e.error.loginType){case f.mat_khau_het_han:this.storeService.userInfo=D(O({},this.storeService.userInfo),{isSpecial:e?.error?.isSpecial,isUpper:e?.error?.isUpper,requestId:e?.error?.requestId}),this.router.navigate([s.Login,s.ExpiredPassword],r);return;case f.xac_thuc_dang_nhap_cmnd:this.router.navigate([s.Login,s.WarningLoginWithId],r);break;case f.xac_thuc_dang_nhap_OTP:this.router.navigate([s.Login,s.WarningLoginWithOTP],r);return}}else this.modalService.notice({type:this.UI.ModalType.Error,message:e?.error?.des});this.isLoading=!1,this.loadingService.hideLoading(),this.refreshCaptcha.emit(!0),this.loginForm.get("captcha")?.setValue("")},complete:()=>{this.isLoading=!1}})}initForm(){this.loginForm=this.fb.group({phoneNumber:["",[v.required,v.pattern(de)]],password:["",[v.required,v.minLength(8)]],captcha:["",[v.required]]}),this.formSub$=this.loginForm.valueChanges.pipe($(this.destroyRef)).subscribe(()=>{this.isShowError=!!(this.f.captcha.touched&&this.f.captcha.invalid&&this.f.captcha.errors?.required)})}onlineRegister(){this.modalService.notice({type:this.UI.ModalType.Info,title:this.translate.instant("thanh_cong"),message:this.translate.instant("dang_ky_online_des")})}handleReturnUrl(){this.homeService.getFeatures().pipe(Y(i=>!!i?.length),K(1)).subscribe(i=>{i?.length&&this.router.navigateByUrl(this.returnUrl)})}ngOnDestroy(){this.formSub$&&this.formSub$.unsubscribe()}static \u0275fac=function(e){return new(e||t)};static \u0275cmp=w({type:t,selectors:[["app-login"]],viewQuery:function(e,r){if(e&1&&re(W,5),e&2){let p;oe(p=ne())&&(r.captchaComponent=p.first)}},standalone:!0,features:[E],decls:20,vars:43,consts:[["hintLoginTpl",""],[1,"nonlogin-page--title"],["autocomplete","off",1,"form",3,"keyup.enter","submit","formGroup"],["formControlName","phoneNumber","appPhoneNumber","",3,"size","placeholder","prefixIcon","maxLength","autocomplete","focusAfterClear","errorMessages"],["formControlName","password",3,"size","placeholder","prefixIcon","maxLength","autocomplete","focusAfterClear","errorMessages"],["formControlName","captcha",3,"focusAfterClear","onRefresh","errorMessages"],[1,"action"],["app-button","","type","submit",3,"size","wFull"],["app-button","","type","button",3,"click","size","color","wFull"],[3,"iconLess"]],template:function(e,r){if(e&1){let p=S();l(0,"p",1),b(1),u(2,"translate"),u(3,"translate"),m(),l(4,"form",2),C("keyup.enter",function(){return g(p),d(r.login())})("submit",function(){return g(p),d(r.login())}),_(5,"app-input",3),u(6,"translate"),_(7,"app-input-password",4),u(8,"translate"),l(9,"div"),_(10,"app-captcha",5),m(),l(11,"div",6)(12,"button",7),b(13),u(14,"translate"),m()()(),l(15,"button",8),C("click",function(){return g(p),d(r.onlineRegister())}),b(16),u(17,"translate"),m(),T(18,Qe,3,4,"ng-template",null,0,F)}e&2&&(a(),se(" ",h(2,28,"chao_mung"),""," ","",h(3,30,"BIDV_Smartbanking"),`
`),a(3),c("formGroup",r.loginForm),a(),c("size",r.UI.InputSize.Large)("placeholder",h(6,32,"sdt"))("prefixIcon","assets/media/icons/doutone/icon-he-thong/ht-phone.svg")("maxLength",10)("autocomplete","off")("focusAfterClear",!0)("errorMessages",L(40,je)),a(2),c("size",r.UI.InputSize.Large)("placeholder",h(8,34,"mat_khau"))("prefixIcon","assets/media/icons/doutone/icon-he-thong/ht-lock.svg")("maxLength",20)("autocomplete","off")("focusAfterClear",!0)("errorMessages",L(41,We)),a(3),c("focusAfterClear",!0)("onRefresh",r.refreshCaptcha)("errorMessages",L(42,He)),a(2),c("size",r.UI.ButtonSize.Lg)("wFull",!0),a(),I(" ",h(14,36,"dang_nhap")," "),a(2),c("size",r.UI.ButtonSize.Lg)("color",r.UI.ButtonColor.SubPrimary)("wFull",!0),a(),I(" ",h(17,38,"dang_ky_online"),`
`))},dependencies:[M,k,A,R,N,P,z,V,ce,W,G,U,Ne,xe,Ue],styles:[".hint-login[_ngcontent-%COMP%]{list-style-type:disc}.hint-login[_ngcontent-%COMP%] > [_ngcontent-%COMP%]:not([hidden]) ~ [_ngcontent-%COMP%]:not([hidden]){--tw-space-y-reverse: 0;margin-top:calc(.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(.5rem * var(--tw-space-y-reverse))}.hint-login[_ngcontent-%COMP%]{padding-left:1rem;font-size:var(--fontSize-body-md-0);line-height:var(--fontSize-body-md-1-lineHeight);font-weight:var(--fontSize-body-md-1-fontWeight)}.conversion-guide[_ngcontent-%COMP%]{margin-top:1rem;margin-bottom:1.5rem;display:flex;justify-content:center;border-bottom-width:1px;border-color:var(--colors-border-lighter);padding-bottom:1.5rem}"]})}return t})();export{At as LoginComponent};
