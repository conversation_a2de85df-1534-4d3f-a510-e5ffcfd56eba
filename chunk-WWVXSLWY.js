import{$ as O}from"./chunk-K5H3SJL5.js";import{$b as f,Cc as M,Da as m,Hb as l,Jb as d,Jd as L,Nb as g,Qd as x,Rb as p,Wb as o,Xb as a,Yb as C,ec as u,fc as h,gc as v,ob as s,oc as _,qc as T,sa as r}from"./chunk-YK6FMNSY.js";var S=["*"];function y(e,c){e&1&&C(0,"div",1)}function A(e,c){e&1&&(o(0,"div",3),v(1),a())}function E(e,c){if(e&1&&(o(0,"div",4)(1,"div"),_(2),a(),f(3,5),a()),e&2){let t=u();s(2),T(" ",t.description," "),s(),d("ngTemplateOutlet",t.descriptionTpl)}}var F=(()=>{class e{color=O.Info;titleLess=!1;iconLess=!1;iconAlign="top";description;descriptionTpl;_hostClass="";get hostClass(){return this._hostClass}ngOnChanges(t){this._hostClass=this.initClass()}ngOnInit(){this._hostClass=this.initClass()}initClass(){let t=["app-message"];return t.push("app-message-"+this.color),t.push("app-message--"+this.iconAlign),t.join(" ")}static \u0275fac=function(i){return new(i||e)};static \u0275cmp=r({type:e,selectors:[["app-message"],["","app-message",""]],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClass)},inputs:{color:"color",titleLess:"titleLess",iconLess:"iconLess",iconAlign:"iconAlign",description:"description",descriptionTpl:"descriptionTpl"},standalone:!0,features:[m,M],ngContentSelectors:S,decls:5,vars:3,consts:[[1,"app-message--inner"],[1,"app-message--icon"],[1,"app-message--content"],[1,"app-message--title"],[1,"app-message--description"],[3,"ngTemplateOutlet"]],template:function(i,n){i&1&&(h(),o(0,"div",0),l(1,y,1,0,"div",1),o(2,"div",2),l(3,A,2,0,"div",3)(4,E,4,2,"div",4),a()()),i&2&&(s(),p(n.iconLess?-1:1),s(2),p(n.titleLess?-1:3),s(),p(n.description||n.descriptionTpl?4:-1))},dependencies:[x,L]})}return e})();export{F as a};
