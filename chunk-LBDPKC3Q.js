import{Gc as a,Jc as c,Kc as l,Xe as i,f as n}from"./chunk-K5H3SJL5.js";import{ha as s,na as r}from"./chunk-YK6FMNSY.js";var f=(()=>{class t{storageService=r(l);httpClient=r(n);crypto=r(c);uploadUrl=a.uploadUrl;uploadCTQTUrl=a.uploadCTQTUrl;uploadAvatarUrl=a.uploadAvatarUrl;currentUser;clearData={data:{}};constructor(){this.currentUser=this.storageService.userInfo,(!this.currentUser||!this.currentUser.accessKey||!this.currentUser.accessToken)&&console.error("avatar_module: server PreexcuteIB khong tra ve bankToken"),this.currentUser&&(this.clearData={mid:"1",version:"1.0.3",isRoot:"1",nonce:"**********.732847",bankToken:this.currentUser.accessKey,requestTime:"**********.732727",ipAddress:"",phone:this.currentUser.user,deviceType:"0",deviceModel:"iPhone",lat:"",imei:"",lang:this.storageService.language,lng:"",sdkVersion:"11",screenSize:"375.000000*812.000000",data:{checksum:"B3C6A0EE4966D2E32C17D884ADDE9A7D"}})}uploadAvatar(e){if(!this.storageService.isAuthenticated)throw new TypeError("not_login");return this.httpClient.post(`${this.uploadAvatarUrl}`,{file:e,clearData:this.clearData})}uploadFile(e){if(!this.storageService.isAuthenticated)throw new TypeError("not_login");return this.httpClient.post(`${this.uploadUrl}`,{file:e,clearData:this.clearData})}uploadCCTT(e){if(!this.storageService.isAuthenticated)throw new TypeError("not_login");let o=this.crypto.genCheckSum(String(this.currentUser?.user),String(this.currentUser.cif),new Date().getFullYear().toString(),String(i.chuyen_tien_quoc_te));return this.clearData.data={checksum:o,cif:this.currentUser&&this.currentUser.cif,year:new Date().getFullYear(),serviceCode:i.chuyen_tien_quoc_te},this.clearData.bankToken=this.currentUser.accessToken,this.httpClient.post(`${this.uploadCTQTUrl}`,{file:e,clearData:this.clearData})}uploadMuaNgoaiTe(e){if(!this.storageService.isAuthenticated)throw new TypeError("not_login");return this.clearData.data={checksum:"B3C6A0EE4966D2E32C17D884ADDE9A7D",cif:this.currentUser&&this.currentUser.cif,year:new Date().getFullYear(),serviceCode:i.dang_ky_giao_dich_mua_ngoai_te},this.httpClient.post(`${this.uploadCTQTUrl}/file_upload`,{file:e,clearData:this.clearData})}viewAvatar(){if(!this.storageService.isAuthenticated)throw new TypeError("not_login");return this.httpClient.post(`${this.uploadUrl}/get_my_avatar`,{clearData:this.clearData})}removeAvatar(){if(!this.storageService.isAuthenticated)throw new TypeError("not_login");return this.httpClient.post(`${this.uploadUrl}/remove_my_avatar`,{clearData:this.clearData})}static \u0275fac=function(o){return new(o||t)};static \u0275prov=s({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();export{f as a};
