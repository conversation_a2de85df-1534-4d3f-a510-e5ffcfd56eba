import{a as pn,c as Tn,d as Vr,e as Bn}from"./chunk-5KYMQ6XR.js";import{a as Yn}from"./chunk-KZWY5WVM.js";import{a as Xn}from"./chunk-S7T4MJEF.js";import{j as Kn}from"./chunk-JG773AST.js";import"./chunk-CLYMFTGQ.js";import"./chunk-SX5BIO4E.js";import{a as Ce}from"./chunk-JXR652IR.js";import"./chunk-SGPU43K3.js";import{a as Zn}from"./chunk-LTTYPHCR.js";import{a as Un,b as Vn}from"./chunk-2Y5PHVVP.js";import"./chunk-M2J6HCG4.js";import"./chunk-6NBO3RRK.js";import"./chunk-LAZOTBTB.js";import{a as nt}from"./chunk-5YY5BVQL.js";import"./chunk-LBDPKC3Q.js";import"./chunk-EJBNJL2X.js";import"./chunk-7BPXJPVY.js";import"./chunk-YJZSDC7G.js";import"./chunk-VFBBYLZU.js";import"./chunk-6KSU3ZJ7.js";import"./chunk-SD7XBMX3.js";import"./chunk-LSYDHUWU.js";import{a as $n}from"./chunk-SG3W3EJ6.js";import{a as xn}from"./chunk-4JTM5ICE.js";import"./chunk-SU5NX4RM.js";import"./chunk-TI2OG4JD.js";import"./chunk-P2H32HMS.js";import"./chunk-UVPCVSBB.js";import"./chunk-IDHI5ISK.js";import"./chunk-IZVSQRWS.js";import{c as Gn}from"./chunk-5EBIDXQC.js";import"./chunk-24JQYWUX.js";import"./chunk-GEIXMSZ2.js";import"./chunk-54E4CCOA.js";import{a as Pn,b as zn,d as Fn,e as jn,f as Ln}from"./chunk-YNRPULUE.js";import"./chunk-5VYTNFSL.js";import"./chunk-JSW6KUUV.js";import"./chunk-SOAH2WLW.js";import"./chunk-5VBIWGCV.js";import{v as En}from"./chunk-AASME2FE.js";import{$ as Bt,S as wn,T as An,U as On,a as ie,aa as Rn,ba as Dn,d as Mn,ea as Hn,fa as it}from"./chunk-AX4DCRSD.js";import{Ad as Z,Af as De,Bd as fe,Bf as He,Ca as Ht,Cd as te,Ce as vn,Da as Pt,Dd as Oe,De as Cn,Fa as qe,Fc as an,Ga as Ji,Gc as A,H as U,Ha as Qi,Ia as qi,Jc as oe,Kc as O,Ne as yn,Oa as dt,Ob as et,Oe as kn,Pd as tt,Pe as $t,Vb as ai,Xd as dn,Yc as ci,Za as zt,Zb as rn,_a as en,a as Ui,bb as tn,be as un,cb as ut,cc as sn,d as de,e as Ki,eb as Ft,f as At,fb as ft,g as $e,gb as nn,h as si,i as Vi,ie as fn,j as Yi,jd as cn,jf as Re,k as Gi,kd as ln,ld as hn,mf as V,n as Ot,na as Be,nf as h,o as Zi,of as ve,p as Qe,pf as bn,q as X,qd as jt,r as Rt,rf as In,s as oi,se as _n,t as Xi,td as we,te as Lt,u as Wi,ud as re,v as Dt,vd as Ae,ve as gn,w as ae,we as Sn,y as ue,yf as Nn,zc as on,zd as mn}from"./chunk-K5H3SJL5.js";import{a as f}from"./chunk-2WPD26RB.js";import{Ad as ri,C as yi,Cb as Ne,Cc as L,Cd as wt,Ea as F,Ec as Ee,F as Te,Fa as j,Fc as Xe,Gc as zi,H as be,Hb as T,Ia as Tt,Ib as xt,Id as Fi,Jb as C,Jd as ji,Ka as qt,Kb as xe,Kd as Li,La as q,Lb as G,Lc as x,M as Ze,Mc as w,Na as Ie,Nb as Et,Nc as We,Oa as Ni,Od as $i,Pa as xi,Pd as Bi,Qa as bt,Qc as ti,Qd as ee,R as ki,Rb as E,Tb as he,Ub as pe,Vb as me,Wa as Ei,Wb as m,Xb as d,Xc as ii,Yb as y,Yc as ni,Zb as ct,_b as lt,_c as Je,ac as B,ba as at,cc as N,da as Ti,ec as k,f as ke,fa as bi,g as Si,gb as It,ha as b,hb as Nt,hc as Hi,ia as Jt,k as st,ka as Ct,kc as ht,lb as Mi,lc as pt,ma as _,mc as mt,md as Mt,n as Ye,na as p,nc as ei,o as Y,ob as l,oc as M,pb as K,pc as Le,qb as wi,qc as D,r as ot,s as Ge,sa as z,sb as Ai,t as Q,ta as Qt,td as Me,ua as yt,ub as Oi,v as vi,vb as Ri,wa as kt,wb as Di,xa as Ii,yc as Pi,z as Ci}from"./chunk-YK6FMNSY.js";import{a as $,b as ye,f as Se,g as je}from"./chunk-TSRGIXR5.js";var Wn={version:"3.0.1.8",major:3,minor:0,patch:1,dev:{pre:"alpha",build:1,raw:"3.0.0-alpha.1",time:"2025-02-20 16:48:29"},sercurity:{pre:"ser",build:12,raw:"1.0.0-ser.12",time:"2021-03-08 13:57:50"},uat:{pre:"beta",build:94,raw:"3.0.0-beta.94",time:"2025-07-12 18:59:22"},pilot:{pre:"rc",build:18,raw:"3.0.0-rc.18",time:"2025-07-15 19:57:39"},live:{pre:"",build:8,raw:"3.0.1.8",time:"2025-07-24 07:52:52"}};var Jn=Se(Oe());var Gr=n=>({currentYear:n});function Zr(n,o){if(n&1&&M(0),n&2){let e=k();D(" ",(e.ver==null?null:e.ver.raw)+" - "," ")}}var Qn=(()=>{class n{footer=!0;mediaBaseUrl=A.mediaBaseUrl;ver;currentPhase=A.currentPhase;envName=A.name;year=A.currentYear;urlHuongDanSuDungDichVu=A.urlHuongDanSuDungDichVu;currentYear=(0,Jn.default)().year();EXTERNAL_LINKS=en;translateService=p(Z);modalService=p(V);constructor(){[Ht,Pt].indexOf(A.name)>=0&&(this.ver=Object(Wn)[A.name])}ngOnInit(){}onViewContact(){this.modalService.confirm({title:this.translateService.instant("msg.ban_can_ho_tro"),message:this.translateService.instant("msg.tong_dai_cskh",{phone:dt}),btnCancel:{title:"btn.cancel",color:U.Outline},btnConfirm:{title:"btn.call",color:U.Primary},confirm:()=>{window.location.href=`tel:${dt}`},maskClosable:!0})}static \u0275fac=function(t){return new(t||n)};static \u0275cmp=z({type:n,selectors:[["app-non-login-footer"]],hostVars:2,hostBindings:function(t,i){t&2&&G("nonlogin-footer",i.footer)},exportAs:["app-non-login-footer"],standalone:!0,features:[L],decls:13,vars:15,consts:[[1,"nonlogin-footer--inner"],[1,"nonlogin-footer--menu"],["target","_blank",1,"nonlogin-footer--menu-item"],[1,"nonlogin-footer--menu-item--icon",3,"src"],[1,"copy-right"]],template:function(t,i){t&1&&(m(0,"div",0)(1,"div",1)(2,"a",2),x(3,"translate"),y(4,"app-svg",3),d(),m(5,"a",2),y(6,"app-svg",3),d(),m(7,"a",2),y(8,"app-svg",3),d()(),m(9,"p",4),T(10,Zr,1,1),M(11),x(12,"translate"),d()()),t&2&&(l(2),xt("href",w(3,8,"links.huong_dan_bao_mat_url"),Nt),l(2),C("src","media/icons/outline/shield.svg"),l(),xt("href",i.EXTERNAL_LINKS.QAInformation,Nt),l(),C("src","media/icons/outline/question.svg"),l(),xt("href",i.urlHuongDanSuDungDichVu,Nt),l(),C("src","media/icons/outline/book.svg"),l(2),E(i.ver?10:-1),l(),D(" ",We(12,10,"common.copy_right",Ee(13,Gr,i.currentYear))," "))},dependencies:[ee,te,fe,tt]})}return n})();var Xr=["class","app-site-layout"];function Wr(n,o){n&1&&y(0,"img",1)}var qn=(()=>{class n{storeService;router;get hostClass(){return["nonlogin-layout","screen-size-"+this.screenSize]}commonService=p(we);destroyRef=p(q);screenSize;screenShowBanner=[et.Xs];SCREEN_SIZE_IS_BANNER=on;constructor(e,t){this.storeService=e,this.router=t}ngOnInit(){this.commonService.getCurrentScreenSize().pipe(re(this.destroyRef)).subscribe(e=>{this.screenSize=e})}static \u0275fac=function(t){return new(t||n)(K(O),K(X))};static \u0275cmp=z({type:n,selectors:[["",8,"app-site-layout"]],hostVars:2,hostBindings:function(t,i){t&2&&Et(i.hostClass)},exportAs:["app-site-layout"],standalone:!0,features:[L],attrs:Xr,decls:5,vars:1,consts:[[1,"nonlogin-layout--inner"],["src","media/img/banner-login.png","alt","",1,"nonlogin-layout--banner"],[1,"main"]],template:function(t,i){t&1&&(m(0,"div",0),T(1,Wr,1,0,"img",1),m(2,"main",2),y(3,"router-outlet"),d(),y(4,"app-non-login-footer"),d()),t&2&&(l(),E(i.SCREEN_SIZE_IS_BANNER.includes(i.screenSize)?1:-1))},dependencies:[ee,Qe,Qn]})}return n})();var ir=Se(Oe());var P=(()=>{class n{storeService;router;location;activatedRoute;modalService;processApi;translate=p(Z);apiService=p(De);configurationDataService=p(xn);constructor(e,t,i,r,a,u){this.storeService=e,this.router=t,this.location=i,this.activatedRoute=r,this.modalService=a,this.processApi=u}getResolvedUrl(e){return e.pathFromRoot.map(t=>t.url.map(i=>i.toString()).join("")).filter(t=>t.length>0)}canActivate(e,t){return new Promise((i,r)=>{let{userInfo:a,accessKey:u,sessionTimeout:s}=this.storeService,c=Date.now();if(u&&a&&a.loginType===nn.dang_nhap_binh_thuong&&Number(s)>=c)return this.processApi.isLockRedirect&&this.processApi.unLockRedirect(),i(!0);let v=t.url&&t.url!==f.Login&&t.url!=="/"?t.url:"";return v?this.router.navigate([`/${f.Login}`],{queryParams:{returnUrl:v}}):this.router.navigate([`/${f.Login}`]),r(!1)}).then(()=>!0).catch(()=>!1)}canActivateChild(e,t){return new Promise((i,r)=>{if(this.processApi.isLockRedirect){let a=this.getResolvedUrl(e);return a.length<=0&&(a=["/"]),this.modalService.confirm({title:this.translate.instant("heading.xac_nhan_huy_giao_dich"),message:this.translate.instant("message.xac_nhan_huy_giao_dich"),btnConfirm:{title:this.translate.instant("btn.accept"),color:U.Primary},btnCancel:{title:this.translate.instant("btn.no"),color:U.Outline},confirm:()=>{this.processApi.unLockRedirect(),this.router.navigate(a)}}),r(!1)}if(e.data&&e.data.phase&&e.data.phase<=A.currentPhase||!e.data.phase)return i(!0);[Ht,Pt].indexOf(A.name)>=0&&this.modalService.warning(`Module n\u1EB1m trong g\xF3i <b class="color-red h5">${e.data.phase}</b> v\xE0 kh\xF4ng thu\u1ED9c g\xF3i \u0111ang \u0111\u01B0\u1EE3c ph\xE1t h\xE0nh <b class="color-red h5">(${A.currentPhase})</b>`),r(!1)}).then(()=>!0).catch(()=>!1)}navigateAccounts(){let{BEGIN_COT_LUCKY:e,FINISH_COT_LUCKY:t}=rn;this.configurationDataService.getConfigs([e,t]).pipe(Te(i=>!!i?.length),Ze(1)).subscribe({next:i=>{if(i?.length){let r=i[0]?.value,a=i[1]?.value,u=Number(String(new Date().getHours()).concat(String(new Date().getMinutes()>9?new Date().getMinutes():"0"+String(new Date().getMinutes()))));r&&a&&(Number(r)>u||Number(a)<u)&&this.modalService.error({message:`${this.translate.instant("hien_tai_da_het_gio_giao_dich_tai_khoan_nhu_y")} ${r[0]+r[1]+"h"+r[2]+r[3]} ${this.translate.instant("den")} ${a[0]+a[1]+"h"+a[2]+a[3]} ${this.translate.instant("hang_ngay_cot_nhu_y")}`,maskClosable:!1,confirm:()=>{this.router.navigate([f.Account])}})}},error:i=>{this.modalService.error(i?.message)}})}navigateSubcribe(e){if(this.storeService?.userInfo?.nationality!=="VN"){this.modalService.confirm({message:this.translate.instant("thong_bao_phat_hanh_the_nguoi_nuoc_ngoai"),btnConfirm:{title:this.translate.instant("tim_kiem_chi_nhanh_pgd"),color:U.Primary},btnCancel:{title:this.translate.instant("dong"),color:U.Outline},maskClosable:!1,confirm:()=>{window.open(zt,"_blank"),this.location.back()},cancel:()=>this.location.back()});return}else if(this.storeService?.userInfo?.cusType===sn){this.modalService.confirm({message:this.translate.instant("thong_bao_phat_hanh_the_hkd"),btnConfirm:{title:this.translate.instant("tim_kiem_chi_nhanh_pgd"),color:U.Primary},btnCancel:{title:this.translate.instant("dong"),color:U.Outline},maskClosable:!1,confirm:()=>{window.open(zt,"_blank"),this.location.back()},cancel:()=>this.location.back()});return}else if((0,ir.default)(new Date).diff(this.storeService?.userInfo?.dob.split("/")[2].concat("-").concat(this.storeService?.userInfo?.dob.split("/")[1]).concat("-").concat(this.storeService?.userInfo?.dob.split("/")[0]),"year")<18){this.modalService.confirm({message:this.translate.instant("thong_bao_phat_hanh_the_nho_hon_18"),btnConfirm:{title:this.translate.instant("tim_kiem_chi_nhanh_pgd"),color:U.Primary},btnCancel:{title:this.translate.instant("dong"),color:U.Outline},maskClosable:!1,confirm:()=>{window.open(zt,"_blank"),this.location.back()},cancel:()=>this.location.back()});return}}static \u0275fac=function(t){return new(t||n)(_(O),_(X),_(Me),_(Zi),_(V),_(ve))};static \u0275prov=b({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var _t=Se(Oe()),nr=Se(Vr());var Qr=["fileInput"],qr=(n,o)=>[n,o];function es(n,o){n&1&&(M(0),x(1,"translate")),n&2&&D(" ",w(1,1,"common.premier_customer")," ")}function ts(n,o){n&1&&(M(0),x(1,"translate")),n&2&&D(" ",w(1,1,"common.premier_elite_customer")," ")}function is(n,o){n&1&&(M(0),x(1,"translate")),n&2&&D(" ",w(1,1,"common.private_customer")," ")}function ns(n,o){if(n&1&&(m(0,"div",7)(1,"div",13)(2,"p",14),T(3,es,2,3)(4,ts,2,3)(5,is,2,3),d()()()),n&2){let e,t=k();l(3),E((e=t.userInfo==null?null:t.userInfo.vipType)===t.VipType.Premier?3:e===t.VipType.PremierElite?4:e===t.VipType.Private?5:-1)}}_t.default.extend(nr.default);var rr=(()=>{class n extends Bt{account=!0;fileInput;theme$;tick;helloMessage="";timeSpace=1e3;modalService=p(V);themeService=p(Ae);UI=p(Re);ACCEPT_IMAGE_FILE_TYPE=ai;THEME_MODE=Be;MainRoute=f;VipType=Rn;ngOnInit(){this.theme$=this.themeService.getTheme(),this.userInfo&&this.onLoopTime()}fileChangeEvent(e){let t=e.target.files[0];if(!t)return;if(!ai.replaceAll(".","image/").split(", ").includes(t.type)){this.modalService.error({message:this.translate.instant("errors.khong_dung_dinh_dang_file_anh")});return}this.openModalChangeAvatar(t)}openModalChangeAvatar(e){this.modalService.open(Un,{data:e,maskClosable:!1})?.afterClose.subscribe(()=>{this.fileInput&&(this.fileInput.nativeElement.value="")})}onLoopTime(){this.onSayGreeting(),this.tick=setInterval(()=>{this.onSayGreeting()},this.timeSpace)}checkRangeTime(e,t){let i=(0,_t.default)(),r=(0,_t.default)(i.format("YYYY-MM-DD")+" "+e,"YYYY-MM-DD HH:mm:ss"),a=(0,_t.default)(i.format("YYYY-MM-DD")+" "+t,"YYYY-MM-DD HH:mm:ss");return i.isBetween(r,a,null,"[]")}onSayGreeting(){this.checkRangeTime("00:00:00","12:00:59")?this.helloMessage="message.chao_buoi_sang":this.checkRangeTime("12:01:00","18:00:59")?this.helloMessage="message.chao_buoi_chieu":this.checkRangeTime("18:01:00","23:59:59")&&(this.helloMessage="message.chao_buoi_toi")}ngOnDestroy(){super.ngOnDestroy(),clearInterval(this.tick)}static \u0275fac=(()=>{let e;return function(i){return(e||(e=Tt(n)))(i||n)}})();static \u0275cmp=z({type:n,selectors:[["sidebar-account"],["","sidebar-account",""]],viewQuery:function(t,i){if(t&1&&ht(Qr,5),t&2){let r;pt(r=mt())&&(i.fileInput=r.first)}},hostVars:2,hostBindings:function(t,i){t&2&&G("sidebar-account",i.account)},standalone:!0,features:[Ne,L],decls:22,vars:25,consts:[["dropDownTpl","nzDropdownMenu"],["fileInput",""],[1,"sidebar-account--inner"],["nz-dropdown","",1,"sidebar-account--avt",3,"size","type","content","enableChange","nzTrigger","nzDropdownMenu","iconChange2Color"],[1,"sidebar-account--content"],[1,"sidebar-account--greeting"],[1,"sidebar-account--owner-name"],[1,"sidebar-account--rank"],["nz-menu",""],["nz-menu-item","",3,"click"],["type","file","hidden","",3,"change","accept"],[3,"icon"],["nz-menu-item","",3,"routerLink"],[1,"sidebar-account--rank-inner"],[1,"sidebar-account--rank-name"]],template:function(t,i){if(t&1){let r=B();m(0,"div",2),y(1,"app-avatar",3),m(2,"div",4)(3,"p",5),M(4),x(5,"translate"),d(),m(6,"p",6),M(7),d(),T(8,ns,6,1,"div",7),d()(),m(9,"nz-dropdown-menu",null,0)(11,"ul",8)(12,"li",9),N("click",function(){F(r);let u=ei(14);return j(u.click())}),m(13,"input",10,1),N("change",function(u){return F(r),j(i.fileChangeEvent(u))}),d(),m(15,"app-dropdown-item",11),M(16),x(17,"translate"),d()(),m(18,"li",12)(19,"app-dropdown-item",11),M(20),x(21,"translate"),d()()()()}if(t&2){let r=ei(10);l(),C("size",i.UI.AvatarSize.midMd)("type",i.UI.AvatarType.Image)("content",(i.userInfo==null?null:i.userInfo.avatarUrl)||"assets/media/icons/solid/profile.svg")("enableChange",!0)("nzTrigger","hover")("nzDropdownMenu",r)("iconChange2Color",!1),l(3),Le(w(5,16,i.helloMessage)),l(3),Le(i.userInfo==null?null:i.userInfo.name),l(),E(i.userInfo!=null&&i.userInfo.vipType?8:-1),l(5),Hi("accept",i.ACCEPT_IMAGE_FILE_TYPE),l(2),C("icon","assets/media/icons/doutone/icon-he-thong/ht-user.svg"),l(),D(" ",w(17,18,"featured.doi_anh_dai_dien")," "),l(2),C("routerLink",Xe(22,qr,i.MainRoute.Setting,i.MainRoute.SettingWallpaper)),l(),C("icon","assets/media/icons/doutone/icon-he-thong/ht-image.svg"),l(),D(" ",w(21,20,"featured.doi_anh_nen")," ")}},dependencies:[ee,Dt,Rt,te,fe,Ln,zn,Pn,Fn,jn,Mn,$n]})}return n})();var rs=["bidv-omni-sidebar-bpoint",""];function ss(n,o){n&1&&(m(0,"p",2),M(1),x(2,"translate"),d()),n&2&&(l(),D(" ",w(2,1,"tich_luy_b_points_de_nhan_uu_dai")," "))}function os(n,o){if(n&1&&M(0),n&2){let e=k(2);D(" ",e.secretKey," ")}}function as(n,o){if(n&1&&(M(0),x(1,"thousandSeparator")),n&2){let e=k(2);D(" ",w(1,1,e.loyaltyData.usablePoint)," ")}}function cs(n,o){n&1&&y(0,"div",13)}function ls(n,o){if(n&1){let e=B();m(0,"button",15),N("click",function(i){F(e);let r=k(2);return j(r.toggleBPoint(i))}),d()}if(n&2){let e=k(2);C("iconOnly",!0)("prefixIcon",e.iconEye)("color",e.UI.ButtonColor.SubWhite)("size",e.UI.ButtonSize.Sm)("iconSize",6)}}function hs(n,o){if(n&1){let e=B();m(0,"div",10),N("click",function(){F(e);let i=k();return j(i.goToRewards())}),m(1,"p"),M(2),x(3,"translate"),d(),y(4,"app-svg",9),d(),m(5,"div",11)(6,"p",12),T(7,os,1,1)(8,as,2,3)(9,cs,1,0,"div",13),d(),T(10,ls,1,5,"button",14),d()}if(n&2){let e=k();l(2),D(" ",w(3,5,"diem_b_points_kha_dung")," "),l(2),C("src","media/icons/outline/chevron-right.svg")("size",4),l(3),E(e.isSecret&&e.secretMode?7:e.loyaltyData?8:9),l(3),E(e.secretMode?10:-1)}}function ps(n,o){n&1&&(m(0,"p",7),M(1),x(2,"translate"),d()),n&2&&(l(),D(" ",w(2,1,"explore_voucher")," "))}function ms(n,o){n&1&&(m(0,"p",16),M(1),x(2,"translate"),d(),m(3,"p",17),M(4),x(5,"translate"),d()),n&2&&(l(),Le(w(2,2,"my_gift")),l(3),D(" ",w(5,4,"explore_voucher")," "))}var sr=(()=>{class n{bpoint=!0;UI=p(Re);loyaltyData;isInRewards=!1;secretMode=!0;isSecret=!0;secretKey="******";iconEye=this.isSecret?"media/icons/outline/eye-hide.svg":"media/icons/outline/eye.svg";isFetching=!1;destroyRef=p(q);apiRewards=p(Kn);router=p(X);ngOnInit(){this.checkShow(this.router.url),this.router.events.pipe(Te(e=>e instanceof Ot)).subscribe(e=>{this.checkShow(e.urlAfterRedirects)}),this.apiRewards.getLoyaltyData(!1).pipe(re(this.destroyRef)).subscribe(e=>{this.loyaltyData=e,this.isFetching=!1})}checkShow(e){this.isInRewards=e.includes(f.Rewards)}goToRewards(){this.router.navigate([f.Rewards])}goToBPoint(){this.router.navigate([f.Rewards,f.RewardsBpoint])}goToMyGifts(){this.router.navigate([f.Rewards,f.RewardsMyGift])}toggleBPoint(e){e.stopPropagation(),this.loyaltyData=void 0,this.isSecret=!this.isSecret,this.iconEye=this.isSecret?"media/icons/outline/eye-hide.svg":"media/icons/outline/eye.svg",this.isSecret||(this.isFetching=!0,this.apiRewards.fetchLoyaltyData(!1,!0))}static \u0275fac=function(t){return new(t||n)};static \u0275cmp=z({type:n,selectors:[["","bidv-omni-sidebar-bpoint",""]],hostVars:2,hostBindings:function(t,i){t&2&&G("block-bpoint",i.bpoint)},standalone:!0,features:[L],attrs:rs,decls:13,vars:7,consts:[[1,"bpoint-inner"],[1,"bpoint-info",3,"click"],[1,"bpoint-info--empty-label"],[1,"bpoint-gift",3,"click"],[1,"bpoint-gift--icon-wrap"],[1,"icon-gift",3,"src","colorChange","size"],[1,"bpoint-gift--info"],[1,"bpoint-info--empty-discover"],[1,"bpoint-gift--arrow"],[3,"src","size"],[1,"bpoint-info--label",3,"click"],[1,"bpoint-info--point-wrap"],[1,"bpoint-info--point"],[1,"bpoint-info--point-loading"],["app-button","","aria-label","hide/show","type","button",1,"bpoint-info--point-check",3,"iconOnly","prefixIcon","color","size","iconSize"],["app-button","","aria-label","hide/show","type","button",1,"bpoint-info--point-check",3,"click","iconOnly","prefixIcon","color","size","iconSize"],[1,"bpoint-gift--info-label"],[1,"bpoint-gift--info-desc"]],template:function(t,i){if(t&1){let r=B();Pi(0),m(1,"div",0)(2,"div",1),N("click",function(){return F(r),j(i.goToRewards())}),T(3,ss,3,3,"p",2)(4,hs,11,7),d(),m(5,"div",3),N("click",function(){return F(r),j(i.goToMyGifts())}),m(6,"div",4),y(7,"app-svg",5),d(),m(8,"div",6),T(9,ps,3,3,"p",7)(10,ms,6,6),d(),m(11,"div",8),y(12,"app-svg",9),d()()()}if(t&2){let r,a=!(i.isSecret&&i.secretMode)&&(!i.isFetching&&!i.loyaltyData||i.loyaltyData&&+((r=i.loyaltyData==null?null:i.loyaltyData.usablePoint)!==null&&r!==void 0?r:0)==0);l(3),E(a?3:4),l(4),C("src","media/icons/doutone/icon-chuc-nang/cn-tang-qua.svg")("colorChange",!1)("size",5),l(2),E(a?9:10),l(3),C("src","media/icons/outline/chevron-right.svg")("size",5)}},dependencies:[ee,tt,te,fe,$t,On]})}return n})();var or=Se(Oe());var ds=["bidv-omni-sidebar",""],us=n=>({currentYear:n});function fs(n,o){n&1&&(m(0,"div",1)(1,"a",11),y(2,"div",12),d()()),n&2&&(l(),C("routerLink","/"))}function _s(n,o){if(n&1){let e=B();m(0,"app-toggle-language",16),N("change",function(){F(e);let i=k(2);return j(i.changeLang())}),d()}}function gs(n,o){if(n&1){let e=B();m(0,"div",2)(1,"button",13),N("click",function(){F(e);let i=k();return j(i.handleCollapsed(!1))}),M(2),x(3,"translate"),d(),m(4,"div",14),T(5,_s,1,0,"app-toggle-language"),m(6,"button",15),N("click",function(){F(e);let i=k();return j(i.handleLogout())}),d()()()}if(n&2){let e=k();l(),C("color",e.UI.ButtonColor.Text)("mute",!0)("prefixIcon","media/icons/outline/close.svg"),l(),D(" ",w(3,9,"common.dong")," "),l(3),E(e.language?5:-1),l(),C("iconOnly",!0)("size",e.UI.ButtonSize.Md)("color",e.UI.ButtonColor.White)("prefixIcon","media/icons/outline/logout.svg")}}function Ss(n,o){n&1&&y(0,"div",5)}function vs(n,o){if(n&1){let e=B();m(0,"div",17),N("click",function(){let i=F(e).$implicit,r=k();return j(r.handelOpenClick(i))}),m(1,"div",18),y(2,"app-svg",19),d(),m(3,"p",20),M(4),x(5,"translate"),d(),m(6,"div",21),y(7,"app-svg",22),d()()}if(n&2){let e=o.$implicit;l(2),C("src",e.icon)("colorChange",!1),l(2),D(" ",w(5,4,e.name)," "),l(3),C("src","media/icons/outline/chevron-right.svg")}}function Cs(n,o){if(n&1&&(m(0,"p",9),M(1),x(2,"translate"),d()),n&2){let e=k();l(),D(" ",We(2,1,"common.copy_right",Ee(4,us,e.currentYear))," ")}}function ys(n,o){if(n&1){let e=B();m(0,"div",23),N("click",function(){F(e);let i=k();return j(i.handleCollapsed(!1))}),d()}}var ar=(()=>{class n extends Bt{sidebar=!0;allowShow=!0;isRewards=!1;expandSidebarEvent=new Ie;language;features=[{name:"featured.tra_soat_khieu_nai",icon:"media/icons/doutone/icon-chuc-nang/cn-lich-su-giao-dich.svg",path:"/"+f.SupportComplaintTracking},{name:"featured.huong_dan_su_dung",icon:"media/icons/doutone/icon-chuc-nang/cn-cau-hoi-thuong-gap.svg",path:"",action:()=>this.openModalFeatures("featured.huong_dan_su_dung",this.featureInstructionForUses),externalLink:!1},{name:"featured.thong_tin_ngan_hang",icon:"media/icons/doutone/icon-chuc-nang/cn-bidv-home.svg",path:"",action:()=>this.openModalFeatures("featured.bank_info",this.featureBankInfos),externalLink:!1},{name:"featured.gop_y_dich_vu",icon:"media/icons/doutone/icon-chuc-nang/cn-gui-gop-y.svg",path:"/"+f.SupportFeedback}];featureInstructionForUses=[{icon:"media/icons/doutone/icon-chuc-nang/cn-chuyen-tien-trong-nuoc.svg",name:"huong_dan_giao_dich",url:"https://omni.bidv.com.vn/static/bidv/media/hdsd-bidv-ib-2024/dist/index.html",externalLink:!0},{icon:"media/icons/doutone/icon-chuc-nang/cn-bao-hiem.svg",name:"huong_dan_giao_dich_an_toan",url:"https://bidv.com.vn/bidv/ca-nhan/thong-bao/thanh-toan-va-chuyen-khoan/huongdangiaodichantoan",externalLink:!0},{icon:"media/icons/doutone/icon-chuc-nang/cn-cau-hoi-thuong-gap.svg",name:"faq",url:"https://design.vnpay.vn/web/support/others/BIDV/smartbanking-qa-web/vi.html",urlEn:"https://design.vnpay.vn/web/support/others/BIDV/smartbanking-qa-web/en.html",externalLink:!0}];featureBankInfos=[{icon:"media/icons/doutone/icon-chuc-nang/cn-tim-kiem-atm.svg",name:"search_atm",url:"https://bidv.com.vn/vn/atm-chi-nhanh",externalLink:!0},{icon:"media/icons/doutone/icon-chuc-nang/cn-ty-gia.svg",name:"search_exchange_rate",url:"https://bidv.com.vn/vn/ca-nhan/cong-cu-tien-ich/ty-gia-ngoai-te-gia-vang",externalLink:!0},{icon:"media/icons/doutone/icon-chuc-nang/cn-lai-suat.svg",name:"search_interest_rate",url:"https://bidv.com.vn/vn/ca-nhan/cong-cu-tien-ich/lai-suat",externalLink:!0}];hotline=dt;currentYear=(0,or.default)().year();destroyRef=p(q);modalService=p(V);translateService=p(Z);commonService=p(we);router=p(X);apiService=p(De);UI=p(Re);constructor(){super()}ngOnInit(){this.router.events.pipe(Te(e=>e instanceof Ot)).subscribe(e=>{this.handleCollapsed(!1)}),this.commonService.getLanguage().pipe(re(this.destroyRef)).subscribe(e=>{this.language=e||ue.Vi})}openPopupSupport(){this.modalService.confirm({title:this.translateService.instant("msg.ban_can_ho_tro"),message:this.translateService.instant("msg.tong_dai_cskh",{phone:this.hotline}),btnCancel:{title:"btn.cancel",color:U.Outline},btnConfirm:{title:"btn.call",color:U.Primary},confirm:()=>{window.location.href=`tel:${this.hotline}`},maskClosable:!0})}handelOpenClick(e){e.path?e.externalLink?(this.language===ue.En&&(e.path=e.path.replace("/vn/","/en/")),window.open(e.path,"_blank")):this.router.navigateByUrl(e.path):e.action&&e.action()}openModalFeatures(e,t){this.modalService.open(Vn,{data:{childFeatures:t,title:e}})?.afterClose.pipe(re(this.destroyRef)).subscribe(r=>{if(r)if(r.externalLink){let a=r.url;this.language===ue.En&&(r.urlEn?a=r.urlEn:a=r.url.replace("/vn/","/en/")),window.open(a,"_blank")}else this.router.navigate([r.url])})}handleCollapsed(e){this.expandSidebarEvent.emit(e)}handleLogout(){this.modalService.confirm({title:this.translate.instant("heading.dang_xuat"),message:this.translate.instant("message.xac_nhan_dang_xuat"),btnConfirm:{title:this.translate.instant("btn.accept"),color:U.Primary},confirm:()=>{this.apiService.logout()}})}static \u0275fac=function(t){return new(t||n)};static \u0275cmp=z({type:n,selectors:[["","bidv-omni-sidebar",""]],hostVars:2,hostBindings:function(t,i){t&2&&G("sidebar",i.sidebar)},inputs:{allowShow:"allowShow",isRewards:"isRewards"},outputs:{expandSidebarEvent:"expandSidebarEvent"},standalone:!0,features:[Ne,L],attrs:ds,decls:12,vars:4,consts:[[1,"sidebar-inner"],[1,"sidebar--logo-main-wrap"],[1,"sidebar--top-actions"],[1,"sidebar-content"],["sidebar-account",""],["bidv-omni-sidebar-bpoint",""],[1,"sidebar-content--bot"],[1,"sidebar-content--features"],[1,"sidebar-content--features-item"],[1,"sidebar--copy-right"],[1,"sidebar-mask"],[1,"sidebar--logo-main-inner",3,"routerLink"],[1,"sidebar--logo-main"],["app-button","",3,"click","color","mute","prefixIcon"],[1,"sidebar--top-actions-right-group"],["app-button","","aria-label","logout out",1,"btn-logout",3,"click","iconOnly","size","color","prefixIcon"],[3,"change"],[1,"sidebar-content--features-item",3,"click"],[1,"sidebar-content--features-item--icon"],[3,"src","colorChange"],[1,"sidebar-content--features-item--title"],[1,"sidebar-content--features-item--arrow"],[3,"src"],[1,"sidebar-mask",3,"click"]],template:function(t,i){t&1&&(m(0,"div",0),T(1,fs,3,1,"div",1)(2,gs,7,11,"div",2),m(3,"div",3),y(4,"div",4),T(5,Ss,1,0,"div",5),m(6,"div",6)(7,"div",7),pe(8,vs,8,6,"div",8,he),d(),T(10,Cs,3,6,"p",9),d()()(),T(11,ys,1,0,"div",10)),t&2&&(l(),E(i.allowShow?1:2),l(4),E(i.isRewards?-1:5),l(3),me(i.features),l(2),E(i.allowShow?10:-1),l(),E(i.allowShow?-1:11))},dependencies:[ee,te,fe,Dt,Rt,$t,tt,rr,sr,pn]})}return n})();var cr=Se(Oe());var ks=["class","app-site-layout"],Ts=(n,o)=>({main:!0,"main-rewards":n,"main-scroll":o}),bs=(n,o,e)=>({"header-muted":n,"header-opaque":o,"header-scroll":e}),Is=n=>({currentYear:n});function Ns(n,o){if(n&1&&(m(0,"p",5),M(1),x(2,"translate"),d()),n&2){let e=k();l(),D(" ",We(2,1,"common.copy_right",Ee(4,Is,e.currentYear))," ")}}var lr=(()=>{class n{storeService;router;get hostClass(){let e=["inlogin-layout","screen-size-"+this.screenSize];return this.sidebarIsCollapsed.includes(this.screenSize)?e.push("inlogin-layout--not-sidebar"):e.push("inlogin-layout--sidebar"),this.sidebarVisible&&e.push("inlogin-layout--sidebar-visibled"),this.isHeaderMuted||e.push("inlogin-layout--header-opaque"),e.join(" ")}sidebarIsCollapsed=[et.Xs,et.Sm,et.Md];sidebarVisible=!1;isScroll=!1;beginCheck=20;delta=20;lastScrollTop=0;currentYear=(0,cr.default)().year();checkScroll(){let e=document.documentElement.scrollTop||document.body.scrollTop||0;Math.abs(this.lastScrollTop-e)<=this.delta||(this.isScroll=e>0&&e>=this.beginCheck,this.lastScrollTop=e)}commonService=p(we);homeService=p(Hn);destroyRef=p(q);storageService=p(O);themeService=p(Ae);isHeaderMuted=!0;isHome$=!0;routeBeHiddenBreadCrumb$;routeBeMuteHeader$;routeIsRewards$;screenSize;constructor(e,t){this.storeService=e,this.router=t}ngOnInit(){this.commonService.getCurrentScreenSize().pipe(re(this.destroyRef)).subscribe(t=>{this.screenSize=t}),this.isHome$=this.commonService.routeIsParamaters([`/${f.Home}`]);let e=this.storageService?.userInfo;this.themeService.setTheme(e?.currentTheme===Dn.Vip?Be.Premier:Be.Mass),this.routeBeHiddenBreadCrumb$=this.commonService.routeIsParamaters([`/${f.Home}`,`/${f.Setting}/doi-hinh-nen`]),this.routeIsRewards$=this.commonService.routeIsParamaters([`/${f.Rewards}`]),this.routeBeMuteHeader$=this.commonService.routeIsParamaters([`/${f.Home}`,`/${f.Rewards}`,`/${f.Setting}/doi-hinh-nen`]),this.routeBeMuteHeader$.subscribe(t=>this.isHeaderMuted=t),this.homeService.processInitApp()}handleExpandedSidebar(e){this.sidebarVisible=e}logout(){this.storeService.tokenOTP=null,this.storeService.loginState=null,this.storeService.userInfo=null,this.router.navigate([f.Login])}static \u0275fac=function(t){return new(t||n)(K(O),K(X))};static \u0275cmp=z({type:n,selectors:[["",8,"app-site-layout"]],hostVars:2,hostBindings:function(t,i){t&1&&N("scroll",function(){return i.checkScroll()},!1,Mi),t&2&&Et(i.hostClass)},exportAs:["app-site-layout"],standalone:!0,features:[L],attrs:ks,decls:13,vars:26,consts:[["bidv-omni-sidebar","",3,"expandSidebarEvent","isRewards","allowShow"],[3,"ngClass"],["bidv-omni-header","",3,"expandSidebarEvent","allowShowBreadcrumb","allowShowSidebar","ngClass"],[1,"main-wraper"],[1,"main-inner"],[1,"main-inner--copy-right"]],template:function(t,i){t&1&&(m(0,"sidebar",0),x(1,"async"),N("expandSidebarEvent",function(a){return i.handleExpandedSidebar(a)}),d(),m(2,"main",1),x(3,"async"),m(4,"header",2),x(5,"async"),x(6,"async"),x(7,"async"),N("expandSidebarEvent",function(a){return i.handleExpandedSidebar(a)}),d(),m(8,"div",3)(9,"div",4),y(10,"router-outlet"),T(11,Ns,3,6,"p",5),x(12,"async"),d()()()),t&2&&(C("isRewards",w(1,7,i.routeIsRewards$))("allowShow",!i.sidebarIsCollapsed.includes(i.screenSize)),l(2),C("ngClass",Xe(19,Ts,w(3,9,i.routeIsRewards$),i.isScroll)),l(2),C("allowShowBreadcrumb",!w(5,11,i.routeBeHiddenBreadCrumb$))("allowShowSidebar",!i.sidebarIsCollapsed.includes(i.screenSize))("ngClass",zi(22,bs,w(6,13,i.routeBeMuteHeader$),!w(7,15,i.routeBeMuteHeader$),i.isScroll)),l(7),E(w(12,17,i.isHome$)&&i.sidebarIsCollapsed.includes(i.screenSize)?11:-1))},dependencies:[ee,wt,Li,Qe,te,fe,ar,Bn]})}return n})();var hr=(()=>{class n{router=p(X);location=p(Me);modalService=p(V);apiOverseasRemittance=p(Yn);storageService=p(O);loadingService=p(He);canActivate(e,t){let i=!1;this.router.getCurrentNavigation()?.extras&&this.router.getCurrentNavigation()?.extras.state&&(i=this.router.getCurrentNavigation()?.extras?.state?.navigateFromChildMenu);let r={functionId:ie.chuyen_tien_quoc_te.toString(),user:this.storageService.userInfo.user,source:qi};return this.loadingService.showLoading(),this.apiOverseasRemittance.checkConditionsOverseasRemittance(r).pipe(Q(a=>a&&a?.code===tn.success?(this.storageService.tokenDataCtqt=a?.tokenData,!0):(a&&a?.code==="03"&&this.modalService.error({message:a?.message,confirm:()=>this.router.navigate([f.Home])}),i||this.router.navigate([f.Home]),!1)),be(a=>(this.modalService.error({message:a?.message}),i||this.router.navigate([f.Home]),Ye(!1))))}static \u0275fac=function(t){return new(t||n)};static \u0275prov=b({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var li=[{path:"",component:lr,canActivate:[P],children:[{path:"",loadChildren:()=>import("./chunk-FZYWOICU.js").then(n=>n.homeRoutes),canActivateChild:[P],pathMatch:"full"},{path:f.Account,loadChildren:()=>import("./chunk-JL4BXRO3.js").then(n=>n.accountRoute),canActivateChild:[P],canActivate:[P],data:{preload:!1,label:"tai_khoan",heading:"tai_khoan",phase:1}},{path:f.Transfer,loadChildren:()=>import("./chunk-DJMM5DCV.js").then(n=>n.transferRoute),canActivateChild:[P],data:{preload:!1,heading:"chuyen_tien",phase:1}},{path:f.CharityGift,loadChildren:()=>import("./chunk-JKWUJBDM.js").then(n=>n.charityGiftRoute),canActivateChild:[P],canActivate:[P,Ce],data:{preload:!1,heading:"label.charity",phase:1,serviceId:ie.chuyen_tien_ung_ho}},{path:f.Setting,loadChildren:()=>import("./chunk-DP5C74SH.js").then(n=>n.settingRoutes),canActivateChild:[P],data:{preload:!1,heading:"label.cai_dat",phase:1}},{path:f.Topup,loadComponent:()=>import("./chunk-YS6PY633.js").then(n=>n.TopupComponent),canActivate:[P,Ce],data:{preload:!0,breadcrumb:"nap_tien_dien_thoai",pageTitle:{name:"nap_tien_dien_thoai"},serviceId:ie.nap_tien_dt}},{path:f.PayTax,loadChildren:()=>import("./chunk-ZMUHJFKM.js").then(n=>n.payTaxRoute),canActivate:[P,Ce],data:{breadcrumb:null,preload:!0,serviceId:ie.nop_thue_theo_mst}},{path:f.CardService,loadChildren:()=>import("./chunk-3OUHO2AN.js").then(n=>n.cardRoute),canActivate:[P],data:{preload:!1,label:"D\u1ECBch v\u1EE5 th\u1EBB",heading:"D\u1ECBch v\u1EE5 th\u1EBB",phase:1}},{path:f.Saving,loadChildren:()=>import("./chunk-JZB3DZVH.js").then(n=>n.savingRoute),data:{breadcrumb:null,preload:!0}},{path:f.TransactionReport,loadChildren:()=>import("./chunk-C5YD6KZE.js").then(n=>n.TransactionReportsRoutes),canActivateChild:[P],canActivate:[P,Ce],data:{preload:!1,label:"heading.bao_cao_giao_dich",heading:"heading.bao_cao_giao_dich",phase:1,serviceId:ie.bao_cao_giao_dich}},{path:f.SupportComplaint,loadChildren:()=>import("./chunk-TNR6CTYN.js").then(n=>n.supportComplaintRoutes),canActivateChild:[P],canActivate:[P,Ce],data:{preload:!1,label:"menu.thong_tin_khieu_nai",heading:"menu.thong_tin_khieu_nai",phase:1,serviceId:ie.tra_soat_khieu_nai}},{path:f.PayFeeSeaport,loadComponent:()=>import("./chunk-YSDNMGYE.js").then(n=>n.PayFeeSeaportComponent),canActivate:[P,Ce],data:{preload:!0,breadcrumb:"menu.nop_phi_ha_tang_cang_bien",pageTitle:{name:"menu.nop_phi_ha_tang_cang_bien",nameEn:"menu.nop_phi_ha_tang_cang_bien"},serviceId:ie.tao_lenh_nop_phi_ha_tang_cang_bien}},{path:f.SupportFeedback,loadChildren:()=>import("./chunk-UEUQMUO7.js").then(n=>n.feedbackRoutes),canActivateChild:[P],data:{preload:!0,breadcrumb:"gop_y_dich_vu",pageTitle:{name:"gop_y_dich_vu",nameEn:"gop_y_dich_vu"}}},{path:f.SupportComplaintTracking,loadChildren:()=>import("./chunk-SVL76LIF.js").then(n=>n.complaintTrackingRoutes),canActivateChild:[P],data:{preload:!0,breadcrumb:"menu.tra_soat",pageTitle:{name:"menu.tra_soat",nameEn:"menu.tra_soat"}}},{path:f.InternationalTransfer,canActivate:[Ce,hr],loadChildren:()=>import("./chunk-7CYPAQSZ.js").then(n=>n.internationalTransferMenuRoute),canActivateChild:[P],data:{preload:!0,breadcrumb:null,serviceId:ie.chuyen_tien_quoc_te}},{path:f.Payment,loadChildren:()=>import("./chunk-LI2ZCYAU.js").then(n=>n.paymentRoutes),canActivateChild:[P],data:{preload:!0,breadcrumb:null,serviceId:ie.danh_sach_thanh_toan_hoa_don_nhom_thanh_toan_hoa_don}},{path:f.RegistrationFeePayment,loadChildren:()=>import("./chunk-VJVQJHWR.js").then(n=>n.registrationFeePaymentRoutes),canActivateChild:[P],canActivate:[Ce],data:{preload:!0,breadcrumb:null,serviceId:ie.nop_phi_truoc_ba}},{path:f.InsuranceInvestment,loadChildren:()=>import("./chunk-BFS2IZDP.js").then(n=>n.insuranceInvestmentRoute),data:{breadcrumb:null,preload:!0}},{path:f.RetrieveInformation,loadChildren:()=>import("./chunk-YV47T4CK.js").then(n=>n.retrieveInformationRoute),canActivateChild:[P],data:{preload:!1,breadcrumb:"featured.tra_cuu_thong_tin",pageTitle:{name:"featured.tra_cuu_thong_tin"}}},{path:f.StoreManagement,loadChildren:()=>import("./chunk-SH4VSNWO.js").then(n=>n.storeRoutes),canActivateChild:[P],data:{preload:!0,breadcrumb:null}},{path:f.Rewards,loadChildren:()=>import("./chunk-QAYHPFCD.js").then(n=>n.rewardsRoute),data:{breadcrumb:null,preload:!0}}]},{path:"",component:qn,canActivate:[Tn],children:[{path:f.Login,loadChildren:()=>import("./chunk-QIJQJHMM.js").then(n=>n.authenticationRoutes)}]},{path:"**",redirectTo:f.Login,pathMatch:"full"}];var hi=Se(Oe());var pr=[{prefix:"/assets/i18n/app/",suffix:".json"},{prefix:"/assets/i18n/app/breadcrumb/",suffix:".json"},{prefix:"/assets/i18n/app/modal/",suffix:".json"},{prefix:"assets/i18n/cai-dat/email/",suffix:".json"},{prefix:"assets/i18n/quan-ly-danh-ba/",suffix:".json"},{prefix:"assets/i18n/thanh-toan/",suffix:".json"},{prefix:"assets/i18n/rewards/",suffix:".json"},{prefix:"assets/i18n/chi-tiet-tai-khoan/",suffix:".json"},{prefix:"assets/i18n/chuyen-tien-quoc-te/",suffix:".json"},{prefix:"assets/i18n/bao-cao-giao-dich/",suffix:".json"},{prefix:"assets/i18n/dich-vu-ngan-hang-so/tra-soat/",suffix:".json"},{prefix:"assets/i18n/nop-thue/",suffix:".json"},{prefix:"assets/i18n/tich-luy-online/",suffix:".json"},{prefix:"assets/i18n/thanh-toan-dinh-ky/",suffix:".json"},{prefix:"assets/i18n/chuyen-tien-quoc-te/",suffix:".json"},{prefix:"assets/i18n/chuyen-tien-dinh-ky/",suffix:".json"},{prefix:"assets/i18n/thay-doi-han-muc/",suffix:".json"},{prefix:"assets/i18n/quan-ly-dich-vu/",suffix:".json"},{prefix:"assets/i18n/common/",suffix:".json"},{prefix:"assets/i18n/quan-ly-mau-thanh-toan/",suffix:".json"},{prefix:"assets/i18n/lich-su-diem/",suffix:".json"},{prefix:"assets/i18n/quan-ly-cua-hang/",suffix:".json"},{prefix:"assets/i18n/quan-ly-cua-hang/",suffix:".json"},{prefix:"assets/i18n/tach-giao-dich-chuyen-tien-247/",suffix:".json"},{prefix:"assets/i18n/chuyen-tien-v2/",suffix:".json"}];var Ut=class{http;resources;processApi;httpBackend=p(Ui);resourceStore=pr;constructor(o,e,t){this.http=o,this.resources=e,this.processApi=t,this.source=this.resources}get host(){let{location:o}=window;return o.protocol+"//"+o.host}get baseUrl(){let o=document.querySelector("base");return this.host+o.getAttribute("href")?.replace(this.host,"")||""}get source(){return this.resourceStore.map(o=>(o.prefix.includes(this.baseUrl)||(o.prefix=this.baseUrl+o.prefix.replace(/\.?\/assets\/i18n/,"assets/i18n")),o))}set source(o){o&&o.forEach(e=>{this.resourceStore.find(i=>i.prefix===e.prefix)||this.resourceStore.push(e)})}cacheLang(o){return je(this,null,function*(){try{let i=A.name==="common"?30:1800,r=yield this.manageCacheKey(i),a=yield caches.open(r),u=yield a.match(o);return!u||!u.ok?(yield a.add(o),ot(this.http.get(o))):yield u.json()}catch{return ot(this.http.get(o))}})}manageCacheKey(o){return je(this,null,function*(){let e=new RegExp(ae.LANGUAGE_CACHE_STORAGE_PREFIX,"gi"),t=yield caches.keys(),i;for(let r of t)if(r.search(e)>=0){let[,a]=r.split(ae.LANGUAGE_CACHE_STORAGE_PREFIX);if((0,hi.default)().diff((0,hi.default)(parseInt(a,10)),"seconds")<=o){i=r;break}}for(let r of t)r!==i&&caches.delete(r);return i||`${ae.LANGUAGE_CACHE_STORAGE_PREFIX}${Date.now()}`})}isObject(o){return o&&typeof o=="object"&&!Array.isArray(o)}mergeDeep(o,...e){if(!e.length)return o;let t=e.shift();if(this.isObject(o)&&this.isObject(t))for(let i in t)this.isObject(t[i])?(o[i]||Object.assign(o,{[i]:{}}),this.mergeDeep(o[i],t[i])):Object.assign(o,{[i]:t[i]});return this.mergeDeep(o,...e)}getTranslation(o){return Ci(this.source.map(e=>je(this,null,function*(){let t=`${e.prefix}${o}${e.suffix}?v=${an}`;return yield this.cacheLang(t)}))).pipe(Q(e=>{let t=e.reduce((i,r)=>this.mergeDeep(i,r));return this.processApi?(this.processApi.langApp[o]=this.mergeDeep(this.processApi.langApp[o]||{},t),this.processApi.langApp[o]):t}))}};var pi=(()=>{class n{translate;storageService;constructor(e,t){this.translate=e,this.storageService=t}resolve(e,t){return this.translate.getTranslation(this.storageService.language).pipe(Ze(1),vi(r=>r?Ye(r):st))}static \u0275fac=function(t){return new(t||n)(_(Z),_(O))};static \u0275prov=b({token:n,factory:n.\u0275fac})}return n})();var mr=(()=>{class n{httpCancelService;constructor(e){this.httpCancelService=e}intercept(e,t){return e.url.search(/\/w1|w2\/(auth|process)$/)<0?t.handle(e):t.handle(e).pipe(at(this.httpCancelService.onCancelPendingRequests()))}static \u0275fac=function(t){return new(t||n)(_(Xn))};static \u0275prov=b({token:n,factory:n.\u0275fac})}return n})();var dr=(()=>{class n{authService;storageService;crypto;constructor(e,t,i){this.authService=e,this.storageService=t,this.crypto=i}intercept(e,t){if(e.url.search(new RegExp(A.uploadUrl,"gi"))<0&&e.url.search(new RegExp(A.uploadAvatarUrl,"gi"))<0)return t.handle(e);if(this.storageService.isAuthenticated){let{file:i,clearData:r}=e.body,a=this.crypto.encryptUpload(r);if(e=e.clone({setHeaders:{Authorization:"Basic "+A.uploadAuthToken}}),i){let u=new FormData;u.append("file",i),u.append("data",a.data_form),u.append("timestamp",a.timestamp.toString()),u.append("mac",a.mac_form),e=e.clone({body:u})}else e=e.clone({body:null,url:`${e.url}?data=${a.data}&timestamp=${a.timestamp.toString()}&mac=${a.mac}`})}return t.handle(e).pipe(Ge(qe),Q(i=>{if(i instanceof de)try{i&&i.body&&Object.hasOwn(i.body,"m")&&Object.hasOwn(i.body,"e")&&(i=i.clone({body:JSON.parse(this.crypto.decryptUpload(i.body))}))}catch(r){Y(r)}return i}),Q(i=>{if(i instanceof de)if(i.body.code&&i.body.code==="00")console.log(i.body);else throw console.log(i.body),i.body;return i}),be(i=>Y({error:i,message:i.desc})))}static \u0275fac=function(t){return new(t||n)(_(nt),_(O),_(oe))};static \u0275prov=b({token:n,factory:n.\u0275fac})}return n})();var ur=(()=>{class n{authService;storageService;crypto;constructor(e,t,i){this.authService=e,this.storageService=t,this.crypto=i}intercept(e,t){if(e.url.search(new RegExp(A.uploadCTQTUrl,"gi"))<0)return t.handle(e);if(this.storageService.isAuthenticated){let{file:i,clearData:r}=e.body;console.log("clearData: data before send,file",r,i);let a=this.crypto.encryptUpload(r);if(e=e.clone({setHeaders:{Authorization:"Basic "+A.uploadCTQTAuthToken}}),i){let u=new FormData;u.append("files",i),u.append("data",a.data_form),u.append("timestamp",a.timestamp.toString()),u.append("mac",a.mac_form),e=e.clone({body:u})}else e=e.clone({body:null,url:`${e.url}?data=${a.data}&timestamp=${a.timestamp.toString()}&mac=${a.mac}`})}return t.handle(e).pipe(Ge(qe),Q(i=>{if(i instanceof de)try{i&&i.body&&Object.hasOwn(i.body,"m")&&Object.hasOwn(i.body,"e")&&(console.log("this.crypto.decryptUpload",this.crypto.decryptUpload(i.body)),i=i.clone({body:JSON.parse(this.crypto.decryptUpload(i.body))}))}catch(r){Y(r)}return i}),Q(i=>{if(i instanceof de)if(i.body.code&&i.body.code==="00")console.log(i.body);else throw console.log(i.body),i.body;return i}),be(i=>Y({error:i,message:i.desc})))}static \u0275fac=function(t){return new(t||n)(_(nt),_(O),_(oe))};static \u0275prov=b({token:n,factory:n.\u0275fac})}return n})();var fr=Se(Oe());var _r=(()=>{class n{helperService=p(it);makeRequestId(e=6){let t=this.getNow(),i=this.helperService.randomNumberString(e);return`${t}${i}`}getNow(e="HHmmssSSS"){return(0,fr.default)().format(e)}static \u0275fac=function(t){return new(t||n)};static \u0275prov=b({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var gr=(()=>{class n{storage;crypto;requestService;constructor(e,t,i){this.storage=e,this.crypto=t,this.requestService=i}intercept(e,t){if(e.url.search(/\/w1|w2\/(auth|process$|process\/exportExcel)/)<0)return t.handle(e);e.url.search(/\/w1|w2\/(auth|process$)/)>-1&&(e=this.getUAParser(e)),this.crypto.isActive||console.log("%cDo not have keys cryptographic","color: red;"),console.log(`%cRequest in mid ${e&&e.body&&e.body.mid}`,"font-weight: bold;"),e=e.clone({headers:e.headers.set("X-Request-ID",this.requestService.makeRequestId())});let{language:i,accessKey:r}=this.storage;return e.headers.has("Content-Type")||(e=e.clone({headers:e.headers.set("Content-Type","application/json")})),e=e.clone({headers:e.headers.set("Accept","application/json")}),i&&(e=e.clone({headers:e.headers.set("Accept-Language",i),body:ye($({},e.body),{lang:i})})),r&&(e=e.clone({headers:e.headers.set("Authorization",r)})),console.log("%c-> data in request ","color:blue; font-weight: bold;"),console.log(e.body),Object.hasOwn(e.body,"mid")&&(e=e.clone({body:{data:this.crypto.encrypt(e.body),mid:e.body.mid,isCache:e.body.isCache,maxRequestInCache:e.body.maxRequestInCache}})),e=e.clone({withCredentials:!0}),t.handle(e)}getUAParser(e){let t={DT:"",PM:"",OV:"",appVersion:"",E:"",clientId:""};try{let i=new An().getResult(),{device:r,os:a,browser:u}=i;r.type?(t.DT=`${a?.name?.toUpperCase()}_WEB`,t.PM=`${r.vendor}_${i.device.model&&i.device.model.toUpperCase()||void 0}_${u.name}`):(t.DT=a?.name?.toString().toUpperCase()||"",t.PM=u.name||""),t.OV=u.version||"";let s=localStorage.getItem(ae.BROWER_ID),c=localStorage.getItem(ae.CLIENT_ID);t.appVersion=this.storage.appVersion,t.E=s?s.replace(A.trustBrower.browerDefaultString,""):"",t.clientId=c?c.replace(A.trustBrower.clientIdDefaultString,""):""}catch(i){console.log(i)}return e.clone({body:$($({},e.body),t)})}static \u0275fac=function(t){return new(t||n)(_(O),_(oe),_(_r))};static \u0275prov=b({token:n,factory:n.\u0275fac})}return n})();var Sr=(()=>{class n{translate;storage;crypto;loading;modalService;apiService;processApi;router;listAllowMidUnAuth=[h.XAC_THUC_CMND,h.XAC_THUC_OTP,h.QUEN_MAT_KHAU,h.KICH_HOAT_LAN_DAU,h.GUI_LAI_OTP];listSaveUserInfo=[h.DANG_NHAP,h.KICH_HOAT_LAN_DAU,h.CROSS_LOGIN];CacheAppStore=new Map;constructor(e,t,i,r,a,u,s,c){this.translate=e,this.storage=t,this.crypto=i,this.loading=r,this.modalService=a,this.apiService=u,this.processApi=s,this.router=c}intercept(e,t){return e.url.search(/\/w1|w2\/(process\/exportExcel)/)<0?t.handle(e):(e=e.clone({body:e.body.data,responseType:"arraybuffer"}),t.handle(e).pipe(Ti(i=>console.log(i))))}static \u0275fac=function(t){return new(t||n)(_(Z),_(O),_(oe),_(He),_(V),_(De),_(ve),_(X))};static \u0275prov=b({token:n,factory:n.\u0275fac})}return n})();var Cr=Se(Oe());var mi=class{imageAddr="./assets/media/brands/logo-smartbanking.svg";downloadSize=13146;startTime=0;endTime=0;maxMbps;callbackIncrease;callbackReduce;speedBps;speedKbps;speedMbps;speed;status;constructor(){}run(o,e,t){this.maxMbps=parseFloat(""+o)?parseFloat(""+o):0,this.callbackIncrease=e,this.callbackReduce=t,this.InitiateSpeedDetection()}speedTest(o){this.run(.9,()=>{o({status:this.status,speed:this.speed})},()=>{o({status:this.status,speed:this.speed})})}InitiateSpeedDetection(){window.setTimeout(this.MeasureConnectionSpeed.bind(this),1)}MeasureConnectionSpeed(){let o=new Image;o.onload=()=>{this.status=null,this.endTime=new Date().getTime(),this.result()},o.onerror=()=>{this.status="offline",this.result()},this.startTime=new Date().getTime();let e="?at="+this.startTime;o.src=this.imageAddr+e}result(){let{endTime:o,startTime:e}=this,t=(o-e)/1e3,i=this.downloadSize*8;this.speedBps=(i/t).toFixed(2),this.speedKbps=(parseFloat(this.speedBps)/1024).toFixed(2),this.speedMbps=(parseFloat(this.speedKbps)/1024).toFixed(2),this.speed=parseFloat(this.speedMbps),this.status==="offline"&&(this.speed=0),this.speed>=(this.maxMbps?this.maxMbps:1)?(this.status=this.status?this.status:"fast",this.callbackIncrease&&this.callbackIncrease(this.speedMbps)):(this.status=this.status?this.status:"slow",this.callbackReduce&&this.callbackReduce(this.speedMbps))}},Vt=(()=>{class n{net=new mi;get netEvent(){return this.events.asObservable()}events=new Si({id:null,type:"",value:""});constructor(){this.detectNetwork(this.online,this.offline)}detectNetwork(e,t){window.addEventListener?(window.addEventListener("online",e.bind(this),!1),window.addEventListener("offline",t.bind(this),!1)):(window.ononline=e,window.onoffline=t)}online(){return console.log("online"),this.events.next({id:"app_network_online_message",type:"network_online",value:!0}),this}offline(){return console.log("offline"),this.events.next({id:"app_network_offline_message",type:"network_offline",value:!0}),this}static \u0275fac=function(t){return new(t||n)};static \u0275prov=b({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var vr="88",yr=(()=>{class n{translate;storage;crypto;loading;modalService;apiService;processApi;router;helperService;networkService;UI;listAllowMidUnAuth=[h.XAC_THUC_CMND,h.XAC_THUC_OTP,h.QUEN_MAT_KHAU,h.KICH_HOAT_LAN_DAU,h.GUI_LAI_OTP];listSaveUserInfo=[h.DANG_NHAP,h.KICH_HOAT_LAN_DAU,h.CROSS_LOGIN,h.XAC_THUC_CMND,h.XAC_THUC_CHUYEN_DOI_OTP,h.KICH_HOAT_LAI_TREN_THIET_BI_KHAC,h.TRUST_TRINH_DUYET_IB];MID_IGRONE=[h.DANH_SACH_THE,h.PHAT_HANH_THE.DANH_SACH_THE_PHAT_HANH];MID_SKIP_ERROR_TRANS_TOKEN_01=[h.PHAT_HANH_THE.XAC_THUC_LAY_THONG_TIN_CHI_TIET_THE];stepService=p(Gn);themeService=p(Ae);authService=p(nt);location=p(Me);nzModalService=p(yn);constructor(e,t,i,r,a,u,s,c,v,g,W){this.translate=e,this.storage=t,this.crypto=i,this.loading=r,this.modalService=a,this.apiService=u,this.processApi=s,this.router=c,this.helperService=v,this.networkService=g,this.UI=W}intercept(e,t){if(!navigator.onLine)return this.networkService.offline(),Y(null);if(e.url.search(/\/w1|w2\/(auth|process)$/)<0)return t.handle(e);if(Object.hasOwn(e.body,"mid")){let i,r=e.body.mid.toString(),a=e.body.isCache,u=e.body.maxRequestInCache||Ji;if(console.log(`%cResponse in mid ${e&&e.body&&e.body.mid}`,"font-weight: bold;"),e=e.clone({body:e.body.data}),a===!0)if(this.apiService.CacheAppStore.has(r)){let s=this.apiService.CacheAppStore.get(r);s&&s.requestCount<u?(i=s.event,this.apiService.CacheAppStore.set(r,{event:i,requestCount:s.requestCount+1})):i=this.handleCache(e,t,r)}else i=this.handleCache(e,t,r);else i=t.handle(e);return this.processApi.processList.set(r.toString(),{status:"start",isBusy:!0}),i.pipe(Ge(qe),Q(s=>{if(s instanceof de)try{s&&s.body&&Object.hasOwn(s.body,"k")&&Object.hasOwn(s.body,"d")&&(s=s.clone({body:JSON.parse(this.crypto.decrypt(s.body))}))}catch(c){Y(c)}return s}),Q(s=>{if(s instanceof de)if(s?.body?.code&&s.body.code==="00"){if(s?.body?.isForeigner==="Y"&&s?.body?.messages?.filter(c=>["CHECK-FOREIGNER-00"].includes(c?.code))[0]?.des&&this.modalService.notice({type:this.UI.ModalType.Info,title:this.translate.instant("notice"),message:s?.body?.messages?.filter(c=>["CHECK-FOREIGNER-00"].includes(c.code))[0]?.des,btnConfirm:{title:this.translate.instant("btn.accept"),color:U.Primary},btnCancel:{title:this.translate.instant("btn.cancel"),color:U.Outline},confirm:()=>{},cancel:()=>{this.processApi.unLockRedirect(),this.router.navigate(["/"])}}),console.log(`%c-> success in mid ${r} `,"color:green; font-weight: bold;"),console.log(s.body),this.handleRequesHaveSoftOTP(r),s.body.mid&&s.body.mid.toString()!==r.toString()&&(this.processApi.processList.set(r.toString(),{status:"end",isBusy:!1}),r=s.body.mid),a!==!0&&this.handleEventDone(s),s.body.mid===h.LAY_DANH_SACH_TAI_KHOAN&&(this.storage.ruleAccount=s.body.accList),s&&s.body&&s.body.mid===h.LAY_CHI_TIET_TAI_KHOAN){let c=s.body.accNo,v=this.storage.ruleAccount&&this.storage.ruleAccount.find(g=>g.accNo===c);s.body.rule=v&&v.rule||[]}}else throw console.log(s.body),s.body;return s}),be(s=>{console.log(`%c-> error in mid ${r} `,"color:red; font-weight: bold;");try{let c=s?.code,v=s?.codeFull;if(s?.code&&s?.des)return s.code!==vr&&this.handleRequesHaveSoftOTP(r),v===ut.TRANS_TOKEN_01&&!this.MID_SKIP_ERROR_TRANS_TOKEN_01.includes(Number(r))?(this.stepService.setErrorConfirmStep(s),this.modalService.error({message:s.des,confirm:()=>{let g=this.stepService.getStepValue();g===1?this.location.back():(g===ie.dich_vu_the&&this.nzModalService.closeAll(),this.stepService.resetStep())},maskClosable:!1}),st):(([Ft.CLEAR_INPUT,Ft.OVER_WRONG_TIMES].includes(v)||[ft.WRONG_OTP,ft.EXPIRED_OTP,ut.ERROR_TIMEOUT_TRANSFER].includes(c))&&this.stepService.setErrorConfirmStep(s),Y(()=>({error:s,message:s?.des})));if(s instanceof Ki){let g,W=g&&g?.des||s.error&&s.error.message||s&&s.statusText;if(s&&s.error&&Object.hasOwn(s.error,"k")&&Object.hasOwn(s.error,"d")&&(g=JSON.parse(this.crypto.decrypt(s.error))),g?.requestId&&this.handleTokenOTP(g.requestId),console.log({error:g,event:s}),g?.codeFull===Ft.OVER_WRONG_TIMES&&this.stepService.setErrorConfirmStep(g),g?.mid?.toString()==h.TRUST_TRINH_DUYET_IB.toString()){if([ft.WRONG_OTP,ft.EXPIRED_OTP].includes(g?.code))return Y(()=>({error:g,message:g?.des}));if(g?.code===ut.EXPIRED_SESSION)return this.modalService.toggleShowOneAtTime(!0),this.modalService.error({message:g?.des,confirm:()=>{this.router.navigate([f.Login])},maskClosable:!1}),Y(()=>null)}if(s.status.toString().search(/(^[3|5]\d+$|404)/)>=0)throw this.handleRequesHaveSoftOTP(r),g;if(e.url.search(/\/w1|w2\/process/)<0&&g&&g.code===ut.EXPIRED_SESSION&&r.toString()!==h.DANG_XUAT.toString())return this.apiService.expiredSession(g),Y(()=>null);if(s.status===401||s.status===403)return e.url.search(/\/w1|w2\/process/)>=0&&this.storage.isAuthenticated&&(!g&&r.toString()!==h.DANG_XUAT.toString()||g&&g.mid.toString()!==h.DANG_XUAT.toString())?(this.apiService.expiredSession(g),Y(()=>null)):r.toString()===h.DANG_XUAT.toString()?(this.storage.cleanSession(),this.apiService.clearCacheData(),this.themeService.setTheme(Be.Mass),this.router.navigate([f.Login]),Y(()=>null)):(r.toString()!==h.DANG_NHAP.toString()&&this.apiService.expiredSession(g),Y(()=>({error:g,message:W})));throw g&&g.code!==vr&&this.handleRequesHaveSoftOTP(r),g}throw s}catch(c){let v=this.handleResponseTimeout(r,c);return Y(()=>v)}}),ki(()=>{console.groupEnd();let s=!1,c=this.processApi.processList.get(r.toString());c?(c.isBusy=!1,c.status="end",this.processApi.processList.set(r.toString(),c)):this.processApi.processList.set(r.toString(),{status:"end",isBusy:!1}),this.processApi.processList.forEach((v,g)=>{v.status==="start"&&(s=!0)}),!s&&!this.MID_IGRONE.includes(Number(r))&&this.loading.hideLoading()}))}return st}handleCache(e,t,i){return t.handle(e).pipe(Q(r=>(r instanceof de&&this.apiService.CacheAppStore.set(i,{event:Ye(r.clone()),requestCount:0}),r)))}handleTokenOTP(e){this.storage.tokenOTP=e}handleEventDone({headers:e,body:t}){if(e&&e.has("authorization")){try{let i=e.get("authorization");this.storage.accessKey=i;let[,r]=i.replace(/^Bearer\s{1,}/gi,"").split(".");r=JSON.parse(atob(r)),r&&r.se&&(r.sessionExpire=r.se,this.handleExpireTime(r))}catch(i){console.log(i)}if(this.listSaveUserInfo.includes(Number(t.mid)))try{t.mid===h.DANG_NHAP&&this.authService.setLoginResponse(t);let i=$({},t);["code","des","mid"].forEach(a=>i[a]&&delete i[a]),i.requestId&&this.handleTokenOTP(i.requestId),this.storage.userInfo=i;let r=i&&i?.favoriteFunction&&i?.favoriteFunction?.split(",")?.slice(0,3);this.helperService.initMenuFavorites(r),this.sendMessageLogin(),this.storage.userInfo&&this.storage.userInfo.sessionExpire&&this.handleExpireTime(this.storage.userInfo)}catch(i){console.log(i)}}e&&e.has("Accept-Language")&&(this.storage.language=e.get("Accept-Language")),t&&t.sessionExpire&&this.handleExpireTime(t),t&&t?.mid!==h.TRUST_TRINH_DUYET_IB&&t.showDes===!0&&this.helperService.handleMessage(t)}handleExpireTime(e){let t=parseInt(e.sessionExpire,10);this.storage.sessionTimeout=(0,Cr.default)().add(t,"m").toDate().getTime()}sendMessageLogin(){let e=document.querySelector("[data-tabid]");e&&e.getAttribute("data-tabid")&&(this.storage.appEvent=this.crypto.encryptText(JSON.stringify({userInfo:this.storage.userInfo,accessKey:this.storage.accessKey,sessionTimeout:this.storage.sessionTimeout})))}handleRequesHaveSoftOTP(e){Number(e)===h.XAC_NHAN_SMART_OTP&&this.processApi.unLockRedirect()}handleResponseTimeout(e,t){let i=[h.KHOI_TAO_NAP_TIEN_DIEN_THOAI,h.KHOI_TAO_XOA_CHUYEN_TIEN_DINH_KY,h.KHOI_TAO_CHUYEN_TIEN_NGOAI_STK,h.KHOI_TAO_BAN_NGOAI_TE,h.KHOI_TAO_TAT_TOAN_TIET_KIEM,h.KHOI_TAO_THANH_TOAN_DINH_KY,h.KHOI_TAO_CHUYEN_TIEN_DINH_KY,h.KHOI_TAO_THANH_TOAN_HOA_DON,h.KHOI_TAO_MO_TIET_KIEM,h.KHOI_TAO_CHUYEN_TIEN_NOI_BO,h.KHOI_TAO_CHUYEN_TIEN_NGOAI_STK_247,h.KHOI_TAO_CHUYEN_TIEN_NGOAI_SO_THE,h.KHOI_TAO_CHUYEN_TIEN_TU_THIEN,h.NAP_TIEN_Y_TE.KHOI_TAO,h.MO_KHOA_THE.KHOI_TAO,h.MO_KHOA_THE.KHOI_TAO,h.THAY_DOI_TAI_KHOAN_LIEN_KET.KHOI_TAO,h.MO_KHOA_THANH_TOAN_TRUC_TUYEN.KHOI_TAO,h.KICH_HOAT_THE.KHOI_TAO,h.KICH_HOAT_LAI_PIN.KHOI_TAO,h.THANH_TOAN_THE_TIN_DUNG.KHOI_TAO],r=[h.XAC_NHAN_NAP_TIEN_DIEN_THOAI,h.XAC_NHAN_CHUYEN_TIEN_NGOAI_STK,h.XAC_NHAN_BAN_NGOAI_TE,h.XAC_NHAN_TAT_TOAN_TIET_KIEM,h.XAC_NHAN_THANH_TOAN_DINH_KY,h.XAC_NHAN_CHUYEN_TIEN_DINH_KY,h.XAC_NHAN_THANH_TOAN_HOA_DON,h.XAC_NHAN_XOA_CHUYEN_TIEN_DINH_KY,h.XAC_NHAN_MO_TIET_KIEM,h.XAC_NHAN_CHUYEN_TIEN_NOI_BO,h.XAC_NHAN_CHUYEN_TIEN_NGOAI_STK_247,h.XAC_NHAN_CHUYEN_TIEN_NGOAI_SO_THE,h.LAY_DANH_SACH_CHUYEN_TIEN_TU_THIEN,h.XAC_NHAN_CHUYEN_TIEN_TU_THIEN],a=[h.NAP_TIEN_Y_TE.XAC_NHAN,h.XAC_NHAN_SMART_OTP,h.MO_KHOA_THE.XAC_NHAN,h.THAY_DOI_TAI_KHOAN_LIEN_KET.XAC_NHAN,h.MO_KHOA_THANH_TOAN_TRUC_TUYEN.XAC_NHAN,h.KICH_HOAT_THE.XAC_NHAN,h.KICH_HOAT_LAI_PIN.XAC_NHAN,h.THANH_TOAN_THE_TIN_DUNG.XAC_NHAN],u=[h.LAY_DANH_SACH_TAI_KHOAN_DINH_DANH],s=[h.LAY_DANH_SACH_TAI_KHOAN],c;return i.includes(e)?c="errors.request_time_out_khoi_tao":r.includes(e)?c="errors.request_time_out_xac_nhan_tai_chinh":a.includes(e)?c="errors.request_time_out_xac_nhan_phi_tai_chinh":u.includes(Number(e))?c="errors.khong_truy_van_ds_tk_chon_ten_nhu_y":s.includes(Number(e))?c="timeout_tai_khoan":c="errors.request_time_out",{error:{code:"time_out",des:c},message:c,code:"time_out",trace:t,mid:e}}static \u0275fac=function(t){return new(t||n)(_(Z),_(O),_(oe),_(He),_(V),_(De),_(ve),_(X),_(it),_(Vt),_(Re))};static \u0275prov=b({token:n,factory:n.\u0275fac})}return n})();var kr=(()=>{class n{handleError(e){/net\:\:ERR_CONNECTION_TIMED_OUT/.test(e?.message)&&window.location.reload(),e&&(console.log(`%c${e?.message}`,"background-color: #f62929; color: white; padding: 2px"),console.log(e))}static \u0275fac=function(t){return new(t||n)};static \u0275prov=b({token:n,factory:n.\u0275fac})}return n})();var di=new Ct("ngx-mask config"),Tr=new Ct("new ngx-mask config"),br=new Ct("initial ngx-mask config"),Os={suffix:"",prefix:"",thousandSeparator:" ",decimalMarker:[".",","],clearIfNotMatch:!1,showTemplate:!1,showMaskTyped:!1,placeHolderCharacter:"_",dropSpecialCharacters:!0,hiddenInput:void 0,shownMaskExpression:"",separatorLimit:"",allowNegativeNumbers:!1,validation:!0,specialCharacters:["-","/","(",")",".",":"," ","+",",","@","[","]",'"',"'"],leadZeroDateTime:!1,apm:!1,leadZero:!1,keepCharacterPositions:!1,triggerOnMaskChange:!1,inputTransformFn:n=>n,outputTransformFn:n=>n,maskFilled:new Ie,patterns:{0:{pattern:new RegExp("\\d")},9:{pattern:new RegExp("\\d"),optional:!0},X:{pattern:new RegExp("\\d"),symbol:"*"},A:{pattern:new RegExp("[a-zA-Z0-9]")},S:{pattern:new RegExp("[a-zA-Z]")},U:{pattern:new RegExp("[A-Z]")},L:{pattern:new RegExp("[a-z]")},d:{pattern:new RegExp("\\d")},m:{pattern:new RegExp("\\d")},M:{pattern:new RegExp("\\d")},H:{pattern:new RegExp("\\d")},h:{pattern:new RegExp("\\d")},s:{pattern:new RegExp("\\d")}}};var Rs=(()=>{class n{constructor(){this._config=p(di),this.dropSpecialCharacters=this._config.dropSpecialCharacters,this.hiddenInput=this._config.hiddenInput,this.clearIfNotMatch=this._config.clearIfNotMatch,this.specialCharacters=this._config.specialCharacters,this.patterns=this._config.patterns,this.prefix=this._config.prefix,this.suffix=this._config.suffix,this.thousandSeparator=this._config.thousandSeparator,this.decimalMarker=this._config.decimalMarker,this.showMaskTyped=this._config.showMaskTyped,this.placeHolderCharacter=this._config.placeHolderCharacter,this.validation=this._config.validation,this.separatorLimit=this._config.separatorLimit,this.allowNegativeNumbers=this._config.allowNegativeNumbers,this.leadZeroDateTime=this._config.leadZeroDateTime,this.leadZero=this._config.leadZero,this.apm=this._config.apm,this.inputTransformFn=this._config.inputTransformFn,this.outputTransformFn=this._config.outputTransformFn,this.keepCharacterPositions=this._config.keepCharacterPositions,this._shift=new Set,this.plusOnePosition=!1,this.maskExpression="",this.actualValue="",this.showKeepCharacterExp="",this.shownMaskExpression="",this.deletedSpecialCharacter=!1,this._formatWithSeparators=(e,t,i,r)=>{let a=[],u="";if(Array.isArray(i)){let W=new RegExp(i.map(R=>"[\\^$.|?*+()".indexOf(R)>=0?`\\${R}`:R).join("|"));a=e.split(W),u=e.match(W)?.[0]??""}else a=e.split(i),u=i;let s=a.length>1?`${u}${a[1]}`:"",c=a[0]??"",v=this.separatorLimit.replace(/\s/g,"");v&&+v&&(c[0]==="-"?c=`-${c.slice(1,c.length).slice(0,v.length)}`:c=c.slice(0,v.length));let g=/(\d+)(\d{3})/;for(;t&&g.test(c);)c=c.replace(g,"$1"+t+"$2");return r===void 0?c+s:r===0?c:c+s.substring(0,r+1)},this.percentage=e=>{let t=e.replace(",","."),i=Number(this.allowNegativeNumbers&&e.includes("-")?t.slice(1,e.length):t);return!isNaN(i)&&i>=0&&i<=100},this.getPrecision=e=>{let t=e.split(".");return t.length>1?Number(t[t.length-1]):1/0},this.checkAndRemoveSuffix=e=>{for(let t=this.suffix?.length-1;t>=0;t--){let i=this.suffix.substring(t,this.suffix?.length);if(e.includes(i)&&t!==this.suffix?.length-1&&(t-1<0||!e.includes(this.suffix.substring(t-1,this.suffix?.length))))return e.replace(i,"")}return e},this.checkInputPrecision=(e,t,i)=>{if(t<1/0){if(Array.isArray(i)){let s=i.find(c=>c!==this.thousandSeparator);i=s||i[0]}let r=new RegExp(this._charToRegExpExpression(i)+`\\d{${t}}.*$`),a=e.match(r),u=(a&&a[0]?.length)??0;if(u-1>t){let s=u-1-t;e=e.substring(0,e.length-s)}t===0&&this._compareOrIncludes(e[e.length-1],i,this.thousandSeparator)&&(e=e.substring(0,e.length-1))}return e}}applyMaskWithPattern(e,t){let[i,r]=t;return this.customPattern=r,this.applyMask(e,i)}applyMask(e,t,i=0,r=!1,a=!1,u=()=>{}){if(!t||typeof e!="string")return"";let s=0,c="",v=!1,g=!1,W=1,R=!1;e.slice(0,this.prefix.length)===this.prefix&&(e=e.slice(this.prefix.length,e.length)),this.suffix&&e?.length>0&&(e=this.checkAndRemoveSuffix(e)),e==="("&&this.prefix&&(e="");let se=e.toString().split("");if(this.allowNegativeNumbers&&e.slice(s,s+1)==="-"&&(c+=e.slice(s,s+1)),t==="IP"){let I=e.split(".");this.ipError=this._validIP(I),t="***************"}let gt=[];for(let I=0;I<e.length;I++)e[I]?.match("\\d")&&gt.push(e[I]??"");if(t==="CPF_CNPJ"&&(this.cpfCnpjError=gt.length!==11&&gt.length!==14,gt.length>11?t="00.000.000/0000-00":t="000.000.000-00"),t.startsWith("percent")){if(e.match("[a-z]|[A-Z]")||e.match(/[-!$%^&*()_+|~=`{}\[\]:";'<>?,\/.]/)&&!a){e=this._stripToDecimal(e);let _e=this.getPrecision(t);e=this.checkInputPrecision(e,_e,this.decimalMarker)}let I=typeof this.decimalMarker=="string"?this.decimalMarker:".";if(e.indexOf(I)>0&&!this.percentage(e.substring(0,e.indexOf(I)))){let _e=e.substring(0,e.indexOf(I)-1);this.allowNegativeNumbers&&e.slice(s,s+1)==="-"&&!a&&(_e=e.substring(0,e.indexOf(I))),e=`${_e}${e.substring(e.indexOf(I),e.length)}`}let S="";this.allowNegativeNumbers&&e.slice(s,s+1)==="-"?S=`-${e.slice(s+1,s+e.length)}`:S=e,this.percentage(S)?c=this._splitPercentZero(e):c=this._splitPercentZero(e.substring(0,e.length-1))}else if(t.startsWith("separator")){(e.match("[w\u0430-\u044F\u0410-\u042F]")||e.match("[\u0401\u0451\u0410-\u044F]")||e.match("[a-z]|[A-Z]")||e.match(/[-@#!$%\\^&*()_£¬'+|~=`{}\]:";<>.?/]/)||e.match("[^A-Za-z0-9,]"))&&(e=this._stripToDecimal(e));let I=this.getPrecision(t),S=Array.isArray(this.decimalMarker)?".":this.decimalMarker;if(I===0?e=this.allowNegativeNumbers?e.length>2&&e[0]==="-"&&e[1]==="0"&&e[2]!==this.thousandSeparator&&e[2]!==","&&e[2]!=="."?"-"+e.slice(2,e.length):e[0]==="0"&&e.length>1&&e[1]!==this.thousandSeparator&&e[1]!==","&&e[1]!=="."?e.slice(1,e.length):e:e.length>1&&e[0]==="0"&&e[1]!==this.thousandSeparator&&e[1]!==","&&e[1]!=="."?e.slice(1,e.length):e:(e[0]===S&&e.length>1&&(e="0"+e.slice(0,e.length+1),this.plusOnePosition=!0),e[0]==="0"&&e[1]!==S&&e[1]!==this.thousandSeparator&&(e=e.length>1?e.slice(0,1)+S+e.slice(1,e.length+1):e,this.plusOnePosition=!0),this.allowNegativeNumbers&&e[0]==="-"&&(e[1]===S||e[1]==="0")&&(e=e[1]===S&&e.length>2?e.slice(0,1)+"0"+e.slice(1,e.length):e[1]==="0"&&e.length>2&&e[2]!==S?e.slice(0,2)+S+e.slice(2,e.length):e,this.plusOnePosition=!0)),a){let J=e.slice(this._findFirstNonZeroDigitIndex(e),e.length),le=e[i]==="0"||e[i]===S,Fe=e[0]==="0",ge=e[0]==="-",St=e[0]===this.thousandSeparator,vt=e[1]===S,Ve=e[1]==="0",Wt=e[2]===S;Fe&&vt&&le&&i<2&&(e=J),ge&&Ve&&Wt&&le&&i<3&&(e="-"+J),J!=="-"&&(i===0&&(Fe||St)||this.allowNegativeNumbers&&i===1&&ge&&!Ve)&&(e=ge?"-"+J:J)}let _e=this._charToRegExpExpression(this.thousandSeparator),ne='@#!$%^&*()_+|~=`{}\\[\\]:\\s,\\.";<>?\\/'.replace(_e,"");if(Array.isArray(this.decimalMarker))for(let J of this.decimalMarker)ne=ne.replace(this._charToRegExpExpression(J),"");else ne=ne.replace(this._charToRegExpExpression(this.decimalMarker),"");let Ke=new RegExp("["+ne+"]");e.match(Ke)&&(e=e.substring(0,e.length-1)),e=this.checkInputPrecision(e,I,this.decimalMarker);let ze=e.replace(new RegExp(_e,"g"),"");c=this._formatWithSeparators(ze,this.thousandSeparator,this.decimalMarker,I);let Xt=c.indexOf(",")-e.indexOf(","),ce=c.length-e.length;if(c[i-1]===this.thousandSeparator&&this.prefix&&a)i=i-1;else if(ce>0&&c[i]!==this.thousandSeparator){g=!0;let J=0;do this._shift.add(i+J),J++;while(J<ce)}else c[i-1]===this.decimalMarker||ce===-4||ce===-3||c[i]===this.thousandSeparator?(this._shift.clear(),this._shift.add(i-1)):Xt!==0&&i>0&&!(c.indexOf(",")>=i&&i>3)||!(c.indexOf(".")>=i&&i>3)&&ce<=0?(this._shift.clear(),g=!0,W=ce,i+=ce,this._shift.add(i)):this._shift.clear()}else for(let I=0,S=se[0];I<se.length&&s!==t.length;I++,S=se[I]??""){let _e="*"in this.patterns;if(this._checkSymbolMask(S,t[s]??"")&&t[s+1]==="?")c+=S,s+=2;else if(t[s+1]==="*"&&v&&this._checkSymbolMask(S,t[s+2]??""))c+=S,s+=3,v=!1;else if(this._checkSymbolMask(S,t[s]??"")&&t[s+1]==="*"&&!_e)c+=S,v=!0;else if(t[s+1]==="?"&&this._checkSymbolMask(S,t[s+2]??""))c+=S,s+=3;else if(this._checkSymbolMask(S,t[s]??"")){if(t[s]==="H"&&(this.apm?Number(S)>9:Number(S)>2)){i=this.leadZeroDateTime?i:i+1,s+=1,this._shiftStep(t,s,se.length),I--,this.leadZeroDateTime&&(c+="0");continue}if(t[s]==="h"&&(this.apm?c.length===1&&Number(c)>1||c==="1"&&Number(S)>2||e.slice(s-1,s).length===1&&Number(e.slice(s-1,s))>2||e.slice(s-1,s)==="1"&&Number(S)>2:c==="2"&&Number(S)>3||(c.slice(s-2,s)==="2"||c.slice(s-3,s)==="2"||c.slice(s-4,s)==="2"||c.slice(s-1,s)==="2")&&Number(S)>3&&s>10)){i=i+1,s+=1,I--;continue}if((t[s]==="m"||t[s]==="s")&&Number(S)>5){i=this.leadZeroDateTime?i:i+1,s+=1,this._shiftStep(t,s,se.length),I--,this.leadZeroDateTime&&(c+="0");continue}let ne=31,Ke=e[s],ze=e[s+1],Xt=e[s+2],ce=e[s-1],J=e[s-2],le=e.slice(s-3,s-1),Fe=e.slice(s-1,s+1),ge=e.slice(s,s+2),St=e.slice(s-2,s);if(t[s]==="d"){let vt=t.slice(0,2)==="M0",Ve=t.slice(0,2)==="M0"&&this.specialCharacters.includes(J);if(Number(S)>3&&this.leadZeroDateTime||!vt&&(Number(ge)>ne||Number(Fe)>ne||this.specialCharacters.includes(ze))||(Ve?Number(Fe)>ne||!this.specialCharacters.includes(Ke)&&this.specialCharacters.includes(Xt)||this.specialCharacters.includes(Ke):Number(ge)>ne||this.specialCharacters.includes(ze))){i=this.leadZeroDateTime?i:i+1,s+=1,this._shiftStep(t,s,se.length),I--,this.leadZeroDateTime&&(c+="0");continue}}if(t[s]==="M"){let Ve=s===0&&(Number(S)>2||Number(ge)>12||this.specialCharacters.includes(ze)&&!a),Wt=t.slice(s+2,s+3),Lr=le.includes(Wt)&&t.includes("d0")&&(this.specialCharacters.includes(J)&&Number(Fe)>12&&!this.specialCharacters.includes(Ke)||this.specialCharacters.includes(Ke)),$r=Number(le)<=ne&&!this.specialCharacters.includes(le)&&this.specialCharacters.includes(ce)&&(Number(ge)>12||this.specialCharacters.includes(ze)),Br=Number(ge)>12&&s===5||this.specialCharacters.includes(ze)&&s===5,Ur=Number(le)>ne&&!this.specialCharacters.includes(le)&&!this.specialCharacters.includes(St)&&Number(St)>12&&t.includes("d0"),Kr=Number(le)<=ne&&!this.specialCharacters.includes(le)&&!this.specialCharacters.includes(ce)&&Number(Fe)>12;if(Number(S)>1&&this.leadZeroDateTime||Ve||Lr||Kr||Ur||$r||Br&&!this.leadZeroDateTime){i=this.leadZeroDateTime?i:i+1,s+=1,this._shiftStep(t,s,se.length),I--,this.leadZeroDateTime&&(c+="0");continue}}c+=S,s++}else this.specialCharacters.includes(S)&&t[s]===S?(c+=S,s++):this.specialCharacters.indexOf(t[s]??"")!==-1?(c+=t[s],s++,this._shiftStep(t,s,se.length),I--):t[s]==="9"&&this.showMaskTyped?this._shiftStep(t,s,se.length):this.patterns[t[s]??""]&&this.patterns[t[s]??""]?.optional?(se[s]&&t!=="***************"&&t!=="000.000.000-00"&&t!=="00.000.000/0000-00"&&!t.match(/^9+\.0+$/)&&!this.patterns[t[s]??""]?.optional&&(c+=se[s]),t.includes("9*")&&t.includes("0*")&&s++,s++,I--):this.maskExpression[s+1]==="*"&&this._findSpecialChar(this.maskExpression[s+2]??"")&&this._findSpecialChar(S)===this.maskExpression[s+2]&&v||this.maskExpression[s+1]==="?"&&this._findSpecialChar(this.maskExpression[s+2]??"")&&this._findSpecialChar(S)===this.maskExpression[s+2]&&v?(s+=3,c+=S):this.showMaskTyped&&this.specialCharacters.indexOf(S)<0&&S!==this.placeHolderCharacter&&this.placeHolderCharacter.length===1&&(R=!0)}c.length+1===t.length&&this.specialCharacters.indexOf(t[t.length-1]??"")!==-1&&(c+=t[t.length-1]);let fi=i+1;for(;this._shift.has(fi);)W++,fi++;let _i=r&&!t.startsWith("separator")?s:this._shift.has(i)?W:0;R&&_i--,u(_i,g),W<0&&this._shift.clear();let gi=!1;a&&(gi=se.every(I=>this.specialCharacters.includes(I)));let Zt=`${this.prefix}${gi?"":c}${this.showMaskTyped?"":this.suffix}`;c.length===0&&(Zt=this.dropSpecialCharacters?`${c}`:`${this.prefix}${c}`);let jr=e.length===1&&this.specialCharacters.includes(t[0])&&e!==t[0];if(!this._checkSymbolMask(e,t[1])&&jr)return"";if(c.includes("-")&&this.prefix&&this.allowNegativeNumbers){if(a&&c==="-")return"";Zt=`-${this.prefix}${c.split("-").join("")}${this.suffix}`}return Zt}_findDropSpecialChar(e){return Array.isArray(this.dropSpecialCharacters)?this.dropSpecialCharacters.find(t=>t===e):this._findSpecialChar(e)}_findSpecialChar(e){return this.specialCharacters.find(t=>t===e)}_checkSymbolMask(e,t){return this.patterns=this.customPattern?this.customPattern:this.patterns,(this.patterns[t]?.pattern&&this.patterns[t]?.pattern.test(e))??!1}_stripToDecimal(e){return e.split("").filter((t,i)=>{let r=typeof this.decimalMarker=="string"?t===this.decimalMarker:this.decimalMarker.includes(t);return t.match("^-?\\d")||t===this.thousandSeparator||r||t==="-"&&i===0&&this.allowNegativeNumbers}).join("")}_charToRegExpExpression(e){return e&&(e===" "?"\\s":"[\\^$.|?*+()".indexOf(e)>=0?`\\${e}`:e)}_shiftStep(e,t,i){let r=/[*?]/g.test(e.slice(0,t))?i:t;this._shift.add(r+this.prefix.length||0)}_compareOrIncludes(e,t,i){return Array.isArray(t)?t.filter(r=>r!==i).includes(e):e===t}_validIP(e){return!(e.length===4&&!e.some((t,i)=>e.length!==i+1?t===""||Number(t)>255:t===""||Number(t.substring(0,3))>255))}_splitPercentZero(e){if(e==="-"&&this.allowNegativeNumbers)return e;let t=typeof this.decimalMarker=="string"?e.indexOf(this.decimalMarker):e.indexOf("."),i=this.allowNegativeNumbers&&e.includes("-")?"-":"";if(t===-1){let r=parseInt(i?e.slice(1,e.length):e,10);return isNaN(r)?"":`${i}${r}`}else{let r=parseInt(e.replace("-","").substring(0,t),10),a=e.substring(t+1),u=isNaN(r)?"":r.toString(),s=typeof this.decimalMarker=="string"?this.decimalMarker:".";return u===""?"":`${i}${u}${s}${a}`}}_findFirstNonZeroDigitIndex(e){for(let t=0;t<e.length;t++){let i=e[t];if(i&&i>="1"&&i<="9")return t}return-1}static{this.\u0275fac=function(t){return new(t||n)}}static{this.\u0275prov=b({token:n,factory:n.\u0275fac})}}return n})(),Ds=(()=>{class n extends Rs{constructor(){super(...arguments),this.isNumberValue=!1,this.maskIsShown="",this.selStart=null,this.selEnd=null,this.writingValue=!1,this.maskChanged=!1,this._maskExpressionArray=[],this.triggerOnMaskChange=!1,this._previousValue="",this._currentValue="",this._emitValue=!1,this.onChange=e=>{},this._elementRef=p(bt,{optional:!0}),this.document=p(Mt),this._config=p(di),this._renderer=p(Ri,{optional:!0})}applyMask(e,t,i=0,r=!1,a=!1,u=()=>{}){if(!t)return e!==this.actualValue?this.actualValue:e;if(this.maskIsShown=this.showMaskTyped?this.showMaskInInput():"",this.maskExpression==="IP"&&this.showMaskTyped&&(this.maskIsShown=this.showMaskInInput(e||"#")),this.maskExpression==="CPF_CNPJ"&&this.showMaskTyped&&(this.maskIsShown=this.showMaskInInput(e||"#")),!e&&this.showMaskTyped)return this.formControlResult(this.prefix),`${this.prefix}${this.maskIsShown}${this.suffix}`;let s=e&&typeof this.selStart=="number"?e[this.selStart]??"":"",c="";if(this.hiddenInput!==void 0&&!this.writingValue){let R=e&&e.length===1?e.split(""):this.actualValue.split("");typeof this.selStart=="object"&&typeof this.selEnd=="object"?(this.selStart=Number(this.selStart),this.selEnd=Number(this.selEnd)):e!==""&&R.length?typeof this.selStart=="number"&&typeof this.selEnd=="number"&&(e.length>R.length?R.splice(this.selStart,0,s):e.length<R.length&&(R.length-e.length===1?a?R.splice(this.selStart-1,1):R.splice(e.length-1,1):R.splice(this.selStart,this.selEnd-this.selStart))):R=[],this.showMaskTyped&&(this.hiddenInput||(e=this.removeMask(e))),c=this.actualValue.length&&R.length<=e.length?this.shiftTypedSymbols(R.join("")):e}if(r&&(this.hiddenInput||!this.hiddenInput)&&(c=e),a&&this.specialCharacters.indexOf(this.maskExpression[i]??"")!==-1&&this.showMaskTyped&&!this.prefix&&(c=this._currentValue),this.deletedSpecialCharacter&&i&&(this.specialCharacters.includes(this.actualValue.slice(i,i+1))?i=i+1:t.slice(i-1,i+1)!=="M0"&&(i=i-2),this.deletedSpecialCharacter=!1),this.showMaskTyped&&this.placeHolderCharacter.length===1&&!this.leadZeroDateTime&&(e=this.removeMask(e)),this.maskChanged?c=e:c=c&&c.length?c:e,this.showMaskTyped&&this.keepCharacterPositions&&this.actualValue&&!r&&!this.writingValue){let R=this.dropSpecialCharacters?this.removeMask(this.actualValue):this.actualValue;return this.formControlResult(R),this.actualValue?this.actualValue:`${this.prefix}${this.maskIsShown}${this.suffix}`}let v=super.applyMask(c,t,i,r,a,u);if(this.actualValue=this.getActualValue(v),this.thousandSeparator==="."&&this.decimalMarker==="."&&(this.decimalMarker=","),this.maskExpression.startsWith("separator")&&this.dropSpecialCharacters===!0&&(this.specialCharacters=this.specialCharacters.filter(R=>!this._compareOrIncludes(R,this.decimalMarker,this.thousandSeparator))),(v||v==="")&&(this._previousValue=this._currentValue,this._currentValue=v,this._emitValue=this._previousValue!==this._currentValue||this.maskChanged||this._previousValue===this._currentValue&&r),this._emitValue&&(this.writingValue&&this.triggerOnMaskChange?requestAnimationFrame(()=>this.formControlResult(v)):this.formControlResult(v)),!this.showMaskTyped||this.showMaskTyped&&this.hiddenInput)return this.hiddenInput?a?this.hideInput(v,this.maskExpression):`${this.hideInput(v,this.maskExpression)}${this.maskIsShown.slice(v.length)}`:v;let g=v.length,W=`${this.prefix}${this.maskIsShown}${this.suffix}`;if(this.maskExpression.includes("H")){let R=this._numberSkipedSymbols(v);return`${v}${W.slice(g+R)}`}else if(this.maskExpression==="IP"||this.maskExpression==="CPF_CNPJ")return`${v}${W}`;return`${v}${W.slice(g)}`}_numberSkipedSymbols(e){let t=/(^|\D)(\d\D)/g,i=t.exec(e),r=0;for(;i!=null;)r+=1,i=t.exec(e);return r}applyValueChanges(e,t,i,r=()=>{}){let a=this._elementRef?.nativeElement;a&&(a.value=this.applyMask(a.value,this.maskExpression,e,t,i,r),a!==this._getActiveElement()&&this.clearIfNotMatchFn())}hideInput(e,t){return e.split("").map((i,r)=>this.patterns&&this.patterns[t[r]??""]&&this.patterns[t[r]??""]?.symbol?this.patterns[t[r]??""]?.symbol:i).join("")}getActualValue(e){let t=e.split("").filter((i,r)=>{let a=this.maskExpression[r]??"";return this._checkSymbolMask(i,a)||this.specialCharacters.includes(a)&&i===a});return t.join("")===e?t.join(""):e}shiftTypedSymbols(e){let t="";return(e&&e.split("").map((r,a)=>{if(this.specialCharacters.includes(e[a+1]??"")&&e[a+1]!==this.maskExpression[a+1])return t=r,e[a+1];if(t.length){let u=t;return t="",u}return r})||[]).join("")}numberToString(e){return!e&&e!==0||this.maskExpression.startsWith("separator")&&(this.leadZero||!this.dropSpecialCharacters)||this.maskExpression.startsWith("separator")&&this.separatorLimit.length>14&&String(e).length>14?String(e):Number(e).toLocaleString("fullwide",{useGrouping:!1,maximumFractionDigits:20}).replace("/-/","-")}showMaskInInput(e){if(this.showMaskTyped&&this.shownMaskExpression){if(this.maskExpression.length!==this.shownMaskExpression.length)throw new Error("Mask expression must match mask placeholder length");return this.shownMaskExpression}else if(this.showMaskTyped){if(e){if(this.maskExpression==="IP")return this._checkForIp(e);if(this.maskExpression==="CPF_CNPJ")return this._checkForCpfCnpj(e)}return this.placeHolderCharacter.length===this.maskExpression.length?this.placeHolderCharacter:this.maskExpression.replace(/\w/g,this.placeHolderCharacter)}return""}clearIfNotMatchFn(){let e=this._elementRef?.nativeElement;e&&this.clearIfNotMatch&&this.prefix.length+this.maskExpression.length+this.suffix.length!==e.value.replace(this.placeHolderCharacter,"").length&&(this.formElementProperty=["value",""],this.applyMask("",this.maskExpression))}set formElementProperty([e,t]){!this._renderer||!this._elementRef||Promise.resolve().then(()=>this._renderer?.setProperty(this._elementRef?.nativeElement,e,t))}checkDropSpecialCharAmount(e){return e.split("").filter(i=>this._findDropSpecialChar(i)).length}removeMask(e){return this._removeMask(this._removeSuffix(this._removePrefix(e)),this.specialCharacters.concat("_").concat(this.placeHolderCharacter))}_checkForIp(e){if(e==="#")return`${this.placeHolderCharacter}.${this.placeHolderCharacter}.${this.placeHolderCharacter}.${this.placeHolderCharacter}`;let t=[];for(let i=0;i<e.length;i++){let r=e[i]??"";r&&r.match("\\d")&&t.push(r)}return t.length<=3?`${this.placeHolderCharacter}.${this.placeHolderCharacter}.${this.placeHolderCharacter}`:t.length>3&&t.length<=6?`${this.placeHolderCharacter}.${this.placeHolderCharacter}`:t.length>6&&t.length<=9?this.placeHolderCharacter:(t.length>9&&t.length<=12,"")}_checkForCpfCnpj(e){let t=`${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}-${this.placeHolderCharacter}${this.placeHolderCharacter}`,i=`${this.placeHolderCharacter}${this.placeHolderCharacter}.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}/${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}-${this.placeHolderCharacter}${this.placeHolderCharacter}`;if(e==="#")return t;let r=[];for(let a=0;a<e.length;a++){let u=e[a]??"";u&&u.match("\\d")&&r.push(u)}return r.length<=3?t.slice(r.length,t.length):r.length>3&&r.length<=6?t.slice(r.length+1,t.length):r.length>6&&r.length<=9?t.slice(r.length+2,t.length):r.length>9&&r.length<11?t.slice(r.length+3,t.length):r.length===11?"":r.length===12?e.length===17?i.slice(16,i.length):i.slice(15,i.length):r.length>12&&r.length<=14?i.slice(r.length+4,i.length):""}_getActiveElement(e=this.document){let t=e?.activeElement?.shadowRoot;return t?.activeElement?this._getActiveElement(t):e.activeElement}formControlResult(e){if(this.writingValue||!this.triggerOnMaskChange&&this.maskChanged){this.triggerOnMaskChange&&this.maskChanged&&this.onChange(this.outputTransformFn(this._toNumber(this._checkSymbols(this._removeSuffix(this._removePrefix(e)))))),this.maskChanged=!1;return}Array.isArray(this.dropSpecialCharacters)?this.onChange(this.outputTransformFn(this._toNumber(this._checkSymbols(this._removeMask(this._removeSuffix(this._removePrefix(e)),this.dropSpecialCharacters))))):this.dropSpecialCharacters||!this.dropSpecialCharacters&&this.prefix===e?this.onChange(this.outputTransformFn(this._toNumber(this._checkSymbols(this._removeSuffix(this._removePrefix(e)))))):this.onChange(this.outputTransformFn(this._toNumber(e)))}_toNumber(e){if(!this.isNumberValue||e===""||this.maskExpression.startsWith("separator")&&(this.leadZero||!this.dropSpecialCharacters))return e;if(String(e).length>16&&this.separatorLimit.length>14)return String(e);let t=Number(e);if(this.maskExpression.startsWith("separator")&&Number.isNaN(t)){let i=String(e).replace(",",".");return Number(i)}return Number.isNaN(t)?e:t}_removeMask(e,t){return this.maskExpression.startsWith("percent")&&e.includes(".")?e:e&&e.replace(this._regExpForRemove(t),"")}_removePrefix(e){return this.prefix?e&&e.replace(this.prefix,""):e}_removeSuffix(e){return this.suffix?e&&e.replace(this.suffix,""):e}_retrieveSeparatorValue(e){let t=Array.isArray(this.dropSpecialCharacters)?this.specialCharacters.filter(i=>this.dropSpecialCharacters.includes(i)):this.specialCharacters;return!this.deletedSpecialCharacter&&this._checkPatternForSpace()&&e.includes(" ")&&this.maskExpression.includes("*")&&(t=t.filter(i=>i!==" ")),this._removeMask(e,t)}_regExpForRemove(e){return new RegExp(e.map(t=>`\\${t}`).join("|"),"gi")}_replaceDecimalMarkerToDot(e){let t=Array.isArray(this.decimalMarker)?this.decimalMarker:[this.decimalMarker];return e.replace(this._regExpForRemove(t),".")}_checkSymbols(e){if(e==="")return e;this.maskExpression.startsWith("percent")&&this.decimalMarker===","&&(e=e.replace(",","."));let t=this._retrieveSeparatorPrecision(this.maskExpression),i=this._replaceDecimalMarkerToDot(this._retrieveSeparatorValue(e));return this.isNumberValue&&t?e===this.decimalMarker?null:this.separatorLimit.length>14?String(i):this._checkPrecision(this.maskExpression,i):i}_checkPatternForSpace(){for(let e in this.patterns)if(this.patterns[e]&&this.patterns[e]?.hasOwnProperty("pattern")){let t=this.patterns[e]?.pattern.toString(),i=this.patterns[e]?.pattern;if(t?.includes(" ")&&i?.test(this.maskExpression))return!0}return!1}_retrieveSeparatorPrecision(e){let t=e.match(new RegExp("^separator\\.([^d]*)"));return t?Number(t[1]):null}_checkPrecision(e,t){let i=e.slice(10,11);return e.indexOf("2")>0||this.leadZero&&Number(i)>0?(this.decimalMarker===","&&this.leadZero&&(t=t.replace(",",".")),this.leadZero?Number(t).toFixed(Number(i)):Number(t).toFixed(2)):this.numberToString(t)}_repeatPatternSymbols(e){return e.match(/{[0-9]+}/)&&e.split("").reduce((t,i,r)=>{if(this._start=i==="{"?r:this._start,i!=="}")return this._findSpecialChar(i)?t+i:t;this._end=r;let a=Number(e.slice(this._start+1,this._end)),u=new Array(a+1).join(e[this._start-1]);if(e.slice(0,this._start).length>1&&e.includes("S")){let s=e.slice(0,this._start-1);return s.includes("{")?t+u:s+t+u}else return t+u},"")||e}currentLocaleDecimalMarker(){return 1.1.toLocaleString().substring(1,2)}static{this.\u0275fac=(()=>{let e;return function(i){return(e||(e=Tt(n)))(i||n)}})()}static{this.\u0275prov=b({token:n,factory:n.\u0275fac})}}return n})();function Hs(){let n=p(br),o=p(Tr);return o instanceof Function?$($({},n),o()):$($({},n),o)}function Ps(n){return[{provide:Tr,useValue:n},{provide:br,useValue:Os},{provide:di,useFactory:Hs},Ds]}function Ir(n){return kt(Ps(n))}var zs="@",Fs=(()=>{class n{constructor(e,t,i,r,a){this.doc=e,this.delegate=t,this.zone=i,this.animationType=r,this.moduleImpl=a,this._rendererFactoryPromise=null,this.scheduler=p(Ai,{optional:!0})}ngOnDestroy(){this._engine?.flush()}loadImpl(){return(this.moduleImpl??import("./chunk-WFOSGPAA.js").then(t=>t)).catch(t=>{throw new bi(5300,!1)}).then(({\u0275createEngine:t,\u0275AnimationRendererFactory:i})=>{this._engine=t(this.animationType,this.doc);let r=new i(this.delegate,this._engine,this.zone);return this.delegate=r,r})}createRenderer(e,t){let i=this.delegate.createRenderer(e,t);if(i.\u0275type===0)return i;typeof i.throwOnSyntheticProps=="boolean"&&(i.throwOnSyntheticProps=!1);let r=new ui(i);return t?.data?.animation&&!this._rendererFactoryPromise&&(this._rendererFactoryPromise=this.loadImpl()),this._rendererFactoryPromise?.then(a=>{let u=a.createRenderer(e,t);r.use(u),this.scheduler?.notify(9)}).catch(a=>{r.use(i)}),r}begin(){this.delegate.begin?.()}end(){this.delegate.end?.()}whenRenderingDone(){return this.delegate.whenRenderingDone?.()??Promise.resolve()}static{this.\u0275fac=function(t){wi()}}static{this.\u0275prov=b({token:n,factory:n.\u0275fac})}}return n})(),ui=class{constructor(o){this.delegate=o,this.replay=[],this.\u0275type=1}use(o){if(this.delegate=o,this.replay!==null){for(let e of this.replay)e(o);this.replay=null}}get data(){return this.delegate.data}destroy(){this.replay=null,this.delegate.destroy()}createElement(o,e){return this.delegate.createElement(o,e)}createComment(o){return this.delegate.createComment(o)}createText(o){return this.delegate.createText(o)}get destroyNode(){return this.delegate.destroyNode}appendChild(o,e){this.delegate.appendChild(o,e)}insertBefore(o,e,t,i){this.delegate.insertBefore(o,e,t,i)}removeChild(o,e,t){this.delegate.removeChild(o,e,t)}selectRootElement(o,e){return this.delegate.selectRootElement(o,e)}parentNode(o){return this.delegate.parentNode(o)}nextSibling(o){return this.delegate.nextSibling(o)}setAttribute(o,e,t,i){this.delegate.setAttribute(o,e,t,i)}removeAttribute(o,e,t){this.delegate.removeAttribute(o,e,t)}addClass(o,e){this.delegate.addClass(o,e)}removeClass(o,e){this.delegate.removeClass(o,e)}setStyle(o,e,t,i){this.delegate.setStyle(o,e,t,i)}removeStyle(o,e,t){this.delegate.removeStyle(o,e,t)}setProperty(o,e,t){this.shouldReplay(e)&&this.replay.push(i=>i.setProperty(o,e,t)),this.delegate.setProperty(o,e,t)}setValue(o,e){this.delegate.setValue(o,e)}listen(o,e,t){return this.shouldReplay(e)&&this.replay.push(i=>i.listen(o,e,t)),this.delegate.listen(o,e,t)}shouldReplay(o){return this.replay!==null&&o.startsWith(zs)}};function Nr(n="animations"){return Di("NgAsyncAnimations"),kt([{provide:Oi,useFactory:(o,e,t)=>new Fs(o,e,t,n),deps:[Mt,Yi,Ni]},{provide:Ei,useValue:n==="noop"?"NoopAnimations":"BrowserAnimations"}])}function js(n){let o=n;return 5}var xr=["vi",[["s","c"],["SA","CH"],void 0],[["SA","CH"],void 0,void 0],[["CN","T2","T3","T4","T5","T6","T7"],["CN","Th 2","Th 3","Th 4","Th 5","Th 6","Th 7"],["Ch\u1EE7 Nh\u1EADt","Th\u1EE9 Hai","Th\u1EE9 Ba","Th\u1EE9 T\u01B0","Th\u1EE9 N\u0103m","Th\u1EE9 S\xE1u","Th\u1EE9 B\u1EA3y"],["CN","T2","T3","T4","T5","T6","T7"]],void 0,[["1","2","3","4","5","6","7","8","9","10","11","12"],["thg 1","thg 2","thg 3","thg 4","thg 5","thg 6","thg 7","thg 8","thg 9","thg 10","thg 11","thg 12"],["th\xE1ng 1","th\xE1ng 2","th\xE1ng 3","th\xE1ng 4","th\xE1ng 5","th\xE1ng 6","th\xE1ng 7","th\xE1ng 8","th\xE1ng 9","th\xE1ng 10","th\xE1ng 11","th\xE1ng 12"]],[["1","2","3","4","5","6","7","8","9","10","11","12"],["Thg 1","Thg 2","Thg 3","Thg 4","Thg 5","Thg 6","Thg 7","Thg 8","Thg 9","Thg 10","Thg 11","Thg 12"],["Th\xE1ng 1","Th\xE1ng 2","Th\xE1ng 3","Th\xE1ng 4","Th\xE1ng 5","Th\xE1ng 6","Th\xE1ng 7","Th\xE1ng 8","Th\xE1ng 9","Th\xE1ng 10","Th\xE1ng 11","Th\xE1ng 12"]],[["tr. CN","sau CN"],["Tr\u01B0\u1EDBc CN","Sau CN"],["Tr\u01B0\u1EDBc Thi\xEAn Ch\xFAa","Sau C\xF4ng Nguy\xEAn"]],1,[6,0],["dd/MM/y","d MMM, y","d MMMM, y","EEEE, d MMMM, y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{0}, {1}",void 0,"{0} {1}",void 0],[",",".",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","#,##0.00\xA0\xA4","#E0"],"VND","\u20AB","\u0110\u1ED3ng Vi\u1EC7t Nam",{AUD:["AU$","$"],BYN:[void 0,"\u0440."],PHP:[void 0,"\u20B1"],THB:["\u0E3F"],TWD:["NT$"],USD:["US$","$"],XXX:[]},"ltr",js];function Ls(n){let o=n,e=Math.floor(Math.abs(n)),t=n.toString().replace(/^[^.]*\.?/,"").length;return e===1&&t===0?1:5}var Er=["en",[["a","p"],["AM","PM"],void 0],[["AM","PM"],void 0,void 0],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],void 0,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],void 0,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",void 0,"{1} 'at' {0}",void 0],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",Ls];ri(xr);ri(Er);var $s={notification:{nzTop:80,nzBottom:20},pagination:{nzSimple:!1},table:{nzSimple:!1}},Bs={scrollPositionRestoration:"top",anchorScrolling:"enabled"},Us=Xi(Bs);function Ks(n,o){return new Ut(n,[{prefix:"./assets/i18n/chuyen-doi-ib/",suffix:".json"},{prefix:"./assets/i18n/kich-hoat/",suffix:".json"},{prefix:"./assets/i18n/mat-khau-het-han/",suffix:".json"},{prefix:"./assets/i18n/quen-mat-khau/",suffix:".json"},{prefix:"./assets/i18n/app/",suffix:".json"},{prefix:"./assets/i18n/dang-nhap/",suffix:".json"},{prefix:"./assets/i18n/trang-chu/",suffix:".json"},{prefix:"./assets/i18n/dich-vu-the/",suffix:".json"},{prefix:"./assets/i18n/dich-vu-ngan-hang-so/tra-soat/",suffix:".json"},{prefix:"assets/i18n/rewards/",suffix:".json"},{prefix:"assets/i18n/lich-su-diem/",suffix:".json"},{prefix:"assets/i18n/cai-dat/tuy-chinh/",suffix:".json"},{prefix:"assets/i18n/quan-ly-danh-ba/",suffix:".json"},{prefix:"assets/i18n/chuyen-tien-v2/",suffix:".json"},{prefix:"assets/i18n/thanh-toan/",suffix:".json"},{prefix:"assets/i18n/tai-khoan-dinh-danh/",suffix:".json"},{prefix:"assets/i18n/chi-tiet-tai-khoan/",suffix:".json"},{prefix:"assets/i18n/chuyen-tien-quoc-te/",suffix:".json"},{prefix:"assets/i18n/tra-gop-the-tin-dung/",suffix:".json"},{prefix:"assets/i18n/mua-ngoai-te/",suffix:".json"},{prefix:"assets/i18n/nop-thue/",suffix:".json"},{prefix:"assets/i18n/tai-khoan/",suffix:".json"},{prefix:"assets/i18n/app/breadcrumb/",suffix:".json"},{prefix:"assets/i18n/chuyen-tien/",suffix:".json"},{prefix:"assets/i18n/vi-giao-thong/",suffix:".json"},{prefix:"assets/i18n/mo-tai-khoan-so-dep/",suffix:".json"},{prefix:"assets/i18n/phat-hanh-the/",suffix:".json"},{prefix:"assets/i18n/chuyen-tien-dinh-ky/",suffix:".json"},{prefix:"assets/i18n/thien-nguyen-tang-qua/",suffix:".json"},{prefix:"assets/i18n/nap-tien-dien-thoai/",suffix:".json"},{prefix:"assets/i18n/tich-luy-online/",suffix:".json"},{prefix:"assets/i18n/tiet-kiem/",suffix:".json"},{prefix:"assets/i18n/dich-vu-ngan-hang-so/",suffix:".json"},{prefix:"assets/i18n/app/modal/",suffix:".json"},{prefix:"assets/i18n/common/",suffix:".json"},{prefix:"assets/i18n/transfer/",suffix:".json"},{prefix:"assets/i18n/chuyen-doi-the/",suffix:".json"},{prefix:"assets/i18n/dich-vu-ngan-hang-so/khieu-nai/",suffix:".json"},{prefix:"assets/i18n/card/",suffix:".json"}],o)}var Mr={providers:[Ii([En,kn,Nn,te.forRoot({loader:{provide:mn,useFactory:Ks,deps:[At,ve]},isolate:!0})]),ii({eventCoalescing:!0}),oi(li,Wi()),si(Vi()),ii({eventCoalescing:!0}),oi(li,Us),hn(jt),Nr(),si(),Ir(),ve,pi,{provide:ni,useValue:ue.Vi},{multi:!0,provide:$e,useClass:mr},{provide:$e,useClass:dr,multi:!0},{provide:$e,useClass:ur,multi:!0},{provide:$e,useClass:gr,multi:!0},{provide:$e,useClass:Sr,multi:!0},{provide:$e,useClass:yr,multi:!0},{provide:xi,useClass:kr},{provide:_n,useValue:$s},{provide:ln,useFactory:n=>{let o=jt;switch(n){case ue.En:o=cn;break;case ue.Vi:o=jt;break}return ye($({},o),{Pagination:{items_per_page:""}})},deps:[ni]},pi,$i,Bi,bn]};var wr=(()=>{class n{status$;offlineModal;networkSv=p(Vt);modalService=p(V);destroyRef=p(q);location=p(Me);loadingService=p(He);translateService=p(Z);ngOnInit(){this.status$=this.networkSv.netEvent.pipe(re(this.destroyRef)).subscribe(e=>this.handleEvents(e))}handleEvents(e){switch(e.type){case"network_online":this.onOnline();break;case"network_offline":this.onOffline();break;default:break}}onOnline(){this.modalService.toggleShowOneAtTime(!1),this.offlineModal.close(),this.modalService.warning({message:this.translateService.instant("network_connection"),confirm:()=>window.location.reload()})}onOffline(){this.modalService.toggleShowOneAtTime(!0),this.loadingService.hideLoading(),this.offlineModal=this.modalService.error({message:this.translateService.instant("errors.network_offline")})}ngOnDestroy(){this.status$&&this.status$.unsubscribe()}static \u0275fac=function(t){return new(t||n)};static \u0275cmp=z({type:n,selectors:[["app-network"]],standalone:!0,features:[L],decls:0,vars:0,template:function(t,i){}})}return n})();var Ys=0,Yt=class{constructor(o,e,t){this.nzSingletonService=o,this.overlay=e,this.injector=t}remove(o){this.container&&(o?this.container.remove(o):this.container.removeAll())}getInstanceId(){return`${this.componentPrefix}-${Ys++}`}withContainer(o){let e=this.nzSingletonService.getSingletonWithKey(this.componentPrefix);if(e)return e;let t=this.overlay.create({hasBackdrop:!1,scrollStrategy:this.overlay.scrollStrategies.noop(),positionStrategy:this.overlay.position().global()}),i=new dn(o,null,this.injector),r=t.attach(i),a=t.hostElement;return a.style.zIndex="1010",e||(this.container=e=r.instance,this.nzSingletonService.registerSingletonWithKey(this.componentPrefix,e),this.container.afterAllInstancesRemoved.subscribe(()=>{this.container=void 0,this.nzSingletonService.unregisterSingletonWithKey(this.componentPrefix),t.dispose()})),e}},Ar=(()=>{class n{constructor(e,t){this.cdr=e,this.nzConfigService=t,this.instances=[],this._afterAllInstancesRemoved=new ke,this.afterAllInstancesRemoved=this._afterAllInstancesRemoved.asObservable(),this.destroy$=new ke,this.updateConfig()}ngOnInit(){this.subscribeConfigChange()}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}create(e){let t=this.onCreate(e);return this.instances.length>=this.config.nzMaxStack&&(this.instances=this.instances.slice(1)),this.instances=[...this.instances,t],this.readyInstances(),t}remove(e,t=!1){this.instances.map((i,r)=>({index:r,instance:i})).filter(({instance:i})=>i.messageId===e).forEach(({index:i,instance:r})=>{this.instances.splice(i,1),this.instances=[...this.instances],this.onRemove(r,t),this.readyInstances()}),this.instances.length||this.onAllInstancesRemoved()}removeAll(){this.instances.forEach(e=>this.onRemove(e,!1)),this.instances=[],this.readyInstances(),this.onAllInstancesRemoved()}onCreate(e){return e.options=this.mergeOptions(e.options),e.onClose=new ke,e}onRemove(e,t){e.onClose.next(t),e.onClose.complete()}onAllInstancesRemoved(){this._afterAllInstancesRemoved.next(),this._afterAllInstancesRemoved.complete()}readyInstances(){this.cdr.detectChanges()}mergeOptions(e){let{nzDuration:t,nzAnimate:i,nzPauseOnHover:r}=this.config;return $({nzDuration:t,nzAnimate:i,nzPauseOnHover:r},e)}static{this.\u0275fac=function(t){return new(t||n)(K(Je),K(Lt))}}static{this.\u0275dir=yt({type:n})}}return n})(),Or=(()=>{class n{constructor(e){this.cdr=e,this.destroyed=new Ie,this.animationStateChanged=new ke,this.userAction=!1}ngOnInit(){this.options=this.instance.options,this.options.nzAnimate&&(this.instance.state="enter",this.animationStateChanged.pipe(Te(e=>e.phaseName==="done"&&e.toState==="leave"),Ze(1)).subscribe(()=>{clearTimeout(this.closeTimer),this.destroyed.next({id:this.instance.messageId,userAction:this.userAction})})),this.autoClose=this.options.nzDuration>0,this.autoClose&&(this.initErase(),this.startEraseTimeout())}ngOnDestroy(){this.autoClose&&this.clearEraseTimeout(),this.animationStateChanged.complete()}onEnter(){this.autoClose&&this.options.nzPauseOnHover&&(this.clearEraseTimeout(),this.updateTTL())}onLeave(){this.autoClose&&this.options.nzPauseOnHover&&this.startEraseTimeout()}destroy(e=!1){this.userAction=e,this.options.nzAnimate?(this.instance.state="leave",this.cdr.detectChanges(),this.closeTimer=setTimeout(()=>{this.closeTimer=void 0,this.destroyed.next({id:this.instance.messageId,userAction:e})},200)):this.destroyed.next({id:this.instance.messageId,userAction:e})}initErase(){this.eraseTTL=this.options.nzDuration,this.eraseTimingStart=Date.now()}updateTTL(){this.autoClose&&(this.eraseTTL-=Date.now()-this.eraseTimingStart)}startEraseTimeout(){this.eraseTTL>0?(this.clearEraseTimeout(),this.eraseTimer=setTimeout(()=>this.destroy(),this.eraseTTL),this.eraseTimingStart=Date.now()):this.destroy()}clearEraseTimeout(){this.eraseTimer!==null&&(clearTimeout(this.eraseTimer),this.eraseTimer=void 0)}static{this.\u0275fac=function(t){return new(t||n)(K(Je))}}static{this.\u0275dir=yt({type:n})}}return n})();var Gs=(n,o)=>({$implicit:n,data:o}),Zs=n=>({$implicit:n});function Xs(n,o){}function Ws(n,o){if(n&1&&T(0,Xs,0,0,"ng-template",1),n&2){let e=k();C("ngTemplateOutlet",e.instance.template)("ngTemplateOutletContext",Xe(2,Gs,e,e.instance.options==null?null:e.instance.options.nzData))}}function Js(n,o){n&1&&y(0,"span",6)}function Qs(n,o){n&1&&y(0,"span",7)}function qs(n,o){n&1&&y(0,"span",8)}function eo(n,o){n&1&&y(0,"span",9)}function to(n,o){if(n&1&&(ct(0),y(1,"div",14),lt()),n&2){let e=k(2);l(),C("innerHTML",e.instance.title,It)}}function io(n,o){if(n&1&&(ct(0),y(1,"div",14),lt()),n&2){let e=k(2);l(),C("innerHTML",e.instance.content,It)}}function no(n,o){}function ro(n,o){if(n&1&&(m(0,"span",13),T(1,no,0,0,"ng-template",1),d()),n&2){let e=k(2);l(),C("ngTemplateOutlet",o)("ngTemplateOutletContext",Ee(2,Zs,e))}}function so(n,o){if(n&1&&(m(0,"div",2)(1,"div",2)(2,"div"),T(3,Js,1,0,"span",6)(4,Qs,1,0,"span",7)(5,qs,1,0,"span",8)(6,eo,1,0,"span",9),m(7,"div",10),T(8,to,2,1,"ng-container",11),d(),m(9,"div",12),T(10,io,2,1,"ng-container",11),d(),T(11,ro,2,4,"span",13),d()()()),n&2){let e,t,i=k();l(2),G("ant-notification-notice-with-icon",i.instance.type!=="blank"),l(),E((e=i.instance.type)==="success"?3:e==="info"?4:e==="warning"?5:e==="error"?6:-1),l(5),C("nzStringTemplateOutlet",i.instance.title),l(2),C("nzStringTemplateOutlet",i.instance.content),l(),E((t=i.instance.options==null?null:i.instance.options.nzButton)?11:-1,t)}}function oo(n,o){if(n&1&&(ct(0),y(1,"span",15),lt()),n&2){let e=o.$implicit;l(),C("nzType",e)}}function ao(n,o){if(n&1&&T(0,oo,2,1,"ng-container",11),n&2){let e=k();C("nzStringTemplateOutlet",e.instance.options==null?null:e.instance.options.nzCloseIcon)}}function co(n,o){n&1&&y(0,"span",5)}function lo(n,o){if(n&1){let e=B();m(0,"nz-notification",7),N("destroyed",function(i){F(e);let r=k();return j(r.remove(i.id,i.userAction))}),d()}if(n&2){let e=o.$implicit;C("instance",e)("placement","topLeft")}}function ho(n,o){if(n&1){let e=B();m(0,"nz-notification",7),N("destroyed",function(i){F(e);let r=k();return j(r.remove(i.id,i.userAction))}),d()}if(n&2){let e=o.$implicit;C("instance",e)("placement","topRight")}}function po(n,o){if(n&1){let e=B();m(0,"nz-notification",7),N("destroyed",function(i){F(e);let r=k();return j(r.remove(i.id,i.userAction))}),d()}if(n&2){let e=o.$implicit;C("instance",e)("placement","bottomLeft")}}function mo(n,o){if(n&1){let e=B();m(0,"nz-notification",7),N("destroyed",function(i){F(e);let r=k();return j(r.remove(i.id,i.userAction))}),d()}if(n&2){let e=o.$implicit;C("instance",e)("placement","bottomRight")}}function uo(n,o){if(n&1){let e=B();m(0,"nz-notification",7),N("destroyed",function(i){F(e);let r=k();return j(r.remove(i.id,i.userAction))}),d()}if(n&2){let e=o.$implicit;C("instance",e)("placement","top")}}function fo(n,o){if(n&1){let e=B();m(0,"nz-notification",7),N("destroyed",function(i){F(e);let r=k();return j(r.remove(i.id,i.userAction))}),d()}if(n&2){let e=o.$implicit;C("instance",e)("placement","bottom")}}var Rr=(()=>{class n extends Or{constructor(e){super(e),this.destroyed=new Ie}ngOnDestroy(){super.ngOnDestroy(),this.instance.onClick.complete()}onClick(e){this.instance.onClick.next(e)}close(){this.destroy(!0)}get state(){if(this.instance.state==="enter")switch(this.placement){case"topLeft":case"bottomLeft":return"enterLeft";case"topRight":case"bottomRight":return"enterRight";case"top":return"enterTop";case"bottom":return"enterBottom";default:return"enterRight"}else return this.instance.state}static{this.\u0275fac=function(t){return new(t||n)(K(Je))}}static{this.\u0275cmp=z({type:n,selectors:[["nz-notification"]],inputs:{instance:"instance",index:"index",placement:"placement"},outputs:{destroyed:"destroyed"},exportAs:["nzNotification"],standalone:!0,features:[Ne,L],decls:7,vars:5,consts:[[1,"ant-notification-notice","ant-notification-notice-closable",3,"click","mouseenter","mouseleave","ngStyle","ngClass"],[3,"ngTemplateOutlet","ngTemplateOutletContext"],[1,"ant-notification-notice-content"],["tabindex","0",1,"ant-notification-notice-close",3,"click"],[1,"ant-notification-notice-close-x"],["nz-icon","","nzType","close",1,"ant-notification-close-icon"],["nz-icon","","nzType","check-circle",1,"ant-notification-notice-icon","ant-notification-notice-icon-success"],["nz-icon","","nzType","info-circle",1,"ant-notification-notice-icon","ant-notification-notice-icon-info"],["nz-icon","","nzType","exclamation-circle",1,"ant-notification-notice-icon","ant-notification-notice-icon-warning"],["nz-icon","","nzType","close-circle",1,"ant-notification-notice-icon","ant-notification-notice-icon-error"],[1,"ant-notification-notice-message"],[4,"nzStringTemplateOutlet"],[1,"ant-notification-notice-description"],[1,"ant-notification-notice-btn"],[3,"innerHTML"],["nz-icon","",3,"nzType"]],template:function(t,i){t&1&&(m(0,"div",0),N("@notificationMotion.done",function(a){return i.animationStateChanged.next(a)})("click",function(a){return i.onClick(a)})("mouseenter",function(){return i.onEnter()})("mouseleave",function(){return i.onLeave()}),T(1,Ws,1,5,null,1)(2,so,12,6,"div",2),m(3,"a",3),N("click",function(){return i.close()}),m(4,"span",4),T(5,ao,1,1,"ng-container")(6,co,1,0,"span",5),d()()()),t&2&&(C("ngStyle",(i.instance.options==null?null:i.instance.options.nzStyle)||null)("ngClass",(i.instance.options==null?null:i.instance.options.nzClass)||"")("@notificationMotion",i.state),l(),E(i.instance.template?1:2),l(4),E(i.instance.options!=null&&i.instance.options.nzCloseIcon?5:6))},dependencies:[Fi,wt,Sn,gn,Cn,vn,ji],encapsulation:2,data:{animation:[In]}})}}return n})(),Gt="notification",_o={nzTop:"24px",nzBottom:"24px",nzPlacement:"topRight",nzDuration:4500,nzMaxStack:8,nzPauseOnHover:!0,nzAnimate:!0,nzDirection:"ltr"},Dr=(()=>{class n extends Ar{constructor(e,t){super(e,t),this.dir="ltr",this.instances=[],this.topLeftInstances=[],this.topRightInstances=[],this.bottomLeftInstances=[],this.bottomRightInstances=[],this.topInstances=[],this.bottomInstances=[];let i=this.nzConfigService.getConfigForComponent(Gt);this.dir=i?.nzDirection||"ltr"}create(e){let t=this.onCreate(e),i=t.options.nzKey,r=this.instances.find(a=>a.options.nzKey===e.options.nzKey);return i&&r?this.replaceNotification(r,t):(this.instances.length>=this.config.nzMaxStack&&(this.instances=this.instances.slice(1)),this.instances=[...this.instances,t]),this.readyInstances(),t}onCreate(e){return e.options=this.mergeOptions(e.options),e.onClose=new ke,e.onClick=new ke,e}subscribeConfigChange(){this.nzConfigService.getConfigChangeEventForComponent(Gt).pipe(at(this.destroy$)).subscribe(()=>{this.updateConfig();let e=this.nzConfigService.getConfigForComponent(Gt);if(e){let{nzDirection:t}=e;this.dir=t||this.dir}})}updateConfig(){this.config=$($($({},_o),this.config),this.nzConfigService.getConfigForComponent(Gt)),this.top=ci(this.config.nzTop),this.bottom=ci(this.config.nzBottom),this.cdr.markForCheck()}replaceNotification(e,t){e.title=t.title,e.content=t.content,e.template=t.template,e.type=t.type,e.options=t.options}readyInstances(){let e={topLeft:[],topRight:[],bottomLeft:[],bottomRight:[],top:[],bottom:[]};this.instances.forEach(t=>{switch(t.options.nzPlacement){case"topLeft":e.topLeft.unshift(t);break;case"topRight":e.topRight.unshift(t);break;case"bottomLeft":e.bottomLeft.unshift(t);break;case"bottomRight":e.bottomRight.unshift(t);break;case"top":e.top.unshift(t);break;case"bottom":e.bottom.unshift(t);break;default:e.topRight.unshift(t)}}),this.topLeftInstances=e.topLeft,this.topRightInstances=e.topRight,this.bottomLeftInstances=e.bottomLeft,this.bottomRightInstances=e.bottomRight,this.topInstances=e.top,this.bottomInstances=e.bottom,this.cdr.detectChanges()}mergeOptions(e){let{nzDuration:t,nzAnimate:i,nzPauseOnHover:r,nzPlacement:a}=this.config;return $({nzDuration:t,nzAnimate:i,nzPauseOnHover:r,nzPlacement:a},e)}static{this.\u0275fac=function(t){return new(t||n)(K(Je),K(Lt))}}static{this.\u0275cmp=z({type:n,selectors:[["nz-notification-container"]],exportAs:["nzNotificationContainer"],standalone:!0,features:[Ne,L],decls:18,vars:40,consts:[[1,"ant-notification","ant-notification-topLeft"],[3,"instance","placement"],[1,"ant-notification","ant-notification-topRight"],[1,"ant-notification","ant-notification-bottomLeft"],[1,"ant-notification","ant-notification-bottomRight"],[1,"ant-notification","ant-notification-top"],[1,"ant-notification","ant-notification-bottom"],[3,"destroyed","instance","placement"]],template:function(t,i){t&1&&(m(0,"div",0),pe(1,lo,1,2,"nz-notification",1,he),d(),m(3,"div",2),pe(4,ho,1,2,"nz-notification",1,he),d(),m(6,"div",3),pe(7,po,1,2,"nz-notification",1,he),d(),m(9,"div",4),pe(10,mo,1,2,"nz-notification",1,he),d(),m(12,"div",5),pe(13,uo,1,2,"nz-notification",1,he),d(),m(15,"div",6),pe(16,fo,1,2,"nz-notification",1,he),d()),t&2&&(xe("top",i.top)("left","0px"),G("ant-notification-rtl",i.dir==="rtl"),l(),me(i.topLeftInstances),l(2),xe("top",i.top)("right","0px"),G("ant-notification-rtl",i.dir==="rtl"),l(),me(i.topRightInstances),l(2),xe("bottom",i.bottom)("left","0px"),G("ant-notification-rtl",i.dir==="rtl"),l(),me(i.bottomLeftInstances),l(2),xe("bottom",i.bottom)("right","0px"),G("ant-notification-rtl",i.dir==="rtl"),l(),me(i.bottomRightInstances),l(2),xe("top",i.top)("left","50%")("transform","translateX(-50%)"),G("ant-notification-rtl",i.dir==="rtl"),l(),me(i.topInstances),l(2),xe("bottom",i.bottom)("left","50%")("transform","translateX(-50%)"),G("ant-notification-rtl",i.dir==="rtl"),l(),me(i.bottomInstances))},dependencies:[Rr],encapsulation:2,changeDetection:0})}}return n})(),Hr=(()=>{class n{static{this.\u0275fac=function(t){return new(t||n)}}static{this.\u0275mod=Qt({type:n})}static{this.\u0275inj=Jt({imports:[Rr,Dr]})}}return n})(),go=0,Pr=(()=>{class n extends Yt{constructor(e,t,i){super(e,t,i),this.componentPrefix="notification-"}success(e,t,i){return this.create("success",e,t,i)}error(e,t,i){return this.create("error",e,t,i)}info(e,t,i){return this.create("info",e,t,i)}warning(e,t,i){return this.create("warning",e,t,i)}blank(e,t,i){return this.create("blank",e,t,i)}create(e,t,i,r){return this.createInstance({type:e,title:t,content:i},r)}template(e,t){return this.createInstance({template:e},t)}generateMessageId(){return`${this.componentPrefix}-${go++}`}createInstance(e,t){return this.container=this.withContainer(Dr),this.container.create(ye($({},e),{createdAt:new Date,messageId:t?.nzKey||this.generateMessageId(),options:t}))}static{this.\u0275fac=function(t){return new(t||n)(_(fn),_(un),_(qt))}}static{this.\u0275prov=b({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})();var vo=["toastTpl"],Co=["closeTpl"];function yo(n,o){if(n&1&&(m(0,"div",6),M(1),d()),n&2){let e=k().data;l(),D(" ",e==null?null:e.message," ")}}function ko(n,o){if(n&1&&(m(0,"div",2),y(1,"div",3),m(2,"div",4)(3,"div",5),M(4),d(),T(5,yo,2,1,"div",6),d()()),n&2){let e=o.data;l(4),Le(e.title),l(),E(e.message?5:-1)}}function To(n,o){n&1&&y(0,"div",7)}var zr=(()=>{class n{nzNotificationService;toastService;destroyRef;data;toastTpl;closeTpl;toast$;constructor(e,t,i){this.nzNotificationService=e,this.toastService=t,this.destroyRef=i}ngOnInit(){this.toast$=this.toastService.getToast(),this.toast$.pipe(re(this.destroyRef)).subscribe(e=>{let t={nzData:e,nzDuration:e.duration||2e3,nzPlacement:e.placement||"bottom",nzClass:e.class||" app-toast app-toast--"+e.type,nzCloseIcon:this.closeTpl,nzAnimate:!0};this.nzNotificationService.template(this.toastTpl,t)})}closeToast(){this.nzNotificationService.remove()}static \u0275fac=function(t){return new(t||n)(K(Pr),K(wn),K(q))};static \u0275cmp=z({type:n,selectors:[["app-toast"]],viewQuery:function(t,i){if(t&1&&(ht(vo,5),ht(Co,5)),t&2){let r;pt(r=mt())&&(i.toastTpl=r.first),pt(r=mt())&&(i.closeTpl=r.first)}},inputs:{data:"data"},standalone:!0,features:[L],decls:4,vars:0,consts:[["toastTpl",""],["closeTpl",""],[1,"app-toast--inner"],[1,"app-toast--icon"],[1,"app-toast--content"],[1,"app-toast--content-title"],[1,"app-toast--content-message"],[1,"app-toast--close"]],template:function(t,i){t&1&&T(0,ko,6,2,"ng-template",null,0,ti)(2,To,1,0,"ng-template",null,1,ti)},dependencies:[ee,Hr]})}return n})();var Fr=(()=>{class n{title="bidv-omni-v3-test";language;tabid="";store$;checkVersionInterval$=yi(0,Qi).subscribe(()=>this.loadVersion());http=p(At);storageService=p(O);translateService=p(Z);storeService=p(O);translate=p(Z);cryptoService=p(oe);modalService=p(V);commonService=p(we);helperService=p(it);themeService=p(Ae);element=p(bt);destroyRef=p(q);constructor(){this.store$=this.storeService.changes.subscribe(e=>{e.key===ae.LANGUAGE&&(this.translate.use(e.value),this.translate.currentLang=e.value,this.language=e.value)}),this.tabid=this.helperService.randomString()}ngOnInit(){this.language=this.storeService.language,this.commonService.setLanguage(this.language),this.element.nativeElement.setAttribute("data-tabid",this.tabid);let{Vi:e,En:t}=ue;this.translateService.addLangs([e,t]),this.translateService.setDefaultLang(this.language),this.translate.currentLang=this.language,this.translateService.use(this.language),this.themeService.setTheme()}ngAfterViewInit(){this.cryptoService.genKeys()}loadVersion(){return je(this,null,function*(){try{let e=yield ot(this.http.get(`./version.json?r=${Date.now()}`));if(!e.version)return;let{version:t}=this.storageService;if(t===e.version)return;this.storageService.version=e.version,this.storageService.cleanCacheStorage(),window.location.reload()}catch{}})}ngOnDestroy(){this.modalService.toggleShowOneAtTime(!1)}static \u0275fac=function(t){return new(t||n)};static \u0275cmp=z({type:n,selectors:[["app-root"]],standalone:!0,features:[L],decls:4,vars:0,template:function(t,i){t&1&&y(0,"router-outlet")(1,"app-network")(2,"app-loading")(3,"app-toast")},dependencies:[Qe,te,wr,Zn,zr]})}return n})();if(A.production){let n=localStorage.getItem(ae.MODE_DEBUG);window&&(!n||n&&n==="OFF")&&(window.console.group=window.console.time=window.console.timeEnd=window.console.groupCollapsed=window.console.groupEnd=window.console.log=window.console.warn=window.console.info=function(){})}Gi(Fr,Mr).catch(n=>console.error(n));
