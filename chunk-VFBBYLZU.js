import{b as $,g as ee,h as te,i as ie,j as ne,k as oe,l as re,n as ae}from"./chunk-5VBIWGCV.js";import{a as K,d as Q,g as W,m as X,r as Y,w as Z}from"./chunk-AASME2FE.js";import{Bd as P,Cd as G,Ld as j,Pd as U,Pe as J,W as k,Y as S}from"./chunk-K5H3SJL5.js";import{$b as x,Bc as D,Cb as R,Cc as O,Cd as N,Dc as A,Ea as C,Ec as d,Ed as B,Fa as v,Hb as s,Ia as M,Jb as o,Jd as L,Lc as _,Mc as g,Nb as h,Od as q,Qc as E,Qd as H,Rb as c,Wb as r,Xb as a,Yb as u,ac as T,cc as w,ec as l,ga as b,nc as I,ob as n,oc as z,pc as F,rc as V,sa as y}from"./chunk-YK6FMNSY.js";var se=t=>({"input-wrapper-disabled":t}),ce=t=>({disabled:t}),me=t=>({minRows:1,maxRows:t}),ue=()=>({}),le=t=>({pasteError:t});function de(t,p){if(t&1&&(r(0,"div",11)(1,"div",12),x(2,7),a()()),t&2){let e=l();n(2),o("ngTemplateOutlet",e.prefix||null)}}function fe(t,p){if(t&1){let e=T();r(0,"a",15),w("click",function(i){C(e);let f=l(2);return v(f.clearFn(i))}),a()}t&2&&o("mute",!0)("iconOnly",!0)("iconColorChange",!1)("size","lg")("prefixIcon","assets/media/icons/solid/cancel.svg")}function xe(t,p){if(t&1&&(r(0,"div",13)(1,"div",12),s(2,fe,1,5,"a",14),x(3,7),a()()),t&2){let e=l();n(2),c(e.clear&&(e.control!=null&&e.control.value)&&!(e.control!=null&&e.control.disabled)?2:-1),n(),o("ngTemplateOutlet",e.suffix||null)}}function _e(t,p){if(t&1&&u(0,"div",2),t&2){let e=l();o("forId",e.id)("label",e.label)("allowShowRequired",e.required&&e.showRequired)("allowShowTooltip",e.showTooltip)("tooltipIcon",e.tooltipIcon)("tooltip",e.tooltip)("tooltipTpl",e.tooltipTpl)}}function ge(t,p){if(t&1&&x(0,7),t&2){l();let e=I(1);o("ngTemplateOutlet",e)}}function Ce(t,p){if(t&1&&(r(0,"p",20),z(1),_(2,"number"),_(3,"number"),a()),t&2){let e=l(2);n(),V(" ",e.control.value!=null&&e.control.value.length?g(2,2,e.control.value==null?null:e.control.value.length):0,"/",g(3,4,e.counter||e.maxLength)," ")}}function ve(t,p){if(t&1&&u(0,"app-svg",21),t&2){let e=l(3);o("size",5)("src",e.hintIcon)}}function he(t,p){if(t&1&&(r(0,"div",18),s(1,ve,1,2,"app-svg",21),r(2,"p"),z(3),a()()),t&2){let e=l(2);n(),c(e.showHintIcon?1:-1),n(2),F(e.hint)}}function Te(t,p){if(t&1&&u(0,"app-validate-error",19),t&2){let e=l(2);o("errors",e.control.errors)("errorMessages",e.errorMessages)}}function we(t,p){if(t&1&&(r(0,"div",10),s(1,Ce,4,6,"p",16),r(2,"div",17),s(3,he,4,2,"div",18)(4,Te,1,2,"app-validate-error",19),a()()),t&2){let e=l();n(),o("ngIf",e.counter),n(2),c(e.hint&&!(e.control.invalid&&e.control.touched)?3:-1),n(),c(e.control.invalid&&e.control.touched?4:-1)}}function Ie(t,p){t&1&&(r(0,"div",10),u(1,"app-validate-error",19),_(2,"translate"),a()),t&2&&(n(),o("errors",d(5,le,A(4,ue)))("errorMessages",d(7,le,g(2,2,"errors.pasted_data_has_been_shortened"))))}var Ke=(()=>{class t extends ne{maxLength;maxRows=10;label;direction=k.Horizontal;id="textarea"+j(10);size=S.Large;clear=!0;placeholder="Enter content";class;tooltip;autocomplete="off";errorMessages;counter=100;nzAutocomplete;prefix;suffix;tooltipTpl;tooltipIcon="assets/media/icons/outline/alert-information.svg";showTooltip=!1;showRequired=!1;hint;showHintIcon=!1;hintIcon="media/icons/solid/alert-information.svg";get hostClass(){return this.initClass()}pasteError=!1;initClass(){let e=["app-input app-textarea",this.direction.toString()];return this.size!==S.Medium&&e.push(this.size),e.join(" ")}clearFn(e){this.control?.setValue(null)}static \u0275fac=(()=>{let e;return function(i){return(e||(e=M(t)))(i||t)}})();static \u0275cmp=y({type:t,selectors:[["app-textarea"]],hostVars:2,hostBindings:function(m,i){m&2&&h(i.hostClass)},inputs:{maxLength:"maxLength",maxRows:"maxRows",label:"label",direction:"direction",id:"id",size:"size",clear:"clear",placeholder:"placeholder",class:"class",tooltip:"tooltip",autocomplete:"autocomplete",errorMessages:"errorMessages",counter:"counter",nzAutocomplete:"nzAutocomplete",prefix:"prefix",suffix:"suffix",tooltipTpl:"tooltipTpl",tooltipIcon:"tooltipIcon",showTooltip:"showTooltip",showRequired:"showRequired",hint:"hint",showHintIcon:"showHintIcon",hintIcon:"hintIcon"},standalone:!0,features:[D([{provide:K,useExisting:b(()=>t),multi:!0}]),R,O],decls:13,vars:20,consts:[["prefixTpl",""],["suffixTpl",""],["app-label-input","",3,"forId","label","allowShowRequired","allowShowTooltip","tooltipIcon","tooltip","tooltipTpl"],[1,"app-root"],[1,"input-wrapper",3,"ngClass"],[3,"ngTemplateOutlet",4,"ngIf"],["nz-input","","aria-label","text-area",1,"input","textarea",3,"pasteError","id","ngClass","formControl","placeholder","nzAutosize","maxlength"],[3,"ngTemplateOutlet"],[1,"frame"],["class","explain",4,"ngIf"],[1,"explain"],[1,"input-prefix"],[1,"wrapper"],[1,"input-suffix"],["app-button","",1,"clear-icon",3,"mute","iconOnly","iconColorChange","size","prefixIcon"],["app-button","",1,"clear-icon",3,"click","mute","iconOnly","iconColorChange","size","prefixIcon"],["class","input-counter",4,"ngIf"],[1,"error-hint"],[1,"hint"],[3,"errors","errorMessages"],[1,"input-counter"],[1,"hint-icon",3,"size","src"]],template:function(m,i){if(m&1){let f=T();s(0,de,3,1,"ng-template",null,0,E)(2,xe,4,2,"ng-template",null,1,E)(4,_e,1,7,"div",2),r(5,"div",3)(6,"div",4),s(7,ge,1,1,"ng-container",5),r(8,"textarea",6),w("pasteError",function(pe){return C(f),v(i.pasteError=pe)}),a(),x(9,7),u(10,"div",8),a(),s(11,we,5,3,"div",9)(12,Ie,3,9,"div",10),a()}if(m&2){let f=I(3);n(4),c(i.label?4:-1),n(2),h(i.size!=="default"?" input-wrapper-"+i.size:" "),o("ngClass",d(14,se,i.control.disabled)),n(),o("ngIf",i.prefix),n(),o("id",i.id)("ngClass",d(16,ce,i.control.disabled))("formControl",i.control)("placeholder",i.placeholder)("nzAutosize",d(18,me,i.maxRows))("maxlength",i.maxLength),n(),o("ngTemplateOutlet",f),n(2),o("ngIf",i.control.invalid&&i.control.touched||i.hint||i.counter),n(),c(i.pasteError?12:-1)}},dependencies:[H,N,B,L,q,Z,Q,W,Y,X,oe,U,J,ie,ee,te,$,re,ae,G,P]})}return t})();export{Ke as a};
