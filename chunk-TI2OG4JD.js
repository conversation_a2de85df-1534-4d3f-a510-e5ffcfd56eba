import{td as s,ud as m,y as a}from"./chunk-K5H3SJL5.js";import{La as n,_c as c,na as o,va as i}from"./chunk-YK6FMNSY.js";var L=(()=>{class r{currentLang;commonService=o(s);cdr=o(c);destroyRef=o(n);constructor(){this.commonService.getLanguage().pipe(m(this.destroyRef)).subscribe(e=>{e&&(this.currentLang=e,this.cdr.markForCheck())})}transform(e,t,f){let p=this.currentLang===a.En?f:t;return e?.[p]||e?.[t]||""}static \u0275fac=function(t){return new(t||r)};static \u0275pipe=i({name:"localizeField",type:r,pure:!1,standalone:!0})}return r})();export{L as a};
