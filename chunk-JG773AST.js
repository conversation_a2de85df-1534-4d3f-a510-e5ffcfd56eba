import{a as ve}from"./chunk-SX5BIO4E.js";import{a as Ce}from"./chunk-54E4CCOA.js";import{e as ue,g as pe,h as me,l as he,n as _e,q as de,u as fe,w as ge}from"./chunk-AASME2FE.js";import{S as Ae,U as Se,_ as De}from"./chunk-AX4DCRSD.js";import{Ad as J,Af as ce,Bd as ee,Bf as le,Cd as te,H as P,Ia as A,Kc as Z,Kd as b,Le as ie,Me as re,Pe as oe,ab as j,bb as S,cb as X,jf as se,kf as ae,mf as ne,nf as o,q as z}from"./chunk-K5H3SJL5.js";import{a as D}from"./chunk-2WPD26RB.js";import{Cc as F,Dc as Q,Ea as y,F as U,Fa as T,H as Y,Hb as E,Jb as d,Lc as h,M,Mc as _,Qc as N,Rb as W,Wb as p,Xb as u,Yb as B,ac as K,cc as x,da as V,ec as f,g as m,ha as $,na as n,nc as I,o as G,ob as l,oc as g,pc as O,qc as H,rc as w,sa as q}from"./chunk-YK6FMNSY.js";import{a as L,b as R}from"./chunk-TSRGIXR5.js";var ke="YYYYMMDDHHmmss";var Ge={QUA_HET_SOCK:"LOYALTY-22",HET_QUA_TRONG_KHO:"LOYALTY-14",QUA_DANG_SHOCK:"LOYALTY-23",HET_SO_LUONG:"LOYALTY-40",HET_THOI_GIAN:"LOYALTY-41",KHONH_DU_SO_LUONG:"LOYALTY-42"};var Le="1";var Re=function(t){return t.TopUp="1001",t}(Re||{}),k=function(t){return t.Normal="0",t.Shocking="1",t}(k||{}),C=function(t){return t.TopUp="1001",t.Vnshop="2001",t.Cashback="3001",t.ChangeMiles="4001",t.EVoucher="0001",t.TVFee="7001",t.TrafficAccount="8001",t.Education="9001",t.GotIt="5001",t.BuyCoursePackage="6001",t.ChangeInterestRate="1102",t}(C||{}),ye=function(t){return t.VoucherCode="voucherCode",t}(ye||{}),Te=function(t){return t.LicensePlate="1",t.ContractNumber="2",t}(Te||{}),xe=function(t){return t.VtvCabon="VTVCABON",t.FptPlay="FPTPLAY",t}(xe||{}),Ie=function(t){return t[t.AccumulatePoint=0]="AccumulatePoint",t[t.RevokePoint=1]="RevokePoint",t[t.ChangeGift=2]="ChangeGift",t[t.ReturnUsedPoint=3]="ReturnUsedPoint",t}(Ie||{});var He=()=>({required:"Vui l\xF2ng ch\u1ECDn"});function Ne(t,v){if(t&1&&(p(0,"p",8),g(1),h(2,"translate"),h(3,"capitalizeFirstChar"),h(4,"thousandSeparator"),u()),t&2){let e=f().item;l(),w(" ",_(3,4,_(2,2,"reward_home.remaining")),": ",_(4,6,e==null?null:e.remainingQuantity)," ")}}function Pe(t,v){if(t&1&&(p(0,"div",5)(1,"p",6),g(2),u(),p(3,"div",7),g(4),u(),E(5,Ne,5,8,"p",8),u()),t&2){let e=v.item,i=v.checked,r=f();l(2),O(e.title),l(),d("color",i?r.UI.BadgeColor.Primary:r.UI.BadgeColor.SubPrimary),l(),H(" ",e==null?null:e.subtitle," "),l(),W(e!=null&&e.remainingQuantity?5:-1)}}function be(t,v){if(t&1){let e=K();p(0,"a",9),x("click",function(){y(e);let r=f();return T(r.back())}),g(1),h(2,"translate"),u(),p(3,"a",10),x("click",function(){y(e);let r=f();return T(r.next())}),g(4),h(5,"translate"),u()}if(t&2){let e=f();d("color",e.UI.ButtonColor.Outline),l(),H(" ",_(2,3,"btn.close")," "),l(3),O(_(5,5,"btn.tiep_tuc"))}}var Ee=(()=>{class t{form;parOptions=[];prices=[];fb=n(fe);UI=n(se);modalRef=n(re);modalData=n(ie);ngOnInit(){this.form=this.fb.group({parValue:[null,ue.required]}),this.prices=this.modalData?.data?.price,this.prices?.length&&(this.parOptions=this.prices.map(e=>{let i=e?.shock?.exchangePoint??e?.exchangePoint??0;return{title:`${b(e.priceValue)} VND`,value:e.priceId,subtitle:`${b(i)} B-Point`,remainingQuantity:+(e?.shock?.remainingQuantity??0)}}),this.form.patchValue({parValue:this.parOptions[0]?.value}))}back(){this.modalRef.destroy()}next(){if(this.form.invalid){this.form.markAsTouched();return}let e=this.form.value?.parValue,i=this.prices.find(r=>r.priceId===e);this.modalRef.destroy(i)}static \u0275fac=function(i){return new(i||t)};static \u0275cmp=q({type:t,selectors:[["bidv-omni-rewards-loyalty-exchange-gift-modal-select-parvalue"]],standalone:!0,features:[F],decls:8,vars:12,consts:[["contentTpl",""],["actionsTpl",""],[3,"title","actions"],[1,"py-3",3,"formGroup"],["formControlName","parValue",3,"data","type","contentTpl","size","extendClass","errorMessages"],[1,"content"],[1,"title"],["app-badge","",3,"color"],[1,"text-body-md-medium","text-danger","mt-2"],["app-button","",3,"click","color"],["app-button","",3,"click"]],template:function(i,r){if(i&1&&(p(0,"app-modal-base",2),h(1,"translate"),p(2,"form",3),B(3,"app-radio",4),E(4,Pe,6,4,"ng-template",null,0,N),u(),E(6,be,6,7,"ng-template",null,1,N),u()),i&2){let s=I(5),a=I(7);d("title",_(1,9,"reward_home.select_par_value"))("actions",a),l(2),d("formGroup",r.form),l(),d("data",r.parOptions)("type",r.UI.RadioType.Card)("contentTpl",s)("size",r.UI.PillSize.Sm)("extendClass","radio-group-promotion")("errorMessages",Q(11,He))}},dependencies:[ge,he,pe,me,_e,de,ae,oe,ve,De,te,ee,Ce,Se]})}return t})();var St=(()=>{class t{loyaltyData$=new m(void 0);shockDealList$=new m(void 0);recommendedGifts$=new m(void 0);giftCategories$=new m(void 0);suppliersData$=new m(new Map);hasLoyaltyAccount$=new m(!0);storageService=n(Z);apiService=n(ce);toastService=n(Ae);loadingService=n(le);modalService=n(ne);translateService=n(J);router=n(z);constructor(){this.apiService.setRewardsService(this)}checkLoyaltyAccount(e){let{user:i}=this.storageService.userInfo,r=R(L({},e),{user:i,source:A});return this.apiService.requestProcess(r,o.REWARDS.KIEM_TRA_TAI_KHOAN)}getAreaList(e){return this.apiService.requestProcess(e,o.REWARDS.LAY_DANH_SACH_KHU_VUC)}getGiftCategory(e){return this.apiService.requestProcess(e,o.REWARDS.LAY_DANH_MUC_QUA_TANG)}getYourFavoriteGiftList(e){return this.apiService.requestProcess(e,o.REWARDS.LAY_DANH_SACH_QUA_TANG_YEU_THICH)}getSuggestedGiftsList(e){return this.apiService.requestProcess(e,o.REWARDS.LAY_DANH_SACH_QUA_TANG_GOI_Y)}getGiftDetails(e){return this.apiService.requestProcess(e,o.REWARDS.LAY_CHI_TIET_QUA_TANG)}getMyGiftList(e){return this.apiService.requestProcess(e,o.REWARDS.LAY_DANH_SACH_QUA_TANG_CUA_TOI)}getGiftList(e){return console.log("params",e),this.apiService.requestProcess(e,o.REWARDS.LAY_DANH_SACH_QUA_TANG)}getMyGiftDetails(e){return this.apiService.requestProcess(e,o.REWARDS.LAY_CHI_TIET_QUA_TANG_CUA_TOI)}getThreeMonthHistory(e){return this.apiService.requestProcess(e,o.REWARDS.LAY_LICH_SU_3_THANG_GAN_NHAT)}likeGift(e){return this.apiService.requestProcess(e,o.REWARDS.LIKE).pipe(V(i=>{i&&i.des&&(e.status===Le?this.toastService.success(i.des):this.toastService.success({title:"Unlike "+i.des}))}),Y(i=>(i.message&&this.toastService.error({title:i.message}),G(i))))}getHistoryByConditions(e){return this.apiService.requestProcess(e,o.REWARDS.LAY_LICH_SU_THEO_DIEU_KIEN)}getHistoryDetails(e){return this.apiService.requestProcess(e,o.REWARDS.LAY_CHI_TIET_LICH_SU)}loyaltyTradingInspection(e){return this.apiService.requestProcess(e,o.REWARDS.TRA_SOAT)}getHomeCategory(e){return this.apiService.requestProcess(e,o.REWARDS.DANH_MUC_MAN_HOME)}checkExchangingGiftsConditions(e){return this.apiService.requestProcess(e,o.REWARDS.DOI_QUA.KIEM_TRA_DIEU_KIEN_DOI_QUA)}initExchangingGifts(e){return this.apiService.requestProcess(e,o.REWARDS.DOI_QUA.KHOI_TAO)}confirmExchangingGifts(e){return this.apiService.requestProcess(e,o.REWARDS.DOI_QUA.XAC_NHAN)}getListLoyaltyCard(e){return this.apiService.requestProcess(e,o.REWARDS.DANH_SACH_THE)}getSuppliersList(e){return this.apiService.requestProcess(e,o.REWARDS.DANH_SACH_NCC)}getListCourse(e){return this.apiService.requestProcess(e,o.REWARDS.DANH_SACH_LOAI_BIEN)}getListLicensePlates(e){return this.apiService.requestProcess(e,o.REWARDS.DANH_SACH_LOAI_BIEN)}getDataFromDMS(e){return this.apiService.requestProcess(e,o.REWARDS.DANH_SACH_LOAI_BIEN)}getScoreAccountStatement(e){return this.apiService.requestProcess(e,o.REWARDS.SAO_KE_TAI_KHOAN_DIEM)}checkLoyaltyPaymentConditions(e){return this.apiService.requestProcess(e,o.REWARDS.KIEM_TRA_DIEU_KIEN_THANH_TOAN_DIEM_LOYALTY_BANG_OMNI)}getShockDealCategory(e){return this.apiService.requestProcess(e,o.REWARDS.DANH_SACH_DEAL_SOC)}changeBpointToRate(e){return this.apiService.requestProcess(e,o.REWARDS.DOI_BPOINT_THANH_LAI_SUAT)}fetchLoyaltyData(e,i=!1){this.loadingService.showLoading(),!(this.loyaltyData$.getValue()&&!i)&&this.checkLoyaltyAccount({}).subscribe({next:s=>{s?.code===S.success&&(this.storageService.userInfo=R(L({},this.storageService.userInfo),{memberId:s?.data?.memberId}),this.setLoyaltyData(s?.data))},error:s=>{e&&(s?.error?.code===X.NOT_A_LOYALTY_MEMBER?this.modalService.confirm({message:s?.error?.des,btnCancel:{title:this.translateService.instant("common.da_hieu"),color:P.Outline},btnConfirm:{title:this.translateService.instant("common.xem_the_le"),color:P.Primary},cancel:()=>{this.router.navigate([D.Rewards])},confirm:()=>{window.open(j.faq,"_blank")}}):this.modalService.error({message:s?.error?.des,confirm:()=>{this.router.navigate([D.Rewards])}}))}})}fetchShockDeals(e,i=!1){if(this.shockDealList$.getValue()&&!i)return;let s={source:A,memberId:e,page:0,size:20};this.getShockDealCategory(s).subscribe({next:a=>{a?.code===S.success&&this.setShockDealList(a?.data)},error:a=>{this.modalService.error(a?.error?.des)}})}fetchRecommendedGifts(e,i=!1){if(this.loadingService.showLoading(),this.shockDealList$.getValue()&&!i)return;let s={source:A,memberId:e};this.getSuggestedGiftsList(s).subscribe({next:a=>{a?.code===S.success&&this.setRecommendedGifts(a?.data)},error:a=>{this.modalService.error(a?.error?.des)}})}fetchGiftCategories(e=!1){if(this.loadingService.showLoading(),this.giftCategories$.getValue()&&!e)return;let r={source:A};this.getHomeCategory(r).subscribe({next:s=>{s?.code===S.success&&this.setGiftCategories(s?.data)},error:s=>{this.modalService.error(s?.error?.des)}})}fetchSuppliersData(e,i=!1){this.loadingService.showLoading();let r=this.suppliersData$.getValue();if(r?.get(e)?.length&&!i)return;let a={source:A,giftTypeId:e};this.getSuppliersList(a).subscribe({next:c=>{c?.code===S.success&&(r.set(e,c?.data),this.setSuppliersData(r))},error:c=>{this.modalService.error(c?.error?.des)}})}getLoyaltyData(e=!0){return e&&(this.loyaltyData$.getValue()||this.fetchLoyaltyData(e)),this.loyaltyData$.asObservable()}setLoyaltyData(e){this.loyaltyData$.next(e)}getHasLoyaltyAccount(){return this.hasLoyaltyAccount$.asObservable()}setHasLoyaltyAccount(e){this.hasLoyaltyAccount$.next(e)}getShockDealList(e){return this.shockDealList$.getValue()?.length||this.fetchShockDeals(e),this.shockDealList$.asObservable()}setShockDealList(e){this.shockDealList$.next(e)}getRecommendedGifts(e){return this.recommendedGifts$.getValue()?.length||this.fetchRecommendedGifts(e),this.recommendedGifts$.asObservable()}setRecommendedGifts(e){this.recommendedGifts$.next(e)}getGiftCategories(){return this.giftCategories$.getValue()?.length||this.fetchGiftCategories(),this.giftCategories$.asObservable()}setGiftCategories(e){this.giftCategories$.next(e)}getSuppliersData(e){return this.suppliersData$.getValue()?.get(e)?.length||this.fetchSuppliersData(e),this.suppliersData$.asObservable()}setSuppliersData(e){this.suppliersData$.next(e)}checkConditionExchangePoint(e,i){let r=this.loyaltyData$.getValue(),{memberId:s}=r||{};this.loadingService.showLoading();let a=L({memberId:s,giftId:e.giftId,shockFlag:e.shockFlag??k.Normal},!!i&&{priceId:i?.priceId});this.checkExchangingGiftsConditions(a).pipe(U(c=>!!c),M(1)).subscribe({next:c=>{this.router.navigate([`/${D.Rewards}/${D.RewardsExchangeGift}`],{state:{price:i,giftDetail:e,changeGiftCondition:c?.data}})},error:c=>{this.modalService.error(c?.error?.des||c?.message)}})}handleExchangeBPoint(e){[C.EVoucher,C.GotIt].includes(e.giftTypeId)?this.modalService.open(Ee,{data:{price:e?.price?.sort((r,s)=>+(r?.exchangePoint??0)-+(s?.exchangePoint??0))||[]}})?.afterClose.subscribe(r=>{r&&this.checkConditionExchangePoint(e,r)}):e.giftTypeId===C.Vnshop?this.modalService.info(this.translateService.instant("reward_error.cannot_change_vnshop_gift")):this.checkConditionExchangePoint(e)}resetData(){this.setLoyaltyData(),this.setShockDealList(),this.setRecommendedGifts(),this.setGiftCategories(),this.setHasLoyaltyAccount(!0)}static \u0275fac=function(i){return new(i||t)};static \u0275prov=$({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();export{ke as a,Ge as b,Re as c,k as d,C as e,ye as f,Te as g,xe as h,Ie as i,St as j};
