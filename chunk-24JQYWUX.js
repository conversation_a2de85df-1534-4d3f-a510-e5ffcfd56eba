import{Bd as H,Cd as N,n as O,q as P,r as R,v as D}from"./chunk-K5H3SJL5.js";import{Cc as F,Ed as w,F as h,Hb as g,Jb as p,Kd as E,Lb as y,Lc as a,Mc as o,Qd as M,Rb as $,Sb as k,Ub as I,Vb as S,Wb as m,Xb as s,Zb as L,_b as T,ec as b,g as _,ha as B,ma as v,na as x,ob as n,oc as u,pc as l,qc as j,sa as C}from"./chunk-YK6FMNSY.js";var A=(()=>{class t{router;_keyBreadcrumb="breadcrumb";_breadcrumbs$=new _([]);breadcrumbs$=this._breadcrumbs$.asObservable();constructor(e){this.router=e,this._breadcrumbs$.next(this.buildBreadcrumb(this.router.routerState.snapshot.root)),this.router.events.pipe(h(r=>r instanceof O)).subscribe(r=>{this._breadcrumbs$.next(this.buildBreadcrumb(this.router.routerState.snapshot.root))})}getBreadcrumb(){return this._breadcrumbs$.asObservable()}buildBreadcrumb(e,r=[],i=[]){if(e){let f=r.concat(e.url.map(d=>d.path));if(e.data[this._keyBreadcrumb]){let d={label:this.getLabel(e.data),url:"/"+f.join("/")};i.push(d)}e.firstChild&&this.buildBreadcrumb(e.firstChild,f,i)}return i}getLabel(e){return typeof e[this._keyBreadcrumb]=="function"?e[this._keyBreadcrumb](e):e[this._keyBreadcrumb]}static \u0275fac=function(r){return new(r||t)(v(P))};static \u0275prov=B({token:t,factory:t.\u0275fac,providedIn:"root"})}return t})();function q(t,c){if(t&1&&(m(0,"a",3)(1,"div"),u(2),a(3,"translate"),s()()),t&2){let e=b().$implicit;p("routerLink",e==null?null:e.url),n(2),l(o(3,2,e.label))}}function z(t,c){if(t&1&&(m(0,"div",2)(1,"div"),u(2),a(3,"translate"),s()()),t&2){let e=b().$implicit;n(2),l(o(3,1,e.label))}}function G(t,c){if(t&1&&(L(0),g(1,q,4,4,"a",1)(2,z,4,3,"div",2),T()),t&2){let e=c.$index,r=c.$count;n(),p("ngIf",e!==r-1),n(),$(e===r-1?2:-1)}}var oe=(()=>{class t{wrap=!0;breadcrumbService=x(A);breadcrumbs$;ngOnInit(){this.breadcrumbs$=this.breadcrumbService.getBreadcrumb()}static \u0275fac=function(r){return new(r||t)};static \u0275cmp=C({type:t,selectors:[["app-breadcrumb"]],hostVars:2,hostBindings:function(r,i){r&2&&y("breadcrumb",i.wrap)},standalone:!0,features:[F],decls:6,vars:5,consts:[["routerLink","/",1,"breadcrumb-item"],["class","breadcrumb-item",3,"routerLink",4,"ngIf"],[1,"breadcrumb-item"],[1,"breadcrumb-item",3,"routerLink"]],template:function(r,i){r&1&&(m(0,"a",0),u(1),a(2,"translate"),s(),I(3,G,3,2,"ng-container",null,k),a(5,"async")),r&2&&(n(),j(" ",o(2,1,"trang_chu"),`
`),n(2),S(o(5,3,i.breadcrumbs$)))},dependencies:[M,w,E,D,R,N,H]})}return t})();export{oe as a};
