import{Bb as w,Bc as p,Cb as u,Da as D,Ia as b,Ib as ne,Lb as re,Na as V,Qa as te,Sc as De,_c as be,cc as B,cd as Ae,ed as I,f as _e,fa as R,fd as m,ga as f,ha as Ve,ia as T,jd as se,ka as C,m as ve,pb as o,t as ye,ta as j,ua as l,vb as ie,z as Ce}from"./chunk-YK6FMNSY.js";import{a as c,b as h}from"./chunk-TSRGIXR5.js";var xe=(()=>{class i{constructor(e,n){this._renderer=e,this._elementRef=n,this.onChange=r=>{},this.onTouched=()=>{}}setProperty(e,n){this._renderer.setProperty(this._elementRef.nativeElement,e,n)}registerOnTouched(e){this.onTouched=e}registerOnChange(e){this.onChange=e}setDisabledState(e){this.setProperty("disabled",e)}static{this.\u0275fac=function(n){return new(n||i)(o(ie),o(te))}}static{this.\u0275dir=l({type:i})}}return i})(),Pe=(()=>{class i extends xe{static{this.\u0275fac=(()=>{let e;return function(r){return(e||(e=b(i)))(r||i)}})()}static{this.\u0275dir=l({type:i,features:[u]})}}return i})(),k=new C(""),ot={provide:k,useExisting:f(()=>at),multi:!0},at=(()=>{class i extends Pe{writeValue(e){this.setProperty("checked",e)}static{this.\u0275fac=(()=>{let e;return function(r){return(e||(e=b(i)))(r||i)}})()}static{this.\u0275dir=l({type:i,selectors:[["input","type","checkbox","formControlName",""],["input","type","checkbox","formControl",""],["input","type","checkbox","ngModel",""]],hostBindings:function(n,r){n&1&&B("change",function(a){return r.onChange(a.target.checked)})("blur",function(){return r.onTouched()})},features:[p([ot]),u]})}}return i})(),lt={provide:k,useExisting:f(()=>ke),multi:!0};function ut(){let i=se()?se().getUserAgent():"";return/android (\d+)/.test(i.toLowerCase())}var ct=new C(""),ke=(()=>{class i extends xe{constructor(e,n,r){super(e,n),this._compositionMode=r,this._composing=!1,this._compositionMode==null&&(this._compositionMode=!ut())}writeValue(e){let n=e??"";this.setProperty("value",n)}_handleInput(e){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(e)}_compositionStart(){this._composing=!0}_compositionEnd(e){this._composing=!1,this._compositionMode&&this.onChange(e)}static{this.\u0275fac=function(n){return new(n||i)(o(ie),o(te),o(ct,8))}}static{this.\u0275dir=l({type:i,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(n,r){n&1&&B("input",function(a){return r._handleInput(a.target.value)})("blur",function(){return r.onTouched()})("compositionstart",function(){return r._compositionStart()})("compositionend",function(a){return r._compositionEnd(a.target.value)})},features:[p([lt]),u]})}}return i})();function _(i){return i==null||(typeof i=="string"||Array.isArray(i))&&i.length===0}function Ge(i){return i!=null&&typeof i.length=="number"}var y=new C(""),F=new C(""),dt=/^(?=.{1,254}$)(?=.{1,64}@)[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,Me=class{static min(t){return ht(t)}static max(t){return ft(t)}static required(t){return pt(t)}static requiredTrue(t){return gt(t)}static email(t){return mt(t)}static minLength(t){return _t(t)}static maxLength(t){return Re(t)}static pattern(t){return Te(t)}static nullValidator(t){return H(t)}static compose(t){return $e(t)}static composeAsync(t){return qe(t)}};function ht(i){return t=>{if(_(t.value)||_(i))return null;let e=parseFloat(t.value);return!isNaN(e)&&e<i?{min:{min:i,actual:t.value}}:null}}function ft(i){return t=>{if(_(t.value)||_(i))return null;let e=parseFloat(t.value);return!isNaN(e)&&e>i?{max:{max:i,actual:t.value}}:null}}function pt(i){return _(i.value)?{required:!0}:null}function gt(i){return i.value===!0?null:{required:!0}}function mt(i){return _(i.value)||dt.test(i.value)?null:{email:!0}}function _t(i){return t=>_(t.value)||!Ge(t.value)?null:t.value.length<i?{minlength:{requiredLength:i,actualLength:t.value.length}}:null}function Re(i){return t=>Ge(t.value)&&t.value.length>i?{maxlength:{requiredLength:i,actualLength:t.value.length}}:null}function Te(i){if(!i)return H;let t,e;return typeof i=="string"?(e="",i.charAt(0)!=="^"&&(e+="^"),e+=i,i.charAt(i.length-1)!=="$"&&(e+="$"),t=new RegExp(e)):(e=i.toString(),t=i),n=>{if(_(n.value))return null;let r=n.value;return t.test(r)?null:{pattern:{requiredPattern:e,actualValue:r}}}}function H(i){return null}function je(i){return i!=null}function Be(i){return De(i)?ve(i):i}function Ue(i){let t={};return i.forEach(e=>{t=e!=null?c(c({},t),e):t}),Object.keys(t).length===0?null:t}function He(i,t){return t.map(e=>e(i))}function vt(i){return!i.validate}function Le(i){return i.map(t=>vt(t)?t:e=>t.validate(e))}function $e(i){if(!i)return null;let t=i.filter(je);return t.length==0?null:function(e){return Ue(He(e,t))}}function We(i){return i!=null?$e(Le(i)):null}function qe(i){if(!i)return null;let t=i.filter(je);return t.length==0?null:function(e){let n=He(e,t).map(Be);return Ce(n).pipe(ye(Ue))}}function ze(i){return i!=null?qe(Le(i)):null}function Ee(i,t){return i===null?[t]:Array.isArray(i)?[...i,t]:[i,t]}function Ze(i){return i._rawValidators}function Xe(i){return i._rawAsyncValidators}function oe(i){return i?Array.isArray(i)?i:[i]:[]}function L(i,t){return Array.isArray(i)?i.includes(t):i===t}function Fe(i,t){let e=oe(t);return oe(i).forEach(r=>{L(e,r)||e.push(r)}),e}function we(i,t){return oe(t).filter(e=>!L(i,e))}var $=class{constructor(){this._rawValidators=[],this._rawAsyncValidators=[],this._onDestroyCallbacks=[]}get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_setValidators(t){this._rawValidators=t||[],this._composedValidatorFn=We(this._rawValidators)}_setAsyncValidators(t){this._rawAsyncValidators=t||[],this._composedAsyncValidatorFn=ze(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_registerOnDestroy(t){this._onDestroyCallbacks.push(t)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(t=>t()),this._onDestroyCallbacks=[]}reset(t=void 0){this.control&&this.control.reset(t)}hasError(t,e){return this.control?this.control.hasError(t,e):!1}getError(t,e){return this.control?this.control.getError(t,e):null}},d=class extends ${get formDirective(){return null}get path(){return null}},g=class extends ${constructor(){super(...arguments),this._parent=null,this.name=null,this.valueAccessor=null}},W=class{constructor(t){this._cd=t}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},yt={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},hi=h(c({},yt),{"[class.ng-submitted]":"isSubmitted"}),fi=(()=>{class i extends W{constructor(e){super(e)}static{this.\u0275fac=function(n){return new(n||i)(o(g,2))}}static{this.\u0275dir=l({type:i,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(n,r){n&2&&re("ng-untouched",r.isUntouched)("ng-touched",r.isTouched)("ng-pristine",r.isPristine)("ng-dirty",r.isDirty)("ng-valid",r.isValid)("ng-invalid",r.isInvalid)("ng-pending",r.isPending)},features:[u]})}}return i})(),pi=(()=>{class i extends W{constructor(e){super(e)}static{this.\u0275fac=function(n){return new(n||i)(o(d,10))}}static{this.\u0275dir=l({type:i,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(n,r){n&2&&re("ng-untouched",r.isUntouched)("ng-touched",r.isTouched)("ng-pristine",r.isPristine)("ng-dirty",r.isDirty)("ng-valid",r.isValid)("ng-invalid",r.isInvalid)("ng-pending",r.isPending)("ng-submitted",r.isSubmitted)},features:[u]})}}return i})();var S="VALID",U="INVALID",A="PENDING",N="DISABLED",v=class{},q=class extends v{constructor(t,e){super(),this.value=t,this.source=e}},O=class extends v{constructor(t,e){super(),this.pristine=t,this.source=e}},x=class extends v{constructor(t,e){super(),this.touched=t,this.source=e}},M=class extends v{constructor(t,e){super(),this.status=t,this.source=e}},ae=class extends v{constructor(t){super(),this.source=t}},le=class extends v{constructor(t){super(),this.source=t}};function de(i){return(J(i)?i.validators:i)||null}function Ct(i){return Array.isArray(i)?We(i):i||null}function he(i,t){return(J(t)?t.asyncValidators:i)||null}function Vt(i){return Array.isArray(i)?ze(i):i||null}function J(i){return i!=null&&!Array.isArray(i)&&typeof i=="object"}function Ye(i,t,e){let n=i.controls;if(!(t?Object.keys(n):n).length)throw new R(1e3,"");if(!n[e])throw new R(1001,"")}function Ke(i,t,e){i._forEachChild((n,r)=>{if(e[r]===void 0)throw new R(1002,"")})}var E=class{constructor(t,e){this._pendingDirty=!1,this._hasOwnPendingAsyncValidator=null,this._pendingTouched=!1,this._onCollectionChange=()=>{},this._parent=null,this._status=I(()=>this.statusReactive()),this.statusReactive=w(void 0),this._pristine=I(()=>this.pristineReactive()),this.pristineReactive=w(!0),this._touched=I(()=>this.touchedReactive()),this.touchedReactive=w(!1),this._events=new _e,this.events=this._events.asObservable(),this._onDisabledChange=[],this._assignValidators(t),this._assignAsyncValidators(e)}get validator(){return this._composedValidatorFn}set validator(t){this._rawValidators=this._composedValidatorFn=t}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(t){this._rawAsyncValidators=this._composedAsyncValidatorFn=t}get parent(){return this._parent}get status(){return m(this.statusReactive)}set status(t){m(()=>this.statusReactive.set(t))}get valid(){return this.status===S}get invalid(){return this.status===U}get pending(){return this.status==A}get disabled(){return this.status===N}get enabled(){return this.status!==N}get pristine(){return m(this.pristineReactive)}set pristine(t){m(()=>this.pristineReactive.set(t))}get dirty(){return!this.pristine}get touched(){return m(this.touchedReactive)}set touched(t){m(()=>this.touchedReactive.set(t))}get untouched(){return!this.touched}get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(t){this._assignValidators(t)}setAsyncValidators(t){this._assignAsyncValidators(t)}addValidators(t){this.setValidators(Fe(t,this._rawValidators))}addAsyncValidators(t){this.setAsyncValidators(Fe(t,this._rawAsyncValidators))}removeValidators(t){this.setValidators(we(t,this._rawValidators))}removeAsyncValidators(t){this.setAsyncValidators(we(t,this._rawAsyncValidators))}hasValidator(t){return L(this._rawValidators,t)}hasAsyncValidator(t){return L(this._rawAsyncValidators,t)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(t={}){let e=this.touched===!1;this.touched=!0;let n=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsTouched(h(c({},t),{sourceControl:n})),e&&t.emitEvent!==!1&&this._events.next(new x(!0,n))}markAllAsTouched(t={}){this.markAsTouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:this}),this._forEachChild(e=>e.markAllAsTouched(t))}markAsUntouched(t={}){let e=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let n=t.sourceControl??this;this._forEachChild(r=>{r.markAsUntouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:n})}),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,n),e&&t.emitEvent!==!1&&this._events.next(new x(!1,n))}markAsDirty(t={}){let e=this.pristine===!0;this.pristine=!1;let n=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsDirty(h(c({},t),{sourceControl:n})),e&&t.emitEvent!==!1&&this._events.next(new O(!1,n))}markAsPristine(t={}){let e=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let n=t.sourceControl??this;this._forEachChild(r=>{r.markAsPristine({onlySelf:!0,emitEvent:t.emitEvent})}),this._parent&&!t.onlySelf&&this._parent._updatePristine(t,n),e&&t.emitEvent!==!1&&this._events.next(new O(!0,n))}markAsPending(t={}){this.status=A;let e=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new M(this.status,e)),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.markAsPending(h(c({},t),{sourceControl:e}))}disable(t={}){let e=this._parentMarkedDirty(t.onlySelf);this.status=N,this.errors=null,this._forEachChild(r=>{r.disable(h(c({},t),{onlySelf:!0}))}),this._updateValue();let n=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new q(this.value,n)),this._events.next(new M(this.status,n)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(h(c({},t),{skipPristineCheck:e}),this),this._onDisabledChange.forEach(r=>r(!0))}enable(t={}){let e=this._parentMarkedDirty(t.onlySelf);this.status=S,this._forEachChild(n=>{n.enable(h(c({},t),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent}),this._updateAncestors(h(c({},t),{skipPristineCheck:e}),this),this._onDisabledChange.forEach(n=>n(!1))}_updateAncestors(t,e){this._parent&&!t.onlySelf&&(this._parent.updateValueAndValidity(t),t.skipPristineCheck||this._parent._updatePristine({},e),this._parent._updateTouched({},e))}setParent(t){this._parent=t}getRawValue(){return this.value}updateValueAndValidity(t={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let n=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===S||this.status===A)&&this._runAsyncValidator(n,t.emitEvent)}let e=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new q(this.value,e)),this._events.next(new M(this.status,e)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.updateValueAndValidity(h(c({},t),{sourceControl:e}))}_updateTreeValidity(t={emitEvent:!0}){this._forEachChild(e=>e._updateTreeValidity(t)),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?N:S}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(t,e){if(this.asyncValidator){this.status=A,this._hasOwnPendingAsyncValidator={emitEvent:e!==!1};let n=Be(this.asyncValidator(this));this._asyncValidationSubscription=n.subscribe(r=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(r,{emitEvent:e,shouldHaveEmitted:t})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let t=this._hasOwnPendingAsyncValidator?.emitEvent??!1;return this._hasOwnPendingAsyncValidator=null,t}return!1}setErrors(t,e={}){this.errors=t,this._updateControlsErrors(e.emitEvent!==!1,this,e.shouldHaveEmitted)}get(t){let e=t;return e==null||(Array.isArray(e)||(e=e.split(".")),e.length===0)?null:e.reduce((n,r)=>n&&n._find(r),this)}getError(t,e){let n=e?this.get(e):this;return n&&n.errors?n.errors[t]:null}hasError(t,e){return!!this.getError(t,e)}get root(){let t=this;for(;t._parent;)t=t._parent;return t}_updateControlsErrors(t,e,n){this.status=this._calculateStatus(),t&&this.statusChanges.emit(this.status),(t||n)&&this._events.next(new M(this.status,e)),this._parent&&this._parent._updateControlsErrors(t,e,n)}_initObservables(){this.valueChanges=new V,this.statusChanges=new V}_calculateStatus(){return this._allControlsDisabled()?N:this.errors?U:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(A)?A:this._anyControlsHaveStatus(U)?U:S}_anyControlsHaveStatus(t){return this._anyControls(e=>e.status===t)}_anyControlsDirty(){return this._anyControls(t=>t.dirty)}_anyControlsTouched(){return this._anyControls(t=>t.touched)}_updatePristine(t,e){let n=!this._anyControlsDirty(),r=this.pristine!==n;this.pristine=n,this._parent&&!t.onlySelf&&this._parent._updatePristine(t,e),r&&this._events.next(new O(this.pristine,e))}_updateTouched(t={},e){this.touched=this._anyControlsTouched(),this._events.next(new x(this.touched,e)),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,e)}_registerOnCollectionChange(t){this._onCollectionChange=t}_setUpdateStrategy(t){J(t)&&t.updateOn!=null&&(this._updateOn=t.updateOn)}_parentMarkedDirty(t){let e=this._parent&&this._parent.dirty;return!t&&!!e&&!this._parent._anyControlsDirty()}_find(t){return null}_assignValidators(t){this._rawValidators=Array.isArray(t)?t.slice():t,this._composedValidatorFn=Ct(this._rawValidators)}_assignAsyncValidators(t){this._rawAsyncValidators=Array.isArray(t)?t.slice():t,this._composedAsyncValidatorFn=Vt(this._rawAsyncValidators)}},z=class extends E{constructor(t,e,n){super(de(e),he(n,e)),this.controls=t,this._initObservables(),this._setUpdateStrategy(e),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}registerControl(t,e){return this.controls[t]?this.controls[t]:(this.controls[t]=e,e.setParent(this),e._registerOnCollectionChange(this._onCollectionChange),e)}addControl(t,e,n={}){this.registerControl(t,e),this.updateValueAndValidity({emitEvent:n.emitEvent}),this._onCollectionChange()}removeControl(t,e={}){this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),delete this.controls[t],this.updateValueAndValidity({emitEvent:e.emitEvent}),this._onCollectionChange()}setControl(t,e,n={}){this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),delete this.controls[t],e&&this.registerControl(t,e),this.updateValueAndValidity({emitEvent:n.emitEvent}),this._onCollectionChange()}contains(t){return this.controls.hasOwnProperty(t)&&this.controls[t].enabled}setValue(t,e={}){Ke(this,!0,t),Object.keys(t).forEach(n=>{Ye(this,!0,n),this.controls[n].setValue(t[n],{onlySelf:!0,emitEvent:e.emitEvent})}),this.updateValueAndValidity(e)}patchValue(t,e={}){t!=null&&(Object.keys(t).forEach(n=>{let r=this.controls[n];r&&r.patchValue(t[n],{onlySelf:!0,emitEvent:e.emitEvent})}),this.updateValueAndValidity(e))}reset(t={},e={}){this._forEachChild((n,r)=>{n.reset(t?t[r]:null,{onlySelf:!0,emitEvent:e.emitEvent})}),this._updatePristine(e,this),this._updateTouched(e,this),this.updateValueAndValidity(e)}getRawValue(){return this._reduceChildren({},(t,e,n)=>(t[n]=e.getRawValue(),t))}_syncPendingControls(){let t=this._reduceChildren(!1,(e,n)=>n._syncPendingControls()?!0:e);return t&&this.updateValueAndValidity({onlySelf:!0}),t}_forEachChild(t){Object.keys(this.controls).forEach(e=>{let n=this.controls[e];n&&t(n,e)})}_setUpControls(){this._forEachChild(t=>{t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(t){for(let[e,n]of Object.entries(this.controls))if(this.contains(e)&&t(n))return!0;return!1}_reduceValue(){let t={};return this._reduceChildren(t,(e,n,r)=>((n.enabled||this.disabled)&&(e[r]=n.value),e))}_reduceChildren(t,e){let n=t;return this._forEachChild((r,s)=>{n=e(n,r,s)}),n}_allControlsDisabled(){for(let t of Object.keys(this.controls))if(this.controls[t].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(t){return this.controls.hasOwnProperty(t)?this.controls[t]:null}};var ue=class extends z{};var G=new C("CallSetDisabledState",{providedIn:"root",factory:()=>Q}),Q="always";function ee(i,t){return[...t.path,i]}function Z(i,t,e=Q){fe(i,t),t.valueAccessor.writeValue(i.value),(i.disabled||e==="always")&&t.valueAccessor.setDisabledState?.(i.disabled),bt(i,t),Mt(i,t),At(i,t),Dt(i,t)}function X(i,t,e=!0){let n=()=>{};t.valueAccessor&&(t.valueAccessor.registerOnChange(n),t.valueAccessor.registerOnTouched(n)),K(i,t),i&&(t._invokeOnDestroyCallbacks(),i._registerOnCollectionChange(()=>{}))}function Y(i,t){i.forEach(e=>{e.registerOnValidatorChange&&e.registerOnValidatorChange(t)})}function Dt(i,t){if(t.valueAccessor.setDisabledState){let e=n=>{t.valueAccessor.setDisabledState(n)};i.registerOnDisabledChange(e),t._registerOnDestroy(()=>{i._unregisterOnDisabledChange(e)})}}function fe(i,t){let e=Ze(i);t.validator!==null?i.setValidators(Ee(e,t.validator)):typeof e=="function"&&i.setValidators([e]);let n=Xe(i);t.asyncValidator!==null?i.setAsyncValidators(Ee(n,t.asyncValidator)):typeof n=="function"&&i.setAsyncValidators([n]);let r=()=>i.updateValueAndValidity();Y(t._rawValidators,r),Y(t._rawAsyncValidators,r)}function K(i,t){let e=!1;if(i!==null){if(t.validator!==null){let r=Ze(i);if(Array.isArray(r)&&r.length>0){let s=r.filter(a=>a!==t.validator);s.length!==r.length&&(e=!0,i.setValidators(s))}}if(t.asyncValidator!==null){let r=Xe(i);if(Array.isArray(r)&&r.length>0){let s=r.filter(a=>a!==t.asyncValidator);s.length!==r.length&&(e=!0,i.setAsyncValidators(s))}}}let n=()=>{};return Y(t._rawValidators,n),Y(t._rawAsyncValidators,n),e}function bt(i,t){t.valueAccessor.registerOnChange(e=>{i._pendingValue=e,i._pendingChange=!0,i._pendingDirty=!0,i.updateOn==="change"&&Je(i,t)})}function At(i,t){t.valueAccessor.registerOnTouched(()=>{i._pendingTouched=!0,i.updateOn==="blur"&&i._pendingChange&&Je(i,t),i.updateOn!=="submit"&&i.markAsTouched()})}function Je(i,t){i._pendingDirty&&i.markAsDirty(),i.setValue(i._pendingValue,{emitModelToViewChange:!1}),t.viewToModelUpdate(i._pendingValue),i._pendingChange=!1}function Mt(i,t){let e=(n,r)=>{t.valueAccessor.writeValue(n),r&&t.viewToModelUpdate(n)};i.registerOnChange(e),t._registerOnDestroy(()=>{i._unregisterOnChange(e)})}function Et(i,t){i==null,fe(i,t)}function Ft(i,t){return K(i,t)}function pe(i,t){if(!i.hasOwnProperty("model"))return!1;let e=i.model;return e.isFirstChange()?!0:!Object.is(t,e.currentValue)}function wt(i){return Object.getPrototypeOf(i.constructor)===Pe}function It(i,t){i._syncPendingControls(),t.forEach(e=>{let n=e.control;n.updateOn==="submit"&&n._pendingChange&&(e.viewToModelUpdate(n._pendingValue),n._pendingChange=!1)})}function ge(i,t){if(!t)return null;Array.isArray(t);let e,n,r;return t.forEach(s=>{s.constructor===ke?e=s:wt(s)?n=s:r=s}),r||n||e||null}function St(i,t){let e=i.indexOf(t);e>-1&&i.splice(e,1)}function Ie(i,t){let e=i.indexOf(t);e>-1&&i.splice(e,1)}function Se(i){return typeof i=="object"&&i!==null&&Object.keys(i).length===2&&"value"in i&&"disabled"in i}var P=class extends E{constructor(t=null,e,n){super(de(e),he(n,e)),this.defaultValue=null,this._onChange=[],this._pendingChange=!1,this._applyFormState(t),this._setUpdateStrategy(e),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),J(e)&&(e.nonNullable||e.initialValueIsDefault)&&(Se(t)?this.defaultValue=t.value:this.defaultValue=t)}setValue(t,e={}){this.value=this._pendingValue=t,this._onChange.length&&e.emitModelToViewChange!==!1&&this._onChange.forEach(n=>n(this.value,e.emitViewToModelChange!==!1)),this.updateValueAndValidity(e)}patchValue(t,e={}){this.setValue(t,e)}reset(t=this.defaultValue,e={}){this._applyFormState(t),this.markAsPristine(e),this.markAsUntouched(e),this.setValue(this.value,e),this._pendingChange=!1}_updateValue(){}_anyControls(t){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(t){this._onChange.push(t)}_unregisterOnChange(t){Ie(this._onChange,t)}registerOnDisabledChange(t){this._onDisabledChange.push(t)}_unregisterOnDisabledChange(t){Ie(this._onDisabledChange,t)}_forEachChild(t){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(t){Se(t)?(this.value=this._pendingValue=t.value,t.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=t}};var Nt=i=>i instanceof P,Ot=(()=>{class i extends d{ngOnInit(){this._checkParentType(),this.formDirective.addFormGroup(this)}ngOnDestroy(){this.formDirective&&this.formDirective.removeFormGroup(this)}get control(){return this.formDirective.getFormGroup(this)}get path(){return ee(this.name==null?this.name:this.name.toString(),this._parent)}get formDirective(){return this._parent?this._parent.formDirective:null}_checkParentType(){}static{this.\u0275fac=(()=>{let e;return function(r){return(e||(e=b(i)))(r||i)}})()}static{this.\u0275dir=l({type:i,features:[u]})}}return i})();var xt={provide:g,useExisting:f(()=>Pt)},Ne=Promise.resolve(),Pt=(()=>{class i extends g{constructor(e,n,r,s,a,st){super(),this._changeDetectorRef=a,this.callSetDisabledState=st,this.control=new P,this._registered=!1,this.name="",this.update=new V,this._parent=e,this._setValidators(n),this._setAsyncValidators(r),this.valueAccessor=ge(this,s)}ngOnChanges(e){if(this._checkForErrors(),!this._registered||"name"in e){if(this._registered&&(this._checkName(),this.formDirective)){let n=e.name.previousValue;this.formDirective.removeControl({name:n,path:this._getPath(n)})}this._setUpControl()}"isDisabled"in e&&this._updateDisabled(e),pe(e,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(e){this.viewModel=e,this.update.emit(e)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!!(this.options&&this.options.standalone)}_setUpStandalone(){Z(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._isStandalone()||this._checkParentType(),this._checkName()}_checkParentType(){}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),!this._isStandalone()&&this.name}_updateValue(e){Ne.then(()=>{this.control.setValue(e,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(e){let n=e.isDisabled.currentValue,r=n!==0&&Ae(n);Ne.then(()=>{r&&!this.control.disabled?this.control.disable():!r&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(e){return this._parent?ee(e,this._parent):[e]}static{this.\u0275fac=function(n){return new(n||i)(o(d,9),o(y,10),o(F,10),o(k,10),o(be,8),o(G,8))}}static{this.\u0275dir=l({type:i,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"],options:[0,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],features:[p([xt]),u,D]})}}return i})(),mi=(()=>{class i{static{this.\u0275fac=function(n){return new(n||i)}}static{this.\u0275dir=l({type:i,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""]})}}return i})();var me=new C(""),kt={provide:g,useExisting:f(()=>Gt)},Gt=(()=>{class i extends g{set isDisabled(e){}static{this._ngModelWarningSentOnce=!1}constructor(e,n,r,s,a){super(),this._ngModelWarningConfig=s,this.callSetDisabledState=a,this.update=new V,this._ngModelWarningSent=!1,this._setValidators(e),this._setAsyncValidators(n),this.valueAccessor=ge(this,r)}ngOnChanges(e){if(this._isControlChanged(e)){let n=e.form.previousValue;n&&X(n,this,!1),Z(this.form,this,this.callSetDisabledState),this.form.updateValueAndValidity({emitEvent:!1})}pe(e,this.viewModel)&&(this.form.setValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.form&&X(this.form,this,!1)}get path(){return[]}get control(){return this.form}viewToModelUpdate(e){this.viewModel=e,this.update.emit(e)}_isControlChanged(e){return e.hasOwnProperty("form")}static{this.\u0275fac=function(n){return new(n||i)(o(y,10),o(F,10),o(k,10),o(me,8),o(G,8))}}static{this.\u0275dir=l({type:i,selectors:[["","formControl",""]],inputs:{form:[0,"formControl","form"],isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"]},outputs:{update:"ngModelChange"},exportAs:["ngForm"],features:[p([kt]),u,D]})}}return i})(),Rt={provide:d,useExisting:f(()=>Qe)},Qe=(()=>{class i extends d{get submitted(){return m(this._submittedReactive)}set submitted(e){this._submittedReactive.set(e)}constructor(e,n,r){super(),this.callSetDisabledState=r,this._submitted=I(()=>this._submittedReactive()),this._submittedReactive=w(!1),this._onCollectionChange=()=>this._updateDomValue(),this.directives=[],this.form=null,this.ngSubmit=new V,this._setValidators(e),this._setAsyncValidators(n)}ngOnChanges(e){this._checkFormPresent(),e.hasOwnProperty("form")&&(this._updateValidators(),this._updateDomValue(),this._updateRegistrations(),this._oldForm=this.form)}ngOnDestroy(){this.form&&(K(this.form,this),this.form._onCollectionChange===this._onCollectionChange&&this.form._registerOnCollectionChange(()=>{}))}get formDirective(){return this}get control(){return this.form}get path(){return[]}addControl(e){let n=this.form.get(e.path);return Z(n,e,this.callSetDisabledState),n.updateValueAndValidity({emitEvent:!1}),this.directives.push(e),n}getControl(e){return this.form.get(e.path)}removeControl(e){X(e.control||null,e,!1),St(this.directives,e)}addFormGroup(e){this._setUpFormContainer(e)}removeFormGroup(e){this._cleanUpFormContainer(e)}getFormGroup(e){return this.form.get(e.path)}addFormArray(e){this._setUpFormContainer(e)}removeFormArray(e){this._cleanUpFormContainer(e)}getFormArray(e){return this.form.get(e.path)}updateModel(e,n){this.form.get(e.path).setValue(n)}onSubmit(e){return this._submittedReactive.set(!0),It(this.form,this.directives),this.ngSubmit.emit(e),this.form._events.next(new ae(this.control)),e?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(e=void 0){this.form.reset(e),this._submittedReactive.set(!1),this.form._events.next(new le(this.form))}_updateDomValue(){this.directives.forEach(e=>{let n=e.control,r=this.form.get(e.path);n!==r&&(X(n||null,e),Nt(r)&&(Z(r,e,this.callSetDisabledState),e.control=r))}),this.form._updateTreeValidity({emitEvent:!1})}_setUpFormContainer(e){let n=this.form.get(e.path);Et(n,e),n.updateValueAndValidity({emitEvent:!1})}_cleanUpFormContainer(e){if(this.form){let n=this.form.get(e.path);n&&Ft(n,e)&&n.updateValueAndValidity({emitEvent:!1})}}_updateRegistrations(){this.form._registerOnCollectionChange(this._onCollectionChange),this._oldForm&&this._oldForm._registerOnCollectionChange(()=>{})}_updateValidators(){fe(this.form,this),this._oldForm&&K(this._oldForm,this)}_checkFormPresent(){this.form}static{this.\u0275fac=function(n){return new(n||i)(o(y,10),o(F,10),o(G,8))}}static{this.\u0275dir=l({type:i,selectors:[["","formGroup",""]],hostBindings:function(n,r){n&1&&B("submit",function(a){return r.onSubmit(a)})("reset",function(){return r.onReset()})},inputs:{form:[0,"formGroup","form"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],features:[p([Rt]),u,D]})}}return i})(),Tt={provide:d,useExisting:f(()=>et)},et=(()=>{class i extends Ot{constructor(e,n,r){super(),this.name=null,this._parent=e,this._setValidators(n),this._setAsyncValidators(r)}_checkParentType(){it(this._parent)}static{this.\u0275fac=function(n){return new(n||i)(o(d,13),o(y,10),o(F,10))}}static{this.\u0275dir=l({type:i,selectors:[["","formGroupName",""]],inputs:{name:[0,"formGroupName","name"]},features:[p([Tt]),u]})}}return i})(),jt={provide:d,useExisting:f(()=>tt)},tt=(()=>{class i extends d{constructor(e,n,r){super(),this.name=null,this._parent=e,this._setValidators(n),this._setAsyncValidators(r)}ngOnInit(){this._checkParentType(),this.formDirective.addFormArray(this)}ngOnDestroy(){this.formDirective&&this.formDirective.removeFormArray(this)}get control(){return this.formDirective.getFormArray(this)}get formDirective(){return this._parent?this._parent.formDirective:null}get path(){return ee(this.name==null?this.name:this.name.toString(),this._parent)}_checkParentType(){it(this._parent)}static{this.\u0275fac=function(n){return new(n||i)(o(d,13),o(y,10),o(F,10))}}static{this.\u0275dir=l({type:i,selectors:[["","formArrayName",""]],inputs:{name:[0,"formArrayName","name"]},features:[p([jt]),u]})}}return i})();function it(i){return!(i instanceof et)&&!(i instanceof Qe)&&!(i instanceof tt)}var Bt={provide:g,useExisting:f(()=>Ut)},Ut=(()=>{class i extends g{set isDisabled(e){}static{this._ngModelWarningSentOnce=!1}constructor(e,n,r,s,a){super(),this._ngModelWarningConfig=a,this._added=!1,this.name=null,this.update=new V,this._ngModelWarningSent=!1,this._parent=e,this._setValidators(n),this._setAsyncValidators(r),this.valueAccessor=ge(this,s)}ngOnChanges(e){this._added||this._setUpControl(),pe(e,this.viewModel)&&(this.viewModel=this.model,this.formDirective.updateModel(this,this.model))}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}viewToModelUpdate(e){this.viewModel=e,this.update.emit(e)}get path(){return ee(this.name==null?this.name:this.name.toString(),this._parent)}get formDirective(){return this._parent?this._parent.formDirective:null}_checkParentType(){}_setUpControl(){this._checkParentType(),this.control=this.formDirective.addControl(this),this._added=!0}static{this.\u0275fac=function(n){return new(n||i)(o(d,13),o(y,10),o(F,10),o(k,10),o(me,8))}}static{this.\u0275dir=l({type:i,selectors:[["","formControlName",""]],inputs:{name:[0,"formControlName","name"],isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"]},outputs:{update:"ngModelChange"},features:[p([Bt]),u,D]})}}return i})();function Ht(i){return typeof i=="number"?i:parseInt(i,10)}var nt=(()=>{class i{constructor(){this._validator=H}ngOnChanges(e){if(this.inputName in e){let n=this.normalizeInput(e[this.inputName].currentValue);this._enabled=this.enabled(n),this._validator=this._enabled?this.createValidator(n):H,this._onChange&&this._onChange()}}validate(e){return this._validator(e)}registerOnValidatorChange(e){this._onChange=e}enabled(e){return e!=null}static{this.\u0275fac=function(n){return new(n||i)}}static{this.\u0275dir=l({type:i,features:[D]})}}return i})();var Lt={provide:y,useExisting:f(()=>$t),multi:!0},$t=(()=>{class i extends nt{constructor(){super(...arguments),this.inputName="maxlength",this.normalizeInput=e=>Ht(e),this.createValidator=e=>Re(e)}static{this.\u0275fac=(()=>{let e;return function(r){return(e||(e=b(i)))(r||i)}})()}static{this.\u0275dir=l({type:i,selectors:[["","maxlength","","formControlName",""],["","maxlength","","formControl",""],["","maxlength","","ngModel",""]],hostVars:1,hostBindings:function(n,r){n&2&&ne("maxlength",r._enabled?r.maxlength:null)},inputs:{maxlength:"maxlength"},features:[p([Lt]),u]})}}return i})(),Wt={provide:y,useExisting:f(()=>qt),multi:!0},qt=(()=>{class i extends nt{constructor(){super(...arguments),this.inputName="pattern",this.normalizeInput=e=>e,this.createValidator=e=>Te(e)}static{this.\u0275fac=(()=>{let e;return function(r){return(e||(e=b(i)))(r||i)}})()}static{this.\u0275dir=l({type:i,selectors:[["","pattern","","formControlName",""],["","pattern","","formControl",""],["","pattern","","ngModel",""]],hostVars:1,hostBindings:function(n,r){n&2&&ne("pattern",r._enabled?r.pattern:null)},inputs:{pattern:"pattern"},features:[p([Wt]),u]})}}return i})();var rt=(()=>{class i{static{this.\u0275fac=function(n){return new(n||i)}}static{this.\u0275mod=j({type:i})}static{this.\u0275inj=T({})}}return i})(),ce=class extends E{constructor(t,e,n){super(de(e),he(n,e)),this.controls=t,this._initObservables(),this._setUpdateStrategy(e),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}at(t){return this.controls[this._adjustIndex(t)]}push(t,e={}){this.controls.push(t),this._registerControl(t),this.updateValueAndValidity({emitEvent:e.emitEvent}),this._onCollectionChange()}insert(t,e,n={}){this.controls.splice(t,0,e),this._registerControl(e),this.updateValueAndValidity({emitEvent:n.emitEvent})}removeAt(t,e={}){let n=this._adjustIndex(t);n<0&&(n=0),this.controls[n]&&this.controls[n]._registerOnCollectionChange(()=>{}),this.controls.splice(n,1),this.updateValueAndValidity({emitEvent:e.emitEvent})}setControl(t,e,n={}){let r=this._adjustIndex(t);r<0&&(r=0),this.controls[r]&&this.controls[r]._registerOnCollectionChange(()=>{}),this.controls.splice(r,1),e&&(this.controls.splice(r,0,e),this._registerControl(e)),this.updateValueAndValidity({emitEvent:n.emitEvent}),this._onCollectionChange()}get length(){return this.controls.length}setValue(t,e={}){Ke(this,!1,t),t.forEach((n,r)=>{Ye(this,!1,r),this.at(r).setValue(n,{onlySelf:!0,emitEvent:e.emitEvent})}),this.updateValueAndValidity(e)}patchValue(t,e={}){t!=null&&(t.forEach((n,r)=>{this.at(r)&&this.at(r).patchValue(n,{onlySelf:!0,emitEvent:e.emitEvent})}),this.updateValueAndValidity(e))}reset(t=[],e={}){this._forEachChild((n,r)=>{n.reset(t[r],{onlySelf:!0,emitEvent:e.emitEvent})}),this._updatePristine(e,this),this._updateTouched(e,this),this.updateValueAndValidity(e)}getRawValue(){return this.controls.map(t=>t.getRawValue())}clear(t={}){this.controls.length<1||(this._forEachChild(e=>e._registerOnCollectionChange(()=>{})),this.controls.splice(0),this.updateValueAndValidity({emitEvent:t.emitEvent}))}_adjustIndex(t){return t<0?t+this.length:t}_syncPendingControls(){let t=this.controls.reduce((e,n)=>n._syncPendingControls()?!0:e,!1);return t&&this.updateValueAndValidity({onlySelf:!0}),t}_forEachChild(t){this.controls.forEach((e,n)=>{t(e,n)})}_updateValue(){this.value=this.controls.filter(t=>t.enabled||this.disabled).map(t=>t.value)}_anyControls(t){return this.controls.some(e=>e.enabled&&t(e))}_setUpControls(){this._forEachChild(t=>this._registerControl(t))}_allControlsDisabled(){for(let t of this.controls)if(t.enabled)return!1;return this.controls.length>0||this.disabled}_registerControl(t){t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange)}_find(t){return this.at(t)??null}};function Oe(i){return!!i&&(i.asyncValidators!==void 0||i.validators!==void 0||i.updateOn!==void 0)}var _i=(()=>{class i{constructor(){this.useNonNullable=!1}get nonNullable(){let e=new i;return e.useNonNullable=!0,e}group(e,n=null){let r=this._reduceControls(e),s={};return Oe(n)?s=n:n!==null&&(s.validators=n.validator,s.asyncValidators=n.asyncValidator),new z(r,s)}record(e,n=null){let r=this._reduceControls(e);return new ue(r,n)}control(e,n,r){let s={};return this.useNonNullable?(Oe(n)?s=n:(s.validators=n,s.asyncValidators=r),new P(e,h(c({},s),{nonNullable:!0}))):new P(e,n,r)}array(e,n,r){let s=e.map(a=>this._createControl(a));return new ce(s,n,r)}_reduceControls(e){let n={};return Object.keys(e).forEach(r=>{n[r]=this._createControl(e[r])}),n}_createControl(e){if(e instanceof P)return e;if(e instanceof E)return e;if(Array.isArray(e)){let n=e[0],r=e.length>1?e[1]:null,s=e.length>2?e[2]:null;return this.control(n,r,s)}else return this.control(e)}static{this.\u0275fac=function(n){return new(n||i)}}static{this.\u0275prov=Ve({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();var vi=(()=>{class i{static withConfig(e){return{ngModule:i,providers:[{provide:G,useValue:e.callSetDisabledState??Q}]}}static{this.\u0275fac=function(n){return new(n||i)}}static{this.\u0275mod=j({type:i})}static{this.\u0275inj=T({imports:[rt]})}}return i})(),yi=(()=>{class i{static withConfig(e){return{ngModule:i,providers:[{provide:me,useValue:e.warnOnNgModelWithFormControl??"always"},{provide:G,useValue:e.callSetDisabledState??Q}]}}static{this.\u0275fac=function(n){return new(n||i)}}static{this.\u0275mod=j({type:i})}static{this.\u0275inj=T({imports:[rt]})}}return i})();export{k as a,at as b,ct as c,ke as d,Me as e,g as f,fi as g,pi as h,z as i,P as j,Pt as k,mi as l,Gt as m,Qe as n,et as o,tt as p,Ut as q,$t as r,qt as s,ce as t,_i as u,vi as v,yi as w};
