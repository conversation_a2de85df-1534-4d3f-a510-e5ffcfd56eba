import{Q as a,fa as h}from"./chunk-AX4DCRSD.js";import{na as p,va as u}from"./chunk-YK6FMNSY.js";var E=(()=>{class n{helperService=p(h);transform(r,e,m,l=a.LIKE){return r?e?(e=e.trim(),r.filter(o=>{let c=!1;for(let f of m){let t=o,i=f,s=f?.split(".");if(s?.length>1&&(i=s[s.length-1],t=o?.[s[0]]),t[i]){if(l===a.LIKE&&this.removeAccents(t?.[i]?.toLowerCase())?.indexOf(this.removeAccents(e.toLowerCase()))!==-1){c=!0;break}if(l===a.EQUAL&&t?.[i]===e){c=!0;break}if(l===a.ADVANCE){e=this.helperService.convertVietnameseCharacters(e).replace(/[^\w\s]/g,"");let v=this.helperService.convertVietnameseCharacters(t?.[i]).replace(/[^\w\s]/g,""),g=this.helperService.generateKeyword(e);if(v?.search(g)>-1){c=!0;break}}}}return c})):r:[]}removeAccents(r){return r.normalize("NFD").replace(/[\u0300-\u036f]/g,"")}static \u0275fac=function(e){return new(e||n)};static \u0275pipe=u({name:"searchFilter",type:n,pure:!0,standalone:!0})}return n})();export{E as a};
