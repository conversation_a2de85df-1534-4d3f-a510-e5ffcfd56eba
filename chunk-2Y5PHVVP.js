import{a as ht,b as pt}from"./chunk-M2J6HCG4.js";import{a as lt}from"./chunk-LBDPKC3Q.js";import{a as ct}from"./chunk-TI2OG4JD.js";import{a as nt}from"./chunk-UVPCVSBB.js";import{g as it,k as rt,v as at}from"./chunk-AASME2FE.js";import{$ as st,S as ot}from"./chunk-AX4DCRSD.js";import{Bd as ie,Bf as tt,Cd as re,Kc as Ze,Le as ae,Me as ne,Pe as Je,jf as oe,kf as se,l as Ne,m as Ge,mf as et,nb as Ke,ud as $e}from"./chunk-K5H3SJL5.js";import{A as P,Cb as Ve,Cc as Y,Da as Se,E as pe,Ea as h,Ed as qe,Fa as p,Hb as W,Ia as Re,Ib as le,Jb as A,Kb as Q,La as Ie,Lb as j,Lc as B,Mc as H,Na as F,Nb as We,Oa as ze,Oc as je,Qa as Te,Qc as Xe,Qd as te,R as Ae,Rb as Be,S as ve,Sb as Fe,Ub as He,Vb as ke,Wb as g,Xb as f,Yb as I,Zb as Ue,Zc as Ye,_b as Le,_c as Pe,ac as T,ba as be,cc as x,ec as m,ha as ee,hb as Ee,kc as q,lb as Oe,lc as N,mc as G,na as M,nc as De,ob as y,oc as X,pb as V,pc as Qe,qc as K,sa as D,vc as me,wc as de,xc as ge}from"./chunk-YK6FMNSY.js";import{a as C,b as L,g as S}from"./chunk-TSRGIXR5.js";function Mt(o,c){if(o&1&&(X(0),B(1,"localizeField")),o&2){let e=m().$implicit;K(" ",je(1,1,e,"name","nameEn")," ")}}function wt(o,c){if(o&1&&(X(0),B(1,"translate")),o&2){let e=m().$implicit;K(" ",H(1,1,"product_catalog."+e.name)," ")}}function yt(o,c){if(o&1){let e=T();g(0,"a",3),x("click",function(){let i=h(e).$implicit,r=m();return p(r.handleClick(i))}),W(1,Mt,2,5)(2,wt,2,3),f()}if(o&2){let e=c.$implicit,t=m();We(e==null?null:e.class),A("sizeIcon",8)("size",t.UI.CardCategorySize.Lg)("type",t.UI.CardCategoryType.Ghost)("cardLink",!1)("prefixIcon",e.icon),y(),Be(e!=null&&e.nameEn?1:2)}}var Kt=(()=>{class o{modalRef=M(ne);UI=M(oe);modalData=M(ae);title=this.modalData?.data?.title;childFeatures=this.modalData?.data?.childFeatures;handleClick(e){this.modalRef.close(e)}static \u0275fac=function(t){return new(t||o)};static \u0275cmp=D({type:o,selectors:[["bidv-omnbi-home-modal-feature-group"]],standalone:!0,features:[Y],decls:4,vars:1,consts:[[3,"title"],[1,"home-modal-group-features"],["app-card-category","",3,"sizeIcon","size","type","cardLink","prefixIcon","class"],["app-card-category","",3,"click","sizeIcon","size","type","cardLink","prefixIcon"]],template:function(t,i){t&1&&(g(0,"app-modal-base",0)(1,"div",1),He(2,yt,3,8,"a",2,Fe),f()()),t&2&&(A("title",i.title),y(2),ke(i.childFeatures))},dependencies:[te,se,nt,re,ie,ct]})}return o})();var At=["wrapper"],vt=["sourceImage"];function bt(o,c){if(o&1){let e=T();g(0,"img",5,1),x("load",function(){h(e);let i=m();return p(i.imageLoadedInView())})("mousedown",function(i){h(e);let r=m();return p(r.startMove(i,r.moveTypes.Drag))})("touchstart",function(i){h(e);let r=m();return p(r.startMove(i,r.moveTypes.Drag))})("error",function(i){h(e);let r=m();return p(r.loadImageError(i))}),f()}if(o&2){let e=m();Q("visibility",e.imageVisible?"visible":"hidden")("transform",e.safeTransformStyle),j("ngx-ic-draggable",!e.disabled&&e.allowMoveImage),A("src",e.safeImgDataUrl,Ee),le("alt",e.imageAltText)}}function St(o,c){if(o&1){let e=T();Ue(0),g(1,"span",9),x("mousedown",function(i){h(e);let r=m(2);return p(r.startMove(i,r.moveTypes.Resize,"topleft"))})("touchstart",function(i){h(e);let r=m(2);return p(r.startMove(i,r.moveTypes.Resize,"topleft"))}),I(2,"span",10),f(),g(3,"span",11),I(4,"span",10),f(),g(5,"span",12),x("mousedown",function(i){h(e);let r=m(2);return p(r.startMove(i,r.moveTypes.Resize,"topright"))})("touchstart",function(i){h(e);let r=m(2);return p(r.startMove(i,r.moveTypes.Resize,"topright"))}),I(6,"span",10),f(),g(7,"span",13),I(8,"span",10),f(),g(9,"span",14),x("mousedown",function(i){h(e);let r=m(2);return p(r.startMove(i,r.moveTypes.Resize,"bottomright"))})("touchstart",function(i){h(e);let r=m(2);return p(r.startMove(i,r.moveTypes.Resize,"bottomright"))}),I(10,"span",10),f(),g(11,"span",15),I(12,"span",10),f(),g(13,"span",16),x("mousedown",function(i){h(e);let r=m(2);return p(r.startMove(i,r.moveTypes.Resize,"bottomleft"))})("touchstart",function(i){h(e);let r=m(2);return p(r.startMove(i,r.moveTypes.Resize,"bottomleft"))}),I(14,"span",10),f(),g(15,"span",17),I(16,"span",10),f(),g(17,"span",18),x("mousedown",function(i){h(e);let r=m(2);return p(r.startMove(i,r.moveTypes.Resize,"top"))})("touchstart",function(i){h(e);let r=m(2);return p(r.startMove(i,r.moveTypes.Resize,"top"))}),f(),g(18,"span",19),x("mousedown",function(i){h(e);let r=m(2);return p(r.startMove(i,r.moveTypes.Resize,"right"))})("touchstart",function(i){h(e);let r=m(2);return p(r.startMove(i,r.moveTypes.Resize,"right"))}),f(),g(19,"span",20),x("mousedown",function(i){h(e);let r=m(2);return p(r.startMove(i,r.moveTypes.Resize,"bottom"))})("touchstart",function(i){h(e);let r=m(2);return p(r.startMove(i,r.moveTypes.Resize,"bottom"))}),f(),g(20,"span",21),x("mousedown",function(i){h(e);let r=m(2);return p(r.startMove(i,r.moveTypes.Resize,"left"))})("touchstart",function(i){h(e);let r=m(2);return p(r.startMove(i,r.moveTypes.Resize,"left"))}),f(),Le()}}function Rt(o,c){if(o&1){let e=T();g(0,"div",6),x("keydown",function(i){h(e);let r=m();return p(r.keyboardAccess(i))}),g(1,"div",7),x("mousedown",function(i){h(e);let r=m();return p(r.startMove(i,r.moveTypes.Move))})("touchstart",function(i){h(e);let r=m();return p(r.startMove(i,r.moveTypes.Move))}),f(),W(2,St,21,0,"ng-container",8),f()}if(o&2){let e=m();Q("top",e.cropper.y1,"px")("left",e.cropper.x1,"px")("width",e.cropper.x2-e.cropper.x1,"px")("height",e.cropper.y2-e.cropper.y1,"px")("margin-left",e.alignImage==="center"?e.marginLeft:null)("visibility",e.imageVisible?"visible":"hidden"),j("ngx-ic-round",e.roundCropper),le("aria-label",e.cropperFrameAriaLabel),y(2),A("ngIf",!e.hideResizeSquares)}}var fe=class{constructor(){this.format="png",this.output="blob",this.maintainAspectRatio=!0,this.transform={},this.aspectRatio=1,this.resetCropOnAspectRatioChange=!0,this.resizeToWidth=0,this.resizeToHeight=0,this.cropperMinWidth=0,this.cropperMinHeight=0,this.cropperMaxHeight=0,this.cropperMaxWidth=0,this.cropperStaticWidth=0,this.cropperStaticHeight=0,this.canvasRotation=0,this.initialStepSize=3,this.roundCropper=!1,this.onlyScaleDown=!1,this.imageQuality=92,this.autoCrop=!0,this.backgroundColor=null,this.containWithinAspectRatio=!1,this.hideResizeSquares=!1,this.alignImage="center",this.cropperFrameAriaLabel="Crop photo",this.cropperScaledMinWidth=20,this.cropperScaledMinHeight=20,this.cropperScaledMaxWidth=20,this.cropperScaledMaxHeight=20,this.stepSize=this.initialStepSize}setOptions(c){Object.keys(c).filter(e=>e in this).forEach(e=>this[e]=c[e]),this.validateOptions()}setOptionsFromChanges(c){Object.keys(c).filter(e=>e in this).forEach(e=>this[e]=c[e].currentValue),this.validateOptions()}validateOptions(){if(this.maintainAspectRatio&&!this.aspectRatio)throw new Error("`aspectRatio` should > 0 when `maintainAspectRatio` is enabled")}},v=function(o){return o.Drag="drag",o.Move="move",o.Resize="resize",o.Pinch="pinch",o}(v||{});function It(o){switch(o){case"ArrowUp":return"top";case"ArrowRight":return"right";case"ArrowDown":return"bottom";case"ArrowLeft":default:return"left"}}function zt(o){switch(o){case"ArrowUp":return"bottom";case"ArrowRight":return"left";case"ArrowDown":return"top";case"ArrowLeft":default:return"right"}}function Tt(o,c){switch(o){case"ArrowUp":return{clientX:0,clientY:c*-1};case"ArrowRight":return{clientX:c,clientY:0};case"ArrowDown":return{clientX:0,clientY:c};case"ArrowLeft":default:return{clientX:c*-1,clientY:0}}}function Et(o,c,e){let t=o.width,i=o.height;c=Math.round(c),e=Math.round(e);let r=t/c,a=i/e,n=Math.ceil(r/2),s=Math.ceil(a/2),d=o.getContext("2d");if(d){let l=d.getImageData(0,0,t,i),u=d.createImageData(c,e),_=l.data,z=u.data;for(let E=0;E<e;E++)for(let O=0;O<c;O++){let R=(O+E*c)*4,b=E*a,w=0,Z=0,ue=0,xe=0,_e=0,Ce=0,Me=0,ft=Math.floor(O*r),ut=Math.floor(E*a),ce=Math.ceil((O+1)*r),he=Math.ceil((E+1)*a);ce=Math.min(ce,t),he=Math.min(he,i);for(let $=ut;$<he;$++){let we=Math.abs(b-$)/s,xt=O*r,_t=we*we;for(let J=ft;J<ce;J++){let ye=Math.abs(xt-J)/n,k=Math.sqrt(_t+ye*ye);if(k>=1)continue;w=2*k*k*k-3*k*k+1;let U=4*(J+$*t);Me+=w*_[U+3],ue+=w,_[U+3]<255&&(w=w*_[U+3]/250),xe+=w*_[U],_e+=w*_[U+1],Ce+=w*_[U+2],Z+=w}}z[R]=xe/Z,z[R+1]=_e/Z,z[R+2]=Ce/Z,z[R+3]=Me/ue}o.width=c,o.height=e,d.putImageData(u,0,0)}}function dt(o,c){return o/100*c}var Ot=(()=>{class o{crop(e,t,i,r,a){let n=this.getImagePosition(e,t,i,a),s=n.x2-n.x1,d=n.y2-n.y1,l=document.createElement("canvas");l.width=s,l.height=d;let u=l.getContext("2d");if(!u)return null;i.backgroundColor!=null&&(u.fillStyle=i.backgroundColor,u.fillRect(0,0,s,d));let _=(i.transform.scale||1)*(i.transform.flipH?-1:1),z=(i.transform.scale||1)*(i.transform.flipV?-1:1),{translateH:E,translateV:O}=this.getCanvasTranslate(e,i,a),R=e.transformed;u.setTransform(_,0,0,z,R.size.width/2+E,R.size.height/2+O),u.translate(-n.x1/_,-n.y1/z),u.rotate((i.transform.rotate||0)*Math.PI/180),u.drawImage(R.image,-R.size.width/2,-R.size.height/2);let b={width:s,height:d,imagePosition:n,cropperPosition:C({},t)};i.containWithinAspectRatio&&(b.offsetImagePosition=this.getOffsetImagePosition(e,t,i,a));let w=this.getResizeRatio(s,d,i);return w!==1&&(b.width=Math.round(s*w),b.height=i.maintainAspectRatio?Math.round(b.width/i.aspectRatio):Math.round(d*w),Et(l,b.width,b.height)),r==="blob"?this.cropToBlob(b,l,i):(b.base64=l.toDataURL("image/"+i.format,this.getQuality(i)),b)}cropToBlob(e,t,i){return S(this,null,function*(){return e.blob=yield new Promise(r=>t.toBlob(r,"image/"+i.format,this.getQuality(i))),e.blob&&(e.objectUrl=URL.createObjectURL(e.blob)),e})}getCanvasTranslate(e,t,i){if(t.transform.translateUnit==="px"){let r=this.getRatio(e,i);return{translateH:(t.transform.translateH||0)*r,translateV:(t.transform.translateV||0)*r}}else return{translateH:t.transform.translateH?dt(t.transform.translateH,e.transformed.size.width):0,translateV:t.transform.translateV?dt(t.transform.translateV,e.transformed.size.height):0}}getRatio(e,t){return e.transformed.size.width/t.width}getImagePosition(e,t,i,r){let a=this.getRatio(e,r),n={x1:Math.round(t.x1*a),y1:Math.round(t.y1*a),x2:Math.round(t.x2*a),y2:Math.round(t.y2*a)};return i.containWithinAspectRatio||(n.x1=Math.max(n.x1,0),n.y1=Math.max(n.y1,0),n.x2=Math.min(n.x2,e.transformed.size.width),n.y2=Math.min(n.y2,e.transformed.size.height)),n}getOffsetImagePosition(e,t,i,r){let a=i.canvasRotation+e.exifTransform.rotate,n=this.getRatio(e,r),s,d;a%2?(s=(e.transformed.size.width-e.original.size.height)/2,d=(e.transformed.size.height-e.original.size.width)/2):(s=(e.transformed.size.width-e.original.size.width)/2,d=(e.transformed.size.height-e.original.size.height)/2);let l={x1:Math.round(t.x1*n)-s,y1:Math.round(t.y1*n)-d,x2:Math.round(t.x2*n)-s,y2:Math.round(t.y2*n)-d};return i.containWithinAspectRatio||(l.x1=Math.max(l.x1,0),l.y1=Math.max(l.y1,0),l.x2=Math.min(l.x2,e.transformed.size.width),l.y2=Math.min(l.y2,e.transformed.size.height)),l}getResizeRatio(e,t,i){let r=i.resizeToWidth/e,a=i.resizeToHeight/t,n=new Array;i.resizeToWidth>0&&n.push(r),i.resizeToHeight>0&&n.push(a);let s=n.length===0?1:Math.min(...n);return s>1&&!i.onlyScaleDown?s:Math.min(s,1)}getQuality(e){return Math.min(1,Math.max(0,e.imageQuality/100))}static{this.\u0275fac=function(t){return new(t||o)}}static{this.\u0275prov=ee({token:o,factory:o.\u0275fac,providedIn:"root"})}}return o})(),Vt=(()=>{class o{resetCropperPosition(e,t,i,r){if(e?.nativeElement)if(i.cropperStaticHeight&&i.cropperStaticWidth)t.x1=0,t.x2=r.width>i.cropperStaticWidth?i.cropperStaticWidth:r.width,t.y1=0,t.y2=r.height>i.cropperStaticHeight?i.cropperStaticHeight:r.height;else{let a=Math.min(i.cropperScaledMaxWidth,r.width),n=Math.min(i.cropperScaledMaxHeight,r.height);if(!i.maintainAspectRatio)t.x1=0,t.x2=a,t.y1=0,t.y2=n;else if(r.width/i.aspectRatio<r.height){t.x1=0,t.x2=a;let s=a/i.aspectRatio;t.y1=(r.height-s)/2,t.y2=t.y1+s}else{t.y1=0,t.y2=n;let s=n*i.aspectRatio;t.x1=(r.width-s)/2,t.x2=t.x1+s}}}move(e,t,i){let r=this.getClientX(e)-t.clientX,a=this.getClientY(e)-t.clientY;i.x1=t.x1+r,i.y1=t.y1+a,i.x2=t.x2+r,i.y2=t.y2+a}resize(e,t,i,r,a){let n=this.getClientX(e)-t.clientX,s=this.getClientY(e)-t.clientY;switch(t.position){case"left":i.x1=Math.min(Math.max(t.x1+n,i.x2-a.cropperScaledMaxWidth),i.x2-a.cropperScaledMinWidth);break;case"topleft":i.x1=Math.min(Math.max(t.x1+n,i.x2-a.cropperScaledMaxWidth),i.x2-a.cropperScaledMinWidth),i.y1=Math.min(Math.max(t.y1+s,i.y2-a.cropperScaledMaxHeight),i.y2-a.cropperScaledMinHeight);break;case"top":i.y1=Math.min(Math.max(t.y1+s,i.y2-a.cropperScaledMaxHeight),i.y2-a.cropperScaledMinHeight);break;case"topright":i.x2=Math.max(Math.min(t.x2+n,i.x1+a.cropperScaledMaxWidth),i.x1+a.cropperScaledMinWidth),i.y1=Math.min(Math.max(t.y1+s,i.y2-a.cropperScaledMaxHeight),i.y2-a.cropperScaledMinHeight);break;case"right":i.x2=Math.max(Math.min(t.x2+n,i.x1+a.cropperScaledMaxWidth),i.x1+a.cropperScaledMinWidth);break;case"bottomright":i.x2=Math.max(Math.min(t.x2+n,i.x1+a.cropperScaledMaxWidth),i.x1+a.cropperScaledMinWidth),i.y2=Math.max(Math.min(t.y2+s,i.y1+a.cropperScaledMaxHeight),i.y1+a.cropperScaledMinHeight);break;case"bottom":i.y2=Math.max(Math.min(t.y2+s,i.y1+a.cropperScaledMaxHeight),i.y1+a.cropperScaledMinHeight);break;case"bottomleft":i.x1=Math.min(Math.max(t.x1+n,i.x2-a.cropperScaledMaxWidth),i.x2-a.cropperScaledMinWidth),i.y2=Math.max(Math.min(t.y2+s,i.y1+a.cropperScaledMaxHeight),i.y1+a.cropperScaledMinHeight);break;case"center":let d=e.scale,l=Math.min(Math.max(a.cropperScaledMinWidth,Math.abs(t.x2-t.x1)*d),a.cropperScaledMaxWidth),u=Math.min(Math.max(a.cropperScaledMinHeight,Math.abs(t.y2-t.y1)*d),a.cropperScaledMaxHeight);i.x1=t.clientX-l/2,i.x2=t.clientX+l/2,i.y1=t.clientY-u/2,i.y2=t.clientY+u/2,i.x1<0?(i.x2-=i.x1,i.x1=0):i.x2>r.width&&(i.x1-=i.x2-r.width,i.x2=r.width),i.y1<0?(i.y2-=i.y1,i.y1=0):i.y2>r.height&&(i.y1-=i.y2-r.height,i.y2=r.height);break}a.maintainAspectRatio&&this.checkAspectRatio(t.position,i,r,a)}checkAspectRatio(e,t,i,r){let a=0,n=0;switch(e){case"top":t.x2=t.x1+(t.y2-t.y1)*r.aspectRatio,a=Math.max(t.x2-i.width,0),n=Math.max(0-t.y1,0),(a>0||n>0)&&(t.x2-=n*r.aspectRatio>a?n*r.aspectRatio:a,t.y1+=n*r.aspectRatio>a?n:a/r.aspectRatio);break;case"bottom":t.x2=t.x1+(t.y2-t.y1)*r.aspectRatio,a=Math.max(t.x2-i.width,0),n=Math.max(t.y2-i.height,0),(a>0||n>0)&&(t.x2-=n*r.aspectRatio>a?n*r.aspectRatio:a,t.y2-=n*r.aspectRatio>a?n:a/r.aspectRatio);break;case"topleft":t.y1=t.y2-(t.x2-t.x1)/r.aspectRatio,a=Math.max(0-t.x1,0),n=Math.max(0-t.y1,0),(a>0||n>0)&&(t.x1+=n*r.aspectRatio>a?n*r.aspectRatio:a,t.y1+=n*r.aspectRatio>a?n:a/r.aspectRatio);break;case"topright":t.y1=t.y2-(t.x2-t.x1)/r.aspectRatio,a=Math.max(t.x2-i.width,0),n=Math.max(0-t.y1,0),(a>0||n>0)&&(t.x2-=n*r.aspectRatio>a?n*r.aspectRatio:a,t.y1+=n*r.aspectRatio>a?n:a/r.aspectRatio);break;case"right":case"bottomright":t.y2=t.y1+(t.x2-t.x1)/r.aspectRatio,a=Math.max(t.x2-i.width,0),n=Math.max(t.y2-i.height,0),(a>0||n>0)&&(t.x2-=n*r.aspectRatio>a?n*r.aspectRatio:a,t.y2-=n*r.aspectRatio>a?n:a/r.aspectRatio);break;case"left":case"bottomleft":t.y2=t.y1+(t.x2-t.x1)/r.aspectRatio,a=Math.max(0-t.x1,0),n=Math.max(t.y2-i.height,0),(a>0||n>0)&&(t.x1+=n*r.aspectRatio>a?n*r.aspectRatio:a,t.y2-=n*r.aspectRatio>a?n:a/r.aspectRatio);break;case"center":t.x2=t.x1+(t.y2-t.y1)*r.aspectRatio,t.y2=t.y1+(t.x2-t.x1)/r.aspectRatio;let s=Math.max(0-t.x1,0),d=Math.max(t.x2-i.width,0),l=Math.max(t.y2-i.height,0),u=Math.max(0-t.y1,0);(s>0||d>0||l>0||u>0)&&(t.x1+=l*r.aspectRatio>s?l*r.aspectRatio:s,t.x2-=u*r.aspectRatio>d?u*r.aspectRatio:d,t.y1+=u*r.aspectRatio>d?u:d/r.aspectRatio,t.y2-=l*r.aspectRatio>s?l:s/r.aspectRatio);break}}getClientX(e){return"touches"in e&&e.touches[0]?e.touches[0].clientX:"clientX"in e?e.clientX:0}getClientY(e){return"touches"in e&&e.touches[0]?e.touches[0].clientY:"clientX"in e?e.clientY:0}static{this.\u0275fac=function(t){return new(t||o)}}static{this.\u0275prov=ee({token:o,factory:o.\u0275fac,providedIn:"root"})}}return o})(),Wt="data:image/jpeg;base64,/9j/4QAiRXhpZgAATU0AKgAAAAgAAQESAAMAAAABAAYAAAAAAAD/2wCEAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAf/AABEIAAEAAgMBEQACEQEDEQH/xABKAAEAAAAAAAAAAAAAAAAAAAALEAEAAAAAAAAAAAAAAAAAAAAAAQEAAAAAAAAAAAAAAAAAAAAAEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8H//2Q==";function Bt(){return new Promise(o=>{let c=new Image;c.onload=()=>{let e=c.width===1&&c.height===2;o(e)},c.src=Wt})}function Ft(o){switch(typeof o=="object"&&(o=Ht(o)),o){case 2:return{rotate:0,flip:!0};case 3:return{rotate:2,flip:!1};case 4:return{rotate:2,flip:!0};case 5:return{rotate:1,flip:!0};case 6:return{rotate:1,flip:!1};case 7:return{rotate:3,flip:!0};case 8:return{rotate:3,flip:!1};default:return{rotate:0,flip:!1}}}function Ht(o){let c=new DataView(o);if(c.getUint16(0,!1)!==65496)return-2;let e=c.byteLength,t=2;for(;t<e;){if(c.getUint16(t+2,!1)<=8)return-1;let i=c.getUint16(t,!1);if(t+=2,i==65505){if(c.getUint32(t+=2,!1)!==1165519206)return-1;let r=c.getUint16(t+=6,!1)==18761;t+=c.getUint32(t+4,r);let a=c.getUint16(t,r);t+=2;for(let n=0;n<a;n++)if(c.getUint16(t+n*12,r)==274)return c.getUint16(t+n*12+8,r)}else{if((i&65280)!==65280)break;t+=c.getUint16(t,!1)}}return-1}var kt=(()=>{class o{constructor(){this.autoRotateSupported=Bt()}loadImageFile(e,t){return S(this,null,function*(){let i=yield e.arrayBuffer();return yield this.checkImageTypeAndLoadImageFromArrayBuffer(i,e.type,t)})}checkImageTypeAndLoadImageFromArrayBuffer(e,t,i){return this.isValidImageType(t)?this.loadImageFromArrayBuffer(e,i,t):Promise.reject(new Error("Invalid image type"))}isValidImageType(e){return/image\/(png|jpg|jpeg|bmp|gif|tiff|svg|webp|x-icon|vnd.microsoft.icon)/.test(e)}loadImageFromURL(e,t){return S(this,null,function*(){let r=yield(yield fetch(e)).blob(),a=yield r.arrayBuffer();return yield this.loadImageFromArrayBuffer(a,t,r.type)})}loadBase64Image(e,t){let i=this.base64ToArrayBuffer(e);return this.loadImageFromArrayBuffer(i,t)}base64ToArrayBuffer(e){e=e.replace(/^data:([^;]+);base64,/gmi,"");let t=atob(e),i=t.length,r=new Uint8Array(i);for(let a=0;a<i;a++)r[a]=t.charCodeAt(a);return r.buffer}loadImageFromArrayBuffer(e,t,i){return S(this,null,function*(){let r=yield new Promise((a,n)=>S(this,null,function*(){try{let s=new Blob([e],i?{type:i}:void 0),d=URL.createObjectURL(s),l=new Image,_=i==="image/svg+xml"?yield this.getSvgImageSize(s):void 0;l.onload=()=>a({originalImage:l,originalImageSize:_,originalObjectUrl:d,originalArrayBuffer:e}),l.onerror=n,l.src=d}catch(s){n(s)}}));return yield this.transformImageFromArrayBuffer(r,t,r.originalImageSize!=null)})}getSvgImageSize(e){return S(this,null,function*(){let r=new DOMParser().parseFromString(yield e.text(),"image/svg+xml").querySelector("svg");if(!r)throw Error("Failed to parse SVG image");let a=r.getAttribute("width"),n=r.getAttribute("height");if(a&&n)return null;let s=r.getAttribute("viewBox")||r.getAttribute("viewbox");if(s){let d=s.split(" ");return{width:+d[2],height:+d[3]}}throw Error("Failed to load SVG image. SVG must have width + height or viewBox definition.")})}transformImageFromArrayBuffer(e,t,i=!1){return S(this,null,function*(){let r=yield this.autoRotateSupported,a=Ft(r?-1:e.originalArrayBuffer);if(!e.originalImage||!e.originalImage.complete)return Promise.reject(new Error("No image loaded"));let n={original:{objectUrl:e.originalObjectUrl,image:e.originalImage,size:e.originalImageSize??{width:e.originalImage.naturalWidth,height:e.originalImage.naturalHeight}},exifTransform:a};return this.transformLoadedImage(n,t,i)})}transformLoadedImage(e,t,i=!1){return S(this,null,function*(){let r=t.canvasRotation+e.exifTransform.rotate,a=e.original.size;if(!i&&r===0&&!e.exifTransform.flip&&!t.containWithinAspectRatio)return{original:{objectUrl:e.original.objectUrl,image:e.original.image,size:C({},a)},transformed:{objectUrl:e.original.objectUrl,image:e.original.image,size:C({},a)},exifTransform:e.exifTransform};let n=this.getTransformedSize(a,e.exifTransform,t),s=document.createElement("canvas");s.width=n.width,s.height=n.height;let d=s.getContext("2d");d?.setTransform(e.exifTransform.flip?-1:1,0,0,1,s.width/2,s.height/2),d?.rotate(Math.PI*(r/2)),d?.drawImage(e.original.image,-a.width/2,-a.height/2);let l=yield new Promise(z=>s.toBlob(z,t.format));if(!l)throw new Error("Failed to get Blob for transformed image.");let u=URL.createObjectURL(l),_=yield this.loadImageFromObjectUrl(u);return{original:{objectUrl:e.original.objectUrl,image:e.original.image,size:C({},a)},transformed:{objectUrl:u,image:_,size:{width:_.width,height:_.height}},exifTransform:e.exifTransform}})}loadImageFromObjectUrl(e){return new Promise((t,i)=>{let r=new Image;r.onload=()=>t(r),r.onerror=i,r.src=e})}getTransformedSize(e,t,i){let r=i.canvasRotation+t.rotate;if(i.containWithinAspectRatio)if(r%2){let a=e.width*i.aspectRatio,n=e.height/i.aspectRatio;return{width:Math.max(e.height,a),height:Math.max(e.width,n)}}else{let a=e.height*i.aspectRatio,n=e.width/i.aspectRatio;return{width:Math.max(e.width,a),height:Math.max(e.height,n)}}return r%2?{height:e.width,width:e.height}:{width:e.width,height:e.height}}static{this.\u0275fac=function(t){return new(t||o)}}static{this.\u0275prov=ee({token:o,factory:o.\u0275fac,providedIn:"root"})}}return o})(),gt=(()=>{class o{constructor(e,t,i,r,a,n,s){this.cropService=e,this.cropperPositionService=t,this.loadImageService=i,this.sanitizer=r,this.cd=a,this.zone=n,this.hammerLoader=s,this.settings=new fe,this.setImageMaxSizeRetries=0,this.resizedWhileHidden=!1,this.marginLeft="0px",this.maxSize={width:0,height:0},this.moveTypes=v,this.imageVisible=!1,this.cropperFrameAriaLabel=this.settings.cropperFrameAriaLabel,this.output=this.settings.output,this.format=this.settings.format,this.transform={},this.maintainAspectRatio=this.settings.maintainAspectRatio,this.aspectRatio=this.settings.aspectRatio,this.resetCropOnAspectRatioChange=this.settings.resetCropOnAspectRatioChange,this.resizeToWidth=this.settings.resizeToWidth,this.resizeToHeight=this.settings.resizeToHeight,this.cropperMinWidth=this.settings.cropperMinWidth,this.cropperMinHeight=this.settings.cropperMinHeight,this.cropperMaxHeight=this.settings.cropperMaxHeight,this.cropperMaxWidth=this.settings.cropperMaxWidth,this.cropperStaticWidth=this.settings.cropperStaticWidth,this.cropperStaticHeight=this.settings.cropperStaticHeight,this.canvasRotation=this.settings.canvasRotation,this.initialStepSize=this.settings.initialStepSize,this.roundCropper=this.settings.roundCropper,this.onlyScaleDown=this.settings.onlyScaleDown,this.imageQuality=this.settings.imageQuality,this.autoCrop=this.settings.autoCrop,this.backgroundColor=this.settings.backgroundColor,this.containWithinAspectRatio=this.settings.containWithinAspectRatio,this.hideResizeSquares=this.settings.hideResizeSquares,this.allowMoveImage=!1,this.cropper={x1:-100,y1:-100,x2:1e4,y2:1e4},this.alignImage=this.settings.alignImage,this.disabled=!1,this.hidden=!1,this.imageCropped=new F,this.startCropImage=new F,this.imageLoaded=new F,this.cropperReady=new F,this.loadImageFailed=new F,this.transformChange=new F,this.reset()}ngOnChanges(e){this.onChangesUpdateSettings(e),this.onChangesInputImage(e),this.loadedImage?.original.image.complete&&(e.containWithinAspectRatio||e.canvasRotation)&&this.loadImageService.transformLoadedImage(this.loadedImage,this.settings).then(t=>this.setLoadedImage(t)).catch(t=>this.loadImageError(t)),(e.cropper||e.maintainAspectRatio||e.aspectRatio)&&(this.setMaxSize(),this.setCropperScaledMinSize(),this.setCropperScaledMaxSize(),this.maintainAspectRatio&&(this.resetCropOnAspectRatioChange||!this.aspectRatioIsCorrect())&&(e.maintainAspectRatio||e.aspectRatio)?this.resetCropperPosition():e.cropper&&(this.checkCropperPosition(!1),this.doAutoCrop())),e.transform&&(this.transform=this.transform||{},this.setCssTransform(),this.doAutoCrop()),e.hidden&&this.resizedWhileHidden&&!this.hidden&&setTimeout(()=>{this.onResize(),this.resizedWhileHidden=!1})}onChangesUpdateSettings(e){this.settings.setOptionsFromChanges(e),this.settings.cropperStaticHeight&&this.settings.cropperStaticWidth&&(this.hideResizeSquares=!0,this.settings.setOptions({hideResizeSquares:!0,cropperMinWidth:this.settings.cropperStaticWidth,cropperMinHeight:this.settings.cropperStaticHeight,cropperMaxHeight:this.settings.cropperStaticHeight,cropperMaxWidth:this.settings.cropperStaticWidth,maintainAspectRatio:!1}))}onChangesInputImage(e){(e.imageChangedEvent||e.imageURL||e.imageBase64||e.imageFile)&&this.reset(),e.imageChangedEvent&&this.isValidImageChangedEvent()&&this.loadImageFile(this.imageChangedEvent.target.files[0]),e.imageURL&&this.imageURL&&this.loadImageFromURL(this.imageURL),e.imageBase64&&this.imageBase64&&this.loadBase64Image(this.imageBase64),e.imageFile&&this.imageFile&&this.loadImageFile(this.imageFile)}isValidImageChangedEvent(){let e=this.imageChangedEvent?.target?.files;return e instanceof FileList&&e.length>0}setCssTransform(){let e=this.transform?.translateUnit||"%";this.safeTransformStyle=this.sanitizer.bypassSecurityTrustStyle(`translate(${this.transform.translateH||0}${e}, ${this.transform.translateV||0}${e}) scaleX(`+(this.transform.scale||1)*(this.transform.flipH?-1:1)+") scaleY("+(this.transform.scale||1)*(this.transform.flipV?-1:1)+") rotate("+(this.transform.rotate||0)+"deg)")}ngOnInit(){this.settings.stepSize=this.initialStepSize,this.activatePinchGesture()}reset(){this.imageVisible=!1,this.loadedImage=void 0,this.safeImgDataUrl="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAAC0lEQVQYV2NgAAIAAAUAAarVyFEAAAAASUVORK5CYII=",this.moveStart={active:!1,type:null,position:null,x1:0,y1:0,x2:0,y2:0,clientX:0,clientY:0},this.maxSize={width:0,height:0},this.cropper.x1=-100,this.cropper.y1=-100,this.cropper.x2=1e4,this.cropper.y2=1e4}loadImageFile(e){this.loadImageService.loadImageFile(e,this.settings).then(t=>this.setLoadedImage(t)).catch(t=>this.loadImageError(t))}loadBase64Image(e){this.loadImageService.loadBase64Image(e,this.settings).then(t=>this.setLoadedImage(t)).catch(t=>this.loadImageError(t))}loadImageFromURL(e){this.loadImageService.loadImageFromURL(e,this.settings).then(t=>this.setLoadedImage(t)).catch(t=>this.loadImageError(t))}setLoadedImage(e){this.loadedImage=e,this.safeImgDataUrl=this.sanitizer.bypassSecurityTrustResourceUrl(e.transformed.objectUrl),this.cd.markForCheck()}loadImageError(e){console.error(e),this.loadImageFailed.emit()}imageLoadedInView(){this.loadedImage!=null&&(this.imageLoaded.emit(this.loadedImage),this.setImageMaxSizeRetries=0,setTimeout(()=>this.checkImageMaxSizeRecursively()))}checkImageMaxSizeRecursively(){this.setImageMaxSizeRetries>40?this.loadImageFailed.emit():this.sourceImageLoaded()?(this.setMaxSize(),this.setCropperScaledMinSize(),this.setCropperScaledMaxSize(),this.resetCropperPosition(),this.cropperReady.emit(C({},this.maxSize)),this.cd.markForCheck()):(this.setImageMaxSizeRetries++,setTimeout(()=>this.checkImageMaxSizeRecursively(),50))}sourceImageLoaded(){return this.sourceImage?.nativeElement?.offsetWidth>0}onResize(){if(this.loadedImage)if(this.hidden)this.resizedWhileHidden=!0;else{let e=C({},this.maxSize);this.setMaxSize(),this.resizeCropperPosition(e),this.setCropperScaledMinSize(),this.setCropperScaledMaxSize()}}activatePinchGesture(){return S(this,null,function*(){yield this.hammerLoader?.();let e=window?.Hammer||null;if(e){let t=new e(this.wrapper.nativeElement);t.get("pinch").set({enable:!0}),t.on("pinchmove",this.onPinch.bind(this)),t.on("pinchend",this.pinchStop.bind(this)),t.on("pinchstart",this.startPinch.bind(this))}else Ye()&&console.warn("[NgxImageCropper] Could not find HammerJS - Pinch Gesture won't work")})}resizeCropperPosition(e){(e.width!==this.maxSize.width||e.height!==this.maxSize.height)&&(this.cropper.x1=this.cropper.x1*this.maxSize.width/e.width,this.cropper.x2=this.cropper.x2*this.maxSize.width/e.width,this.cropper.y1=this.cropper.y1*this.maxSize.height/e.height,this.cropper.y2=this.cropper.y2*this.maxSize.height/e.height)}resetCropperPosition(){this.cropperPositionService.resetCropperPosition(this.sourceImage,this.cropper,this.settings,this.maxSize),this.doAutoCrop(),this.imageVisible=!0}keyboardAccess(e){this.changeKeyboardStepSize(e),this.keyboardMoveCropper(e)}changeKeyboardStepSize(e){let t=+e.key;t>=1&&t<=9&&(this.settings.stepSize=t)}keyboardMoveCropper(e){if(!["ArrowUp","ArrowDown","ArrowRight","ArrowLeft"].includes(e.key))return;let i=e.shiftKey?v.Resize:v.Move,r=e.altKey?zt(e.key):It(e.key),a=Tt(e.key,this.settings.stepSize);e.preventDefault(),e.stopPropagation(),this.startMove({clientX:0,clientY:0},i,r),this.handleMouseMove(a),this.handleMouseUp()}startMove(e,t,i=null){this.disabled||this.moveStart?.active&&this.moveStart?.type===v.Pinch||t===v.Drag&&!this.allowMoveImage||("preventDefault"in e&&e.preventDefault(),this.moveStart=C({active:!0,type:t,position:i,transform:C({},this.transform),clientX:this.cropperPositionService.getClientX(e),clientY:this.cropperPositionService.getClientY(e)},this.cropper),this.initMouseMove())}initMouseMove(){pe(P(document,"mousemove"),P(document,"touchmove")).pipe(be(pe(P(document,"mouseup"),P(document,"touchend")).pipe(ve()))).subscribe({next:e=>this.zone.run(()=>{this.handleMouseMove(e),this.cd.markForCheck()}),complete:()=>this.zone.run(()=>{this.handleMouseUp(),this.cd.markForCheck()})})}startPinch(e){this.safeImgDataUrl&&(e.preventDefault&&e.preventDefault(),this.moveStart=C({active:!0,type:v.Pinch,position:"center",clientX:this.cropper.x1+(this.cropper.x2-this.cropper.x1)/2,clientY:this.cropper.y1+(this.cropper.y2-this.cropper.y1)/2},this.cropper))}handleMouseMove(e){if(this.moveStart.active){if("stopPropagation"in e&&e.stopPropagation(),"preventDefault"in e&&e.preventDefault(),this.moveStart.type===v.Move)this.cropperPositionService.move(e,this.moveStart,this.cropper),this.checkCropperPosition(!0);else if(this.moveStart.type===v.Resize)!this.cropperStaticWidth&&!this.cropperStaticHeight&&this.cropperPositionService.resize(e,this.moveStart,this.cropper,this.maxSize,this.settings),this.checkCropperPosition(!1);else if(this.moveStart.type===v.Drag){let t=this.cropperPositionService.getClientX(e)-this.moveStart.clientX,i=this.cropperPositionService.getClientY(e)-this.moveStart.clientY;this.transform=L(C({},this.transform),{translateH:(this.moveStart.transform?.translateH||0)+t,translateV:(this.moveStart.transform?.translateV||0)+i}),this.setCssTransform()}}}onPinch(e){this.moveStart.active&&(e.stopPropagation&&e.stopPropagation(),e.preventDefault&&e.preventDefault(),this.moveStart.type===v.Pinch&&(this.cropperPositionService.resize(e,this.moveStart,this.cropper,this.maxSize,this.settings),this.checkCropperPosition(!1)),this.cd.markForCheck())}setMaxSize(){if(this.sourceImage){let e=getComputedStyle(this.sourceImage.nativeElement);this.maxSize.width=parseFloat(e.width),this.maxSize.height=parseFloat(e.height),this.marginLeft=this.sanitizer.bypassSecurityTrustStyle("calc(50% - "+this.maxSize.width/2+"px)")}}setCropperScaledMinSize(){this.loadedImage?.transformed?.image?(this.setCropperScaledMinWidth(),this.setCropperScaledMinHeight()):(this.settings.cropperScaledMinWidth=20,this.settings.cropperScaledMinHeight=20)}setCropperScaledMinWidth(){this.settings.cropperScaledMinWidth=this.cropperMinWidth>0?Math.max(20,this.cropperMinWidth/this.loadedImage.transformed.image.width*this.maxSize.width):20}setCropperScaledMinHeight(){this.maintainAspectRatio?this.settings.cropperScaledMinHeight=Math.max(20,this.settings.cropperScaledMinWidth/this.aspectRatio):this.cropperMinHeight>0?this.settings.cropperScaledMinHeight=Math.max(20,this.cropperMinHeight/this.loadedImage.transformed.image.height*this.maxSize.height):this.settings.cropperScaledMinHeight=20}setCropperScaledMaxSize(){if(this.loadedImage?.transformed?.image){let e=this.loadedImage.transformed.size.width/this.maxSize.width;this.settings.cropperScaledMaxWidth=this.cropperMaxWidth>20?this.cropperMaxWidth/e:this.maxSize.width,this.settings.cropperScaledMaxHeight=this.cropperMaxHeight>20?this.cropperMaxHeight/e:this.maxSize.height,this.maintainAspectRatio&&(this.settings.cropperScaledMaxWidth>this.settings.cropperScaledMaxHeight*this.aspectRatio?this.settings.cropperScaledMaxWidth=this.settings.cropperScaledMaxHeight*this.aspectRatio:this.settings.cropperScaledMaxWidth<this.settings.cropperScaledMaxHeight*this.aspectRatio&&(this.settings.cropperScaledMaxHeight=this.settings.cropperScaledMaxWidth/this.aspectRatio))}else this.settings.cropperScaledMaxWidth=this.maxSize.width,this.settings.cropperScaledMaxHeight=this.maxSize.height}checkCropperPosition(e=!1){this.cropper.x1<0&&(this.cropper.x2-=e?this.cropper.x1:0,this.cropper.x1=0),this.cropper.y1<0&&(this.cropper.y2-=e?this.cropper.y1:0,this.cropper.y1=0),this.cropper.x2>this.maxSize.width&&(this.cropper.x1-=e?this.cropper.x2-this.maxSize.width:0,this.cropper.x2=this.maxSize.width),this.cropper.y2>this.maxSize.height&&(this.cropper.y1-=e?this.cropper.y2-this.maxSize.height:0,this.cropper.y2=this.maxSize.height)}handleMouseUp(){this.moveStart.active&&(this.moveStart.active=!1,this.moveStart?.type===v.Drag?this.transformChange.emit(this.transform):this.doAutoCrop())}pinchStop(){this.moveStart.active&&(this.moveStart.active=!1,this.doAutoCrop())}doAutoCrop(){this.autoCrop&&this.crop()}crop(e=this.settings.output){if(this.loadedImage?.transformed?.image!=null){if(this.startCropImage.emit(),e==="blob")return this.cropToBlob();if(e==="base64")return this.cropToBase64()}return null}cropToBlob(){return new Promise((e,t)=>this.zone.run(()=>S(this,null,function*(){let i=yield this.cropService.crop(this.loadedImage,this.cropper,this.settings,"blob",this.maxSize);i?(this.imageCropped.emit(i),e(i)):t("Crop image failed")})))}cropToBase64(){let e=this.cropService.crop(this.loadedImage,this.cropper,this.settings,"base64",this.maxSize);return e?(this.imageCropped.emit(e),e):null}aspectRatioIsCorrect(){return(this.cropper.x2-this.cropper.x1)/(this.cropper.y2-this.cropper.y1)===this.aspectRatio}static{this.\u0275fac=function(t){return new(t||o)(V(Ot),V(Vt),V(kt),V(Ge),V(Pe),V(ze),V(Ne,8))}}static{this.\u0275cmp=D({type:o,selectors:[["image-cropper"]],viewQuery:function(t,i){if(t&1&&(q(At,7),q(vt,5)),t&2){let r;N(r=G())&&(i.wrapper=r.first),N(r=G())&&(i.sourceImage=r.first)}},hostVars:6,hostBindings:function(t,i){t&1&&x("resize",function(){return i.onResize()},!1,Oe),t&2&&(Q("text-align",i.alignImage),j("disabled",i.disabled)("ngx-ix-hidden",i.hidden))},inputs:{imageChangedEvent:"imageChangedEvent",imageURL:"imageURL",imageBase64:"imageBase64",imageFile:"imageFile",imageAltText:"imageAltText",cropperFrameAriaLabel:"cropperFrameAriaLabel",output:"output",format:"format",transform:"transform",maintainAspectRatio:"maintainAspectRatio",aspectRatio:"aspectRatio",resetCropOnAspectRatioChange:"resetCropOnAspectRatioChange",resizeToWidth:"resizeToWidth",resizeToHeight:"resizeToHeight",cropperMinWidth:"cropperMinWidth",cropperMinHeight:"cropperMinHeight",cropperMaxHeight:"cropperMaxHeight",cropperMaxWidth:"cropperMaxWidth",cropperStaticWidth:"cropperStaticWidth",cropperStaticHeight:"cropperStaticHeight",canvasRotation:"canvasRotation",initialStepSize:"initialStepSize",roundCropper:"roundCropper",onlyScaleDown:"onlyScaleDown",imageQuality:"imageQuality",autoCrop:"autoCrop",backgroundColor:"backgroundColor",containWithinAspectRatio:"containWithinAspectRatio",hideResizeSquares:"hideResizeSquares",allowMoveImage:"allowMoveImage",cropper:"cropper",alignImage:"alignImage",disabled:"disabled",hidden:"hidden"},outputs:{imageCropped:"imageCropped",startCropImage:"startCropImage",imageLoaded:"imageLoaded",cropperReady:"cropperReady",loadImageFailed:"loadImageFailed",transformChange:"transformChange"},standalone:!0,features:[Se,Y],decls:5,vars:10,consts:[["wrapper",""],["sourceImage",""],["class","ngx-ic-source-image","role","presentation",3,"src","visibility","transform","ngx-ic-draggable","load","mousedown","touchstart","error",4,"ngIf"],[1,"ngx-ic-overlay"],["class","ngx-ic-cropper","tabindex","0",3,"ngx-ic-round","top","left","width","height","margin-left","visibility","keydown",4,"ngIf"],["role","presentation",1,"ngx-ic-source-image",3,"load","mousedown","touchstart","error","src"],["tabindex","0",1,"ngx-ic-cropper",3,"keydown"],["role","presentation",1,"ngx-ic-move",3,"mousedown","touchstart"],[4,"ngIf"],["role","presentation",1,"ngx-ic-resize","ngx-ic-topleft",3,"mousedown","touchstart"],[1,"ngx-ic-square"],[1,"ngx-ic-resize","ngx-ic-top"],["role","presentation",1,"ngx-ic-resize","ngx-ic-topright",3,"mousedown","touchstart"],[1,"ngx-ic-resize","ngx-ic-right"],["role","presentation",1,"ngx-ic-resize","ngx-ic-bottomright",3,"mousedown","touchstart"],[1,"ngx-ic-resize","ngx-ic-bottom"],["role","presentation",1,"ngx-ic-resize","ngx-ic-bottomleft",3,"mousedown","touchstart"],[1,"ngx-ic-resize","ngx-ic-left"],["role","presentation",1,"ngx-ic-resize-bar","ngx-ic-top",3,"mousedown","touchstart"],["role","presentation",1,"ngx-ic-resize-bar","ngx-ic-right",3,"mousedown","touchstart"],["role","presentation",1,"ngx-ic-resize-bar","ngx-ic-bottom",3,"mousedown","touchstart"],["role","presentation",1,"ngx-ic-resize-bar","ngx-ic-left",3,"mousedown","touchstart"]],template:function(t,i){t&1&&(g(0,"div",null,0),W(2,bt,2,8,"img",2),I(3,"div",3),W(4,Rt,3,16,"div",4),f()),t&2&&(Q("background",i.imageVisible&&i.backgroundColor),y(2),A("ngIf",i.safeImgDataUrl),y(),Q("width",i.maxSize.width,"px")("height",i.maxSize.height,"px")("margin-left",i.alignImage==="center"?i.marginLeft:null),y(),A("ngIf",i.imageVisible))},dependencies:[qe],styles:['[_nghost-%COMP%]{display:flex;position:relative;width:100%;max-width:100%;max-height:100%;overflow:hidden;padding:5px;text-align:center}[_nghost-%COMP%] > div[_ngcontent-%COMP%]{width:100%;position:relative}[_nghost-%COMP%] > div[_ngcontent-%COMP%]   img.ngx-ic-source-image[_ngcontent-%COMP%]{max-width:100%;max-height:100%;transform-origin:center}[_nghost-%COMP%] > div[_ngcontent-%COMP%]   img.ngx-ic-source-image.ngx-ic-draggable[_ngcontent-%COMP%]{user-drag:none;-webkit-user-drag:none;user-select:none;-moz-user-select:none;-webkit-user-select:none;-ms-user-select:none;cursor:grab}[_nghost-%COMP%]   .ngx-ic-overlay[_ngcontent-%COMP%]{position:absolute;pointer-events:none;touch-action:none;outline:var(--cropper-overlay-color, white) solid 100vw;top:0;left:0}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]{position:absolute;display:flex;color:#53535c;background:transparent;outline:rgba(255,255,255,.3) solid 100vw;outline:var(--cropper-outline-color, rgba(255, 255, 255, .3)) solid 100vw;touch-action:none}@media (orientation: portrait){[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]{outline-width:100vh}}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]:after{position:absolute;content:"";inset:0;pointer-events:none;border:dashed 1px;opacity:.75;color:inherit;z-index:1}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-move[_ngcontent-%COMP%]{width:100%;cursor:move;border:1px solid rgba(255,255,255,.5)}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]:focus   .ngx-ic-move[_ngcontent-%COMP%]{border-color:#1e90ff;border-width:2px}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize[_ngcontent-%COMP%]{position:absolute;display:inline-block;line-height:6px;padding:8px;opacity:.85;z-index:1}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize[_ngcontent-%COMP%]   .ngx-ic-square[_ngcontent-%COMP%]{display:inline-block;background:#53535c;width:6px;height:6px;border:1px solid rgba(255,255,255,.5);box-sizing:content-box}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-topleft[_ngcontent-%COMP%]{top:-12px;left:-12px;cursor:nwse-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-top[_ngcontent-%COMP%]{top:-12px;left:calc(50% - 12px);cursor:ns-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-topright[_ngcontent-%COMP%]{top:-12px;right:-12px;cursor:nesw-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-right[_ngcontent-%COMP%]{top:calc(50% - 12px);right:-12px;cursor:ew-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-bottomright[_ngcontent-%COMP%]{bottom:-12px;right:-12px;cursor:nwse-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-bottom[_ngcontent-%COMP%]{bottom:-12px;left:calc(50% - 12px);cursor:ns-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-bottomleft[_ngcontent-%COMP%]{bottom:-12px;left:-12px;cursor:nesw-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-left[_ngcontent-%COMP%]{top:calc(50% - 12px);left:-12px;cursor:ew-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize-bar[_ngcontent-%COMP%]{position:absolute;z-index:1}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize-bar.ngx-ic-top[_ngcontent-%COMP%]{top:-11px;left:11px;width:calc(100% - 22px);height:22px;cursor:ns-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize-bar.ngx-ic-right[_ngcontent-%COMP%]{top:11px;right:-11px;height:calc(100% - 22px);width:22px;cursor:ew-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize-bar.ngx-ic-bottom[_ngcontent-%COMP%]{bottom:-11px;left:11px;width:calc(100% - 22px);height:22px;cursor:ns-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize-bar.ngx-ic-left[_ngcontent-%COMP%]{top:11px;left:-11px;height:calc(100% - 22px);width:22px;cursor:ew-resize}[_nghost-%COMP%]   .ngx-ic-cropper.ngx-ic-round[_ngcontent-%COMP%]{outline-color:transparent}[_nghost-%COMP%]   .ngx-ic-cropper.ngx-ic-round[_ngcontent-%COMP%]:after{border-radius:100%;box-shadow:0 0 0 100vw #ffffff4d;box-shadow:0 0 0 100vw var(--cropper-outline-color, rgba(255, 255, 255, .3))}@media (orientation: portrait){[_nghost-%COMP%]   .ngx-ic-cropper.ngx-ic-round[_ngcontent-%COMP%]:after{box-shadow:0 0 0 100vh #ffffff4d;box-shadow:0 0 0 100vh var(--cropper-outline-color, rgba(255, 255, 255, .3))}}[_nghost-%COMP%]   .ngx-ic-cropper.ngx-ic-round[_ngcontent-%COMP%]   .ngx-ic-move[_ngcontent-%COMP%]{border-radius:100%}.disabled[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize[_ngcontent-%COMP%], .disabled[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize-bar[_ngcontent-%COMP%], .disabled[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-move[_ngcontent-%COMP%]{display:none}.ngx-ix-hidden[_nghost-%COMP%]{display:none}'],changeDetection:0})}}return o})();var Ut=["imageCropper"];function Lt(o,c){if(o&1){let e=T();g(0,"a",8),x("click",function(){h(e);let i=m();return p(i.closeModal())}),X(1),B(2,"translate"),f(),g(3,"a",9),x("click",function(){h(e);let i=m();return p(i.submit())}),X(4),B(5,"translate"),f()}if(o&2){let e=m();A("color",e.UI.ButtonColor.Outline),y(),K(" ",H(2,3,"button.close")," "),y(3),Qe(H(5,5,"button.luu"))}}var Li=(()=>{class o extends st{change=!0;imageCropper;cropper;storageService=M(Ze);uploadService=M(lt);toastService=M(ot);modalService=M(et);modal=M(ne);destroyRef=M(Ie);loadingService=M(tt);modalData=M(ae);UI=M(oe);croppedImage;cropperWidthInit=200;scale=1;scaleRange=1;transform={translateUnit:"px"};ngOnInit(){this.cropper={x1:0,y1:0,x2:this.cropperWidthInit,y2:this.cropperWidthInit}}closeModal(){this.modal.destroy()}imageCropped(e){this.croppedImage=new File([e.blob],"avatar.png")}cropperReady(){let e=this.imageCropper.nativeElement,t=e.offsetWidth,i=e.offsetHeight,r=(t-this.cropperWidthInit)/2,a=(i-this.cropperWidthInit)/2;this.cropper={x1:r,y1:a,x2:this.cropperWidthInit+r,y2:this.cropperWidthInit+a}}imageLoaded(e){}startCropImage(){}loadImageFailed(){this.modalService.error(this.translate.instant("that_bai"))}submit(){let e=this.croppedImage,t=e.size/1e6;e&&t<Ke?(this.loadingService.showLoading(),this.uploadService.uploadAvatar(e).pipe($e(this.destroyRef),Ae(()=>{this.closeModal(),this.loadingService.hideLoading()})).subscribe(i=>{i.data&&(this.toastService.success({title:this.translate.instant("message.doi_hinh_dai_dien_thanh_cong")}),this.storageService.userInfo=L(C({},this.storageService.userInfo),{avatarUrl:i.data}),this.userInfo=this.storageService.userInfo)})):this.toastService.error(this.translate.instant("errors.anh_dai_dien_max_size"))}handleScaleToSlider(e){this.scale=this.scaleRange/10+1,e==0?this.transform=L(C({},this.transform),{scale:this.scale,translateH:0,translateV:0}):this.transform=L(C({},this.transform),{scale:this.scale})}static \u0275fac=(()=>{let e;return function(i){return(e||(e=Re(o)))(i||o)}})();static \u0275cmp=D({type:o,selectors:[["bidv-omni-settings-modal-change-avatar"]],viewQuery:function(t,i){if(t&1&&q(Ut,5,Te),t&2){let r;N(r=G())&&(i.imageCropper=r.first)}},hostVars:2,hostBindings:function(t,i){t&2&&j("modal-change-avatar",i.change)},standalone:!0,features:[Ve,Y],decls:11,vars:22,consts:[["imageCropper",""],["actionsTpl",""],[3,"actions","title"],[1,"space-y-4"],[1,"cropper-wrap"],[3,"imageCropped","imageLoaded","loadImageFailed","startCropImage","cropperReady","transformChange","imageFile","maintainAspectRatio","cropperMinWidth","aspectRatio","roundCropper","onlyScaleDown","allowMoveImage","imageAltText","backgroundColor","output","format","cropper","transform"],[1,"scale-slider-wrap"],[1,"scale-slider",3,"ngModelChange","nzMax","nzMin","ngModel"],["app-button","",3,"click","color"],["app-button","",3,"click"]],template:function(t,i){if(t&1){let r=T();g(0,"app-modal-base",2),B(1,"translate"),g(2,"div",3)(3,"div",4)(4,"image-cropper",5,0),B(6,"translate"),x("imageCropped",function(n){return h(r),p(i.imageCropped(n))})("imageLoaded",function(n){return h(r),p(i.imageLoaded(n))})("loadImageFailed",function(){return h(r),p(i.loadImageFailed())})("startCropImage",function(){return h(r),p(i.startCropImage())})("cropperReady",function(){return h(r),p(i.cropperReady())}),ge("transformChange",function(n){return h(r),de(i.transform,n)||(i.transform=n),p(n)}),f()(),g(7,"div",6)(8,"nz-slider",7),ge("ngModelChange",function(n){return h(r),de(i.scaleRange,n)||(i.scaleRange=n),p(n)}),x("ngModelChange",function(n){return h(r),p(i.handleScaleToSlider(n))}),f()(),W(9,Lt,6,7,"ng-template",null,1,Xe),f()()}if(t&2){let r=De(10);A("actions",r)("title",H(1,18,"featured.doi_anh_dai_dien")),y(4),A("imageFile",i.modalData.data)("maintainAspectRatio",!0)("cropperMinWidth",128)("aspectRatio",1/1)("roundCropper",!0)("onlyScaleDown",!0)("allowMoveImage",!0)("imageAltText",H(6,20,"featured.doi_anh_dai_dien"))("backgroundColor","#1A1A1ACC")("output","blob")("format","png")("cropper",i.cropper),me("transform",i.transform),y(4),A("nzMax",100)("nzMin",0),me("ngModel",i.scaleRange)}},dependencies:[te,se,Je,re,ie,gt,at,it,rt,pt,ht]})}return o})();export{Li as a,Kt as b};
