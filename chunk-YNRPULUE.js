import{$d as He,<PERSON> as O<PERSON>,<PERSON> as _e,Qd as pe,Sd as O,Xc as Le,Yd as Be,ae as ie,be as We,ce as he,de as Qe,ee as Ge,ke as u,me as Ye,n as Fe,q as Ve,qf as Ue,r as Ie,sf as me,te as qe,ue as xe,uf as Ke,ve as Xe,vf as K,we as Ze}from"./chunk-K5H3SJL5.js";import{$ as fe,A as se,Bc as U,Cc as P,Cd as ce,Da as j,E as _,Ea as k,Eb as q,F as N,Fa as $,G as re,Hb as v,Id as je,Jb as z,Jd as we,Kb as ge,Lb as C,N as G,Na as R,P as J,Qa as F,Qc as Pe,Rb as B,Wb as y,Xb as S,Yb as x,Zb as Ce,_b as De,_c as W,aa as ae,ac as X,ba as a,bc as be,cc as E,cd as h,ec as p,f as c,fc as H,g as f,gc as A,ha as le,ia as ee,jc as Z,k as $e,ka as ze,kc as te,l as Re,lc as w,ma as ve,mc as I,na as l,nc as de,ob as g,oc as ye,pb as r,pc as Se,rb as Ae,sa as T,t as V,ta as ne,u as L,ua as ue,v as Ee,vb as Y,zb as Me}from"./chunk-YK6FMNSY.js";var hn=["nz-menu-item",""],en=["*"],mn=["nz-submenu-inline-child",""];function fn(i,d){}var zn=["nz-submenu-none-inline-child",""];function vn(i,d){}var Mn=["nz-submenu-title",""];function gn(i,d){if(i&1&&x(0,"span",0),i&2){let e=p();z("nzType",e.nzIcon)}}function Cn(i,d){if(i&1&&(Ce(0),y(1,"span",4),ye(2),S(),De()),i&2){let e=p();g(2),Se(e.nzTitle)}}function Dn(i,d){i&1&&x(0,"span",5)}function bn(i,d){i&1&&x(0,"span",6)}function yn(i,d){if(i&1&&(y(0,"span",2),v(1,Dn,1,0,"span",5)(2,bn,1,0,"span",6),S()),i&2){let e,t=p();g(),B((e=t.dir)==="rtl"?1:2)}}function Sn(i,d){i&1&&x(0,"span",3)}var wn=["nz-submenu",""],In=[[["","title",""]],"*"],On=["[title]","*"];function _n(i,d){i&1&&A(0)}function Nn(i,d){if(i&1&&x(0,"div",3),i&2){let e=p(),t=de(6);z("mode",e.mode)("nzOpen",e.nzOpen)("@.disabled",!!(e.noAnimation!=null&&e.noAnimation.nzNoAnimation))("nzNoAnimation",e.noAnimation==null?null:e.noAnimation.nzNoAnimation)("menuClass",e.nzMenuClassName)("templateOutlet",t)}}function Tn(i,d){if(i&1){let e=X();y(0,"div",6),E("subMenuMouseState",function(n){k(e);let o=p(2);return $(o.setMouseEnterState(n))}),S()}if(i&2){let e=p(2),t=de(6);z("theme",e.theme)("mode",e.mode)("nzOpen",e.nzOpen)("position",e.position)("nzDisabled",e.nzDisabled)("isMenuInsideDropDown",e.isMenuInsideDropDown)("templateOutlet",t)("menuClass",e.nzMenuClassName)("@.disabled",!!(e.noAnimation!=null&&e.noAnimation.nzNoAnimation))("nzNoAnimation",e.noAnimation==null?null:e.noAnimation.nzNoAnimation)}}function kn(i,d){if(i&1){let e=X();v(0,Tn,1,10,"ng-template",5),E("positionChange",function(n){k(e);let o=p();return $(o.onPositionChange(n))})}if(i&2){let e=p(),t=de(1);z("cdkConnectedOverlayPositions",e.overlayPositions)("cdkConnectedOverlayOrigin",t)("cdkConnectedOverlayWidth",e.triggerWidth)("cdkConnectedOverlayOpen",e.nzOpen)("cdkConnectedOverlayTransformOriginOn",".ant-menu-submenu")}}function $n(i,d){i&1&&A(0,1)}var Rn=["titleElement"],En=["nz-menu-group",""],An=["*",[["","title",""]]],Pn=["*","[title]"];function jn(i,d){if(i&1&&(Ce(0),ye(1),De()),i&2){let e=p();g(),Se(e.nzTitle)}}function Fn(i,d){i&1&&A(0,1)}var b=new ze("NzIsInDropDownMenuToken"),nn=new ze("NzMenuServiceLocalToken"),D=(()=>{class i{constructor(){this.descendantMenuItemClick$=new c,this.childMenuItemClick$=new c,this.theme$=new f("light"),this.mode$=new f("vertical"),this.inlineIndent$=new f(24),this.isChildSubMenuOpen$=new f(!1)}onDescendantMenuItemClick(e){this.descendantMenuItemClick$.next(e)}onChildMenuItemClick(e){this.childMenuItemClick$.next(e)}setMode(e){this.mode$.next(e)}setTheme(e){this.theme$.next(e)}setInlineIndent(e){this.inlineIndent$.next(e)}static{this.\u0275fac=function(t){return new(t||i)}}static{this.\u0275prov=le({token:i,factory:i.\u0275fac})}}return i})(),Ne=(()=>{class i{onChildMenuItemClick(e){this.childMenuItemClick$.next(e)}setOpenStateWithoutDebounce(e){this.isCurrentSubMenuOpen$.next(e)}setMouseEnterTitleOrOverlayState(e){this.isMouseEnterTitleOrOverlay$.next(e)}constructor(e){this.nzMenuService=e,this.mode$=this.nzMenuService.mode$.pipe(V(s=>s==="inline"?"inline":s==="vertical"||this.nzHostSubmenuService?"vertical":"horizontal")),this.level=1,this.isMenuInsideDropDown=l(b),this.isCurrentSubMenuOpen$=new f(!1),this.isChildSubMenuOpen$=new f(!1),this.isMouseEnterTitleOrOverlay$=new c,this.childMenuItemClick$=new c,this.destroy$=new c,this.nzHostSubmenuService=l(i,{optional:!0,skipSelf:!0}),this.nzHostSubmenuService&&(this.level=this.nzHostSubmenuService.level+1);let t=this.childMenuItemClick$.pipe(Ee(()=>this.mode$),N(s=>s!=="inline"||this.isMenuInsideDropDown),G(!1)),n=_(this.isMouseEnterTitleOrOverlay$,t);L([this.isChildSubMenuOpen$,n]).pipe(V(([s,m])=>s||m),re(150),J(),a(this.destroy$)).pipe(J()).subscribe(s=>{this.setOpenStateWithoutDebounce(s),this.nzHostSubmenuService?this.nzHostSubmenuService.isChildSubMenuOpen$.next(s):this.nzMenuService.isChildSubMenuOpen$.next(s)})}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}static{this.\u0275fac=function(t){return new(t||i)(ve(D))}}static{this.\u0275prov=le({token:i,factory:i.\u0275fac})}}return i})(),tn=(()=>{class i{clickMenuItem(e){this.nzDisabled?(e.preventDefault(),e.stopPropagation()):(this.nzMenuService.onDescendantMenuItemClick(this),this.nzSubmenuService?this.nzSubmenuService.onChildMenuItemClick(this):this.nzMenuService.onChildMenuItemClick(this))}setSelectedState(e){this.nzSelected=e,this.selected$.next(e)}updateRouterActive(){!this.listOfRouterLink||!this.router||!this.router.navigated||!this.nzMatchRouter||Promise.resolve().then(()=>{let e=this.hasActiveLinks();this.nzSelected!==e&&(this.nzSelected=e,this.setSelectedState(this.nzSelected),this.cdr.markForCheck())})}hasActiveLinks(){let e=this.isLinkActive(this.router);return this.routerLink&&e(this.routerLink)||this.listOfRouterLink.some(e)}isLinkActive(e){return t=>e.isActive(t.urlTree||"",{paths:this.nzMatchRouterExact?"exact":"subset",queryParams:this.nzMatchRouterExact?"exact":"subset",fragment:"ignored",matrixParams:"ignored"})}constructor(e,t){this.nzMenuService=e,this.cdr=t,this.destroy$=new c,this.nzSubmenuService=l(Ne,{optional:!0}),this.directionality=l(O),this.routerLink=l(Ie,{optional:!0}),this.router=l(Ve,{optional:!0}),this.isMenuInsideDropDown=l(b),this.level=this.nzSubmenuService?this.nzSubmenuService.level+1:1,this.selected$=new c,this.inlinePaddingLeft=null,this.dir="ltr",this.nzDisabled=!1,this.nzSelected=!1,this.nzDanger=!1,this.nzMatchRouterExact=!1,this.nzMatchRouter=!1,this.router&&this.router.events.pipe(a(this.destroy$),N(n=>n instanceof Fe)).subscribe(()=>{this.updateRouterActive()})}ngOnInit(){L([this.nzMenuService.mode$,this.nzMenuService.inlineIndent$]).pipe(a(this.destroy$)).subscribe(([e,t])=>{this.inlinePaddingLeft=e==="inline"?this.level*t:null}),this.dir=this.directionality.value,this.directionality.change?.pipe(a(this.destroy$)).subscribe(e=>{this.dir=e})}ngAfterContentInit(){this.listOfRouterLink.changes.pipe(a(this.destroy$)).subscribe(()=>this.updateRouterActive()),this.updateRouterActive()}ngOnChanges(e){e.nzSelected&&this.setSelectedState(this.nzSelected)}ngOnDestroy(){this.destroy$.next(!0),this.destroy$.complete()}static{this.\u0275fac=function(t){return new(t||i)(r(D),r(W))}}static{this.\u0275cmp=T({type:i,selectors:[["","nz-menu-item",""]],contentQueries:function(t,n,o){if(t&1&&Z(o,Ie,5),t&2){let s;w(s=I())&&(n.listOfRouterLink=s)}},hostVars:20,hostBindings:function(t,n){t&1&&E("click",function(s){return n.clickMenuItem(s)}),t&2&&(ge("padding-left",n.dir==="rtl"?null:n.nzPaddingLeft||n.inlinePaddingLeft,"px")("padding-right",n.dir==="rtl"?n.nzPaddingLeft||n.inlinePaddingLeft:null,"px"),C("ant-dropdown-menu-item",n.isMenuInsideDropDown)("ant-dropdown-menu-item-selected",n.isMenuInsideDropDown&&n.nzSelected)("ant-dropdown-menu-item-danger",n.isMenuInsideDropDown&&n.nzDanger)("ant-dropdown-menu-item-disabled",n.isMenuInsideDropDown&&n.nzDisabled)("ant-menu-item",!n.isMenuInsideDropDown)("ant-menu-item-selected",!n.isMenuInsideDropDown&&n.nzSelected)("ant-menu-item-danger",!n.isMenuInsideDropDown&&n.nzDanger)("ant-menu-item-disabled",!n.isMenuInsideDropDown&&n.nzDisabled))},inputs:{nzPaddingLeft:[2,"nzPaddingLeft","nzPaddingLeft",Le],nzDisabled:[2,"nzDisabled","nzDisabled",h],nzSelected:[2,"nzSelected","nzSelected",h],nzDanger:[2,"nzDanger","nzDanger",h],nzMatchRouterExact:[2,"nzMatchRouterExact","nzMatchRouterExact",h],nzMatchRouter:[2,"nzMatchRouter","nzMatchRouter",h]},exportAs:["nzMenuItem"],standalone:!0,features:[q,j,P],attrs:hn,ngContentSelectors:en,decls:2,vars:0,consts:[[1,"ant-menu-title-content"]],template:function(t,n){t&1&&(H(),y(0,"span",0),A(1),S())},encapsulation:2,changeDetection:0})}}return i})(),Vn=(()=>{class i{constructor(e,t,n){this.elementRef=e,this.renderer=t,this.directionality=n,this.templateOutlet=null,this.menuClass="",this.mode="vertical",this.nzOpen=!1,this.listOfCacheClassName=[],this.expandState="collapsed",this.dir="ltr",this.destroy$=new c}calcMotionState(){this.nzOpen?this.expandState="expanded":this.expandState="collapsed"}ngOnInit(){this.calcMotionState(),this.dir=this.directionality.value,this.directionality.change?.pipe(a(this.destroy$)).subscribe(e=>{this.dir=e})}ngOnChanges(e){let{mode:t,nzOpen:n,menuClass:o}=e;(t||n)&&this.calcMotionState(),o&&(this.listOfCacheClassName.length&&this.listOfCacheClassName.filter(s=>!!s).forEach(s=>{this.renderer.removeClass(this.elementRef.nativeElement,s)}),this.menuClass&&(this.listOfCacheClassName=this.menuClass.split(" "),this.listOfCacheClassName.filter(s=>!!s).forEach(s=>{this.renderer.addClass(this.elementRef.nativeElement,s)})))}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}static{this.\u0275fac=function(t){return new(t||i)(r(F),r(Y),r(O))}}static{this.\u0275cmp=T({type:i,selectors:[["","nz-submenu-inline-child",""]],hostAttrs:[1,"ant-menu","ant-menu-inline","ant-menu-sub"],hostVars:3,hostBindings:function(t,n){t&2&&(be("@collapseMotion",n.expandState),C("ant-menu-rtl",n.dir==="rtl"))},inputs:{templateOutlet:"templateOutlet",menuClass:"menuClass",mode:"mode",nzOpen:"nzOpen"},exportAs:["nzSubmenuInlineChild"],standalone:!0,features:[j,P],attrs:mn,decls:1,vars:1,consts:[[3,"ngTemplateOutlet"]],template:function(t,n){t&1&&v(0,fn,0,0,"ng-template",0),t&2&&z("ngTemplateOutlet",n.templateOutlet)},dependencies:[we],encapsulation:2,data:{animation:[Ue]},changeDetection:0})}}return i})(),Ln=(()=>{class i{constructor(e){this.directionality=e,this.menuClass="",this.theme="light",this.templateOutlet=null,this.isMenuInsideDropDown=!1,this.mode="vertical",this.position="right",this.nzDisabled=!1,this.nzOpen=!1,this.subMenuMouseState=new R,this.expandState="collapsed",this.dir="ltr",this.destroy$=new c}setMouseState(e){this.nzDisabled||this.subMenuMouseState.next(e)}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}calcMotionState(){this.nzOpen?this.mode==="horizontal"?this.expandState="bottom":this.mode==="vertical"&&(this.expandState="active"):this.expandState="collapsed"}ngOnInit(){this.calcMotionState(),this.dir=this.directionality.value,this.directionality.change?.pipe(a(this.destroy$)).subscribe(e=>{this.dir=e})}ngOnChanges(e){let{mode:t,nzOpen:n}=e;(t||n)&&this.calcMotionState()}static{this.\u0275fac=function(t){return new(t||i)(r(O))}}static{this.\u0275cmp=T({type:i,selectors:[["","nz-submenu-none-inline-child",""]],hostAttrs:[1,"ant-menu-submenu","ant-menu-submenu-popup"],hostVars:14,hostBindings:function(t,n){t&1&&E("mouseenter",function(){return n.setMouseState(!0)})("mouseleave",function(){return n.setMouseState(!1)}),t&2&&(be("@slideMotion",n.expandState)("@zoomBigMotion",n.expandState),C("ant-menu-light",n.theme==="light")("ant-menu-dark",n.theme==="dark")("ant-menu-submenu-placement-bottom",n.mode==="horizontal")("ant-menu-submenu-placement-right",n.mode==="vertical"&&n.position==="right")("ant-menu-submenu-placement-left",n.mode==="vertical"&&n.position==="left")("ant-menu-submenu-rtl",n.dir==="rtl"))},inputs:{menuClass:"menuClass",theme:"theme",templateOutlet:"templateOutlet",isMenuInsideDropDown:"isMenuInsideDropDown",mode:"mode",position:"position",nzDisabled:"nzDisabled",nzOpen:"nzOpen"},outputs:{subMenuMouseState:"subMenuMouseState"},exportAs:["nzSubmenuNoneInlineChild"],standalone:!0,features:[j,P],attrs:zn,decls:2,vars:16,consts:[[3,"ngClass"],[3,"ngTemplateOutlet"]],template:function(t,n){t&1&&(y(0,"div",0),v(1,vn,0,0,"ng-template",1),S()),t&2&&(C("ant-dropdown-menu",n.isMenuInsideDropDown)("ant-menu",!n.isMenuInsideDropDown)("ant-dropdown-menu-vertical",n.isMenuInsideDropDown)("ant-menu-vertical",!n.isMenuInsideDropDown)("ant-dropdown-menu-sub",n.isMenuInsideDropDown)("ant-menu-sub",!n.isMenuInsideDropDown)("ant-menu-rtl",n.dir==="rtl"),z("ngClass",n.menuClass),g(),z("ngTemplateOutlet",n.templateOutlet))},dependencies:[ce,we],encapsulation:2,data:{animation:[Ke,me]},changeDetection:0})}}return i})(),on=(()=>{class i{constructor(e,t){this.cdr=e,this.directionality=t,this.nzIcon=null,this.nzTitle=null,this.isMenuInsideDropDown=!1,this.nzDisabled=!1,this.paddingLeft=null,this.mode="vertical",this.toggleSubMenu=new R,this.subMenuMouseState=new R,this.dir="ltr",this.destroy$=new c}ngOnInit(){this.dir=this.directionality.value,this.directionality.change?.pipe(a(this.destroy$)).subscribe(e=>{this.dir=e,this.cdr.detectChanges()})}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}setMouseState(e){this.nzDisabled||this.subMenuMouseState.next(e)}clickTitle(){this.mode==="inline"&&!this.nzDisabled&&this.toggleSubMenu.emit()}static{this.\u0275fac=function(t){return new(t||i)(r(W),r(O))}}static{this.\u0275cmp=T({type:i,selectors:[["","nz-submenu-title",""]],hostVars:8,hostBindings:function(t,n){t&1&&E("click",function(){return n.clickTitle()})("mouseenter",function(){return n.setMouseState(!0)})("mouseleave",function(){return n.setMouseState(!1)}),t&2&&(ge("padding-left",n.dir==="rtl"?null:n.paddingLeft,"px")("padding-right",n.dir==="rtl"?n.paddingLeft:null,"px"),C("ant-dropdown-menu-submenu-title",n.isMenuInsideDropDown)("ant-menu-submenu-title",!n.isMenuInsideDropDown))},inputs:{nzIcon:"nzIcon",nzTitle:"nzTitle",isMenuInsideDropDown:"isMenuInsideDropDown",nzDisabled:"nzDisabled",paddingLeft:"paddingLeft",mode:"mode"},outputs:{toggleSubMenu:"toggleSubMenu",subMenuMouseState:"subMenuMouseState"},exportAs:["nzSubmenuTitle"],standalone:!0,features:[P],attrs:Mn,ngContentSelectors:en,decls:5,vars:3,consts:[["nz-icon","",3,"nzType"],[4,"nzStringTemplateOutlet"],[1,"ant-dropdown-menu-submenu-expand-icon"],[1,"ant-menu-submenu-arrow"],[1,"ant-menu-title-content"],["nz-icon","","nzType","left",1,"ant-dropdown-menu-submenu-arrow-icon"],["nz-icon","","nzType","right",1,"ant-dropdown-menu-submenu-arrow-icon"]],template:function(t,n){t&1&&(H(),v(0,gn,1,1,"span",0)(1,Cn,3,1,"ng-container",1),A(2),v(3,yn,3,1,"span",2)(4,Sn,1,0,"span",3)),t&2&&(B(n.nzIcon?0:-1),g(),z("nzStringTemplateOutlet",n.nzTitle),g(2),B(n.isMenuInsideDropDown?3:4))},dependencies:[Ze,Xe,_e,Oe],encapsulation:2,changeDetection:0})}}return i})(),Je=[u.rightTop,u.right,u.rightBottom,u.leftTop,u.left,u.leftBottom],Bn=[u.bottomLeft,u.bottomRight,u.topRight,u.topLeft],sn=(()=>{class i{setOpenStateWithoutDebounce(e){this.nzSubmenuService.setOpenStateWithoutDebounce(e)}toggleSubMenu(){this.setOpenStateWithoutDebounce(!this.nzOpen)}setMouseEnterState(e){this.isActive=e,this.mode!=="inline"&&this.nzSubmenuService.setMouseEnterTitleOrOverlayState(e)}setTriggerWidth(){this.mode==="horizontal"&&this.platform.isBrowser&&this.cdkOverlayOrigin&&this.nzPlacement==="bottomLeft"&&(this.triggerWidth=this.cdkOverlayOrigin.nativeElement.getBoundingClientRect().width)}onPositionChange(e){let t=Ye(e);t==="rightTop"||t==="rightBottom"||t==="right"?this.position="right":(t==="leftTop"||t==="leftBottom"||t==="left")&&(this.position="left")}constructor(e,t,n,o){this.nzMenuService=e,this.cdr=t,this.nzSubmenuService=n,this.platform=o,this.nzMenuClassName="",this.nzPaddingLeft=null,this.nzTitle=null,this.nzIcon=null,this.nzOpen=!1,this.nzDisabled=!1,this.nzPlacement="bottomLeft",this.nzOpenChange=new R,this.cdkOverlayOrigin=null,this.listOfNzSubMenuComponent=null,this.listOfNzMenuItemDirective=null,this.level=this.nzSubmenuService.level,this.destroy$=new c,this.position="right",this.triggerWidth=null,this.theme="light",this.mode="vertical",this.inlinePaddingLeft=null,this.overlayPositions=Je,this.isSelected=!1,this.isActive=!1,this.dir="ltr",this.isMenuInsideDropDown=l(b),this.noAnimation=l(K,{optional:!0,host:!0}),this.directionality=l(O)}ngOnInit(){this.nzMenuService.theme$.pipe(a(this.destroy$)).subscribe(e=>{this.theme=e,this.cdr.markForCheck()}),this.nzSubmenuService.mode$.pipe(a(this.destroy$)).subscribe(e=>{this.mode=e,e==="horizontal"?this.overlayPositions=[u[this.nzPlacement],...Bn]:e==="vertical"&&(this.overlayPositions=Je),this.cdr.markForCheck()}),L([this.nzSubmenuService.mode$,this.nzMenuService.inlineIndent$]).pipe(a(this.destroy$)).subscribe(([e,t])=>{this.inlinePaddingLeft=e==="inline"?this.level*t:null,this.cdr.markForCheck()}),this.nzSubmenuService.isCurrentSubMenuOpen$.pipe(a(this.destroy$)).subscribe(e=>{this.isActive=e,e!==this.nzOpen&&(this.setTriggerWidth(),this.nzOpen=e,this.nzOpenChange.emit(this.nzOpen),this.cdr.markForCheck())}),this.dir=this.directionality.value,this.directionality.change?.pipe(a(this.destroy$)).subscribe(e=>{this.dir=e,this.cdr.markForCheck()})}ngAfterContentInit(){this.setTriggerWidth();let e=this.listOfNzMenuItemDirective,t=e.changes,n=_(t,...e.map(o=>o.selected$));t.pipe(fe(e),ae(()=>n),fe(!0),V(()=>e.some(o=>o.nzSelected)),a(this.destroy$)).subscribe(o=>{this.isSelected=o,this.cdr.markForCheck()})}ngOnChanges(e){let{nzOpen:t}=e;t&&(this.nzSubmenuService.setOpenStateWithoutDebounce(this.nzOpen),this.setTriggerWidth())}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}static{this.\u0275fac=function(t){return new(t||i)(r(D),r(W),r(Ne),r(pe))}}static{this.\u0275cmp=T({type:i,selectors:[["","nz-submenu",""]],contentQueries:function(t,n,o){if(t&1&&(Z(o,i,5),Z(o,tn,5)),t&2){let s;w(s=I())&&(n.listOfNzSubMenuComponent=s),w(s=I())&&(n.listOfNzMenuItemDirective=s)}},viewQuery:function(t,n){if(t&1&&te(he,7,F),t&2){let o;w(o=I())&&(n.cdkOverlayOrigin=o.first)}},hostVars:34,hostBindings:function(t,n){t&2&&C("ant-dropdown-menu-submenu",n.isMenuInsideDropDown)("ant-dropdown-menu-submenu-disabled",n.isMenuInsideDropDown&&n.nzDisabled)("ant-dropdown-menu-submenu-open",n.isMenuInsideDropDown&&n.nzOpen)("ant-dropdown-menu-submenu-selected",n.isMenuInsideDropDown&&n.isSelected)("ant-dropdown-menu-submenu-vertical",n.isMenuInsideDropDown&&n.mode==="vertical")("ant-dropdown-menu-submenu-horizontal",n.isMenuInsideDropDown&&n.mode==="horizontal")("ant-dropdown-menu-submenu-inline",n.isMenuInsideDropDown&&n.mode==="inline")("ant-dropdown-menu-submenu-active",n.isMenuInsideDropDown&&n.isActive)("ant-menu-submenu",!n.isMenuInsideDropDown)("ant-menu-submenu-disabled",!n.isMenuInsideDropDown&&n.nzDisabled)("ant-menu-submenu-open",!n.isMenuInsideDropDown&&n.nzOpen)("ant-menu-submenu-selected",!n.isMenuInsideDropDown&&n.isSelected)("ant-menu-submenu-vertical",!n.isMenuInsideDropDown&&n.mode==="vertical")("ant-menu-submenu-horizontal",!n.isMenuInsideDropDown&&n.mode==="horizontal")("ant-menu-submenu-inline",!n.isMenuInsideDropDown&&n.mode==="inline")("ant-menu-submenu-active",!n.isMenuInsideDropDown&&n.isActive)("ant-menu-submenu-rtl",n.dir==="rtl")},inputs:{nzMenuClassName:"nzMenuClassName",nzPaddingLeft:"nzPaddingLeft",nzTitle:"nzTitle",nzIcon:"nzIcon",nzOpen:[2,"nzOpen","nzOpen",h],nzDisabled:[2,"nzDisabled","nzDisabled",h],nzPlacement:"nzPlacement"},outputs:{nzOpenChange:"nzOpenChange"},exportAs:["nzSubmenu"],standalone:!0,features:[U([Ne]),q,j,P],attrs:wn,ngContentSelectors:On,decls:7,vars:8,consts:[["origin","cdkOverlayOrigin"],["subMenuTemplate",""],["nz-submenu-title","","cdkOverlayOrigin","",3,"subMenuMouseState","toggleSubMenu","nzIcon","nzTitle","mode","nzDisabled","isMenuInsideDropDown","paddingLeft"],["nz-submenu-inline-child","",3,"mode","nzOpen","nzNoAnimation","menuClass","templateOutlet"],["cdkConnectedOverlay","",3,"cdkConnectedOverlayPositions","cdkConnectedOverlayOrigin","cdkConnectedOverlayWidth","cdkConnectedOverlayOpen","cdkConnectedOverlayTransformOriginOn"],["cdkConnectedOverlay","",3,"positionChange","cdkConnectedOverlayPositions","cdkConnectedOverlayOrigin","cdkConnectedOverlayWidth","cdkConnectedOverlayOpen","cdkConnectedOverlayTransformOriginOn"],["nz-submenu-none-inline-child","",3,"subMenuMouseState","theme","mode","nzOpen","position","nzDisabled","isMenuInsideDropDown","templateOutlet","menuClass","nzNoAnimation"]],template:function(t,n){if(t&1){let o=X();H(In),y(0,"div",2,0),E("subMenuMouseState",function(m){return k(o),$(n.setMouseEnterState(m))})("toggleSubMenu",function(){return k(o),$(n.toggleSubMenu())}),v(2,_n,1,0),S(),v(3,Nn,1,6,"div",3)(4,kn,1,5,null,4)(5,$n,1,0,"ng-template",null,1,Pe)}t&2&&(z("nzIcon",n.nzIcon)("nzTitle",n.nzTitle)("mode",n.mode)("nzDisabled",n.nzDisabled)("isMenuInsideDropDown",n.isMenuInsideDropDown)("paddingLeft",n.nzPaddingLeft||n.inlinePaddingLeft),g(2),B(n.nzTitle?-1:2),g(),B(n.mode==="inline"?3:4))},dependencies:[on,Vn,K,Ln,Ge,Qe,he],encapsulation:2,changeDetection:0})}}return i})();function Hn(){let i=l(D,{skipSelf:!0,optional:!0}),d=l(nn);return i??d}function Wn(){return l(b,{skipSelf:!0,optional:!0})??!1}var wt=(()=>{class i{setInlineCollapsed(e){this.nzInlineCollapsed=e,this.inlineCollapsed$.next(e)}updateInlineCollapse(){this.listOfNzMenuItemDirective&&(this.nzInlineCollapsed?(this.listOfOpenedNzSubMenuComponent=this.listOfNzSubMenuComponent.filter(e=>e.nzOpen),this.listOfNzSubMenuComponent.forEach(e=>e.setOpenStateWithoutDebounce(!1))):(this.listOfOpenedNzSubMenuComponent.forEach(e=>e.setOpenStateWithoutDebounce(!0)),this.listOfOpenedNzSubMenuComponent=[]))}constructor(e,t){this.nzMenuService=e,this.cdr=t,this.isMenuInsideDropDown=l(b),this.nzInlineIndent=24,this.nzTheme="light",this.nzMode="vertical",this.nzInlineCollapsed=!1,this.nzSelectable=!this.isMenuInsideDropDown,this.nzClick=new R,this.actualMode="vertical",this.dir="ltr",this.inlineCollapsed$=new f(this.nzInlineCollapsed),this.mode$=new f(this.nzMode),this.destroy$=new c,this.listOfOpenedNzSubMenuComponent=[],this.directionality=l(O)}ngOnInit(){L([this.inlineCollapsed$,this.mode$]).pipe(a(this.destroy$)).subscribe(([e,t])=>{this.actualMode=e?"vertical":t,this.nzMenuService.setMode(this.actualMode),this.cdr.markForCheck()}),this.nzMenuService.descendantMenuItemClick$.pipe(a(this.destroy$)).subscribe(e=>{this.nzClick.emit(e),this.nzSelectable&&!e.nzMatchRouter&&this.listOfNzMenuItemDirective.forEach(t=>t.setSelectedState(t===e))}),this.dir=this.directionality.value,this.directionality.change?.pipe(a(this.destroy$)).subscribe(e=>{this.dir=e,this.nzMenuService.setMode(this.actualMode),this.cdr.markForCheck()})}ngAfterContentInit(){this.inlineCollapsed$.pipe(a(this.destroy$)).subscribe(()=>{this.updateInlineCollapse(),this.cdr.markForCheck()})}ngOnChanges(e){let{nzInlineCollapsed:t,nzInlineIndent:n,nzTheme:o,nzMode:s}=e;t&&this.inlineCollapsed$.next(this.nzInlineCollapsed),n&&this.nzMenuService.setInlineIndent(this.nzInlineIndent),o&&this.nzMenuService.setTheme(this.nzTheme),s&&(this.mode$.next(this.nzMode),!e.nzMode.isFirstChange()&&this.listOfNzSubMenuComponent&&this.listOfNzSubMenuComponent.forEach(m=>m.setOpenStateWithoutDebounce(!1)))}ngOnDestroy(){this.destroy$.next(!0),this.destroy$.complete()}static{this.\u0275fac=function(t){return new(t||i)(r(D),r(W))}}static{this.\u0275dir=ue({type:i,selectors:[["","nz-menu",""]],contentQueries:function(t,n,o){if(t&1&&(Z(o,tn,5),Z(o,sn,5)),t&2){let s;w(s=I())&&(n.listOfNzMenuItemDirective=s),w(s=I())&&(n.listOfNzSubMenuComponent=s)}},hostVars:34,hostBindings:function(t,n){t&2&&C("ant-dropdown-menu",n.isMenuInsideDropDown)("ant-dropdown-menu-root",n.isMenuInsideDropDown)("ant-dropdown-menu-light",n.isMenuInsideDropDown&&n.nzTheme==="light")("ant-dropdown-menu-dark",n.isMenuInsideDropDown&&n.nzTheme==="dark")("ant-dropdown-menu-vertical",n.isMenuInsideDropDown&&n.actualMode==="vertical")("ant-dropdown-menu-horizontal",n.isMenuInsideDropDown&&n.actualMode==="horizontal")("ant-dropdown-menu-inline",n.isMenuInsideDropDown&&n.actualMode==="inline")("ant-dropdown-menu-inline-collapsed",n.isMenuInsideDropDown&&n.nzInlineCollapsed)("ant-menu",!n.isMenuInsideDropDown)("ant-menu-root",!n.isMenuInsideDropDown)("ant-menu-light",!n.isMenuInsideDropDown&&n.nzTheme==="light")("ant-menu-dark",!n.isMenuInsideDropDown&&n.nzTheme==="dark")("ant-menu-vertical",!n.isMenuInsideDropDown&&n.actualMode==="vertical")("ant-menu-horizontal",!n.isMenuInsideDropDown&&n.actualMode==="horizontal")("ant-menu-inline",!n.isMenuInsideDropDown&&n.actualMode==="inline")("ant-menu-inline-collapsed",!n.isMenuInsideDropDown&&n.nzInlineCollapsed)("ant-menu-rtl",n.dir==="rtl")},inputs:{nzInlineIndent:"nzInlineIndent",nzTheme:"nzTheme",nzMode:"nzMode",nzInlineCollapsed:[2,"nzInlineCollapsed","nzInlineCollapsed",h],nzSelectable:[2,"nzSelectable","nzSelectable",h]},outputs:{nzClick:"nzClick"},exportAs:["nzMenu"],standalone:!0,features:[U([{provide:nn,useClass:D},{provide:D,useFactory:Hn},{provide:b,useFactory:Wn}]),q,j]})}}return i})();function Qn(){return l(b,{optional:!0,skipSelf:!0})??!1}var Gn=(()=>{class i{constructor(e,t){this.elementRef=e,this.renderer=t,this.isMenuInsideDropDown=l(b);let n=this.isMenuInsideDropDown?"ant-dropdown-menu-item-group":"ant-menu-item-group";this.renderer.addClass(e.nativeElement,n)}ngAfterViewInit(){let e=this.titleElement.nativeElement.nextElementSibling;if(e){let t=this.isMenuInsideDropDown?"ant-dropdown-menu-item-group-list":"ant-menu-item-group-list";this.renderer.addClass(e,t)}}static{this.\u0275fac=function(t){return new(t||i)(r(F),r(Y))}}static{this.\u0275cmp=T({type:i,selectors:[["","nz-menu-group",""]],viewQuery:function(t,n){if(t&1&&te(Rn,5),t&2){let o;w(o=I())&&(n.titleElement=o.first)}},inputs:{nzTitle:"nzTitle"},exportAs:["nzMenuGroup"],standalone:!0,features:[U([{provide:b,useFactory:Qn}]),P],attrs:En,ngContentSelectors:Pn,decls:5,vars:6,consts:[["titleElement",""],[4,"nzStringTemplateOutlet"]],template:function(t,n){t&1&&(H(An),y(0,"div",null,0),v(2,jn,2,1,"ng-container",1)(3,Fn,1,0),S(),A(4)),t&2&&(C("ant-menu-item-group-title",!n.isMenuInsideDropDown)("ant-dropdown-menu-item-group-title",n.isMenuInsideDropDown),g(2),z("nzStringTemplateOutlet",n.nzTitle),g(),B(n.nzTitle?-1:3))},dependencies:[_e,Oe],encapsulation:2,changeDetection:0})}}return i})();var rn=(()=>{class i{static{this.\u0275fac=function(t){return new(t||i)}}static{this.\u0275mod=ne({type:i})}static{this.\u0275inj=ee({imports:[sn,Gn,on]})}}return i})();var xn=["*"];function Xn(i,d){if(i&1){let e=X();y(0,"div",0),E("@slideMotion.done",function(n){k(e);let o=p();return $(o.onAnimationEvent(n))})("mouseenter",function(){k(e);let n=p();return $(n.setMouseState(!0))})("mouseleave",function(){k(e);let n=p();return $(n.setMouseState(!1))}),A(1),S()}if(i&2){let e=p();C("ant-dropdown-rtl",e.dir==="rtl"),z("ngClass",e.nzOverlayClassName)("ngStyle",e.nzOverlayStyle)("@slideMotion",void 0)("@.disabled",!!(e.noAnimation!=null&&e.noAnimation.nzNoAnimation))("nzNoAnimation",e.noAnimation==null?null:e.noAnimation.nzNoAnimation)}}var Zn="dropDown",Un=[u.bottomLeft,u.bottomRight,u.topRight,u.topLeft],Kt=(()=>{class i{setDropdownMenuValue(e,t){this.nzDropdownMenu&&this.nzDropdownMenu.setValue(e,t)}constructor(e,t,n,o,s,m){this.nzConfigService=e,this.elementRef=t,this.overlay=n,this.renderer=o,this.viewContainerRef=s,this.platform=m,this._nzModuleName=Zn,this.overlayRef=null,this.destroy$=new c,this.positionStrategy=this.overlay.position().flexibleConnectedTo(this.elementRef.nativeElement).withLockedPosition().withTransformOriginOn(".ant-dropdown"),this.inputVisible$=new f(!1),this.nzTrigger$=new f("hover"),this.overlayClose$=new c,this.nzDropdownMenu=null,this.nzTrigger="hover",this.nzMatchWidthElement=null,this.nzBackdrop=!1,this.nzClickHide=!0,this.nzDisabled=!1,this.nzVisible=!1,this.nzOverlayClassName="",this.nzOverlayStyle={},this.nzPlacement="bottomLeft",this.nzVisibleChange=new R}ngAfterViewInit(){if(this.nzDropdownMenu){let e=this.elementRef.nativeElement,t=_(se(e,"mouseenter").pipe(G(!0)),se(e,"mouseleave").pipe(G(!1))),n=this.nzDropdownMenu.mouseState$,o=_(n,t),s=se(e,"click").pipe(V(()=>!this.nzVisible)),m=this.nzTrigger$.pipe(ae(M=>M==="hover"?o:M==="click"?s:$e)),oe=this.nzDropdownMenu.descendantMenuItemClick$.pipe(N(()=>this.nzClickHide),G(!1)),an=_(m,oe,this.overlayClose$).pipe(N(()=>!this.nzDisabled)),ln=_(this.inputVisible$,an);L([ln,this.nzDropdownMenu.isChildSubMenuOpen$]).pipe(V(([M,Te])=>M||Te),re(150),J(),N(()=>this.platform.isBrowser),a(this.destroy$)).subscribe(M=>{let ke=(this.nzMatchWidthElement?this.nzMatchWidthElement.nativeElement:e).getBoundingClientRect().width;if(this.nzVisible!==M&&this.nzVisibleChange.emit(M),this.nzVisible=M,M){if(!this.overlayRef)this.overlayRef=this.overlay.create({positionStrategy:this.positionStrategy,minWidth:ke,disposeOnNavigation:!0,hasBackdrop:this.nzBackdrop&&this.nzTrigger==="click",scrollStrategy:this.overlay.scrollStrategies.reposition()}),_(this.overlayRef.backdropClick(),this.overlayRef.detachments(),this.overlayRef.outsidePointerEvents().pipe(N(Q=>!this.elementRef.nativeElement.contains(Q.target))),this.overlayRef.keydownEvents().pipe(N(Q=>Q.keyCode===27&&!He(Q)))).pipe(a(this.destroy$)).subscribe(()=>{this.overlayClose$.next(!1)});else{let Q=this.overlayRef.getConfig();Q.minWidth=ke}this.positionStrategy.withPositions([u[this.nzPlacement],...Un]),(!this.portal||this.portal.templateRef!==this.nzDropdownMenu.templateRef)&&(this.portal=new Be(this.nzDropdownMenu.templateRef,this.viewContainerRef)),this.overlayRef.attach(this.portal)}else this.overlayRef&&this.overlayRef.detach()}),this.nzDropdownMenu.animationStateChange$.pipe(a(this.destroy$)).subscribe(M=>{M.toState==="void"&&(this.overlayRef&&this.overlayRef.dispose(),this.overlayRef=null)})}}ngOnDestroy(){this.destroy$.next(!0),this.destroy$.complete(),this.overlayRef&&(this.overlayRef.dispose(),this.overlayRef=null)}ngOnChanges(e){let{nzVisible:t,nzDisabled:n,nzOverlayClassName:o,nzOverlayStyle:s,nzTrigger:m}=e;if(m&&this.nzTrigger$.next(this.nzTrigger),t&&this.inputVisible$.next(this.nzVisible),n){let oe=this.elementRef.nativeElement;this.nzDisabled?(this.renderer.setAttribute(oe,"disabled",""),this.inputVisible$.next(!1)):this.renderer.removeAttribute(oe,"disabled")}o&&this.setDropdownMenuValue("nzOverlayClassName",this.nzOverlayClassName),s&&this.setDropdownMenuValue("nzOverlayStyle",this.nzOverlayStyle)}static{this.\u0275fac=function(t){return new(t||i)(r(qe),r(F),r(We),r(Y),r(Me),r(pe))}}static{this.\u0275dir=ue({type:i,selectors:[["","nz-dropdown",""]],hostAttrs:[1,"ant-dropdown-trigger"],inputs:{nzDropdownMenu:"nzDropdownMenu",nzTrigger:"nzTrigger",nzMatchWidthElement:"nzMatchWidthElement",nzBackdrop:[2,"nzBackdrop","nzBackdrop",h],nzClickHide:[2,"nzClickHide","nzClickHide",h],nzDisabled:[2,"nzDisabled","nzDisabled",h],nzVisible:[2,"nzVisible","nzVisible",h],nzOverlayClassName:"nzOverlayClassName",nzOverlayStyle:"nzOverlayStyle",nzPlacement:"nzPlacement"},outputs:{nzVisibleChange:"nzVisibleChange"},exportAs:["nzDropdown"],standalone:!0,features:[q,j]})}}return Re([xe()],i.prototype,"nzBackdrop",void 0),i})(),Kn=(()=>{class i{static{this.\u0275fac=function(t){return new(t||i)}}static{this.\u0275mod=ne({type:i})}static{this.\u0275inj=ee({})}}return i})();var Jt=(()=>{class i{onAnimationEvent(e){this.animationStateChange$.emit(e)}setMouseState(e){this.mouseState$.next(e)}setValue(e,t){this[e]=t,this.cdr.markForCheck()}constructor(e,t,n,o,s,m){this.cdr=e,this.elementRef=t,this.renderer=n,this.viewContainerRef=o,this.nzMenuService=s,this.directionality=m,this.mouseState$=new f(!1),this.isChildSubMenuOpen$=this.nzMenuService.isChildSubMenuOpen$,this.descendantMenuItemClick$=this.nzMenuService.descendantMenuItemClick$,this.animationStateChange$=new R,this.nzOverlayClassName="",this.nzOverlayStyle={},this.dir="ltr",this.destroy$=new c,this.noAnimation=l(K,{host:!0,optional:!0})}ngOnInit(){this.directionality.change?.pipe(a(this.destroy$)).subscribe(e=>{this.dir=e,this.cdr.detectChanges()}),this.dir=this.directionality.value}ngAfterContentInit(){this.renderer.removeChild(this.renderer.parentNode(this.elementRef.nativeElement),this.elementRef.nativeElement)}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}static{this.\u0275fac=function(t){return new(t||i)(r(W),r(F),r(Y),r(Me),r(D),r(O))}}static{this.\u0275cmp=T({type:i,selectors:[["nz-dropdown-menu"]],viewQuery:function(t,n){if(t&1&&te(Ae,7),t&2){let o;w(o=I())&&(n.templateRef=o.first)}},exportAs:["nzDropdownMenu"],standalone:!0,features:[U([D,{provide:b,useValue:!0}]),P],ngContentSelectors:xn,decls:1,vars:0,consts:[[1,"ant-dropdown",3,"mouseenter","mouseleave","ngClass","ngStyle","nzNoAnimation"]],template:function(t,n){t&1&&(H(),v(0,Xn,2,7,"ng-template"))},dependencies:[ce,je,K],encapsulation:2,data:{animation:[me]},changeDetection:0})}}return i})(),ei=(()=>{class i{static{this.\u0275fac=function(t){return new(t||i)}}static{this.\u0275mod=ne({type:i})}static{this.\u0275inj=ee({imports:[Kn,rn]})}}return i})(),ni=[new ie({originX:"start",originY:"top"},{overlayX:"start",overlayY:"top"}),new ie({originX:"start",originY:"top"},{overlayX:"start",overlayY:"bottom"}),new ie({originX:"start",originY:"top"},{overlayX:"end",overlayY:"bottom"}),new ie({originX:"start",originY:"top"},{overlayX:"end",overlayY:"top"})];export{tn as a,wt as b,rn as c,Kt as d,Jt as e,ei as f};
