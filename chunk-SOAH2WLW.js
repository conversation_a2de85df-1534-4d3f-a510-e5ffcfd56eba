import{f as m}from"./chunk-AASME2FE.js";import{fa as p}from"./chunk-AX4DCRSD.js";import{A as n,Qa as a,ba as o,f as c,na as r,ua as l}from"./chunk-YK6FMNSY.js";var b=(()=>{class t{elementRef=r(a);control=r(m);helperService=r(p);destroy$=new c;ngOnInit(){n(this.elementRef.nativeElement,"paste").pipe(o(this.destroy$)).subscribe(i=>{setTimeout(()=>{let e=this.helperService.convertVietnameseCharacters(this.control.value);this.control&&this.control.control?.setValue(e)},0)}),n(this.elementRef.nativeElement,"input").pipe(o(this.destroy$)).subscribe(i=>{let e=i.target,s=e.selectionStart,h=this.helperService.convertVietnameseCharacters(e.value);this.control&&this.control.control?.setValue(h),e.setSelectionRange(s,s)})}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}static \u0275fac=function(e){return new(e||t)};static \u0275dir=l({type:t,selectors:[["","appNoneUnikey",""]],standalone:!0})}return t})();export{b as a};
