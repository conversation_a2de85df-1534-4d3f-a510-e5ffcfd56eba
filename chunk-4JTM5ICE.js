import{Af as C,Bf as j,Kc as v,bb as S,mf as u}from"./chunk-K5H3SJL5.js";import{E as p,H as l,ha as h,n as g,na as o,t as d}from"./chunk-YK6FMNSY.js";import{a as m}from"./chunk-TSRGIXR5.js";var M=(()=>{class r{apiService=o(C);loadingService=o(j);modalService=o(u);storageService=o(v);getConfigs(n,t=!1,D={}){let c=this.storageService.configurationData,s=[],i=[];n.forEach((e,a)=>{let f=c[e];f?s.push(f):i.push(e)}),i.length>0&&t&&this.loadingService.showLoading();let E=i.join(",");return p(g(s),i?.length?this.apiService.getValueConfig(m({codes:E},D)).pipe(d(e=>(e?.code===S.success&&(e?.list?.forEach(a=>{c[a.code]=a}),this.storageService.configurationData=c),e?.list||[])),l(e=>(this.modalService.error(e?.error?.des),[]))):[])}static \u0275fac=function(t){return new(t||r)};static \u0275prov=h({token:r,factory:r.\u0275fac,providedIn:"root"})}return r})();export{M as a};
