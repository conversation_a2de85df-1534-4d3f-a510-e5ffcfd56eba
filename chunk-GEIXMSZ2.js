import{f as m}from"./chunk-AASME2FE.js";import{Qa as p}from"./chunk-K5H3SJL5.js";import{A as o,Ka as l,Qa as a,ba as n,f as s,na as i,ua as c}from"./chunk-YK6FMNSY.js";var E=(()=>{class t{elementRef=i(a);injector=i(l);control=i(m);destroy$=new s;ngOnInit(){o(this.elementRef.nativeElement,"paste").pipe(n(this.destroy$)).subscribe(r=>{setTimeout(()=>{let e=this.control.value;e&&typeof e=="string"&&(e=e.replace(/\s/gi,""),this.control&&this.control.control?.setValue(e))})}),o(this.elementRef.nativeElement,"keydown").pipe(n(this.destroy$)).subscribe(r=>{if(r.keyCode===p){r.preventDefault();return}})}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}static \u0275fac=function(e){return new(e||t)};static \u0275dir=c({type:t,selectors:[["","appNoneSpace",""]],standalone:!0})}return t})();export{E as a};
