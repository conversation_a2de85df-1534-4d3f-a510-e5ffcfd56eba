import{a as Ce}from"./chunk-YJZSDC7G.js";import{a as fe}from"./chunk-VFBBYLZU.js";import{c as Ae}from"./chunk-6KSU3ZJ7.js";import{q as he}from"./chunk-LSYDHUWU.js";import{a as be}from"./chunk-SOAH2WLW.js";import{o as de}from"./chunk-5VBIWGCV.js";import{g as ce,h as se,l as _e,n as le,q as ue,u as Ne,w as pe}from"./chunk-AASME2FE.js";import{l as me,o as d}from"./chunk-AX4DCRSD.js";import{Ad as W,Af as oe,Bd as Q,Bf as ie,Cd as Z,H as L,Le as J,Me as ee,Pe as te,Xe as i,aa as $,jf as ae,kf as re,mf as ne,nf as a,q as z}from"./chunk-K5H3SJL5.js";import{Cc as R,Ea as S,Fa as H,Hb as w,Jb as p,Lc as _,Mc as l,Qc as F,Qd as j,Wb as A,Xb as C,Yb as I,ac as V,c as B,cc as O,ec as g,g as q,ha as M,k as Y,n as G,na as s,nc as X,ob as N,oc as E,qc as D,sa as x,v as K}from"./chunk-YK6FMNSY.js";import{a as P,b as y}from"./chunk-TSRGIXR5.js";function He(o,Oe){if(o&1){let e=V();A(0,"a",6),O("click",function(){S(e);let t=g();return H(t.handleClose())}),E(1),_(2,"translate"),C(),A(3,"a",7),O("click",function(){S(e);let t=g();return H(t.handleSave())}),E(4),_(5,"translate"),C()}if(o&2){let e=g();p("color",e.UI.ButtonColor.Outline),N(),D(" ",l(2,3,"huy")," "),N(3),D("",l(5,5,"luu_danh_ba")," ")}}var Te=(()=>{class o{fb=s(Ne);modalRef=s(ee);modalData=s(J);UI=s(ae);form;ngOnInit(){if(this.initForm(),this.modalData){let{name:e,amount:r,content:t}=this.modalData?.data||{};this.form.patchValue({name:e,amount:Number(r??0),content:t})}}initForm(){this.form=this.fb.group({name:[null],amount:[null],content:[null]})}handleSave(){this.handleClose(this.form.getRawValue())}handleClose(e){this.modalRef.destroy(e)}static \u0275fac=function(r){return new(r||o)};static \u0275cmp=x({type:o,selectors:[["app-modal-save-contact"]],standalone:!0,features:[R],decls:14,vars:32,consts:[["actionTpl",""],[3,"title","actions"],[1,"space-y-4","md:space-y-6",3,"formGroup"],["formControlName","name","appNoneUnikey","","appNoneSpecial","",3,"direction","label","placeholder","maxLength"],["formControlName","amount",3,"direction","suffix","maxLength","label","placeholder","isSimple"],["formControlName","content","appNoneUnikey","","appNoneSpecial","","allow",".,",3,"direction","label","placeholder","maxLength","counter"],["app-button","",3,"click","color"],["app-button","",3,"click"]],template:function(r,t){if(r&1&&(A(0,"app-modal-base",1),_(1,"translate"),A(2,"form",2),I(3,"app-input",3),_(4,"translate"),_(5,"translate"),I(6,"app-input-money",4),_(7,"translate"),_(8,"translate"),I(9,"app-textarea",5),_(10,"translate"),_(11,"translate"),C(),w(12,He,6,7,"ng-template",null,0,F),C()),r&2){let n=X(13);p("title",l(1,18,"luu_danh_ba_thu_huong"))("actions",n),N(2),p("formGroup",t.form),N(),p("direction",t.UI.Direction.Vertical)("label",l(4,20,"ten_goi_nho_modal"))("placeholder",l(5,22,"ten_goi_nho_modal"))("maxLength",20),N(3),p("direction",t.UI.Direction.Vertical)("suffix","VND")("maxLength",12)("label",l(7,24,"so_tien_chuyen"))("placeholder",l(8,26,"so_tien_chuyen"))("isSimple",!0),N(3),p("direction",t.UI.Direction.Vertical)("label",l(10,28,"noi_dung"))("placeholder",l(11,30,"noi_dung"))("maxLength",(t.modalData==null||t.modalData.data==null?null:t.modalData.data.maxLength)||250)("counter",(t.modalData==null||t.modalData.data==null?null:t.modalData.data.maxLength)||250)}},dependencies:[j,pe,_e,ce,se,le,ue,re,te,de,Ae,Z,Q,Ce,be,fe]})}return o})();var ct=(()=>{class o{apiService=s(oe);router=s(z);modalService=s(ne);translate=s(W);loadingService=s(ie);accountListsub$=new q(null);initWithinBIDVTransfer(e){return this.apiService.requestProcess(e,a.KHOI_TAO_CHUYEN_TIEN_NOI_BO,!1,null,!0)}confirmWithinBIDVTransfer(e){return this.apiService.requestProcess(e,a.XAC_NHAN_CHUYEN_TIEN_NOI_BO,!1,null,!0)}getBIDVAccountInfor(e){return this.apiService.requestProcess(e,a.LAY_THONG_TIN_TAI_KHOAN_NOI_BO)}checkHighTransactionLimit(e){return this.apiService.requestProcess(e,a.DIEU_KIEN_GD_HAN_MUC_CAO)}initInterbankTransferToAccount(e,r=!0){return r?this.apiService.requestProcess(e,a.KHOI_TAO_CHUYEN_TIEN_NGOAI_STK_247):this.apiService.requestProcess(e,a.KHOI_TAO_CHUYEN_TIEN_NGOAI_STK)}confirmInterbankTransferToAccount(e,r){return r?this.apiService.requestProcess(e,a.XAC_NHAN_CHUYEN_TIEN_NGOAI_STK_247):this.apiService.requestProcess(e,a.XAC_NHAN_CHUYEN_TIEN_NGOAI_STK)}initInterbankTransferToAccountHighValue(e){return e.bankCode247?this.apiService.requestProcess(e,a.KHOI_TAO_CHUYEN_TIEN_NGOAI_STK_247_GIA_TRI_CAO):this.apiService.requestProcess(e,a.KHOI_TAO_CHUYEN_TIEN_NGOAI_STK)}confirmInterbankTransferToAccountHighValue(e,r){return r?this.apiService.requestProcess(e,a.XAC_NHAN_CHUYEN_TIEN_NGOAI_STK_247_GIA_TRI_CAO):this.apiService.requestProcess(e,a.XAC_NHAN_CHUYEN_TIEN_NGOAI_STK)}initInterbankTransferToCardNumber(e){return this.apiService.requestProcess(e,a.KHOI_TAO_CHUYEN_TIEN_NGOAI_SO_THE,!1,null,!0)}confirmInterbankTransferToCardNumber(e){return this.apiService.requestProcess(e,a.XAC_NHAN_CHUYEN_TIEN_NGOAI_SO_THE)}initPeriodicTransfer(e){return this.apiService.requestProcess(e,a.KHOI_TAO_CHUYEN_TIEN_DINH_KY)}confirmPeriodicTransfer(e){return this.apiService.requestProcess(e,a.XAC_NHAN_CHUYEN_TIEN_DINH_KY)}getListPeriodicTransfer(e){return this.apiService.requestProcess(e,a.LAY_DANH_SACH_CHUYEN_TIEN_DINH_KY)}initDeletePeriodicTransfer(e){return this.apiService.requestProcess(e,a.KHOI_TAO_XOA_CHUYEN_TIEN_DINH_KY)}confirmDeletePeriodicTransfer(e){return this.apiService.requestProcess(e,a.XAC_NHAN_XOA_CHUYEN_TIEN_DINH_KY)}getInterbankAccountInfor(e){return this.apiService.requestProcess(e,a.LAY_THONG_TIN_TAI_KHOAN_LIEN_NGAN_HANG)}getListSocietyConnection(e){return this.apiService.requestProcess(e,a.LAY_DANH_SACH_CHUYEN_TIEN_TU_THIEN,!0,5)}initCharityTransfer(e){return this.apiService.requestProcess(e,a.KHOI_TAO_CHUYEN_TIEN_TU_THIEN,!1,null,!0)}confirmSocietyConnection(e){return this.apiService.requestProcess(e,a.XAC_NHAN_CHUYEN_TIEN_TU_THIEN)}initSellForeignCurrency(e){return this.apiService.requestProcess(e,a.KHOI_TAO_BAN_NGOAI_TE)}confirmSellForeignCurrency(e){return this.apiService.requestProcess(e,a.XAC_NHAN_BAN_NGOAI_TE)}getListBeneficiary(e,r){return e.newDesign=d.NewDesign,this.apiService.requestProcess(e,a.LAY_DANH_SACH_DANH_BA_THU_HUONG,r)}deleteBeneficiary(e){return e.newDesign=d.NewDesign,this.apiService.requestProcess(e,a.XOA_DANH_BA_THU_HUONG)}updateBeneficiary(e){return e.newDesign=d.NewDesign,this.apiService.requestProcess(e,a.SUA_DANH_BA_THU_HUONG)}addBeneficiary(e){return e.newDesign=d.NewDesign,this.apiService.requestProcess(e,a.THEM_DANH_BA_THU_HUONG)}getListTransferTemplate(e){return this.apiService.requestProcess(e,a.LAY_DANH_SACH_MAU_CT)}addTransferTemplate(e){return this.apiService.requestProcess(e,a.THEM_MAU_CT)}updateTransferTemplate(e){return this.apiService.requestProcess(e,a.SUA_MAU_CT)}deleteTransferTemplate(e){return this.apiService.requestProcess(e,a.XOA_MAU_CT)}getListRecentTransactions(e){return this.apiService.requestProcess(e,a.LAY_DANH_SACH_GIAO_DICH_GAN_DAY)}deleteRecentTransactions(e){return this.apiService.requestProcess(e,a.XOA_GIAO_DICH_GAN_DAY)}gotoPage(e){let r={},t="";switch(e.serviceCode){case i.ck_noi_bo_khac_chu_tk:r={accName:e.toAccName,accNo:e.toAcc},t="noi-bo";break;case i.ck_noi_bo_so_the:r={accName:e.toAccName,cardNo:e.toCard},t="noi-bo";break;case i.ck_nhanh_lien_ngan_hang:r={bankCode247:e.bankCode247,accNo:e.toAcc,accName:e.toAccName,bankName:e.bankName,sortName:e.sortName,urlLogo:e.urlLogo,bankId:e.bankId,contactId:e.contactId},t="ngoai-bidv";break;case i.ck_thuong_lien_ngan_hang:r={bankCode:e.bankCode,accNo:e.toAcc,accName:e.toAccName,bankName:e.bankName,sortName:e.sortName,urlLogo:e.urlLogo,bankId:e.bankId,contactId:e.contactId,bankCode247:e.bankCode247},t="ngoai-bidv";break;case i.ck_nhanh_lien_ngan_hang_den_so_the:r={cardNo:e.toCard,accName:e.toAccName,bankName:e.bankName,sortName:e.sortName,urlLogo:e.urlLogo},t="ngoai-bidv-qua-so-the";break}r.serviceCode=e.serviceCode;let n={state:{taiKhoan:r}};this.router.navigate(["chuyen-tien",t],n)}gotoPageCT(e){let r={},t={},n="";switch(r={accNo:e.fromAcc,accName:e.fromAccName},e.serviceCode){case i.ck_noi_bo_cung_chu_tk:t={accName:e.toAccName,accNo:e.toAcc,cardNo:e.toCard},n="noi-bo";break;case i.ck_noi_bo_khac_chu_tk:t={accName:e.toAccName,accNo:e.toAcc,cardNo:e.toCard},n="noi-bo";break;case i.ck_noi_bo_so_the:t={accName:e.toAccName,cardNo:e.toCard},n="noi-bo";break;case i.ck_nhanh_lien_ngan_hang:if(e?.toAcc){t={bankCode247:e.bankCode247,accNo:e.toAcc,accName:e.toAccName,bankName:e.bankName,sortName:e.sortName,urlLogo:e.urlLogo,bankId:e.bankId},n="ngoai-bidv";break}else{this.router.navigate(["/chuyen-tien",{tabTimKiem:"tab2",bankId:e?.bankId}]);break}case i.ck_thuong_lien_ngan_hang:t={bankCode:e.bankCode,accNo:e.toAcc,accName:e.toAccName,bankName:e.bankName,sortName:e.sortName,urlLogo:e.urlLogo,bankId:e.bankId},n="ngoai-bidv";break;case i.ck_nhanh_lien_ngan_hang_den_so_the:t={cardNo:e.toCard,accName:e.toAccName||e.toAcc,bankName:e.bankName,sortName:e.sortName,urlLogo:e.urlLogo,bankCode:e.bankCode,bankCode247:e.bankCode247},n="ngoai-bidv-qua-so-the";break}t.serviceCode=e.serviceCode,e.loaiCK=me.Quick;let b={state:{taiKhoanNhan:t,contact:e}};this.router.navigate(["chuyen-tien",n],b)}setList(e){this.accountListsub$.next(e)}getList(){return this.accountListsub$.asObservable()}checkBINCodeCardNumber(e,r,t){let n=t.slice(0,he);if((r.binCodeList?.split(",")||[]).some(c=>n.includes(c)))return G(!0);let m=e.filter(c=>(c.binCodeList?.split(",")||[]).some(v=>n.includes(v))),f=m.map(c=>c.sortName||c.bankName).join(", "),h=r?.sortName||r?.bankName,k=m.length===1?this.translate.instant("message.card_number_belong_other_bank",{otherBank:f,bank:h}):m.length>1?this.translate.instant("message.card_number_belong_other_banks"):this.translate.instant("message.card_number_not_belong_bank",{otherBank:f,bank:h});return new B(c=>{this.modalService.confirm({message:k,btnConfirm:{title:this.translate.instant("tiep_tuc"),color:L.Primary},btnCancel:{title:this.translate.instant("huy_bo"),color:L.Outline},confirm:()=>c.next(!0),cancel:()=>c.next(!1)})})}handleSaveDirectory(e){let{toAcc:r,serviceCode:t,bankId:n,bankCode:b,bankCode247:m,cardNo:f,toAccName:h,bankName:k,urlLogo:c,email:v,idAcc:U,type:Ie,amount:ge,remark:ke}=e||{},ve={name:h,amount:ge,content:ke,maxLength:m?150:190};this.modalService.open(Te,{data:ve,size:$.Large,maskClosable:!1})?.afterClose.pipe(K(u=>{if(u){let T={toAcc:r,beneName:u?.name,amount:u?.amount,remark:u?.content,serviceCode:t,bankId:n,bankCode:b||"",toCard:f,toAccName:h,bankName:k,urlLogo:c,email:v,newDesign:d.NewDesign};return U&&(T=y(P({},T),{virtualAccount:U,virtualAccountType:Ie}),delete T.toAcc),this.loadingService.showLoading(),this.addBeneficiary(T)}return Y})).subscribe({next:u=>{this.modalService.success(this.translate.instant("luu_danh_ba_thanh_cong"))},error:u=>{this.modalService.error(u?.message||u?.error?.des||this.translate.instant("that_bai"))}})}static \u0275fac=function(r){return new(r||o)};static \u0275prov=M({token:o,factory:o.\u0275fac,providedIn:"root"})}return o})();export{Te as a,ct as b};
