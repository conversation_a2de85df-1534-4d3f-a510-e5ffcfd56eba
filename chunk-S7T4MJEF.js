import{H as o,f as t,ha as s,o as n,t as c}from"./chunk-YK6FMNSY.js";var d=(()=>{class e{cancelPendingRequests$=new t;constructor(){}cancelPendingRequests(){this.cancelPendingRequests$.next({code:"request_is_canceled_by_user_or_server",des:null})}onCancelPendingRequests(){return this.cancelPendingRequests$.asObservable().pipe(c(r=>{throw r}),o(r=>n({error:r,message:null})))}static \u0275fac=function(a){return new(a||e)};static \u0275prov=s({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();export{d as a};
