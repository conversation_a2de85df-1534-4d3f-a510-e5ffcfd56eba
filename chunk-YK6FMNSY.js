import{a as xe,b as dt,d as vc}from"./chunk-TSRGIXR5.js";function Dc(t,e){return Object.is(t,e)}var re=null,lr=!1,Qr=1,Je=Symbol("SIGNAL");function O(t){let e=re;return re=t,e}function Ec(){return re}function ug(){return lr}var Mn={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function Ms(t){if(lr)throw new Error("");if(re===null)return;re.consumerOnSignalRead(t);let e=re.nextProducerIndex++;if(eo(re),e<re.producerNode.length&&re.producerNode[e]!==t&&ur(re)){let n=re.producerNode[e];Xr(n,re.producerIndexOfThis[e])}re.producerNode[e]!==t&&(re.producerNode[e]=t,re.producerIndexOfThis[e]=ur(re)?Cc(t,re,e):0),re.producerLastReadVersion[e]=t.version}function cg(){Qr++}function wc(t){if(!(ur(t)&&!t.dirty)&&!(!t.dirty&&t.lastCleanEpoch===Qr)){if(!t.producerMustRecompute(t)&&!Yr(t)){t.dirty=!1,t.lastCleanEpoch=Qr;return}t.producerRecomputeValue(t),t.dirty=!1,t.lastCleanEpoch=Qr}}function _c(t){if(t.liveConsumerNode===void 0)return;let e=lr;lr=!0;try{for(let n of t.liveConsumerNode)n.dirty||bc(n)}finally{lr=e}}function Ic(){return re?.consumerAllowSignalWrites!==!1}function bc(t){t.dirty=!0,_c(t),t.consumerMarkedDirty?.(t)}function cr(t){return t&&(t.nextProducerIndex=0),O(t)}function Kr(t,e){if(O(e),!(!t||t.producerNode===void 0||t.producerIndexOfThis===void 0||t.producerLastReadVersion===void 0)){if(ur(t))for(let n=t.nextProducerIndex;n<t.producerNode.length;n++)Xr(t.producerNode[n],t.producerIndexOfThis[n]);for(;t.producerNode.length>t.nextProducerIndex;)t.producerNode.pop(),t.producerLastReadVersion.pop(),t.producerIndexOfThis.pop()}}function Yr(t){eo(t);for(let e=0;e<t.producerNode.length;e++){let n=t.producerNode[e],r=t.producerLastReadVersion[e];if(r!==n.version||(wc(n),r!==n.version))return!0}return!1}function Jr(t){if(eo(t),ur(t))for(let e=0;e<t.producerNode.length;e++)Xr(t.producerNode[e],t.producerIndexOfThis[e]);t.producerNode.length=t.producerLastReadVersion.length=t.producerIndexOfThis.length=0,t.liveConsumerNode&&(t.liveConsumerNode.length=t.liveConsumerIndexOfThis.length=0)}function Cc(t,e,n){if(Sc(t),t.liveConsumerNode.length===0&&Mc(t))for(let r=0;r<t.producerNode.length;r++)t.producerIndexOfThis[r]=Cc(t.producerNode[r],t,r);return t.liveConsumerIndexOfThis.push(n),t.liveConsumerNode.push(e)-1}function Xr(t,e){if(Sc(t),t.liveConsumerNode.length===1&&Mc(t))for(let r=0;r<t.producerNode.length;r++)Xr(t.producerNode[r],t.producerIndexOfThis[r]);let n=t.liveConsumerNode.length-1;if(t.liveConsumerNode[e]=t.liveConsumerNode[n],t.liveConsumerIndexOfThis[e]=t.liveConsumerIndexOfThis[n],t.liveConsumerNode.length--,t.liveConsumerIndexOfThis.length--,e<t.liveConsumerNode.length){let r=t.liveConsumerIndexOfThis[e],o=t.liveConsumerNode[e];eo(o),o.producerIndexOfThis[r]=e}}function ur(t){return t.consumerIsAlwaysLive||(t?.liveConsumerNode?.length??0)>0}function eo(t){t.producerNode??=[],t.producerIndexOfThis??=[],t.producerLastReadVersion??=[]}function Sc(t){t.liveConsumerNode??=[],t.liveConsumerIndexOfThis??=[]}function Mc(t){return t.producerNode!==void 0}function Tc(t){let e=Object.create(dg);e.computation=t;let n=()=>{if(wc(e),Ms(e),e.value===Zr)throw e.error;return e.value};return n[Je]=e,n}var bs=Symbol("UNSET"),Cs=Symbol("COMPUTING"),Zr=Symbol("ERRORED"),dg=dt(xe({},Mn),{value:bs,dirty:!0,error:null,equal:Dc,producerMustRecompute(t){return t.value===bs||t.value===Cs},producerRecomputeValue(t){if(t.value===Cs)throw new Error("Detected cycle in computations.");let e=t.value;t.value=Cs;let n=cr(t),r;try{r=t.computation()}catch(o){r=Zr,t.error=o}finally{Kr(t,n)}if(e!==bs&&e!==Zr&&r!==Zr&&t.equal(e,r)){t.value=e;return}t.value=r,t.version++}});function fg(){throw new Error}var Nc=fg;function xc(){Nc()}function Ac(t){Nc=t}var hg=null;function Oc(t){let e=Object.create(Fc);e.value=t;let n=()=>(Ms(e),e.value);return n[Je]=e,n}function Ts(t,e){Ic()||xc(),t.equal(t.value,e)||(t.value=e,pg(t))}function Pc(t,e){Ic()||xc(),Ts(t,e(t.value))}var Fc=dt(xe({},Mn),{equal:Dc,value:void 0});function pg(t){t.version++,cg(),_c(t),hg?.()}function Rc(t,e,n){let r=Object.create(mg);n&&(r.consumerAllowSignalWrites=!0),r.fn=t,r.schedule=e;let o=l=>{r.cleanupFn=l};function i(l){return l.fn===null&&l.schedule===null}function s(l){i(l)||(Jr(l),l.cleanupFn(),l.fn=null,l.schedule=null,l.cleanupFn=Ss)}let a=()=>{if(r.fn===null)return;if(ug())throw new Error("Schedulers cannot synchronously execute watches while scheduling.");if(r.dirty=!1,r.hasRun&&!Yr(r))return;r.hasRun=!0;let l=cr(r);try{r.cleanupFn(),r.cleanupFn=Ss,r.fn(o)}finally{Kr(r,l)}};return r.ref={notify:()=>bc(r),run:a,cleanup:()=>r.cleanupFn(),destroy:()=>s(r),[Je]:r},r.ref}var Ss=()=>{},mg=dt(xe({},Mn),{consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!1,consumerMarkedDirty:t=>{t.schedule!==null&&t.schedule(t.ref)},hasRun:!1,cleanupFn:Ss});function b(t){return typeof t=="function"}function Ot(t){let n=t(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var to=Ot(t=>function(n){t(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function Xt(t,e){if(t){let n=t.indexOf(e);0<=n&&t.splice(n,1)}}var X=class t{constructor(e){this.initialTeardown=e,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let e;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(b(r))try{r()}catch(i){e=i instanceof to?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{kc(i)}catch(s){e=e??[],s instanceof to?e=[...e,...s.errors]:e.push(s)}}if(e)throw new to(e)}}add(e){var n;if(e&&e!==this)if(this.closed)kc(e);else{if(e instanceof t){if(e.closed||e._hasParent(this))return;e._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(e)}}_hasParent(e){let{_parentage:n}=this;return n===e||Array.isArray(n)&&n.includes(e)}_addParent(e){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(e),n):n?[n,e]:e}_removeParent(e){let{_parentage:n}=this;n===e?this._parentage=null:Array.isArray(n)&&Xt(n,e)}remove(e){let{_finalizers:n}=this;n&&Xt(n,e),e instanceof t&&e._removeParent(this)}};X.EMPTY=(()=>{let t=new X;return t.closed=!0,t})();var Ns=X.EMPTY;function no(t){return t instanceof X||t&&"closed"in t&&b(t.remove)&&b(t.add)&&b(t.unsubscribe)}function kc(t){b(t)?t():t.unsubscribe()}var Ue={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Tn={setTimeout(t,e,...n){let{delegate:r}=Tn;return r?.setTimeout?r.setTimeout(t,e,...n):setTimeout(t,e,...n)},clearTimeout(t){let{delegate:e}=Tn;return(e?.clearTimeout||clearTimeout)(t)},delegate:void 0};function ro(t){Tn.setTimeout(()=>{let{onUnhandledError:e}=Ue;if(e)e(t);else throw t})}function ft(){}var Lc=xs("C",void 0,void 0);function jc(t){return xs("E",void 0,t)}function Vc(t){return xs("N",t,void 0)}function xs(t,e,n){return{kind:t,value:e,error:n}}var en=null;function Nn(t){if(Ue.useDeprecatedSynchronousErrorHandling){let e=!en;if(e&&(en={errorThrown:!1,error:null}),t(),e){let{errorThrown:n,error:r}=en;if(en=null,n)throw r}}else t()}function Bc(t){Ue.useDeprecatedSynchronousErrorHandling&&en&&(en.errorThrown=!0,en.error=t)}var tn=class extends X{constructor(e){super(),this.isStopped=!1,e?(this.destination=e,no(e)&&e.add(this)):this.destination=vg}static create(e,n,r){return new ht(e,n,r)}next(e){this.isStopped?Os(Vc(e),this):this._next(e)}error(e){this.isStopped?Os(jc(e),this):(this.isStopped=!0,this._error(e))}complete(){this.isStopped?Os(Lc,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(e){this.destination.next(e)}_error(e){try{this.destination.error(e)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},gg=Function.prototype.bind;function As(t,e){return gg.call(t,e)}var Ps=class{constructor(e){this.partialObserver=e}next(e){let{partialObserver:n}=this;if(n.next)try{n.next(e)}catch(r){oo(r)}}error(e){let{partialObserver:n}=this;if(n.error)try{n.error(e)}catch(r){oo(r)}else oo(e)}complete(){let{partialObserver:e}=this;if(e.complete)try{e.complete()}catch(n){oo(n)}}},ht=class extends tn{constructor(e,n,r){super();let o;if(b(e)||!e)o={next:e??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&Ue.useDeprecatedNextContext?(i=Object.create(e),i.unsubscribe=()=>this.unsubscribe(),o={next:e.next&&As(e.next,i),error:e.error&&As(e.error,i),complete:e.complete&&As(e.complete,i)}):o=e}this.destination=new Ps(o)}};function oo(t){Ue.useDeprecatedSynchronousErrorHandling?Bc(t):ro(t)}function yg(t){throw t}function Os(t,e){let{onStoppedNotification:n}=Ue;n&&Tn.setTimeout(()=>n(t,e))}var vg={closed:!0,next:ft,error:yg,complete:ft};var xn=typeof Symbol=="function"&&Symbol.observable||"@@observable";function ue(t){return t}function Dg(...t){return Fs(t)}function Fs(t){return t.length===0?ue:t.length===1?t[0]:function(n){return t.reduce((r,o)=>o(r),n)}}var R=(()=>{class t{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new t;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=wg(n)?n:new ht(n,r,o);return Nn(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=$c(r),new r((o,i)=>{let s=new ht({next:a=>{try{n(a)}catch(l){i(l),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[xn](){return this}pipe(...n){return Fs(n)(this)}toPromise(n){return n=$c(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return t.create=e=>new t(e),t})();function $c(t){var e;return(e=t??Ue.Promise)!==null&&e!==void 0?e:Promise}function Eg(t){return t&&b(t.next)&&b(t.error)&&b(t.complete)}function wg(t){return t&&t instanceof tn||Eg(t)&&no(t)}function Rs(t){return b(t?.lift)}function w(t){return e=>{if(Rs(e))return e.lift(function(n){try{return t(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function _(t,e,n,r,o){return new ks(t,e,n,r,o)}var ks=class extends tn{constructor(e,n,r,o,i,s){super(e),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(l){e.error(l)}}:super._next,this._error=o?function(a){try{o(a)}catch(l){e.error(l)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){e.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var e;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((e=this.onFinalize)===null||e===void 0||e.call(this))}}};function Ls(){return w((t,e)=>{let n=null;t._refCount++;let r=_(e,void 0,void 0,void 0,()=>{if(!t||t._refCount<=0||0<--t._refCount){n=null;return}let o=t._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),e.unsubscribe()});t.subscribe(r),r.closed||(n=t.connect())})}var js=class extends R{constructor(e,n){super(),this.source=e,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,Rs(e)&&(this.lift=e.lift)}_subscribe(e){return this.getSubject().subscribe(e)}getSubject(){let e=this._subject;return(!e||e.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:e}=this;this._subject=this._connection=null,e?.unsubscribe()}connect(){let e=this._connection;if(!e){e=this._connection=new X;let n=this.getSubject();e.add(this.source.subscribe(_(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),e.closed&&(this._connection=null,e=X.EMPTY)}return e}refCount(){return Ls()(this)}};var An={schedule(t){let e=requestAnimationFrame,n=cancelAnimationFrame,{delegate:r}=An;r&&(e=r.requestAnimationFrame,n=r.cancelAnimationFrame);let o=e(i=>{n=void 0,t(i)});return new X(()=>n?.(o))},requestAnimationFrame(...t){let{delegate:e}=An;return(e?.requestAnimationFrame||requestAnimationFrame)(...t)},cancelAnimationFrame(...t){let{delegate:e}=An;return(e?.cancelAnimationFrame||cancelAnimationFrame)(...t)},delegate:void 0};var Hc=Ot(t=>function(){t(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var Ie=(()=>{class t extends R{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new io(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new Hc}next(n){Nn(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){Nn(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){Nn(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Ns:(this.currentObservers=null,i.push(n),new X(()=>{this.currentObservers=null,Xt(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new R;return n.source=this,n}}return t.create=(e,n)=>new io(e,n),t})(),io=class extends Ie{constructor(e,n){super(),this.destination=e,this.source=n}next(e){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,e)}error(e){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,e)}complete(){var e,n;(n=(e=this.destination)===null||e===void 0?void 0:e.complete)===null||n===void 0||n.call(e)}_subscribe(e){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(e))!==null&&r!==void 0?r:Ns}};var dr=class extends Ie{constructor(e){super(),this._value=e}get value(){return this.getValue()}_subscribe(e){let n=super._subscribe(e);return!n.closed&&e.next(this._value),n}getValue(){let{hasError:e,thrownError:n,_value:r}=this;if(e)throw n;return this._throwIfClosed(),r}next(e){super.next(this._value=e)}};var fr={now(){return(fr.delegate||Date).now()},delegate:void 0};var hr=class extends Ie{constructor(e=1/0,n=1/0,r=fr){super(),this._bufferSize=e,this._windowTime=n,this._timestampProvider=r,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=n===1/0,this._bufferSize=Math.max(1,e),this._windowTime=Math.max(1,n)}next(e){let{isStopped:n,_buffer:r,_infiniteTimeWindow:o,_timestampProvider:i,_windowTime:s}=this;n||(r.push(e),!o&&r.push(i.now()+s)),this._trimBuffer(),super.next(e)}_subscribe(e){this._throwIfClosed(),this._trimBuffer();let n=this._innerSubscribe(e),{_infiniteTimeWindow:r,_buffer:o}=this,i=o.slice();for(let s=0;s<i.length&&!e.closed;s+=r?1:2)e.next(i[s]);return this._checkFinalizedStatuses(e),n}_trimBuffer(){let{_bufferSize:e,_timestampProvider:n,_buffer:r,_infiniteTimeWindow:o}=this,i=(o?1:2)*e;if(e<1/0&&i<r.length&&r.splice(0,r.length-i),!o){let s=n.now(),a=0;for(let l=1;l<r.length&&r[l]<=s;l+=2)a=l;a&&r.splice(0,a+1)}}};var so=class extends X{constructor(e,n){super()}schedule(e,n=0){return this}};var pr={setInterval(t,e,...n){let{delegate:r}=pr;return r?.setInterval?r.setInterval(t,e,...n):setInterval(t,e,...n)},clearInterval(t){let{delegate:e}=pr;return(e?.clearInterval||clearInterval)(t)},delegate:void 0};var Pt=class extends so{constructor(e,n){super(e,n),this.scheduler=e,this.work=n,this.pending=!1}schedule(e,n=0){var r;if(this.closed)return this;this.state=e;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(e,n,r=0){return pr.setInterval(e.flush.bind(e,this),r)}recycleAsyncId(e,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&pr.clearInterval(n)}execute(e,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(e,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(e,n){let r=!1,o;try{this.work(e)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:e,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,Xt(r,this),e!=null&&(this.id=this.recycleAsyncId(n,e,null)),this.delay=null,super.unsubscribe()}}};var _g=1,Vs,Bs={};function Uc(t){return t in Bs?(delete Bs[t],!0):!1}var zc={setImmediate(t){let e=_g++;return Bs[e]=!0,Vs||(Vs=Promise.resolve()),Vs.then(()=>Uc(e)&&t()),e},clearImmediate(t){Uc(t)}};var{setImmediate:Ig,clearImmediate:bg}=zc,mr={setImmediate(...t){let{delegate:e}=mr;return(e?.setImmediate||Ig)(...t)},clearImmediate(t){let{delegate:e}=mr;return(e?.clearImmediate||bg)(t)},delegate:void 0};var ao=class extends Pt{constructor(e,n){super(e,n),this.scheduler=e,this.work=n}requestAsyncId(e,n,r=0){return r!==null&&r>0?super.requestAsyncId(e,n,r):(e.actions.push(this),e._scheduled||(e._scheduled=mr.setImmediate(e.flush.bind(e,void 0))))}recycleAsyncId(e,n,r=0){var o;if(r!=null?r>0:this.delay>0)return super.recycleAsyncId(e,n,r);let{actions:i}=e;n!=null&&((o=i[i.length-1])===null||o===void 0?void 0:o.id)!==n&&(mr.clearImmediate(n),e._scheduled=void 0)}};var On=class t{constructor(e,n=t.now){this.schedulerActionCtor=e,this.now=n}schedule(e,n=0,r){return new this.schedulerActionCtor(this,e).schedule(r,n)}};On.now=fr.now;var Ft=class extends On{constructor(e,n=On.now){super(e,n),this.actions=[],this._active=!1}flush(e){let{actions:n}=this;if(this._active){n.push(e);return}let r;this._active=!0;do if(r=e.execute(e.state,e.delay))break;while(e=n.shift());if(this._active=!1,r){for(;e=n.shift();)e.unsubscribe();throw r}}};var lo=class extends Ft{flush(e){this._active=!0;let n=this._scheduled;this._scheduled=void 0;let{actions:r}=this,o;e=e||r.shift();do if(o=e.execute(e.state,e.delay))break;while((e=r[0])&&e.id===n&&r.shift());if(this._active=!1,o){for(;(e=r[0])&&e.id===n&&r.shift();)e.unsubscribe();throw o}}};var Cg=new lo(ao);var ze=new Ft(Pt),qc=ze;var uo=class extends Pt{constructor(e,n){super(e,n),this.scheduler=e,this.work=n}requestAsyncId(e,n,r=0){return r!==null&&r>0?super.requestAsyncId(e,n,r):(e.actions.push(this),e._scheduled||(e._scheduled=An.requestAnimationFrame(()=>e.flush(void 0))))}recycleAsyncId(e,n,r=0){var o;if(r!=null?r>0:this.delay>0)return super.recycleAsyncId(e,n,r);let{actions:i}=e;n!=null&&((o=i[i.length-1])===null||o===void 0?void 0:o.id)!==n&&(An.cancelAnimationFrame(n),e._scheduled=void 0)}};var co=class extends Ft{flush(e){this._active=!0;let n=this._scheduled;this._scheduled=void 0;let{actions:r}=this,o;e=e||r.shift();do if(o=e.execute(e.state,e.delay))break;while((e=r[0])&&e.id===n&&r.shift());if(this._active=!1,o){for(;(e=r[0])&&e.id===n&&r.shift();)e.unsubscribe();throw o}}};var Sg=new co(uo);var nn=new R(t=>t.complete());function fo(t){return t&&b(t.schedule)}function $s(t){return t[t.length-1]}function Pn(t){return b($s(t))?t.pop():void 0}function Xe(t){return fo($s(t))?t.pop():void 0}function Gc(t,e){return typeof $s(t)=="number"?t.pop():e}function fM(t,e,n,r){var o=arguments.length,i=o<3?e:r===null?r=Object.getOwnPropertyDescriptor(e,n):r,s;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")i=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(s=t[a])&&(i=(o<3?s(i):o>3?s(e,n,i):s(e,n))||i);return o>3&&i&&Object.defineProperty(e,n,i),i}function Qc(t,e,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(c){try{u(r.next(c))}catch(d){s(d)}}function l(c){try{u(r.throw(c))}catch(d){s(d)}}function u(c){c.done?i(c.value):o(c.value).then(a,l)}u((r=r.apply(t,e||[])).next())})}function Wc(t){var e=typeof Symbol=="function"&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&typeof t.length=="number")return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function rn(t){return this instanceof rn?(this.v=t,this):new rn(t)}function Zc(t,e,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(t,e||[]),o,i=[];return o={},s("next"),s("throw"),s("return"),o[Symbol.asyncIterator]=function(){return this},o;function s(h){r[h]&&(o[h]=function(f){return new Promise(function(p,m){i.push([h,f,p,m])>1||a(h,f)})})}function a(h,f){try{l(r[h](f))}catch(p){d(i[0][3],p)}}function l(h){h.value instanceof rn?Promise.resolve(h.value.v).then(u,c):d(i[0][2],h)}function u(h){a("next",h)}function c(h){a("throw",h)}function d(h,f){h(f),i.shift(),i.length&&a(i[0][0],i[0][1])}}function Kc(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e=t[Symbol.asyncIterator],n;return e?e.call(t):(t=typeof Wc=="function"?Wc(t):t[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=t[i]&&function(s){return new Promise(function(a,l){s=t[i](s),o(a,l,s.done,s.value)})}}function o(i,s,a,l){Promise.resolve(l).then(function(u){i({value:u,done:a})},s)}}var Fn=t=>t&&typeof t.length=="number"&&typeof t!="function";function ho(t){return b(t?.then)}function po(t){return b(t[xn])}function mo(t){return Symbol.asyncIterator&&b(t?.[Symbol.asyncIterator])}function go(t){return new TypeError(`You provided ${t!==null&&typeof t=="object"?"an invalid object":`'${t}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function Mg(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var yo=Mg();function vo(t){return b(t?.[yo])}function Do(t){return Zc(this,arguments,function*(){let n=t.getReader();try{for(;;){let{value:r,done:o}=yield rn(n.read());if(o)return yield rn(void 0);yield yield rn(r)}}finally{n.releaseLock()}})}function Eo(t){return b(t?.getReader)}function P(t){if(t instanceof R)return t;if(t!=null){if(po(t))return Tg(t);if(Fn(t))return Ng(t);if(ho(t))return xg(t);if(mo(t))return Yc(t);if(vo(t))return Ag(t);if(Eo(t))return Og(t)}throw go(t)}function Tg(t){return new R(e=>{let n=t[xn]();if(b(n.subscribe))return n.subscribe(e);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Ng(t){return new R(e=>{for(let n=0;n<t.length&&!e.closed;n++)e.next(t[n]);e.complete()})}function xg(t){return new R(e=>{t.then(n=>{e.closed||(e.next(n),e.complete())},n=>e.error(n)).then(null,ro)})}function Ag(t){return new R(e=>{for(let n of t)if(e.next(n),e.closed)return;e.complete()})}function Yc(t){return new R(e=>{Pg(t,e).catch(n=>e.error(n))})}function Og(t){return Yc(Do(t))}function Pg(t,e){var n,r,o,i;return Qc(this,void 0,void 0,function*(){try{for(n=Kc(t);r=yield n.next(),!r.done;){let s=r.value;if(e.next(s),e.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}e.complete()})}function he(t,e,n,r=0,o=!1){let i=e.schedule(function(){n(),o?t.add(this.schedule(null,r)):this.unsubscribe()},r);if(t.add(i),!o)return i}function wo(t,e=0){return w((n,r)=>{n.subscribe(_(r,o=>he(r,t,()=>r.next(o),e),()=>he(r,t,()=>r.complete(),e),o=>he(r,t,()=>r.error(o),e)))})}function _o(t,e=0){return w((n,r)=>{r.add(t.schedule(()=>n.subscribe(r),e))})}function Jc(t,e){return P(t).pipe(_o(e),wo(e))}function Xc(t,e){return P(t).pipe(_o(e),wo(e))}function ed(t,e){return new R(n=>{let r=0;return e.schedule(function(){r===t.length?n.complete():(n.next(t[r++]),n.closed||this.schedule())})})}function td(t,e){return new R(n=>{let r;return he(n,e,()=>{r=t[yo](),he(n,e,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>b(r?.return)&&r.return()})}function Io(t,e){if(!t)throw new Error("Iterable cannot be null");return new R(n=>{he(n,e,()=>{let r=t[Symbol.asyncIterator]();he(n,e,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function nd(t,e){return Io(Do(t),e)}function rd(t,e){if(t!=null){if(po(t))return Jc(t,e);if(Fn(t))return ed(t,e);if(ho(t))return Xc(t,e);if(mo(t))return Io(t,e);if(vo(t))return td(t,e);if(Eo(t))return nd(t,e)}throw go(t)}function et(t,e){return e?rd(t,e):P(t)}function Fg(...t){let e=Xe(t);return et(t,e)}function Rg(t,e){let n=b(t)?t:()=>t,r=o=>o.error(n());return new R(e?o=>e.schedule(r,0,o):r)}function kg(t){return!!t&&(t instanceof R||b(t.lift)&&b(t.subscribe))}var pt=Ot(t=>function(){t(this),this.name="EmptyError",this.message="no elements in sequence"});function Lg(t,e){let n=typeof e=="object";return new Promise((r,o)=>{let i=!1,s;t.subscribe({next:a=>{s=a,i=!0},error:o,complete:()=>{i?r(s):n?r(e.defaultValue):o(new pt)}})})}function bo(t){return t instanceof Date&&!isNaN(t)}var jg=Ot(t=>function(n=null){t(this),this.message="Timeout has occurred",this.name="TimeoutError",this.info=n});function Vg(t,e){let{first:n,each:r,with:o=Bg,scheduler:i=e??ze,meta:s=null}=bo(t)?{first:t}:typeof t=="number"?{each:t}:t;if(n==null&&r==null)throw new TypeError("No timeout provided.");return w((a,l)=>{let u,c,d=null,h=0,f=p=>{c=he(l,i,()=>{try{u.unsubscribe(),P(o({meta:s,lastValue:d,seen:h})).subscribe(l)}catch(m){l.error(m)}},p)};u=a.subscribe(_(l,p=>{c?.unsubscribe(),h++,l.next(d=p),r>0&&f(r)},void 0,void 0,()=>{c?.closed||c?.unsubscribe(),d=null})),!h&&f(n!=null?typeof n=="number"?n:+n-i.now():r)})}function Bg(t){throw new jg(t)}function tt(t,e){return w((n,r)=>{let o=0;n.subscribe(_(r,i=>{r.next(t.call(e,i,o++))}))})}var{isArray:$g}=Array;function Hg(t,e){return $g(e)?t(...e):t(e)}function Rn(t){return tt(e=>Hg(t,e))}var{isArray:Ug}=Array,{getPrototypeOf:zg,prototype:qg,keys:Gg}=Object;function Co(t){if(t.length===1){let e=t[0];if(Ug(e))return{args:e,keys:null};if(Wg(e)){let n=Gg(e);return{args:n.map(r=>e[r]),keys:n}}}return{args:t,keys:null}}function Wg(t){return t&&typeof t=="object"&&zg(t)===qg}function So(t,e){return t.reduce((n,r,o)=>(n[r]=e[o],n),{})}function Qg(...t){let e=Xe(t),n=Pn(t),{args:r,keys:o}=Co(t);if(r.length===0)return et([],e);let i=new R(Zg(r,e,o?s=>So(o,s):ue));return n?i.pipe(Rn(n)):i}function Zg(t,e,n=ue){return r=>{od(e,()=>{let{length:o}=t,i=new Array(o),s=o,a=o;for(let l=0;l<o;l++)od(e,()=>{let u=et(t[l],e),c=!1;u.subscribe(_(r,d=>{i[l]=d,c||(c=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function od(t,e,n){t?he(n,t,e):e()}function Mo(t,e,n,r,o,i,s,a){let l=[],u=0,c=0,d=!1,h=()=>{d&&!l.length&&!u&&e.complete()},f=m=>u<r?p(m):l.push(m),p=m=>{i&&e.next(m),u++;let D=!1;P(n(m,c++)).subscribe(_(e,E=>{o?.(E),i?f(E):e.next(E)},()=>{D=!0},void 0,()=>{if(D)try{for(u--;l.length&&u<r;){let E=l.shift();s?he(e,s,()=>p(E)):p(E)}h()}catch(E){e.error(E)}}))};return t.subscribe(_(e,f,()=>{d=!0,h()})),()=>{a?.()}}function Le(t,e,n=1/0){return b(e)?Le((r,o)=>tt((i,s)=>e(r,i,o,s))(P(t(r,o))),n):(typeof e=="number"&&(n=e),w((r,o)=>Mo(r,o,t,n)))}function gr(t=1/0){return Le(ue,t)}function id(){return gr(1)}function kn(...t){return id()(et(t,Xe(t)))}function Hs(t){return new R(e=>{P(t()).subscribe(e)})}function Kg(...t){let e=Pn(t),{args:n,keys:r}=Co(t),o=new R(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),l=s,u=s;for(let c=0;c<s;c++){let d=!1;P(n[c]).subscribe(_(i,h=>{d||(d=!0,u--),a[c]=h},()=>l--,void 0,()=>{(!l||!d)&&(u||i.next(r?So(r,a):a),i.complete())}))}});return e?o.pipe(Rn(e)):o}var Yg=["addListener","removeListener"],Jg=["addEventListener","removeEventListener"],Xg=["on","off"];function Us(t,e,n,r){if(b(n)&&(r=n,n=void 0),r)return Us(t,e,n).pipe(Rn(r));let[o,i]=ny(t)?Jg.map(s=>a=>t[s](e,a,n)):ey(t)?Yg.map(sd(t,e)):ty(t)?Xg.map(sd(t,e)):[];if(!o&&Fn(t))return Le(s=>Us(s,e,n))(P(t));if(!o)throw new TypeError("Invalid event target");return new R(s=>{let a=(...l)=>s.next(1<l.length?l:l[0]);return o(a),()=>i(a)})}function sd(t,e){return n=>r=>t[n](e,r)}function ey(t){return b(t.addListener)&&b(t.removeListener)}function ty(t){return b(t.on)&&b(t.off)}function ny(t){return b(t.addEventListener)&&b(t.removeEventListener)}function ry(t,e,n){return Hs(()=>t()?e:n)}function on(t=0,e,n=qc){let r=-1;return e!=null&&(fo(e)?n=e:r=e),new R(o=>{let i=bo(t)?+t-n.now():t;i<0&&(i=0);let s=0;return n.schedule(function(){o.closed||(o.next(s++),0<=r?this.schedule(void 0,r):o.complete())},i)})}function oy(t=0,e=ze){return t<0&&(t=0),on(t,t,e)}function iy(...t){let e=Xe(t),n=Gc(t,1/0),r=t;return r.length?r.length===1?P(r[0]):gr(n)(et(r,e)):nn}function Rt(t,e){return w((n,r)=>{let o=0;n.subscribe(_(r,i=>t.call(e,i,o++)&&r.next(i)))})}function ad(t){return w((e,n)=>{let r=!1,o=null,i=null,s=!1,a=()=>{if(i?.unsubscribe(),i=null,r){r=!1;let u=o;o=null,n.next(u)}s&&n.complete()},l=()=>{i=null,s&&n.complete()};e.subscribe(_(n,u=>{r=!0,o=u,i||P(t(u)).subscribe(i=_(n,a,l))},()=>{s=!0,(!r||!i||i.closed)&&n.complete()}))})}function sy(t,e=ze){return ad(()=>on(t,e))}function ld(t){return w((e,n)=>{let r=null,o=!1,i;r=e.subscribe(_(n,void 0,void 0,s=>{i=P(t(s,ld(t)(e))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function To(t,e,n,r,o){return(i,s)=>{let a=n,l=e,u=0;i.subscribe(_(s,c=>{let d=u++;l=a?t(l,c,d):(a=!0,c),r&&s.next(l)},o&&(()=>{a&&s.next(l),s.complete()})))}}function ud(t,e){return w(To(t,e,arguments.length>=2,!1,!0))}var ay=(t,e)=>(t.push(e),t);function ly(){return w((t,e)=>{ud(ay,[])(t).subscribe(e)})}function uy(t,e){return b(e)?Le(t,e,1):Le(t,1)}function cd(t,e=ze){return w((n,r)=>{let o=null,i=null,s=null,a=()=>{if(o){o.unsubscribe(),o=null;let u=i;i=null,r.next(u)}};function l(){let u=s+t,c=e.now();if(c<u){o=this.schedule(void 0,u-c),r.add(o);return}a()}n.subscribe(_(r,u=>{i=u,s=e.now(),o||(o=e.schedule(l,t),r.add(o))},()=>{a(),r.complete()},void 0,()=>{i=o=null}))})}function yr(t){return w((e,n)=>{let r=!1;e.subscribe(_(n,o=>{r=!0,n.next(o)},()=>{r||n.next(t),n.complete()}))})}function Ln(t){return t<=0?()=>nn:w((e,n)=>{let r=0;e.subscribe(_(n,o=>{++r<=t&&(n.next(o),t<=r&&n.complete())}))})}function dd(){return w((t,e)=>{t.subscribe(_(e,ft))})}function zs(t){return tt(()=>t)}function qs(t,e){return e?n=>kn(e.pipe(Ln(1),dd()),n.pipe(qs(t))):Le((n,r)=>P(t(n,r)).pipe(Ln(1),zs(n)))}function cy(t,e=ze){let n=on(t,e);return qs(()=>n)}function fd(t,e=ue){return t=t??dy,w((n,r)=>{let o,i=!0;n.subscribe(_(r,s=>{let a=e(s);(i||!t(o,a))&&(i=!1,o=a,r.next(s))}))})}function dy(t,e){return t===e}function No(t=fy){return w((e,n)=>{let r=!1;e.subscribe(_(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(t())))})}function fy(){return new pt}function hy(t,e=1/0,n){return e=(e||0)<1?1/0:e,w((r,o)=>Mo(r,o,t,e,void 0,!0,n))}function py(t){return w((e,n)=>{try{e.subscribe(n)}finally{n.add(t)}})}function hd(t,e){let n=arguments.length>=2;return r=>r.pipe(t?Rt((o,i)=>t(o,i,r)):ue,Ln(1),n?yr(e):No(()=>new pt))}function Gs(t){return t<=0?()=>nn:w((e,n)=>{let r=[];e.subscribe(_(n,o=>{r.push(o),t<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function my(t,e){let n=arguments.length>=2;return r=>r.pipe(t?Rt((o,i)=>t(o,i,r)):ue,Gs(1),n?yr(e):No(()=>new pt))}function pd(){return w((t,e)=>{let n,r=!1;t.subscribe(_(e,o=>{let i=n;n=o,r&&e.next([i,o]),r=!0}))})}function gy(t){return w((e,n)=>{let r,o=!1,i,s=()=>{r=e.subscribe(_(n,void 0,void 0,a=>{i||(i=new Ie,P(t(i)).subscribe(_(n,()=>r?s():o=!0))),i&&i.next(a)})),o&&(r.unsubscribe(),r=null,o=!1,s())};s()})}function yy(t,e){return w(To(t,e,arguments.length>=2,!0))}function Qs(t={}){let{connector:e=()=>new Ie,resetOnError:n=!0,resetOnComplete:r=!0,resetOnRefCountZero:o=!0}=t;return i=>{let s,a,l,u=0,c=!1,d=!1,h=()=>{a?.unsubscribe(),a=void 0},f=()=>{h(),s=l=void 0,c=d=!1},p=()=>{let m=s;f(),m?.unsubscribe()};return w((m,D)=>{u++,!d&&!c&&h();let E=l=l??e();D.add(()=>{u--,u===0&&!d&&!c&&(a=Ws(p,o))}),E.subscribe(D),!s&&u>0&&(s=new ht({next:T=>E.next(T),error:T=>{d=!0,h(),a=Ws(f,n,T),E.error(T)},complete:()=>{c=!0,h(),a=Ws(f,r),E.complete()}}),P(m).subscribe(s))})(i)}}function Ws(t,e,...n){if(e===!0){t();return}if(e===!1)return;let r=new ht({next:()=>{r.unsubscribe(),t()}});return P(e(...n)).subscribe(r)}function vy(t,e,n){let r,o=!1;return t&&typeof t=="object"?{bufferSize:r=1/0,windowTime:e=1/0,refCount:o=!1,scheduler:n}=t:r=t??1/0,Qs({connector:()=>new hr(r,e,n),resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:o})}function md(t){return Rt((e,n)=>t<=n)}function gd(...t){let e=Xe(t);return w((n,r)=>{(e?kn(t,n,e):kn(t,n)).subscribe(r)})}function yd(t,e){return w((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(_(r,l=>{o?.unsubscribe();let u=0,c=i++;P(t(l,c)).subscribe(o=_(r,d=>r.next(e?e(l,d,c,u++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function vd(t){return w((e,n)=>{P(t).subscribe(_(n,()=>n.complete(),ft)),!n.closed&&e.subscribe(n)})}function Dy(t,e=!1){return w((n,r)=>{let o=0;n.subscribe(_(r,i=>{let s=t(i,o++);(s||e)&&r.next(i),!s&&r.complete()}))})}function Dd(t,e,n){let r=b(t)||e||n?{next:t,error:e,complete:n}:t;return r?w((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(_(i,l=>{var u;(u=r.next)===null||u===void 0||u.call(r,l),i.next(l)},()=>{var l;a=!1,(l=r.complete)===null||l===void 0||l.call(r),i.complete()},l=>{var u;a=!1,(u=r.error)===null||u===void 0||u.call(r,l),i.error(l)},()=>{var l,u;a&&((l=r.unsubscribe)===null||l===void 0||l.call(r)),(u=r.finalize)===null||u===void 0||u.call(r)}))}):ue}function Ey(...t){let e=Pn(t);return w((n,r)=>{let o=t.length,i=new Array(o),s=t.map(()=>!1),a=!1;for(let l=0;l<o;l++)P(t[l]).subscribe(_(r,u=>{i[l]=u,!a&&!s[l]&&(s[l]=!0,(a=s.every(ue))&&(s=null))},ft));n.subscribe(_(r,l=>{if(a){let u=[l,...i];r.next(e?e(...u):u)}}))})}var hf="https://g.co/ng/security#xss",g=class extends Error{constructor(e,n){super(pf(e,n)),this.code=e}};function pf(t,e){return`${`NG0${Math.abs(t)}`}${e?": "+e:""}`}function xr(t){return{toString:t}.toString()}var xo="__parameters__";function wy(t){return function(...n){if(t){let r=t(...n);for(let o in r)this[o]=r[o]}}}function _l(t,e,n){return xr(()=>{let r=wy(e);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(l,u,c){let d=l.hasOwnProperty(xo)?l[xo]:Object.defineProperty(l,xo,{value:[]})[xo];for(;d.length<=c;)d.push(null);return(d[c]=d[c]||[]).push(s),l}}return n&&(o.prototype=Object.create(n.prototype)),o.prototype.ngMetadataName=t,o.annotationCls=o,o})}var Lt=globalThis;function U(t){for(let e in t)if(t[e]===U)return e;throw Error("Could not find renamed property on target object.")}function _y(t,e){for(let n in e)e.hasOwnProperty(n)&&!t.hasOwnProperty(n)&&(t[n]=e[n])}function Ee(t){if(typeof t=="string")return t;if(Array.isArray(t))return"["+t.map(Ee).join(", ")+"]";if(t==null)return""+t;if(t.overriddenName)return`${t.overriddenName}`;if(t.name)return`${t.name}`;let e=t.toString();if(e==null)return""+e;let n=e.indexOf(`
`);return n===-1?e:e.substring(0,n)}function la(t,e){return t==null||t===""?e===null?"":e:e==null||e===""?t:t+" "+e}var Iy=U({__forward_ref__:U});function mf(t){return t.__forward_ref__=mf,t.toString=function(){return Ee(this())},t}function ce(t){return gf(t)?t():t}function gf(t){return typeof t=="function"&&t.hasOwnProperty(Iy)&&t.__forward_ref__===mf}function z(t){return{token:t.token,providedIn:t.providedIn||null,factory:t.factory,value:void 0}}function yf(t){return{providers:t.providers||[],imports:t.imports||[]}}function vi(t){return Ed(t,vf)||Ed(t,Df)}function hF(t){return vi(t)!==null}function Ed(t,e){return t.hasOwnProperty(e)?t[e]:null}function by(t){let e=t&&(t[vf]||t[Df]);return e||null}function wd(t){return t&&(t.hasOwnProperty(_d)||t.hasOwnProperty(Cy))?t[_d]:null}var vf=U({\u0275prov:U}),_d=U({\u0275inj:U}),Df=U({ngInjectableDef:U}),Cy=U({ngInjectorDef:U}),j=class{constructor(e,n){this._desc=e,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=z({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function Ef(t){return t&&!!t.\u0275providers}var Sy=U({\u0275cmp:U}),My=U({\u0275dir:U}),Ty=U({\u0275pipe:U}),Ny=U({\u0275mod:U}),zo=U({\u0275fac:U}),Dr=U({__NG_ELEMENT_ID__:U}),Id=U({__NG_ENV_ID__:U});function oe(t){return typeof t=="string"?t:t==null?"":String(t)}function xy(t){return typeof t=="function"?t.name||t.toString():typeof t=="object"&&t!=null&&typeof t.type=="function"?t.type.name||t.type.toString():oe(t)}function Ay(t,e){let n=e?`. Dependency path: ${e.join(" > ")} > ${t}`:"";throw new g(-200,t)}function Il(t,e){throw new g(-201,!1)}var F=function(t){return t[t.Default=0]="Default",t[t.Host=1]="Host",t[t.Self=2]="Self",t[t.SkipSelf=4]="SkipSelf",t[t.Optional=8]="Optional",t}(F||{}),ua;function wf(){return ua}function be(t){let e=ua;return ua=t,e}function _f(t,e,n){let r=vi(t);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&F.Optional)return null;if(e!==void 0)return e;Il(t,"Injector")}var Oy={},wr=Oy,ca="__NG_DI_FLAG__",qo="ngTempTokenPath",Py="ngTokenPath",Fy=/\n/gm,Ry="\u0275",bd="__source",$n;function ky(){return $n}function kt(t){let e=$n;return $n=t,e}function Ly(t,e=F.Default){if($n===void 0)throw new g(-203,!1);return $n===null?_f(t,void 0,e):$n.get(t,e&F.Optional?null:void 0,e)}function ge(t,e=F.Default){return(wf()||Ly)(ce(t),e)}function A(t,e=F.Default){return ge(t,Di(e))}function Di(t){return typeof t>"u"||typeof t=="number"?t:0|(t.optional&&8)|(t.host&&1)|(t.self&&2)|(t.skipSelf&&4)}function da(t){let e=[];for(let n=0;n<t.length;n++){let r=ce(t[n]);if(Array.isArray(r)){if(r.length===0)throw new g(900,!1);let o,i=F.Default;for(let s=0;s<r.length;s++){let a=r[s],l=jy(a);typeof l=="number"?l===-1?o=a.token:i|=l:o=a}e.push(ge(o,i))}else e.push(ge(r))}return e}function bl(t,e){return t[ca]=e,t.prototype[ca]=e,t}function jy(t){return t[ca]}function Vy(t,e,n,r){let o=t[qo];throw e[bd]&&o.unshift(e[bd]),t.message=By(`
`+t.message,o,n,r),t[Py]=o,t[qo]=null,t}function By(t,e,n,r=null){t=t&&t.charAt(0)===`
`&&t.charAt(1)==Ry?t.slice(2):t;let o=Ee(e);if(Array.isArray(e))o=e.map(Ee).join(" -> ");else if(typeof e=="object"){let i=[];for(let s in e)if(e.hasOwnProperty(s)){let a=e[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):Ee(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${t.replace(Fy,`
  `)}`}var pF=bl(_l("Inject",t=>({token:t})),-1),If=bl(_l("Optional"),8);var bf=bl(_l("SkipSelf"),4);function ln(t,e){let n=t.hasOwnProperty(zo);return n?t[zo]:null}function $y(t,e,n){if(t.length!==e.length)return!1;for(let r=0;r<t.length;r++){let o=t[r],i=e[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function Hy(t){return t.flat(Number.POSITIVE_INFINITY)}function Cl(t,e){t.forEach(n=>Array.isArray(n)?Cl(n,e):e(n))}function Cf(t,e,n){e>=t.length?t.push(n):t.splice(e,0,n)}function Go(t,e){return e>=t.length-1?t.pop():t.splice(e,1)[0]}function Uy(t,e){let n=[];for(let r=0;r<t;r++)n.push(e);return n}function zy(t,e,n,r){let o=t.length;if(o==e)t.push(n,r);else if(o===1)t.push(r,t[0]),t[0]=n;else{for(o--,t.push(t[o-1],t[o]);o>e;){let i=o-2;t[o]=t[i],o--}t[e]=n,t[e+1]=r}}function Dn(t,e,n){let r=Ar(t,e);return r>=0?t[r|1]=n:(r=~r,zy(t,r,e,n)),r}function Zs(t,e){let n=Ar(t,e);if(n>=0)return t[n|1]}function Ar(t,e){return qy(t,e,1)}function qy(t,e,n){let r=0,o=t.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=t[i<<n];if(e===s)return i<<n;s>e?o=i:r=i+1}return~(o<<n)}var mt={},Ce=[],Wo=new j(""),Sf=new j("",-1),Mf=new j(""),Qo=class{get(e,n=wr){if(n===wr){let r=new Error(`NullInjectorError: No provider for ${Ee(e)}!`);throw r.name="NullInjectorError",r}return n}},Tf=function(t){return t[t.OnPush=0]="OnPush",t[t.Default=1]="Default",t}(Tf||{}),_r=function(t){return t[t.Emulated=0]="Emulated",t[t.None=2]="None",t[t.ShadowDom=3]="ShadowDom",t}(_r||{}),Bt=function(t){return t[t.None=0]="None",t[t.SignalBased=1]="SignalBased",t[t.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",t}(Bt||{});function Gy(t,e,n){let r=t.length;for(;;){let o=t.indexOf(e,n);if(o===-1)return o;if(o===0||t.charCodeAt(o-1)<=32){let i=e.length;if(o+i===r||t.charCodeAt(o+i)<=32)return o}n=o+1}}function fa(t,e,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];t.setAttribute(e,s,a,i)}else{let i=o,s=n[++r];Wy(i)?t.setProperty(e,i,s):t.setAttribute(e,i,s),r++}}return r}function Nf(t){return t===3||t===4||t===6}function Wy(t){return t.charCodeAt(0)===64}function Ir(t,e){if(!(e===null||e.length===0))if(t===null||t.length===0)t=e.slice();else{let n=-1;for(let r=0;r<e.length;r++){let o=e[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?Cd(t,n,o,null,e[++r]):Cd(t,n,o,null,null))}}return t}function Cd(t,e,n,r,o){let i=0,s=t.length;if(e===-1)s=-1;else for(;i<t.length;){let a=t[i++];if(typeof a=="number"){if(a===e){s=-1;break}else if(a>e){s=i-1;break}}}for(;i<t.length;){let a=t[i];if(typeof a=="number")break;if(a===n){if(r===null){o!==null&&(t[i+1]=o);return}else if(r===t[i+1]){t[i+2]=o;return}}i++,r!==null&&i++,o!==null&&i++}s!==-1&&(t.splice(s,0,e),i=s+1),t.splice(i++,0,n),r!==null&&t.splice(i++,0,r),o!==null&&t.splice(i++,0,o)}var xf="ng-template";function Qy(t,e,n,r){let o=0;if(r){for(;o<e.length&&typeof e[o]=="string";o+=2)if(e[o]==="class"&&Gy(e[o+1].toLowerCase(),n,0)!==-1)return!0}else if(Sl(t))return!1;if(o=e.indexOf(1,o),o>-1){let i;for(;++o<e.length&&typeof(i=e[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function Sl(t){return t.type===4&&t.value!==xf}function Zy(t,e,n){let r=t.type===4&&!n?xf:t.value;return e===r}function Ky(t,e,n){let r=4,o=t.attrs,i=o!==null?Xy(o):0,s=!1;for(let a=0;a<e.length;a++){let l=e[a];if(typeof l=="number"){if(!s&&!qe(r)&&!qe(l))return!1;if(s&&qe(l))continue;s=!1,r=l|r&1;continue}if(!s)if(r&4){if(r=2|r&1,l!==""&&!Zy(t,l,n)||l===""&&e.length===1){if(qe(r))return!1;s=!0}}else if(r&8){if(o===null||!Qy(t,o,l,n)){if(qe(r))return!1;s=!0}}else{let u=e[++a],c=Yy(l,o,Sl(t),n);if(c===-1){if(qe(r))return!1;s=!0;continue}if(u!==""){let d;if(c>i?d="":d=o[c+1].toLowerCase(),r&2&&u!==d){if(qe(r))return!1;s=!0}}}}return qe(r)||s}function qe(t){return(t&1)===0}function Yy(t,e,n,r){if(e===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<e.length;){let s=e[o];if(s===t)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=e[++o];for(;typeof a=="string";)a=e[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return ev(e,t)}function Af(t,e,n=!1){for(let r=0;r<e.length;r++)if(Ky(t,e[r],n))return!0;return!1}function Jy(t){let e=t.attrs;if(e!=null){let n=e.indexOf(5);if(!(n&1))return e[n+1]}return null}function Xy(t){for(let e=0;e<t.length;e++){let n=t[e];if(Nf(n))return e}return t.length}function ev(t,e){let n=t.indexOf(4);if(n>-1)for(n++;n<t.length;){let r=t[n];if(typeof r=="number")return-1;if(r===e)return n;n++}return-1}function tv(t,e){e:for(let n=0;n<e.length;n++){let r=e[n];if(t.length===r.length){for(let o=0;o<t.length;o++)if(t[o]!==r[o])continue e;return!0}}return!1}function Sd(t,e){return t?":not("+e.trim()+")":e}function nv(t){let e=t[0],n=1,r=2,o="",i=!1;for(;n<t.length;){let s=t[n];if(typeof s=="string")if(r&2){let a=t[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!qe(s)&&(e+=Sd(i,o),o=""),r=s,i=i||!qe(r);n++}return o!==""&&(e+=Sd(i,o)),e}function rv(t){return t.map(nv).join(",")}function ov(t){let e=[],n=[],r=1,o=2;for(;r<t.length;){let i=t[r];if(typeof i=="string")o===2?i!==""&&e.push(i,t[++r]):o===8&&n.push(i);else{if(!qe(o))break;o=i}r++}return{attrs:e,classes:n}}function mF(t){return xr(()=>{let e=Rf(t),n=dt(xe({},e),{decls:t.decls,vars:t.vars,template:t.template,consts:t.consts||null,ngContentSelectors:t.ngContentSelectors,onPush:t.changeDetection===Tf.OnPush,directiveDefs:null,pipeDefs:null,dependencies:e.standalone&&t.dependencies||null,getStandaloneInjector:null,signals:t.signals??!1,data:t.data||{},encapsulation:t.encapsulation||_r.Emulated,styles:t.styles||Ce,_:null,schemas:t.schemas||null,tView:null,id:""});kf(n);let r=t.dependencies;return n.directiveDefs=Td(r,!1),n.pipeDefs=Td(r,!0),n.id=lv(n),n})}function iv(t){return gt(t)||Ml(t)}function sv(t){return t!==null}function Of(t){return xr(()=>({type:t.type,bootstrap:t.bootstrap||Ce,declarations:t.declarations||Ce,imports:t.imports||Ce,exports:t.exports||Ce,transitiveCompileScopes:null,schemas:t.schemas||null,id:t.id||null}))}function Md(t,e){if(t==null)return mt;let n={};for(let r in t)if(t.hasOwnProperty(r)){let o=t[r],i,s,a=Bt.None;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i):(i=o,s=o),e?(n[i]=a!==Bt.None?[r,a]:r,e[i]=s):n[i]=r}return n}function _t(t){return xr(()=>{let e=Rf(t);return kf(e),e})}function En(t){return{type:t.type,name:t.name,factory:null,pure:t.pure!==!1,standalone:t.standalone===!0,onDestroy:t.type.prototype.ngOnDestroy||null}}function gt(t){return t[Sy]||null}function Ml(t){return t[My]||null}function Pf(t){return t[Ty]||null}function av(t){let e=gt(t)||Ml(t)||Pf(t);return e!==null?e.standalone:!1}function Ff(t,e){let n=t[Ny]||null;if(!n&&e===!0)throw new Error(`Type ${Ee(t)} does not have '\u0275mod' property.`);return n}function Rf(t){let e={};return{type:t.type,providersResolver:null,factory:null,hostBindings:t.hostBindings||null,hostVars:t.hostVars||0,hostAttrs:t.hostAttrs||null,contentQueries:t.contentQueries||null,declaredInputs:e,inputTransforms:null,inputConfig:t.inputs||mt,exportAs:t.exportAs||null,standalone:t.standalone===!0,signals:t.signals===!0,selectors:t.selectors||Ce,viewQuery:t.viewQuery||null,features:t.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:Md(t.inputs,e),outputs:Md(t.outputs),debugInfo:null}}function kf(t){t.features?.forEach(e=>e(t))}function Td(t,e){if(!t)return null;let n=e?Pf:iv;return()=>(typeof t=="function"?t():t).map(r=>n(r)).filter(sv)}function lv(t){let e=0,n=[t.selectors,t.ngContentSelectors,t.hostVars,t.hostAttrs,t.consts,t.vars,t.decls,t.encapsulation,t.standalone,t.signals,t.exportAs,JSON.stringify(t.inputs),JSON.stringify(t.outputs),Object.getOwnPropertyNames(t.type.prototype),!!t.contentQueries,!!t.viewQuery].join("|");for(let o of n)e=Math.imul(31,e)+o.charCodeAt(0)<<0;return e+=**********,"c"+e}function uv(t){return{\u0275providers:t}}function cv(...t){return{\u0275providers:Lf(!0,t),\u0275fromNgModule:!0}}function Lf(t,...e){let n=[],r=new Set,o,i=s=>{n.push(s)};return Cl(e,s=>{let a=s;ha(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&jf(o,i),n}function jf(t,e){for(let n=0;n<t.length;n++){let{ngModule:r,providers:o}=t[n];Tl(o,i=>{e(i,r)})}}function ha(t,e,n,r){if(t=ce(t),!t)return!1;let o=null,i=wd(t),s=!i&&gt(t);if(!i&&!s){let l=t.ngModule;if(i=wd(l),i)o=l;else return!1}else{if(s&&!s.standalone)return!1;o=t}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let l=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let u of l)ha(u,e,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let u;try{Cl(i.imports,c=>{ha(c,e,n,r)&&(u||=[],u.push(c))})}finally{}u!==void 0&&jf(u,e)}if(!a){let u=ln(o)||(()=>new o);e({provide:o,useFactory:u,deps:Ce},o),e({provide:Mf,useValue:o,multi:!0},o),e({provide:Wo,useValue:()=>ge(o),multi:!0},o)}let l=i.providers;if(l!=null&&!a){let u=t;Tl(l,c=>{e(c,u)})}}else return!1;return o!==t&&t.providers!==void 0}function Tl(t,e){for(let n of t)Ef(n)&&(n=n.\u0275providers),Array.isArray(n)?Tl(n,e):e(n)}var dv=U({provide:String,useValue:U});function Vf(t){return t!==null&&typeof t=="object"&&dv in t}function fv(t){return!!(t&&t.useExisting)}function hv(t){return!!(t&&t.useFactory)}function Un(t){return typeof t=="function"}function pv(t){return!!t.useClass}var Bf=new j(""),Lo={},mv={},Ks;function Ei(){return Ks===void 0&&(Ks=new Qo),Ks}var $t=class{},br=class extends $t{get destroyed(){return this._destroyed}constructor(e,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,this.records=new Map,this._ngOnDestroyHooks=new Set,this._onDestroyHooks=[],this._destroyed=!1,ma(e,s=>this.processProvider(s)),this.records.set(Sf,jn(void 0,this)),o.has("environment")&&this.records.set($t,jn(void 0,this));let i=this.records.get(Bf);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Mf,Ce,F.Self))}destroy(){this.assertNotDestroyed(),this._destroyed=!0;let e=O(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),O(e)}}onDestroy(e){return this.assertNotDestroyed(),this._onDestroyHooks.push(e),()=>this.removeOnDestroy(e)}runInContext(e){this.assertNotDestroyed();let n=kt(this),r=be(void 0),o;try{return e()}finally{kt(n),be(r)}}get(e,n=wr,r=F.Default){if(this.assertNotDestroyed(),e.hasOwnProperty(Id))return e[Id](this);r=Di(r);let o,i=kt(this),s=be(void 0);try{if(!(r&F.SkipSelf)){let l=this.records.get(e);if(l===void 0){let u=Ev(e)&&vi(e);u&&this.injectableDefInScope(u)?l=jn(pa(e),Lo):l=null,this.records.set(e,l)}if(l!=null)return this.hydrate(e,l)}let a=r&F.Self?Ei():this.parent;return n=r&F.Optional&&n===wr?null:n,a.get(e,n)}catch(a){if(a.name==="NullInjectorError"){if((a[qo]=a[qo]||[]).unshift(Ee(e)),i)throw a;return Vy(a,e,"R3InjectorError",this.source)}else throw a}finally{be(s),kt(i)}}resolveInjectorInitializers(){let e=O(null),n=kt(this),r=be(void 0),o;try{let i=this.get(Wo,Ce,F.Self);for(let s of i)s()}finally{kt(n),be(r),O(e)}}toString(){let e=[],n=this.records;for(let r of n.keys())e.push(Ee(r));return`R3Injector[${e.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new g(205,!1)}processProvider(e){e=ce(e);let n=Un(e)?e:ce(e&&e.provide),r=yv(e);if(!Un(e)&&e.multi===!0){let o=this.records.get(n);o||(o=jn(void 0,Lo,!0),o.factory=()=>da(o.multi),this.records.set(n,o)),n=e,o.multi.push(e)}this.records.set(n,r)}hydrate(e,n){let r=O(null);try{return n.value===Lo&&(n.value=mv,n.value=n.factory()),typeof n.value=="object"&&n.value&&Dv(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{O(r)}}injectableDefInScope(e){if(!e.providedIn)return!1;let n=ce(e.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(e){let n=this._onDestroyHooks.indexOf(e);n!==-1&&this._onDestroyHooks.splice(n,1)}};function pa(t){let e=vi(t),n=e!==null?e.factory:ln(t);if(n!==null)return n;if(t instanceof j)throw new g(204,!1);if(t instanceof Function)return gv(t);throw new g(204,!1)}function gv(t){if(t.length>0)throw new g(204,!1);let n=by(t);return n!==null?()=>n.factory(t):()=>new t}function yv(t){if(Vf(t))return jn(void 0,t.useValue);{let e=$f(t);return jn(e,Lo)}}function $f(t,e,n){let r;if(Un(t)){let o=ce(t);return ln(o)||pa(o)}else if(Vf(t))r=()=>ce(t.useValue);else if(hv(t))r=()=>t.useFactory(...da(t.deps||[]));else if(fv(t))r=()=>ge(ce(t.useExisting));else{let o=ce(t&&(t.useClass||t.provide));if(vv(t))r=()=>new o(...da(t.deps));else return ln(o)||pa(o)}return r}function jn(t,e,n=!1){return{factory:t,value:e,multi:n?[]:void 0}}function vv(t){return!!t.deps}function Dv(t){return t!==null&&typeof t=="object"&&typeof t.ngOnDestroy=="function"}function Ev(t){return typeof t=="function"||typeof t=="object"&&t instanceof j}function ma(t,e){for(let n of t)Array.isArray(n)?ma(n,e):n&&Ef(n)?ma(n.\u0275providers,e):e(n)}function wv(t,e){t instanceof br&&t.assertNotDestroyed();let n,r=kt(t),o=be(void 0);try{return e()}finally{kt(r),be(o)}}function Hf(){return wf()!==void 0||ky()!=null}function Nl(t){if(!Hf())throw new g(-203,!1)}var gF=Function;function _v(t){return typeof t=="function"}var st=0,M=1,S=2,ye=3,We=4,Me=5,zn=6,Zo=7,pe=8,qn=9,ot=10,Z=11,Cr=12,Nd=13,er=14,Ae=15,un=16,Vn=17,yt=18,wi=19,Uf=20,jt=21,jo=22,je=23,ee=25,xl=1;var cn=7,Ko=8,Gn=9,me=10,Yo=function(t){return t[t.None=0]="None",t[t.HasTransplantedViews=2]="HasTransplantedViews",t}(Yo||{});function Vt(t){return Array.isArray(t)&&typeof t[xl]=="object"}function It(t){return Array.isArray(t)&&t[xl]===!0}function Al(t){return(t.flags&4)!==0}function _i(t){return t.componentOffset>-1}function Ii(t){return(t.flags&1)===1}function vt(t){return!!t.template}function ga(t){return(t[S]&512)!==0}var ya=class{constructor(e,n,r){this.previousValue=e,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function zf(t,e,n,r){e!==null?e.applyValueToInputSignal(e,r):t[n]=r}function Ol(){return qf}function qf(t){return t.type.prototype.ngOnChanges&&(t.setInput=bv),Iv}Ol.ngInherit=!0;function Iv(){let t=Wf(this),e=t?.current;if(e){let n=t.previous;if(n===mt)t.previous=e;else for(let r in e)n[r]=e[r];t.current=null,this.ngOnChanges(e)}}function bv(t,e,n,r,o){let i=this.declaredInputs[r],s=Wf(t)||Cv(t,{previous:mt,current:null}),a=s.current||(s.current={}),l=s.previous,u=l[i];a[i]=new ya(u&&u.currentValue,n,l===mt),zf(t,e,o,n)}var Gf="__ngSimpleChanges__";function Wf(t){return t[Gf]||null}function Cv(t,e){return t[Gf]=e}var xd=null;var nt=function(t,e,n){xd?.(t,e,n)},Qf="svg",Sv="math";function it(t){for(;Array.isArray(t);)t=t[st];return t}function Mv(t){for(;Array.isArray(t);){if(typeof t[xl]=="object")return t;t=t[st]}return null}function Zf(t,e){return it(e[t])}function Ve(t,e){return it(e[t.index])}function Pl(t,e){return t.data[e]}function tr(t,e){return t[e]}function qt(t,e){let n=e[t];return Vt(n)?n:n[st]}function Tv(t){return(t[S]&4)===4}function Fl(t){return(t[S]&128)===128}function Nv(t){return It(t[ye])}function Ht(t,e){return e==null?null:t[e]}function Kf(t){t[Vn]=0}function Yf(t){t[S]&1024||(t[S]|=1024,Fl(t)&&bi(t))}function xv(t,e){for(;t>0;)e=e[er],t--;return e}function Sr(t){return!!(t[S]&9216||t[je]?.dirty)}function va(t){t[ot].changeDetectionScheduler?.notify(7),t[S]&64&&(t[S]|=1024),Sr(t)&&bi(t)}function bi(t){t[ot].changeDetectionScheduler?.notify(0);let e=dn(t);for(;e!==null&&!(e[S]&8192||(e[S]|=8192,!Fl(e)));)e=dn(e)}function Jf(t,e){if((t[S]&256)===256)throw new g(911,!1);t[jt]===null&&(t[jt]=[]),t[jt].push(e)}function Av(t,e){if(t[jt]===null)return;let n=t[jt].indexOf(e);n!==-1&&t[jt].splice(n,1)}function dn(t){let e=t[ye];return It(e)?e[ye]:e}var x={lFrame:lh(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var Xf=!1;function Ov(){return x.lFrame.elementDepthCount}function Pv(){x.lFrame.elementDepthCount++}function Fv(){x.lFrame.elementDepthCount--}function eh(){return x.bindingsEnabled}function th(){return x.skipHydrationRootTNode!==null}function Rv(t){return x.skipHydrationRootTNode===t}function kv(){x.skipHydrationRootTNode=null}function v(){return x.lFrame.lView}function q(){return x.lFrame.tView}function yF(t){return x.lFrame.contextLView=t,t[pe]}function vF(t){return x.lFrame.contextLView=null,t}function le(){let t=nh();for(;t!==null&&t.type===64;)t=t.parent;return t}function nh(){return x.lFrame.currentTNode}function Lv(){let t=x.lFrame,e=t.currentTNode;return t.isParent?e:e.parent}function Gt(t,e){let n=x.lFrame;n.currentTNode=t,n.isParent=e}function Rl(){return x.lFrame.isParent}function kl(){x.lFrame.isParent=!1}function rh(){return x.lFrame.contextLView}function oh(){return Xf}function Ad(t){Xf=t}function Be(){let t=x.lFrame,e=t.bindingRootIndex;return e===-1&&(e=t.bindingRootIndex=t.tView.bindingStartIndex),e}function Ci(){return x.lFrame.bindingIndex}function jv(t){return x.lFrame.bindingIndex=t}function wn(){return x.lFrame.bindingIndex++}function nr(t){let e=x.lFrame,n=e.bindingIndex;return e.bindingIndex=e.bindingIndex+t,n}function Vv(){return x.lFrame.inI18n}function Bv(t,e){let n=x.lFrame;n.bindingIndex=n.bindingRootIndex=t,Da(e)}function $v(){return x.lFrame.currentDirectiveIndex}function Da(t){x.lFrame.currentDirectiveIndex=t}function Ll(t){let e=x.lFrame.currentDirectiveIndex;return e===-1?null:t[e]}function ih(){return x.lFrame.currentQueryIndex}function jl(t){x.lFrame.currentQueryIndex=t}function Hv(t){let e=t[M];return e.type===2?e.declTNode:e.type===1?t[Me]:null}function sh(t,e,n){if(n&F.SkipSelf){let o=e,i=t;for(;o=o.parent,o===null&&!(n&F.Host);)if(o=Hv(i),o===null||(i=i[er],o.type&10))break;if(o===null)return!1;e=o,t=i}let r=x.lFrame=ah();return r.currentTNode=e,r.lView=t,!0}function Vl(t){let e=ah(),n=t[M];x.lFrame=e,e.currentTNode=n.firstChild,e.lView=t,e.tView=n,e.contextLView=t,e.bindingIndex=n.bindingStartIndex,e.inI18n=!1}function ah(){let t=x.lFrame,e=t===null?null:t.child;return e===null?lh(t):e}function lh(t){let e={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:t,child:null,inI18n:!1};return t!==null&&(t.child=e),e}function uh(){let t=x.lFrame;return x.lFrame=t.parent,t.currentTNode=null,t.lView=null,t}var ch=uh;function Bl(){let t=uh();t.isParent=!0,t.tView=null,t.selectedIndex=-1,t.contextLView=null,t.elementDepthCount=0,t.currentDirectiveIndex=-1,t.currentNamespace=null,t.bindingRootIndex=-1,t.bindingIndex=-1,t.currentQueryIndex=0}function Uv(t){return(x.lFrame.contextLView=xv(t,x.lFrame.contextLView))[pe]}function Oe(){return x.lFrame.selectedIndex}function fn(t){x.lFrame.selectedIndex=t}function Or(){let t=x.lFrame;return Pl(t.tView,t.selectedIndex)}function DF(){x.lFrame.currentNamespace=Qf}function EF(){zv()}function zv(){x.lFrame.currentNamespace=null}function qv(){return x.lFrame.currentNamespace}var dh=!0;function Si(){return dh}function Mi(t){dh=t}function Gv(t,e,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=e.type.prototype;if(r){let s=qf(e);(n.preOrderHooks??=[]).push(t,s),(n.preOrderCheckHooks??=[]).push(t,s)}o&&(n.preOrderHooks??=[]).push(0-t,o),i&&((n.preOrderHooks??=[]).push(t,i),(n.preOrderCheckHooks??=[]).push(t,i))}function Ti(t,e){for(let n=e.directiveStart,r=e.directiveEnd;n<r;n++){let i=t.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:l,ngAfterViewChecked:u,ngOnDestroy:c}=i;s&&(t.contentHooks??=[]).push(-n,s),a&&((t.contentHooks??=[]).push(n,a),(t.contentCheckHooks??=[]).push(n,a)),l&&(t.viewHooks??=[]).push(-n,l),u&&((t.viewHooks??=[]).push(n,u),(t.viewCheckHooks??=[]).push(n,u)),c!=null&&(t.destroyHooks??=[]).push(n,c)}}function Vo(t,e,n){fh(t,e,3,n)}function Bo(t,e,n,r){(t[S]&3)===n&&fh(t,e,n,r)}function Ys(t,e){let n=t[S];(n&3)===e&&(n&=16383,n+=1,t[S]=n)}function fh(t,e,n,r){let o=r!==void 0?t[Vn]&65535:0,i=r??-1,s=e.length-1,a=0;for(let l=o;l<s;l++)if(typeof e[l+1]=="number"){if(a=e[l],r!=null&&a>=r)break}else e[l]<0&&(t[Vn]+=65536),(a<i||i==-1)&&(Wv(t,n,e,l),t[Vn]=(t[Vn]&**********)+l+2),l++}function Od(t,e){nt(4,t,e);let n=O(null);try{e.call(t)}finally{O(n),nt(5,t,e)}}function Wv(t,e,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=t[s];o?t[S]>>14<t[Vn]>>16&&(t[S]&3)===e&&(t[S]+=16384,Od(a,i)):Od(a,i)}var Hn=-1,hn=class{constructor(e,n,r){this.factory=e,this.resolving=!1,this.canSeeViewProviders=n,this.injectImpl=r}};function Qv(t){return t instanceof hn}function Zv(t){return(t.flags&8)!==0}function Kv(t){return(t.flags&16)!==0}var Js={},Ea=class{constructor(e,n){this.injector=e,this.parentInjector=n}get(e,n,r){r=Di(r);let o=this.injector.get(e,Js,r);return o!==Js||n===Js?o:this.parentInjector.get(e,n,r)}};function hh(t){return t!==Hn}function Jo(t){return t&32767}function Yv(t){return t>>16}function Xo(t,e){let n=Yv(t),r=e;for(;n>0;)r=r[er],n--;return r}var wa=!0;function ei(t){let e=wa;return wa=t,e}var Jv=256,ph=Jv-1,mh=5,Xv=0,rt={};function eD(t,e,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(Dr)&&(r=n[Dr]),r==null&&(r=n[Dr]=Xv++);let o=r&ph,i=1<<o;e.data[t+(o>>mh)]|=i}function ti(t,e){let n=gh(t,e);if(n!==-1)return n;let r=e[M];r.firstCreatePass&&(t.injectorIndex=e.length,Xs(r.data,t),Xs(e,null),Xs(r.blueprint,null));let o=$l(t,e),i=t.injectorIndex;if(hh(o)){let s=Jo(o),a=Xo(o,e),l=a[M].data;for(let u=0;u<8;u++)e[i+u]=a[s+u]|l[s+u]}return e[i+8]=o,i}function Xs(t,e){t.push(0,0,0,0,0,0,0,0,e)}function gh(t,e){return t.injectorIndex===-1||t.parent&&t.parent.injectorIndex===t.injectorIndex||e[t.injectorIndex+8]===null?-1:t.injectorIndex}function $l(t,e){if(t.parent&&t.parent.injectorIndex!==-1)return t.parent.injectorIndex;let n=0,r=null,o=e;for(;o!==null;){if(r=wh(o),r===null)return Hn;if(n++,o=o[er],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return Hn}function _a(t,e,n){eD(t,e,n)}function tD(t,e){if(e==="class")return t.classes;if(e==="style")return t.styles;let n=t.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(Nf(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===e)return n[o+1];o=o+2}}}return null}function yh(t,e,n){if(n&F.Optional||t!==void 0)return t;Il(e,"NodeInjector")}function vh(t,e,n,r){if(n&F.Optional&&r===void 0&&(r=null),!(n&(F.Self|F.Host))){let o=t[qn],i=be(void 0);try{return o?o.get(e,r,n&F.Optional):_f(e,r,n&F.Optional)}finally{be(i)}}return yh(r,e,n)}function Dh(t,e,n,r=F.Default,o){if(t!==null){if(e[S]&2048&&!(r&F.Self)){let s=iD(t,e,n,r,rt);if(s!==rt)return s}let i=Eh(t,e,n,r,rt);if(i!==rt)return i}return vh(e,n,r,o)}function Eh(t,e,n,r,o){let i=rD(n);if(typeof i=="function"){if(!sh(e,t,r))return r&F.Host?yh(o,n,r):vh(e,n,r,o);try{let s;if(s=i(r),s==null&&!(r&F.Optional))Il(n);else return s}finally{ch()}}else if(typeof i=="number"){let s=null,a=gh(t,e),l=Hn,u=r&F.Host?e[Ae][Me]:null;for((a===-1||r&F.SkipSelf)&&(l=a===-1?$l(t,e):e[a+8],l===Hn||!Fd(r,!1)?a=-1:(s=e[M],a=Jo(l),e=Xo(l,e)));a!==-1;){let c=e[M];if(Pd(i,a,c.data)){let d=nD(a,e,n,s,r,u);if(d!==rt)return d}l=e[a+8],l!==Hn&&Fd(r,e[M].data[a+8]===u)&&Pd(i,a,e)?(s=c,a=Jo(l),e=Xo(l,e)):a=-1}}return o}function nD(t,e,n,r,o,i){let s=e[M],a=s.data[t+8],l=r==null?_i(a)&&wa:r!=s&&(a.type&3)!==0,u=o&F.Host&&i===a,c=$o(a,s,n,l,u);return c!==null?pn(e,s,c,a):rt}function $o(t,e,n,r,o){let i=t.providerIndexes,s=e.data,a=i&1048575,l=t.directiveStart,u=t.directiveEnd,c=i>>20,d=r?a:a+c,h=o?a+c:u;for(let f=d;f<h;f++){let p=s[f];if(f<l&&n===p||f>=l&&p.type===n)return f}if(o){let f=s[l];if(f&&vt(f)&&f.type===n)return l}return null}function pn(t,e,n,r){let o=t[n],i=e.data;if(Qv(o)){let s=o;s.resolving&&Ay(xy(i[n]));let a=ei(s.canSeeViewProviders);s.resolving=!0;let l,u=s.injectImpl?be(s.injectImpl):null,c=sh(t,r,F.Default);try{o=t[n]=s.factory(void 0,i,t,r),e.firstCreatePass&&n>=r.directiveStart&&Gv(n,i[n],e)}finally{u!==null&&be(u),ei(a),s.resolving=!1,ch()}}return o}function rD(t){if(typeof t=="string")return t.charCodeAt(0)||0;let e=t.hasOwnProperty(Dr)?t[Dr]:void 0;return typeof e=="number"?e>=0?e&ph:oD:e}function Pd(t,e,n){let r=1<<t;return!!(n[e+(t>>mh)]&r)}function Fd(t,e){return!(t&F.Self)&&!(t&F.Host&&e)}var an=class{constructor(e,n){this._tNode=e,this._lView=n}get(e,n,r){return Dh(this._tNode,this._lView,e,Di(r),n)}};function oD(){return new an(le(),v())}function wF(t){return xr(()=>{let e=t.prototype.constructor,n=e[zo]||Ia(e),r=Object.prototype,o=Object.getPrototypeOf(t.prototype).constructor;for(;o&&o!==r;){let i=o[zo]||Ia(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Ia(t){return gf(t)?()=>{let e=Ia(ce(t));return e&&e()}:ln(t)}function iD(t,e,n,r,o){let i=t,s=e;for(;i!==null&&s!==null&&s[S]&2048&&!(s[S]&512);){let a=Eh(i,s,n,r|F.Self,rt);if(a!==rt)return a;let l=i.parent;if(!l){let u=s[Uf];if(u){let c=u.get(n,rt,r);if(c!==rt)return c}l=wh(s),s=s[er]}i=l}return o}function wh(t){let e=t[M],n=e.type;return n===2?e.declTNode:n===1?t[Me]:null}function sD(t){return tD(le(),t)}function Rd(t,e=null,n=null,r){let o=_h(t,e,n,r);return o.resolveInjectorInitializers(),o}function _h(t,e=null,n=null,r,o=new Set){let i=[n||Ce,cv(t)];return r=r||(typeof t=="object"?void 0:Ee(t)),new br(i,e||Ei(),r||null,o)}var Dt=class t{static{this.THROW_IF_NOT_FOUND=wr}static{this.NULL=new Qo}static create(e,n){if(Array.isArray(e))return Rd({name:""},n,e,"");{let r=e.name??"";return Rd({name:r},e.parent,e.providers,r)}}static{this.\u0275prov=z({token:t,providedIn:"any",factory:()=>ge(Sf)})}static{this.__NG_ELEMENT_ID__=-1}};var aD=new j("");aD.__NG_ELEMENT_ID__=t=>{let e=le();if(e===null)throw new g(204,!1);if(e.type&2)return e.value;if(t&F.Optional)return null;throw new g(204,!1)};var lD="ngOriginalError";function ea(t){return t[lD]}var Ni=(()=>{class t{static{this.__NG_ELEMENT_ID__=uD}static{this.__NG_ENV_ID__=n=>n}}return t})(),ba=class extends Ni{constructor(e){super(),this._lView=e}onDestroy(e){return Jf(this._lView,e),()=>Av(this._lView,e)}};function uD(){return new ba(v())}var Pr=(()=>{class t{constructor(){this.taskId=0,this.pendingTasks=new Set,this.hasPendingTasks=new dr(!1)}get _hasPendingTasks(){return this.hasPendingTasks.value}add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static{this.\u0275prov=z({token:t,providedIn:"root",factory:()=>new t})}}return t})();var Ca=class extends Ie{constructor(e=!1){super(),this.destroyRef=void 0,this.pendingTasks=void 0,this.__isAsync=e,Hf()&&(this.destroyRef=A(Ni,{optional:!0})??void 0,this.pendingTasks=A(Pr,{optional:!0})??void 0)}emit(e){let n=O(null);try{super.next(e)}finally{O(n)}}subscribe(e,n,r){let o=e,i=n||(()=>null),s=r;if(e&&typeof e=="object"){let l=e;o=l.next?.bind(l),i=l.error?.bind(l),s=l.complete?.bind(l)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return e instanceof X&&e.add(a),a}wrapInTimeout(e){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{e(n),r!==void 0&&this.pendingTasks?.remove(r)})}}},Ge=Ca;function ni(...t){}function Ih(t){let e,n;function r(){t=ni;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),e!==void 0&&clearTimeout(e)}catch{}}return e=setTimeout(()=>{t(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{t(),r()})),()=>r()}function kd(t){return queueMicrotask(()=>t()),()=>{t=ni}}var Hl="isAngularZone",ri=Hl+"_ID",cD=0,Se=class t{constructor({enableLongStackTrace:e=!1,shouldCoalesceEventChangeDetection:n=!1,shouldCoalesceRunChangeDetection:r=!1}){if(this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new Ge(!1),this.onMicrotaskEmpty=new Ge(!1),this.onStable=new Ge(!1),this.onError=new Ge(!1),typeof Zone>"u")throw new g(908,!1);Zone.assertZonePatched();let o=this;o._nesting=0,o._outer=o._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(o._inner=o._inner.fork(new Zone.TaskTrackingZoneSpec)),e&&Zone.longStackTraceZoneSpec&&(o._inner=o._inner.fork(Zone.longStackTraceZoneSpec)),o.shouldCoalesceEventChangeDetection=!r&&n,o.shouldCoalesceRunChangeDetection=r,o.callbackScheduled=!1,hD(o)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(Hl)===!0}static assertInAngularZone(){if(!t.isInAngularZone())throw new g(909,!1)}static assertNotInAngularZone(){if(t.isInAngularZone())throw new g(909,!1)}run(e,n,r){return this._inner.run(e,n,r)}runTask(e,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,e,dD,ni,ni);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(e,n,r){return this._inner.runGuarded(e,n,r)}runOutsideAngular(e){return this._outer.run(e)}},dD={};function Ul(t){if(t._nesting==0&&!t.hasPendingMicrotasks&&!t.isStable)try{t._nesting++,t.onMicrotaskEmpty.emit(null)}finally{if(t._nesting--,!t.hasPendingMicrotasks)try{t.runOutsideAngular(()=>t.onStable.emit(null))}finally{t.isStable=!0}}}function fD(t){t.isCheckStableRunning||t.callbackScheduled||(t.callbackScheduled=!0,Zone.root.run(()=>{Ih(()=>{t.callbackScheduled=!1,Sa(t),t.isCheckStableRunning=!0,Ul(t),t.isCheckStableRunning=!1})}),Sa(t))}function hD(t){let e=()=>{fD(t)},n=cD++;t._inner=t._inner.fork({name:"angular",properties:{[Hl]:!0,[ri]:n,[ri+n]:!0},onInvokeTask:(r,o,i,s,a,l)=>{if(pD(l))return r.invokeTask(i,s,a,l);try{return Ld(t),r.invokeTask(i,s,a,l)}finally{(t.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||t.shouldCoalesceRunChangeDetection)&&e(),jd(t)}},onInvoke:(r,o,i,s,a,l,u)=>{try{return Ld(t),r.invoke(i,s,a,l,u)}finally{t.shouldCoalesceRunChangeDetection&&!t.callbackScheduled&&!mD(l)&&e(),jd(t)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(t._hasPendingMicrotasks=s.microTask,Sa(t),Ul(t)):s.change=="macroTask"&&(t.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),t.runOutsideAngular(()=>t.onError.emit(s)),!1)})}function Sa(t){t._hasPendingMicrotasks||(t.shouldCoalesceEventChangeDetection||t.shouldCoalesceRunChangeDetection)&&t.callbackScheduled===!0?t.hasPendingMicrotasks=!0:t.hasPendingMicrotasks=!1}function Ld(t){t._nesting++,t.isStable&&(t.isStable=!1,t.onUnstable.emit(null))}function jd(t){t._nesting--,Ul(t)}var Ma=class{constructor(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new Ge,this.onMicrotaskEmpty=new Ge,this.onStable=new Ge,this.onError=new Ge}run(e,n,r){return e.apply(n,r)}runGuarded(e,n,r){return e.apply(n,r)}runOutsideAngular(e){return e()}runTask(e,n,r,o){return e.apply(n,r)}};function pD(t){return bh(t,"__ignore_ng_zone__")}function mD(t){return bh(t,"__scheduler_tick__")}function bh(t,e){return!Array.isArray(t)||t.length!==1?!1:t[0]?.data?.[e]===!0}var mn=class{constructor(){this._console=console}handleError(e){let n=this._findOriginalError(e);this._console.error("ERROR",e),n&&this._console.error("ORIGINAL ERROR",n)}_findOriginalError(e){let n=e&&ea(e);for(;n&&ea(n);)n=ea(n);return n||null}},gD=new j("",{providedIn:"root",factory:()=>{let t=A(Se),e=A(mn);return n=>t.runOutsideAngular(()=>e.handleError(n))}});function yD(){return rr(le(),v())}function rr(t,e){return new Wt(Ve(t,e))}var Wt=(()=>{class t{constructor(n){this.nativeElement=n}static{this.__NG_ELEMENT_ID__=yD}}return t})();function vD(t){return t instanceof Wt?t.nativeElement:t}function DD(){return this._results[Symbol.iterator]()}var Ta=class t{get changes(){return this._changes??=new Ge}constructor(e=!1){this._emitDistinctChangesOnly=e,this.dirty=!0,this._onDirty=void 0,this._results=[],this._changesDetected=!1,this._changes=void 0,this.length=0,this.first=void 0,this.last=void 0;let n=t.prototype;n[Symbol.iterator]||(n[Symbol.iterator]=DD)}get(e){return this._results[e]}map(e){return this._results.map(e)}filter(e){return this._results.filter(e)}find(e){return this._results.find(e)}reduce(e,n){return this._results.reduce(e,n)}forEach(e){this._results.forEach(e)}some(e){return this._results.some(e)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(e,n){this.dirty=!1;let r=Hy(e);(this._changesDetected=!$y(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.emit(this)}onDirty(e){this._onDirty=e}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}};function Ch(t){return(t.flags&128)===128}var Sh=new Map,ED=0;function wD(){return ED++}function _D(t){Sh.set(t[wi],t)}function ID(t){Sh.delete(t[wi])}var Vd="__ngContext__";function Ut(t,e){Vt(e)?(t[Vd]=e[wi],_D(e)):t[Vd]=e}function Mh(t){return Nh(t[Cr])}function Th(t){return Nh(t[We])}function Nh(t){for(;t!==null&&!It(t);)t=t[We];return t}var Na;function _F(t){Na=t}function xh(){if(Na!==void 0)return Na;if(typeof document<"u")return document;throw new g(210,!1)}var IF=new j("",{providedIn:"root",factory:()=>bD}),bD="ng",CD=new j(""),zl=new j("",{providedIn:"platform",factory:()=>"unknown"});var bF=new j(""),CF=new j("",{providedIn:"root",factory:()=>xh().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var SD="h",MD="b";var TD=()=>null;function ql(t,e,n=!1){return TD(t,e,n)}var Ah=!1,ND=new j("",{providedIn:"root",factory:()=>Ah});var Ao;function Oh(){if(Ao===void 0&&(Ao=null,Lt.trustedTypes))try{Ao=Lt.trustedTypes.createPolicy("angular",{createHTML:t=>t,createScript:t=>t,createScriptURL:t=>t})}catch{}return Ao}function xi(t){return Oh()?.createHTML(t)||t}function xD(t){return Oh()?.createScriptURL(t)||t}var Oo;function Ph(){if(Oo===void 0&&(Oo=null,Lt.trustedTypes))try{Oo=Lt.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:t=>t,createScript:t=>t,createScriptURL:t=>t})}catch{}return Oo}function Bd(t){return Ph()?.createHTML(t)||t}function $d(t){return Ph()?.createScriptURL(t)||t}var Et=class{constructor(e){this.changingThisBreaksApplicationSecurity=e}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${hf})`}},xa=class extends Et{getTypeName(){return"HTML"}},Aa=class extends Et{getTypeName(){return"Style"}},Oa=class extends Et{getTypeName(){return"Script"}},Pa=class extends Et{getTypeName(){return"URL"}},Fa=class extends Et{getTypeName(){return"ResourceURL"}};function _n(t){return t instanceof Et?t.changingThisBreaksApplicationSecurity:t}function Gl(t,e){let n=AD(t);if(n!=null&&n!==e){if(n==="ResourceURL"&&e==="URL")return!0;throw new Error(`Required a safe ${e}, got a ${n} (see ${hf})`)}return n===e}function AD(t){return t instanceof Et&&t.getTypeName()||null}function SF(t){return new xa(t)}function MF(t){return new Aa(t)}function TF(t){return new Oa(t)}function NF(t){return new Pa(t)}function xF(t){return new Fa(t)}function OD(t){let e=new ka(t);return PD()?new Ra(e):e}var Ra=class{constructor(e){this.inertDocumentHelper=e}getInertBodyElement(e){e="<body><remove></remove>"+e;try{let n=new window.DOMParser().parseFromString(xi(e),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(e):(n.firstChild?.remove(),n)}catch{return null}}},ka=class{constructor(e){this.defaultDoc=e,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(e){let n=this.inertDocument.createElement("template");return n.innerHTML=xi(e),n}};function PD(){try{return!!new window.DOMParser().parseFromString(xi(""),"text/html")}catch{return!1}}var FD=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Fh(t){return t=String(t),t.match(FD)?t:"unsafe:"+t}function bt(t){let e={};for(let n of t.split(","))e[n]=!0;return e}function Fr(...t){let e={};for(let n of t)for(let r in n)n.hasOwnProperty(r)&&(e[r]=!0);return e}var Rh=bt("area,br,col,hr,img,wbr"),kh=bt("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),Lh=bt("rp,rt"),RD=Fr(Lh,kh),kD=Fr(kh,bt("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),LD=Fr(Lh,bt("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),Hd=Fr(Rh,kD,LD,RD),jh=bt("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),jD=bt("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),VD=bt("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),BD=Fr(jh,jD,VD),$D=bt("script,style,template"),La=class{constructor(){this.sanitizedSomething=!1,this.buf=[]}sanitizeChildren(e){let n=e.firstChild,r=!0,o=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild){o.push(n),n=zD(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=UD(n);if(i){n=i;break}n=o.pop()}}return this.buf.join("")}startElement(e){let n=Ud(e).toLowerCase();if(!Hd.hasOwnProperty(n))return this.sanitizedSomething=!0,!$D.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let r=e.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!BD.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let l=i.value;jh[a]&&(l=Fh(l)),this.buf.push(" ",s,'="',zd(l),'"')}return this.buf.push(">"),!0}endElement(e){let n=Ud(e).toLowerCase();Hd.hasOwnProperty(n)&&!Rh.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(e){this.buf.push(zd(e))}};function HD(t,e){return(t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function UD(t){let e=t.nextSibling;if(e&&t!==e.previousSibling)throw Vh(e);return e}function zD(t){let e=t.firstChild;if(e&&HD(t,e))throw Vh(e);return e}function Ud(t){let e=t.nodeName;return typeof e=="string"?e:"FORM"}function Vh(t){return new Error(`Failed to sanitize html because the element is clobbered: ${t.outerHTML}`)}var qD=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,GD=/([^\#-~ |!])/g;function zd(t){return t.replace(/&/g,"&amp;").replace(qD,function(e){let n=e.charCodeAt(0),r=e.charCodeAt(1);return"&#"+((n-55296)*1024+(r-56320)+65536)+";"}).replace(GD,function(e){return"&#"+e.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var Po;function WD(t,e){let n=null;try{Po=Po||OD(t);let r=e?String(e):"";n=Po.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=Po.getInertBodyElement(r)}while(r!==i);let a=new La().sanitizeChildren(qd(n)||n);return xi(a)}finally{if(n){let r=qd(n)||n;for(;r.firstChild;)r.firstChild.remove()}}}function qd(t){return"content"in t&&QD(t)?t.content:null}function QD(t){return t.nodeType===Node.ELEMENT_NODE&&t.nodeName==="TEMPLATE"}var Ai=function(t){return t[t.NONE=0]="NONE",t[t.HTML=1]="HTML",t[t.STYLE=2]="STYLE",t[t.SCRIPT=3]="SCRIPT",t[t.URL=4]="URL",t[t.RESOURCE_URL=5]="RESOURCE_URL",t}(Ai||{});function AF(t){let e=Wl();return e?Bd(e.sanitize(Ai.HTML,t)||""):Gl(t,"HTML")?Bd(_n(t)):WD(xh(),oe(t))}function ZD(t){let e=Wl();return e?e.sanitize(Ai.URL,t)||"":Gl(t,"URL")?_n(t):Fh(oe(t))}function KD(t){let e=Wl();if(e)return $d(e.sanitize(Ai.RESOURCE_URL,t)||"");if(Gl(t,"ResourceURL"))return $d(_n(t));throw new g(904,!1)}function OF(t){return xD(t[0])}function YD(t,e){return e==="src"&&(t==="embed"||t==="frame"||t==="iframe"||t==="media"||t==="script")||e==="href"&&(t==="base"||t==="link")?KD:ZD}function PF(t,e,n){return YD(e,n)(t)}function Wl(){let t=v();return t&&t[ot].sanitizer}var JD=/^>|^->|<!--|-->|--!>|<!-$/g,XD=/(<|>)/g,eE="\u200B$1\u200B";function tE(t){return t.replace(JD,e=>e.replace(XD,eE))}function FF(t){return t.ownerDocument.defaultView}function RF(t){return t.ownerDocument}function Bh(t){return t instanceof Function?t():t}function $h(t){return(t??A(Dt)).get(zl)==="browser"}var Mr=function(t){return t[t.Important=1]="Important",t[t.DashCase=2]="DashCase",t}(Mr||{}),nE;function Ql(t,e){return nE(t,e)}function Bn(t,e,n,r,o){if(r!=null){let i,s=!1;It(r)?i=r:Vt(r)&&(s=!0,r=r[st]);let a=it(r);t===0&&n!==null?o==null?Gh(e,n,a):oi(e,n,a,o||null,!0):t===1&&n!==null?oi(e,n,a,o||null,!0):t===2?gE(e,a,s):t===3&&e.destroyNode(a),i!=null&&vE(e,t,i,n,o)}}function rE(t,e){return t.createText(e)}function oE(t,e,n){t.setValue(e,n)}function iE(t,e){return t.createComment(tE(e))}function Hh(t,e,n){return t.createElement(e,n)}function sE(t,e){Uh(t,e),e[st]=null,e[Me]=null}function aE(t,e,n,r,o,i){r[st]=o,r[Me]=e,Fi(t,r,n,1,o,i)}function Uh(t,e){e[ot].changeDetectionScheduler?.notify(8),Fi(t,e,e[Z],2,null,null)}function lE(t){let e=t[Cr];if(!e)return ta(t[M],t);for(;e;){let n=null;if(Vt(e))n=e[Cr];else{let r=e[me];r&&(n=r)}if(!n){for(;e&&!e[We]&&e!==t;)Vt(e)&&ta(e[M],e),e=e[ye];e===null&&(e=t),Vt(e)&&ta(e[M],e),n=e&&e[We]}e=n}}function uE(t,e,n,r){let o=me+r,i=n.length;r>0&&(n[o-1][We]=e),r<i-me?(e[We]=n[o],Cf(n,me+r,e)):(n.push(e),e[We]=null),e[ye]=n;let s=e[un];s!==null&&n!==s&&zh(s,e);let a=e[yt];a!==null&&a.insertView(t),va(e),e[S]|=128}function zh(t,e){let n=t[Gn],r=e[ye];if(Vt(r))t[S]|=Yo.HasTransplantedViews;else{let o=r[ye][Ae];e[Ae]!==o&&(t[S]|=Yo.HasTransplantedViews)}n===null?t[Gn]=[e]:n.push(e)}function Zl(t,e){let n=t[Gn],r=n.indexOf(e);n.splice(r,1)}function Tr(t,e){if(t.length<=me)return;let n=me+e,r=t[n];if(r){let o=r[un];o!==null&&o!==t&&Zl(o,r),e>0&&(t[n-1][We]=r[We]);let i=Go(t,me+e);sE(r[M],r);let s=i[yt];s!==null&&s.detachView(i[M]),r[ye]=null,r[We]=null,r[S]&=-129}return r}function Oi(t,e){if(!(e[S]&256)){let n=e[Z];n.destroyNode&&Fi(t,e,n,3,null,null),lE(e)}}function ta(t,e){if(e[S]&256)return;let n=O(null);try{e[S]&=-129,e[S]|=256,e[je]&&Jr(e[je]),dE(t,e),cE(t,e),e[M].type===1&&e[Z].destroy();let r=e[un];if(r!==null&&It(e[ye])){r!==e[ye]&&Zl(r,e);let o=e[yt];o!==null&&o.detachView(t)}ID(e)}finally{O(n)}}function cE(t,e){let n=t.cleanup,r=e[Zo];if(n!==null)for(let i=0;i<n.length-1;i+=2)if(typeof n[i]=="string"){let s=n[i+3];s>=0?r[s]():r[-s].unsubscribe(),i+=2}else{let s=r[n[i+1]];n[i].call(s)}r!==null&&(e[Zo]=null);let o=e[jt];if(o!==null){e[jt]=null;for(let i=0;i<o.length;i++){let s=o[i];s()}}}function dE(t,e){let n;if(t!=null&&(n=t.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=e[n[r]];if(!(o instanceof hn)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],l=i[s+1];nt(4,a,l);try{l.call(a)}finally{nt(5,a,l)}}else{nt(4,o,i);try{i.call(o)}finally{nt(5,o,i)}}}}}function qh(t,e,n){return fE(t,e.parent,n)}function fE(t,e,n){let r=e;for(;r!==null&&r.type&168;)e=r,r=e.parent;if(r===null)return n[st];{let{componentOffset:o}=r;if(o>-1){let{encapsulation:i}=t.data[r.directiveStart+o];if(i===_r.None||i===_r.Emulated)return null}return Ve(r,n)}}function oi(t,e,n,r,o){t.insertBefore(e,n,r,o)}function Gh(t,e,n){t.appendChild(e,n)}function Gd(t,e,n,r,o){r!==null?oi(t,e,n,r,o):Gh(t,e,n)}function Wh(t,e){return t.parentNode(e)}function hE(t,e){return t.nextSibling(e)}function Qh(t,e,n){return mE(t,e,n)}function pE(t,e,n){return t.type&40?Ve(t,n):null}var mE=pE,Wd;function Pi(t,e,n,r){let o=qh(t,r,e),i=e[Z],s=r.parent||e[Me],a=Qh(s,r,e);if(o!=null)if(Array.isArray(n))for(let l=0;l<n.length;l++)Gd(i,o,n[l],a,!1);else Gd(i,o,n,a,!1);Wd!==void 0&&Wd(i,r,e,n,o)}function vr(t,e){if(e!==null){let n=e.type;if(n&3)return Ve(e,t);if(n&4)return ja(-1,t[e.index]);if(n&8){let r=e.child;if(r!==null)return vr(t,r);{let o=t[e.index];return It(o)?ja(-1,o):it(o)}}else{if(n&128)return vr(t,e.next);if(n&32)return Ql(e,t)()||it(t[e.index]);{let r=Zh(t,e);if(r!==null){if(Array.isArray(r))return r[0];let o=dn(t[Ae]);return vr(o,r)}else return vr(t,e.next)}}}return null}function Zh(t,e){if(e!==null){let r=t[Ae][Me],o=e.projection;return r.projection[o]}return null}function ja(t,e){let n=me+t+1;if(n<e.length){let r=e[n],o=r[M].firstChild;if(o!==null)return vr(r,o)}return e[cn]}function gE(t,e,n){t.removeChild(null,e,n)}function Kl(t,e,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],l=n.type;if(s&&e===0&&(a&&Ut(it(a),r),n.flags|=2),(n.flags&32)!==32)if(l&8)Kl(t,e,n.child,r,o,i,!1),Bn(e,t,o,a,i);else if(l&32){let u=Ql(n,r),c;for(;c=u();)Bn(e,t,o,c,i);Bn(e,t,o,a,i)}else l&16?Kh(t,e,r,n,o,i):Bn(e,t,o,a,i);n=s?n.projectionNext:n.next}}function Fi(t,e,n,r,o,i){Kl(n,r,t.firstChild,e,o,i,!1)}function yE(t,e,n){let r=e[Z],o=qh(t,n,e),i=n.parent||e[Me],s=Qh(i,n,e);Kh(r,0,e,n,o,s)}function Kh(t,e,n,r,o,i){let s=n[Ae],l=s[Me].projection[r.projection];if(Array.isArray(l))for(let u=0;u<l.length;u++){let c=l[u];Bn(e,t,o,c,i)}else{let u=l,c=s[ye];Ch(r)&&(u.flags|=128),Kl(t,e,u,c,o,i,!0)}}function vE(t,e,n,r,o){let i=n[cn],s=it(n);i!==s&&Bn(e,t,r,i,o);for(let a=me;a<n.length;a++){let l=n[a];Fi(l[M],l,t,e,r,i)}}function DE(t,e,n,r,o){if(e)o?t.addClass(n,r):t.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:Mr.DashCase;o==null?t.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=Mr.Important),t.setStyle(n,r,o,i))}}function EE(t,e,n){t.setAttribute(e,"style",n)}function Yh(t,e,n){n===""?t.removeAttribute(e,"class"):t.setAttribute(e,"class",n)}function Jh(t,e,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&fa(t,e,r),o!==null&&Yh(t,e,o),i!==null&&EE(t,e,i)}var te={};function kF(t=1){Xh(q(),v(),Oe()+t,!1)}function Xh(t,e,n,r){if(!r)if((e[S]&3)===3){let i=t.preOrderCheckHooks;i!==null&&Vo(e,i,n)}else{let i=t.preOrderHooks;i!==null&&Bo(e,i,0,n)}fn(n)}function H(t,e=F.Default){let n=v();if(n===null)return ge(t,e);let r=le();return Dh(r,n,ce(t),e)}function LF(){let t="invalid";throw new Error(t)}function ep(t,e,n,r,o,i){let s=O(null);try{let a=null;o&Bt.SignalBased&&(a=e[r][Je]),a!==null&&a.transformFn!==void 0&&(i=a.transformFn(i)),o&Bt.HasDecoratorInputTransform&&(i=t.inputTransforms[r].call(e,i)),t.setInput!==null?t.setInput(e,a,i,n,r):zf(e,a,r,i)}finally{O(s)}}function wE(t,e){let n=t.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)fn(~o);else{let i=o,s=n[++r],a=n[++r];Bv(s,i);let l=e[i];a(2,l)}}}finally{fn(-1)}}function Ri(t,e,n,r,o,i,s,a,l,u,c){let d=e.blueprint.slice();return d[st]=o,d[S]=r|4|128|8|64,(u!==null||t&&t[S]&2048)&&(d[S]|=2048),Kf(d),d[ye]=d[er]=t,d[pe]=n,d[ot]=s||t&&t[ot],d[Z]=a||t&&t[Z],d[qn]=l||t&&t[qn]||null,d[Me]=i,d[wi]=wD(),d[zn]=c,d[Uf]=u,d[Ae]=e.type==2?t[Ae]:d,d}function In(t,e,n,r,o){let i=t.data[e];if(i===null)i=_E(t,e,n,r,o),Vv()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=Lv();i.injectorIndex=s===null?-1:s.injectorIndex}return Gt(i,!0),i}function _E(t,e,n,r,o){let i=nh(),s=Rl(),a=s?i:i&&i.parent,l=t.data[e]=TE(t,a,n,e,r,o);return t.firstChild===null&&(t.firstChild=l),i!==null&&(s?i.child==null&&l.parent!==null&&(i.child=l):i.next===null&&(i.next=l,l.prev=i)),l}function tp(t,e,n,r){if(n===0)return-1;let o=e.length;for(let i=0;i<n;i++)e.push(r),t.blueprint.push(r),t.data.push(null);return o}function np(t,e,n,r,o){let i=Oe(),s=r&2;try{fn(-1),s&&e.length>ee&&Xh(t,e,ee,!1),nt(s?2:0,o),n(r,o)}finally{fn(i),nt(s?3:1,o)}}function Yl(t,e,n){if(Al(e)){let r=O(null);try{let o=e.directiveStart,i=e.directiveEnd;for(let s=o;s<i;s++){let a=t.data[s];if(a.contentQueries){let l=n[s];a.contentQueries(1,l,s)}}}finally{O(r)}}}function Jl(t,e,n){eh()&&(FE(t,e,n,Ve(n,e)),(n.flags&64)===64&&ip(t,e,n))}function Xl(t,e,n=Ve){let r=e.localNames;if(r!==null){let o=e.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(e,t):t[s];t[o++]=a}}}function rp(t){let e=t.tView;return e===null||e.incompleteFirstPass?t.tView=eu(1,null,t.template,t.decls,t.vars,t.directiveDefs,t.pipeDefs,t.viewQuery,t.schemas,t.consts,t.id):e}function eu(t,e,n,r,o,i,s,a,l,u,c){let d=ee+r,h=d+o,f=IE(d,h),p=typeof u=="function"?u():u;return f[M]={type:t,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:e,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:h,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:l,consts:p,incompleteFirstPass:!1,ssrId:c}}function IE(t,e){let n=[];for(let r=0;r<e;r++)n.push(r<t?null:te);return n}function bE(t,e,n,r){let i=r.get(ND,Ah)||n===_r.ShadowDom,s=t.selectRootElement(e,i);return CE(s),s}function CE(t){SE(t)}var SE=()=>null;function ME(t,e,n,r){let o=lp(e);o.push(n),t.firstCreatePass&&up(t).push(r,o.length-1)}function TE(t,e,n,r,o,i){let s=e?e.injectorIndex:-1,a=0;return th()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:e,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}function Qd(t,e,n,r,o){for(let i in e){if(!e.hasOwnProperty(i))continue;let s=e[i];if(s===void 0)continue;r??={};let a,l=Bt.None;Array.isArray(s)?(a=s[0],l=s[1]):a=s;let u=i;if(o!==null){if(!o.hasOwnProperty(i))continue;u=o[i]}t===0?Zd(r,n,u,a,l):Zd(r,n,u,a)}return r}function Zd(t,e,n,r,o){let i;t.hasOwnProperty(n)?(i=t[n]).push(e,r):i=t[n]=[e,r],o!==void 0&&i.push(o)}function NE(t,e,n){let r=e.directiveStart,o=e.directiveEnd,i=t.data,s=e.attrs,a=[],l=null,u=null;for(let c=r;c<o;c++){let d=i[c],h=n?n.get(d):null,f=h?h.inputs:null,p=h?h.outputs:null;l=Qd(0,d.inputs,c,l,f),u=Qd(1,d.outputs,c,u,p);let m=l!==null&&s!==null&&!Sl(e)?qE(l,c,s):null;a.push(m)}l!==null&&(l.hasOwnProperty("class")&&(e.flags|=8),l.hasOwnProperty("style")&&(e.flags|=16)),e.initialInputs=a,e.inputs=l,e.outputs=u}function xE(t){return t==="class"?"className":t==="for"?"htmlFor":t==="formaction"?"formAction":t==="innerHtml"?"innerHTML":t==="readonly"?"readOnly":t==="tabindex"?"tabIndex":t}function ki(t,e,n,r,o,i,s,a){let l=Ve(e,n),u=e.inputs,c;!a&&u!=null&&(c=u[r])?(nu(t,n,c,r,o),_i(e)&&AE(n,e.index)):e.type&3?(r=xE(r),o=s!=null?s(o,e.value||"",r):o,i.setProperty(l,r,o)):e.type&12}function AE(t,e){let n=qt(e,t);n[S]&16||(n[S]|=64)}function tu(t,e,n,r){if(eh()){let o=r===null?null:{"":-1},i=kE(t,n),s,a;i===null?s=a=null:[s,a]=i,s!==null&&op(t,e,n,s,o,a),o&&LE(n,r,o)}n.mergedAttrs=Ir(n.mergedAttrs,n.attrs)}function op(t,e,n,r,o,i){for(let u=0;u<r.length;u++)_a(ti(n,e),t,r[u].type);VE(n,t.data.length,r.length);for(let u=0;u<r.length;u++){let c=r[u];c.providersResolver&&c.providersResolver(c)}let s=!1,a=!1,l=tp(t,e,r.length,null);for(let u=0;u<r.length;u++){let c=r[u];n.mergedAttrs=Ir(n.mergedAttrs,c.hostAttrs),BE(t,n,e,l,c),jE(l,c,o),c.contentQueries!==null&&(n.flags|=4),(c.hostBindings!==null||c.hostAttrs!==null||c.hostVars!==0)&&(n.flags|=64);let d=c.type.prototype;!s&&(d.ngOnChanges||d.ngOnInit||d.ngDoCheck)&&((t.preOrderHooks??=[]).push(n.index),s=!0),!a&&(d.ngOnChanges||d.ngDoCheck)&&((t.preOrderCheckHooks??=[]).push(n.index),a=!0),l++}NE(t,n,i)}function OE(t,e,n,r,o){let i=o.hostBindings;if(i){let s=t.hostBindingOpCodes;s===null&&(s=t.hostBindingOpCodes=[]);let a=~e.index;PE(s)!=a&&s.push(a),s.push(n,r,i)}}function PE(t){let e=t.length;for(;e>0;){let n=t[--e];if(typeof n=="number"&&n<0)return n}return 0}function FE(t,e,n,r){let o=n.directiveStart,i=n.directiveEnd;_i(n)&&$E(e,n,t.data[o+n.componentOffset]),t.firstCreatePass||ti(n,e),Ut(r,e);let s=n.initialInputs;for(let a=o;a<i;a++){let l=t.data[a],u=pn(e,t,a,n);if(Ut(u,e),s!==null&&zE(e,a-o,u,l,n,s),vt(l)){let c=qt(n.index,e);c[pe]=pn(e,t,a,n)}}}function ip(t,e,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=$v();try{fn(i);for(let a=r;a<o;a++){let l=t.data[a],u=e[a];Da(a),(l.hostBindings!==null||l.hostVars!==0||l.hostAttrs!==null)&&RE(l,u)}}finally{fn(-1),Da(s)}}function RE(t,e){t.hostBindings!==null&&t.hostBindings(1,e)}function kE(t,e){let n=t.directiveRegistry,r=null,o=null;if(n)for(let i=0;i<n.length;i++){let s=n[i];if(Af(e,s.selectors,!1))if(r||(r=[]),vt(s))if(s.findHostDirectiveDefs!==null){let a=[];o=o||new Map,s.findHostDirectiveDefs(s,a,o),r.unshift(...a,s);let l=a.length;Va(t,e,l)}else r.unshift(s),Va(t,e,0);else o=o||new Map,s.findHostDirectiveDefs?.(s,r,o),r.push(s)}return r===null?null:[r,o]}function Va(t,e,n){e.componentOffset=n,(t.components??=[]).push(e.index)}function LE(t,e,n){if(e){let r=t.localNames=[];for(let o=0;o<e.length;o+=2){let i=n[e[o+1]];if(i==null)throw new g(-301,!1);r.push(e[o],i)}}}function jE(t,e,n){if(n){if(e.exportAs)for(let r=0;r<e.exportAs.length;r++)n[e.exportAs[r]]=t;vt(e)&&(n[""]=t)}}function VE(t,e,n){t.flags|=1,t.directiveStart=e,t.directiveEnd=e+n,t.providerIndexes=e}function BE(t,e,n,r,o){t.data[r]=o;let i=o.factory||(o.factory=ln(o.type,!0)),s=new hn(i,vt(o),H);t.blueprint[r]=s,n[r]=s,OE(t,e,r,tp(t,n,o.hostVars,te),o)}function $E(t,e,n){let r=Ve(e,t),o=rp(n),i=t[ot].rendererFactory,s=16;n.signals?s=4096:n.onPush&&(s=64);let a=Li(t,Ri(t,o,null,s,r,e,null,i.createRenderer(r,n),null,null,null));t[e.index]=a}function HE(t,e,n,r,o,i){let s=Ve(t,e);UE(e[Z],s,i,t.value,n,r,o)}function UE(t,e,n,r,o,i,s){if(i==null)t.removeAttribute(e,o,n);else{let a=s==null?oe(i):s(i,r||"",o);t.setAttribute(e,o,a,n)}}function zE(t,e,n,r,o,i){let s=i[e];if(s!==null)for(let a=0;a<s.length;){let l=s[a++],u=s[a++],c=s[a++],d=s[a++];ep(r,n,l,u,c,d)}}function qE(t,e,n){let r=null,o=0;for(;o<n.length;){let i=n[o];if(i===0){o+=4;continue}else if(i===5){o+=2;continue}if(typeof i=="number")break;if(t.hasOwnProperty(i)){r===null&&(r=[]);let s=t[i];for(let a=0;a<s.length;a+=3)if(s[a]===e){r.push(i,s[a+1],s[a+2],n[o+1]);break}}o+=2}return r}function sp(t,e,n,r){return[t,!0,0,e,null,r,null,n,null,null]}function ap(t,e){let n=t.contentQueries;if(n!==null){let r=O(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=t.data[s];jl(i),a.contentQueries(2,e[s],s)}}}finally{O(r)}}}function Li(t,e){return t[Cr]?t[Nd][We]=e:t[Cr]=e,t[Nd]=e,e}function Ba(t,e,n){jl(0);let r=O(null);try{e(t,n)}finally{O(r)}}function lp(t){return t[Zo]??=[]}function up(t){return t.cleanup??=[]}function cp(t,e,n){return(t===null||vt(t))&&(n=Mv(n[e.index])),n[Z]}function dp(t,e){let n=t[qn],r=n?n.get(mn,null):null;r&&r.handleError(e)}function nu(t,e,n,r,o){for(let i=0;i<n.length;){let s=n[i++],a=n[i++],l=n[i++],u=e[s],c=t.data[s];ep(c,u,r,a,l,o)}}function Rr(t,e,n){let r=Zf(e,t);oE(t[Z],r,n)}function GE(t,e){let n=qt(e,t),r=n[M];WE(r,n);let o=n[st];o!==null&&n[zn]===null&&(n[zn]=ql(o,n[qn])),ru(r,n,n[pe])}function WE(t,e){for(let n=e.length;n<t.blueprint.length;n++)e.push(t.blueprint[n])}function ru(t,e,n){Vl(e);try{let r=t.viewQuery;r!==null&&Ba(1,r,n);let o=t.template;o!==null&&np(t,e,o,1,n),t.firstCreatePass&&(t.firstCreatePass=!1),e[yt]?.finishViewCreation(t),t.staticContentQueries&&ap(t,e),t.staticViewQueries&&Ba(2,t.viewQuery,n);let i=t.components;i!==null&&QE(e,i)}catch(r){throw t.firstCreatePass&&(t.incompleteFirstPass=!0,t.firstCreatePass=!1),r}finally{e[S]&=-5,Bl()}}function QE(t,e){for(let n=0;n<e.length;n++)GE(t,e[n])}function kr(t,e,n,r){let o=O(null);try{let i=e.tView,a=t[S]&4096?4096:16,l=Ri(t,i,n,a,null,e,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),u=t[e.index];l[un]=u;let c=t[yt];return c!==null&&(l[yt]=c.createEmbeddedView(i)),ru(i,l,n),l}finally{O(o)}}function fp(t,e){let n=me+e;if(n<t.length)return t[n]}function Wn(t,e){return!e||e.firstChild===null||Ch(t)}function Lr(t,e,n,r=!0){let o=e[M];if(uE(o,e,t,n),r){let s=ja(n,t),a=e[Z],l=Wh(a,t[cn]);l!==null&&aE(o,t[Me],a,e,l,s)}let i=e[zn];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function hp(t,e){let n=Tr(t,e);return n!==void 0&&Oi(n[M],n),n}function ii(t,e,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=e[n.index];i!==null&&r.push(it(i)),It(i)&&ZE(i,r);let s=n.type;if(s&8)ii(t,e,n.child,r);else if(s&32){let a=Ql(n,e),l;for(;l=a();)r.push(l)}else if(s&16){let a=Zh(e,n);if(Array.isArray(a))r.push(...a);else{let l=dn(e[Ae]);ii(l[M],l,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function ZE(t,e){for(let n=me;n<t.length;n++){let r=t[n],o=r[M].firstChild;o!==null&&ii(r[M],r,o,e)}t[cn]!==t[st]&&e.push(t[cn])}var pp=[];function KE(t){return t[je]??YE(t)}function YE(t){let e=pp.pop()??Object.create(XE);return e.lView=t,e}function JE(t){t.lView[je]!==t&&(t.lView=null,pp.push(t))}var XE=dt(xe({},Mn),{consumerIsAlwaysLive:!0,consumerMarkedDirty:t=>{bi(t.lView)},consumerOnSignalRead(){this.lView[je]=this}});function ew(t){let e=t[je]??Object.create(tw);return e.lView=t,e}var tw=dt(xe({},Mn),{consumerIsAlwaysLive:!0,consumerMarkedDirty:t=>{let e=dn(t.lView);for(;e&&!mp(e[M]);)e=dn(e);e&&Yf(e)},consumerOnSignalRead(){this.lView[je]=this}});function mp(t){return t.type!==2}var nw=100;function gp(t,e=!0,n=0){let r=t[ot],o=r.rendererFactory,i=!1;i||o.begin?.();try{rw(t,n)}catch(s){throw e&&dp(t,s),s}finally{i||(o.end?.(),r.inlineEffectRunner?.flush())}}function rw(t,e){let n=oh();try{Ad(!0),$a(t,e);let r=0;for(;Sr(t);){if(r===nw)throw new g(103,!1);r++,$a(t,1)}}finally{Ad(n)}}function ow(t,e,n,r){let o=e[S];if((o&256)===256)return;let i=!1,s=!1;!i&&e[ot].inlineEffectRunner?.flush(),Vl(e);let a=!0,l=null,u=null;i||(mp(t)?(u=KE(e),l=cr(u)):Ec()===null?(a=!1,u=ew(e),l=cr(u)):e[je]&&(Jr(e[je]),e[je]=null));try{Kf(e),jv(t.bindingStartIndex),n!==null&&np(t,e,n,2,r);let c=(o&3)===3;if(!i)if(c){let f=t.preOrderCheckHooks;f!==null&&Vo(e,f,null)}else{let f=t.preOrderHooks;f!==null&&Bo(e,f,0,null),Ys(e,0)}if(s||iw(e),yp(e,0),t.contentQueries!==null&&ap(t,e),!i)if(c){let f=t.contentCheckHooks;f!==null&&Vo(e,f)}else{let f=t.contentHooks;f!==null&&Bo(e,f,1),Ys(e,1)}wE(t,e);let d=t.components;d!==null&&Dp(e,d,0);let h=t.viewQuery;if(h!==null&&Ba(2,h,r),!i)if(c){let f=t.viewCheckHooks;f!==null&&Vo(e,f)}else{let f=t.viewHooks;f!==null&&Bo(e,f,2),Ys(e,2)}if(t.firstUpdatePass===!0&&(t.firstUpdatePass=!1),e[jo]){for(let f of e[jo])f();e[jo]=null}i||(e[S]&=-73)}catch(c){throw i||bi(e),c}finally{u!==null&&(Kr(u,l),a&&JE(u)),Bl()}}function yp(t,e){for(let n=Mh(t);n!==null;n=Th(n))for(let r=me;r<n.length;r++){let o=n[r];vp(o,e)}}function iw(t){for(let e=Mh(t);e!==null;e=Th(e)){if(!(e[S]&Yo.HasTransplantedViews))continue;let n=e[Gn];for(let r=0;r<n.length;r++){let o=n[r];Yf(o)}}}function sw(t,e,n){let r=qt(e,t);vp(r,n)}function vp(t,e){Fl(t)&&$a(t,e)}function $a(t,e){let r=t[M],o=t[S],i=t[je],s=!!(e===0&&o&16);if(s||=!!(o&64&&e===0),s||=!!(o&1024),s||=!!(i?.dirty&&Yr(i)),s||=!1,i&&(i.dirty=!1),t[S]&=-9217,s)ow(r,t,r.template,t[pe]);else if(o&8192){yp(t,1);let a=r.components;a!==null&&Dp(t,a,1)}}function Dp(t,e,n){for(let r=0;r<e.length;r++)sw(t,e[r],n)}function ou(t,e){let n=oh()?64:1088;for(t[ot].changeDetectionScheduler?.notify(e);t;){t[S]|=n;let r=dn(t);if(ga(t)&&!r)return t;t=r}return null}var gn=class{get rootNodes(){let e=this._lView,n=e[M];return ii(n,e,n.firstChild,[])}constructor(e,n,r=!0){this._lView=e,this._cdRefInjectingView=n,this.notifyErrorHandler=r,this._appRef=null,this._attachedToViewContainer=!1}get context(){return this._lView[pe]}set context(e){this._lView[pe]=e}get destroyed(){return(this._lView[S]&256)===256}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let e=this._lView[ye];if(It(e)){let n=e[Ko],r=n?n.indexOf(this):-1;r>-1&&(Tr(e,r),Go(n,r))}this._attachedToViewContainer=!1}Oi(this._lView[M],this._lView)}onDestroy(e){Jf(this._lView,e)}markForCheck(){ou(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[S]&=-129}reattach(){va(this._lView),this._lView[S]|=128}detectChanges(){this._lView[S]|=1024,gp(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new g(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let e=ga(this._lView),n=this._lView[un];n!==null&&!e&&Zl(n,this._lView),Uh(this._lView[M],this._lView)}attachToAppRef(e){if(this._attachedToViewContainer)throw new g(902,!1);this._appRef=e;let n=ga(this._lView),r=this._lView[un];r!==null&&!n&&zh(r,this._lView),va(this._lView)}},wt=(()=>{class t{static{this.__NG_ELEMENT_ID__=uw}}return t})(),aw=wt,lw=class extends aw{constructor(e,n,r){super(),this._declarationLView=e,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(e,n){return this.createEmbeddedViewImpl(e,n)}createEmbeddedViewImpl(e,n,r){let o=kr(this._declarationLView,this._declarationTContainer,e,{embeddedViewInjector:n,dehydratedView:r});return new gn(o)}};function uw(){return ji(le(),v())}function ji(t,e){return t.type&4?new lw(e,t,rr(t,e)):null}var VF=new RegExp(`^(\\d+)*(${MD}|${SD})*(.*)`);var cw=()=>null;function Qn(t,e){return cw(t,e)}var Zn=class{},iu=new j("",{providedIn:"root",factory:()=>!1});var Ep=new j(""),Ha=class{},si=class{};function dw(t){let e=Error(`No component factory found for ${Ee(t)}.`);return e[fw]=t,e}var fw="ngComponent";var Ua=class{resolveComponentFactory(e){throw dw(e)}},Kn=class{static{this.NULL=new Ua}},ai=class{},Vi=(()=>{class t{constructor(){this.destroyNode=null}static{this.__NG_ELEMENT_ID__=()=>hw()}}return t})();function hw(){let t=v(),e=le(),n=qt(e.index,t);return(Vt(n)?n:t)[Z]}var pw=(()=>{class t{static{this.\u0275prov=z({token:t,providedIn:"root",factory:()=>null})}}return t})();var Kd=new Set;function Qe(t){Kd.has(t)||(Kd.add(t),performance?.mark?.("mark_feature_usage",{detail:{feature:t}}))}var De=function(t){return t[t.EarlyRead=0]="EarlyRead",t[t.Write=1]="Write",t[t.MixedReadWrite=2]="MixedReadWrite",t[t.Read=3]="Read",t}(De||{}),wp={destroy(){}};function mw(t,e){!e&&Nl(mw);let n=e?.injector??A(Dt);return $h(n)?(Qe("NgAfterRender"),_p(t,n,!1,e?.phase??De.MixedReadWrite)):wp}function gw(t,e){!e&&Nl(gw);let n=e?.injector??A(Dt);return $h(n)?(Qe("NgAfterNextRender"),_p(t,n,!0,e?.phase??De.MixedReadWrite)):wp}function yw(t,e){if(t instanceof Function)switch(e){case De.EarlyRead:return{earlyRead:t};case De.Write:return{write:t};case De.MixedReadWrite:return{mixedReadWrite:t};case De.Read:return{read:t}}return t}function _p(t,e,n,r){let o=yw(t,r),i=e.get(su),s=i.handler??=new qa,a=[],l=[],u=()=>{for(let f of l)s.unregister(f);c()},c=e.get(Ni).onDestroy(u),d=0,h=(f,p)=>{if(!p)return;let m=n?(...E)=>(d--,d<1&&u(),p(...E)):p,D=wv(e,()=>new za(f,a,m));s.register(D),l.push(D),d++};return h(De.EarlyRead,o.earlyRead),h(De.Write,o.write),h(De.MixedReadWrite,o.mixedReadWrite),h(De.Read,o.read),{destroy:u}}var za=class{constructor(e,n,r){this.phase=e,this.pipelinedArgs=n,this.callbackFn=r,this.zone=A(Se),this.errorHandler=A(mn,{optional:!0}),A(Zn,{optional:!0})?.notify(6)}invoke(){try{let e=this.zone.runOutsideAngular(()=>this.callbackFn.apply(null,this.pipelinedArgs));this.pipelinedArgs.splice(0,this.pipelinedArgs.length,e)}catch(e){this.errorHandler?.handleError(e)}}},qa=class{constructor(){this.executingCallbacks=!1,this.buckets={[De.EarlyRead]:new Set,[De.Write]:new Set,[De.MixedReadWrite]:new Set,[De.Read]:new Set},this.deferredCallbacks=new Set}register(e){(this.executingCallbacks?this.deferredCallbacks:this.buckets[e.phase]).add(e)}unregister(e){this.buckets[e.phase].delete(e),this.deferredCallbacks.delete(e)}execute(){this.executingCallbacks=!0;for(let e of Object.values(this.buckets))for(let n of e)n.invoke();this.executingCallbacks=!1;for(let e of this.deferredCallbacks)this.buckets[e.phase].add(e);this.deferredCallbacks.clear()}destroy(){for(let e of Object.values(this.buckets))e.clear();this.deferredCallbacks.clear()}},su=(()=>{class t{constructor(){this.handler=null,this.internalCallbacks=[]}execute(){this.executeInternalCallbacks(),this.handler?.execute()}executeInternalCallbacks(){let n=[...this.internalCallbacks];this.internalCallbacks.length=0;for(let r of n)r()}ngOnDestroy(){this.handler?.destroy(),this.handler=null,this.internalCallbacks.length=0}static{this.\u0275prov=z({token:t,providedIn:"root",factory:()=>new t})}}return t})();function li(t,e,n){let r=n?t.styles:null,o=n?t.classes:null,i=0;if(e!==null)for(let s=0;s<e.length;s++){let a=e[s];if(typeof a=="number")i=a;else if(i==1)o=la(o,a);else if(i==2){let l=a,u=e[++s];r=la(r,l+": "+u+";")}}n?t.styles=r:t.stylesWithoutHost=r,n?t.classes=o:t.classesWithoutHost=o}var ui=class extends Kn{constructor(e){super(),this.ngModule=e}resolveComponentFactory(e){let n=gt(e);return new yn(n,this.ngModule)}};function Yd(t,e){let n=[];for(let r in t){if(!t.hasOwnProperty(r))continue;let o=t[r];if(o===void 0)continue;let i=Array.isArray(o),s=i?o[0]:o,a=i?o[1]:Bt.None;e?n.push({propName:s,templateName:r,isSignal:(a&Bt.SignalBased)!==0}):n.push({propName:s,templateName:r})}return n}function vw(t){let e=t.toLowerCase();return e==="svg"?Qf:e==="math"?Sv:null}var yn=class extends si{get inputs(){let e=this.componentDef,n=e.inputTransforms,r=Yd(e.inputs,!0);if(n!==null)for(let o of r)n.hasOwnProperty(o.propName)&&(o.transform=n[o.propName]);return r}get outputs(){return Yd(this.componentDef.outputs,!1)}constructor(e,n){super(),this.componentDef=e,this.ngModule=n,this.componentType=e.type,this.selector=rv(e.selectors),this.ngContentSelectors=e.ngContentSelectors?e.ngContentSelectors:[],this.isBoundToModule=!!n}create(e,n,r,o){let i=O(null);try{o=o||this.ngModule;let s=o instanceof $t?o:o?.injector;s&&this.componentDef.getStandaloneInjector!==null&&(s=this.componentDef.getStandaloneInjector(s)||s);let a=s?new Ea(e,s):e,l=a.get(ai,null);if(l===null)throw new g(407,!1);let u=a.get(pw,null),c=a.get(su,null),d=a.get(Zn,null),h={rendererFactory:l,sanitizer:u,inlineEffectRunner:null,afterRenderEventManager:c,changeDetectionScheduler:d},f=l.createRenderer(null,this.componentDef),p=this.componentDef.selectors[0][0]||"div",m=r?bE(f,r,this.componentDef.encapsulation,a):Hh(f,p,vw(p)),D=512;this.componentDef.signals?D|=4096:this.componentDef.onPush||(D|=16);let E=null;m!==null&&(E=ql(m,a,!0));let T=eu(0,null,null,1,0,null,null,null,null,null,null),k=Ri(null,T,null,D,null,null,h,f,a,null,E);Vl(k);let V,de;try{let Y=this.componentDef,J,se=null;Y.findHostDirectiveDefs?(J=[],se=new Map,Y.findHostDirectiveDefs(Y,J,se),J.push(Y)):J=[Y];let ct=Dw(k,m),xt=Ew(ct,m,Y,J,k,h,f);de=Pl(T,ee),m&&Iw(f,Y,m,r),n!==void 0&&bw(de,this.ngContentSelectors,n),V=_w(xt,Y,J,se,k,[Cw]),ru(T,k,null)}finally{Bl()}return new Ga(this.componentType,V,rr(de,k),k,de)}finally{O(i)}}},Ga=class extends Ha{constructor(e,n,r,o,i){super(),this.location=r,this._rootLView=o,this._tNode=i,this.previousInputValues=null,this.instance=n,this.hostView=this.changeDetectorRef=new gn(o,void 0,!1),this.componentType=e}setInput(e,n){let r=this._tNode.inputs,o;if(r!==null&&(o=r[e])){if(this.previousInputValues??=new Map,this.previousInputValues.has(e)&&Object.is(this.previousInputValues.get(e),n))return;let i=this._rootLView;nu(i[M],i,o,e,n),this.previousInputValues.set(e,n);let s=qt(this._tNode.index,i);ou(s,1)}}get injector(){return new an(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(e){this.hostView.onDestroy(e)}};function Dw(t,e){let n=t[M],r=ee;return t[r]=e,In(n,r,2,"#host",null)}function Ew(t,e,n,r,o,i,s){let a=o[M];ww(r,t,e,s);let l=null;e!==null&&(l=ql(e,o[qn]));let u=i.rendererFactory.createRenderer(e,n),c=16;n.signals?c=4096:n.onPush&&(c=64);let d=Ri(o,rp(n),null,c,o[t.index],t,i,u,null,null,l);return a.firstCreatePass&&Va(a,t,r.length-1),Li(o,d),o[t.index]=d}function ww(t,e,n,r){for(let o of t)e.mergedAttrs=Ir(e.mergedAttrs,o.hostAttrs);e.mergedAttrs!==null&&(li(e,e.mergedAttrs,!0),n!==null&&Jh(r,n,e))}function _w(t,e,n,r,o,i){let s=le(),a=o[M],l=Ve(s,o);op(a,o,s,n,null,r);for(let c=0;c<n.length;c++){let d=s.directiveStart+c,h=pn(o,a,d,s);Ut(h,o)}ip(a,o,s),l&&Ut(l,o);let u=pn(o,a,s.directiveStart+s.componentOffset,s);if(t[pe]=o[pe]=u,i!==null)for(let c of i)c(u,e);return Yl(a,s,o),u}function Iw(t,e,n,r){if(r)fa(t,n,["ng-version","18.2.0"]);else{let{attrs:o,classes:i}=ov(e.selectors[0]);o&&fa(t,n,o),i&&i.length>0&&Yh(t,n,i.join(" "))}}function bw(t,e,n){let r=t.projection=[];for(let o=0;o<e.length;o++){let i=n[o];r.push(i!=null?Array.from(i):null)}}function Cw(){let t=le();Ti(v()[M],t)}var Ct=(()=>{class t{static{this.__NG_ELEMENT_ID__=Sw}}return t})();function Sw(){let t=le();return bp(t,v())}var Mw=Ct,Ip=class extends Mw{constructor(e,n,r){super(),this._lContainer=e,this._hostTNode=n,this._hostLView=r}get element(){return rr(this._hostTNode,this._hostLView)}get injector(){return new an(this._hostTNode,this._hostLView)}get parentInjector(){let e=$l(this._hostTNode,this._hostLView);if(hh(e)){let n=Xo(e,this._hostLView),r=Jo(e),o=n[M].data[r+8];return new an(o,n)}else return new an(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(e){let n=Jd(this._lContainer);return n!==null&&n[e]||null}get length(){return this._lContainer.length-me}createEmbeddedView(e,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Qn(this._lContainer,e.ssrId),a=e.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,Wn(this._hostTNode,s)),a}createComponent(e,n,r,o,i){let s=e&&!_v(e),a;if(s)a=n;else{let p=n||{};a=p.index,r=p.injector,o=p.projectableNodes,i=p.environmentInjector||p.ngModuleRef}let l=s?e:new yn(gt(e)),u=r||this.parentInjector;if(!i&&l.ngModule==null){let m=(s?u:this.parentInjector).get($t,null);m&&(i=m)}let c=gt(l.componentType??{}),d=Qn(this._lContainer,c?.id??null),h=d?.firstChild??null,f=l.create(u,o,h,i);return this.insertImpl(f.hostView,a,Wn(this._hostTNode,d)),f}insert(e,n){return this.insertImpl(e,n,!0)}insertImpl(e,n,r){let o=e._lView;if(Nv(o)){let a=this.indexOf(e);if(a!==-1)this.detach(a);else{let l=o[ye],u=new Ip(l,l[Me],l[ye]);u.detach(u.indexOf(e))}}let i=this._adjustIndex(n),s=this._lContainer;return Lr(s,o,i,r),e.attachToViewContainerRef(),Cf(na(s),i,e),e}move(e,n){return this.insert(e,n)}indexOf(e){let n=Jd(this._lContainer);return n!==null?n.indexOf(e):-1}remove(e){let n=this._adjustIndex(e,-1),r=Tr(this._lContainer,n);r&&(Go(na(this._lContainer),n),Oi(r[M],r))}detach(e){let n=this._adjustIndex(e,-1),r=Tr(this._lContainer,n);return r&&Go(na(this._lContainer),n)!=null?new gn(r):null}_adjustIndex(e,n=0){return e??this.length+n}};function Jd(t){return t[Ko]}function na(t){return t[Ko]||(t[Ko]=[])}function bp(t,e){let n,r=e[t.index];return It(r)?n=r:(n=sp(r,e,null,t),e[t.index]=n,Li(e,n)),Nw(n,e,t,r),new Ip(n,t,e)}function Tw(t,e){let n=t[Z],r=n.createComment(""),o=Ve(e,t),i=Wh(n,o);return oi(n,i,r,hE(n,o),!1),r}var Nw=Ow,xw=()=>!1;function Aw(t,e,n){return xw(t,e,n)}function Ow(t,e,n,r){if(t[cn])return;let o;n.type&8?o=it(r):o=Tw(e,n),t[cn]=o}var Wa=class t{constructor(e){this.queryList=e,this.matches=null}clone(){return new t(this.queryList)}setDirty(){this.queryList.setDirty()}},Qa=class t{constructor(e=[]){this.queries=e}createEmbeddedView(e){let n=e.queries;if(n!==null){let r=e.contentQueries!==null?e.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new t(o)}return null}insertView(e){this.dirtyQueriesWithMatches(e)}detachView(e){this.dirtyQueriesWithMatches(e)}finishViewCreation(e){this.dirtyQueriesWithMatches(e)}dirtyQueriesWithMatches(e){for(let n=0;n<this.queries.length;n++)au(e,n).matches!==null&&this.queries[n].setDirty()}},ci=class{constructor(e,n,r=null){this.flags=n,this.read=r,typeof e=="string"?this.predicate=Bw(e):this.predicate=e}},Za=class t{constructor(e=[]){this.queries=e}elementStart(e,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(e,n)}elementEnd(e){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(e)}embeddedTView(e){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(e,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new t(n):null}template(e,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(e,n)}getByIndex(e){return this.queries[e]}get length(){return this.queries.length}track(e){this.queries.push(e)}},Ka=class t{constructor(e,n=-1){this.metadata=e,this.matches=null,this.indexInDeclarationView=-1,this.crossesNgTemplate=!1,this._appliesToNextNode=!0,this._declarationNodeIndex=n}elementStart(e,n){this.isApplyingToNode(n)&&this.matchTNode(e,n)}elementEnd(e){this._declarationNodeIndex===e.index&&(this._appliesToNextNode=!1)}template(e,n){this.elementStart(e,n)}embeddedTView(e,n){return this.isApplyingToNode(e)?(this.crossesNgTemplate=!0,this.addMatch(-e.index,n),new t(this.metadata)):null}isApplyingToNode(e){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=e.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(e,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(e,n,Pw(n,i)),this.matchTNodeWithReadOption(e,n,$o(n,e,i,!1,!1))}else r===wt?n.type&4&&this.matchTNodeWithReadOption(e,n,-1):this.matchTNodeWithReadOption(e,n,$o(n,e,r,!1,!1))}matchTNodeWithReadOption(e,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===Wt||o===Ct||o===wt&&n.type&4)this.addMatch(n.index,-2);else{let i=$o(n,e,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(e,n){this.matches===null?this.matches=[e,n]:this.matches.push(e,n)}};function Pw(t,e){let n=t.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===e)return n[r+1]}return null}function Fw(t,e){return t.type&11?rr(t,e):t.type&4?ji(t,e):null}function Rw(t,e,n,r){return n===-1?Fw(e,t):n===-2?kw(t,e,r):pn(t,t[M],n,e)}function kw(t,e,n){if(n===Wt)return rr(e,t);if(n===wt)return ji(e,t);if(n===Ct)return bp(e,t)}function Cp(t,e,n,r){let o=e[yt].queries[r];if(o.matches===null){let i=t.data,s=n.matches,a=[];for(let l=0;s!==null&&l<s.length;l+=2){let u=s[l];if(u<0)a.push(null);else{let c=i[u];a.push(Rw(e,c,s[l+1],n.metadata.read))}}o.matches=a}return o.matches}function Ya(t,e,n,r){let o=t.queries.getByIndex(n),i=o.matches;if(i!==null){let s=Cp(t,e,o,n);for(let a=0;a<i.length;a+=2){let l=i[a];if(l>0)r.push(s[a/2]);else{let u=i[a+1],c=e[-l];for(let d=me;d<c.length;d++){let h=c[d];h[un]===h[ye]&&Ya(h[M],h,u,r)}if(c[Gn]!==null){let d=c[Gn];for(let h=0;h<d.length;h++){let f=d[h];Ya(f[M],f,u,r)}}}}}return r}function Lw(t,e){return t[yt].queries[e].queryList}function Sp(t,e,n){let r=new Ta((n&4)===4);return ME(t,e,r,r.destroy),(e[yt]??=new Qa).queries.push(new Wa(r))-1}function jw(t,e,n){let r=q();return r.firstCreatePass&&(Mp(r,new ci(t,e,n),-1),(e&2)===2&&(r.staticViewQueries=!0)),Sp(r,v(),e)}function Vw(t,e,n,r){let o=q();if(o.firstCreatePass){let i=le();Mp(o,new ci(e,n,r),i.index),$w(o,t),(n&2)===2&&(o.staticContentQueries=!0)}return Sp(o,v(),n)}function Bw(t){return t.split(",").map(e=>e.trim())}function Mp(t,e,n){t.queries===null&&(t.queries=new Za),t.queries.track(new Ka(e,n))}function $w(t,e){let n=t.contentQueries||(t.contentQueries=[]),r=n.length?n[n.length-1]:-1;e!==r&&n.push(t.queries.length-1,e)}function au(t,e){return t.queries.getByIndex(e)}function Hw(t,e){let n=t[M],r=au(n,e);return r.crossesNgTemplate?Ya(n,t,e,[]):Cp(n,t,r,e)}function Uw(t){return typeof t=="function"&&t[Je]!==void 0}function $F(t,e){Qe("NgSignals");let n=Oc(t),r=n[Je];return e?.equal&&(r.equal=e.equal),n.set=o=>Ts(r,o),n.update=o=>Pc(r,o),n.asReadonly=zw.bind(n),n}function zw(){let t=this[Je];if(t.readonlyFn===void 0){let e=()=>this();e[Je]=t,t.readonlyFn=e}return t.readonlyFn}function Tp(t){return Uw(t)&&typeof t.set=="function"}function qw(t){return Object.getPrototypeOf(t.prototype).constructor}function Gw(t){let e=qw(t.type),n=!0,r=[t];for(;e;){let o;if(vt(t))o=e.\u0275cmp||e.\u0275dir;else{if(e.\u0275cmp)throw new g(903,!1);o=e.\u0275dir}if(o){if(n){r.push(o);let s=t;s.inputs=Fo(t.inputs),s.inputTransforms=Fo(t.inputTransforms),s.declaredInputs=Fo(t.declaredInputs),s.outputs=Fo(t.outputs);let a=o.hostBindings;a&&Yw(t,a);let l=o.viewQuery,u=o.contentQueries;if(l&&Zw(t,l),u&&Kw(t,u),Ww(t,o),_y(t.outputs,o.outputs),vt(o)&&o.data.animation){let c=t.data;c.animation=(c.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(t),a===Gw&&(n=!1)}}e=Object.getPrototypeOf(e)}Qw(r)}function Ww(t,e){for(let n in e.inputs){if(!e.inputs.hasOwnProperty(n)||t.inputs.hasOwnProperty(n))continue;let r=e.inputs[n];if(r!==void 0&&(t.inputs[n]=r,t.declaredInputs[n]=e.declaredInputs[n],e.inputTransforms!==null)){let o=Array.isArray(r)?r[0]:r;if(!e.inputTransforms.hasOwnProperty(o))continue;t.inputTransforms??={},t.inputTransforms[o]=e.inputTransforms[o]}}}function Qw(t){let e=0,n=null;for(let r=t.length-1;r>=0;r--){let o=t[r];o.hostVars=e+=o.hostVars,o.hostAttrs=Ir(o.hostAttrs,n=Ir(n,o.hostAttrs))}}function Fo(t){return t===mt?{}:t===Ce?[]:t}function Zw(t,e){let n=t.viewQuery;n?t.viewQuery=(r,o)=>{e(r,o),n(r,o)}:t.viewQuery=e}function Kw(t,e){let n=t.contentQueries;n?t.contentQueries=(r,o,i)=>{e(r,o,i),n(r,o,i)}:t.contentQueries=e}function Yw(t,e){let n=t.hostBindings;n?t.hostBindings=(r,o)=>{e(r,o),n(r,o)}:t.hostBindings=e}function HF(t){let e=n=>{let r=(Array.isArray(t)?t:t()).map(o=>typeof o=="function"?{directive:ce(o),inputs:mt,outputs:mt}:{directive:ce(o.directive),inputs:Xd(o.inputs),outputs:Xd(o.outputs)});n.hostDirectives===null?(n.findHostDirectiveDefs=Np,n.hostDirectives=r):n.hostDirectives.unshift(...r)};return e.ngInherit=!0,e}function Np(t,e,n){if(t.hostDirectives!==null)for(let r of t.hostDirectives){let o=Ml(r.directive);Jw(o.declaredInputs,r.inputs),Np(o,e,n),n.set(o,r),e.push(o)}}function Xd(t){if(t===void 0||t.length===0)return mt;let e={};for(let n=0;n<t.length;n+=2)e[t[n]]=t[n+1];return e}function Jw(t,e){for(let n in e)if(e.hasOwnProperty(n)){let r=e[n],o=t[n];t[r]=o}}function Xw(t){let e=t.inputConfig,n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r];Array.isArray(o)&&o[3]&&(n[r]=o[3])}t.inputTransforms=n}var zt=class{},Ja=class{};var Xa=class extends zt{constructor(e,n,r,o=!0){super(),this.ngModuleType=e,this._parent=n,this._bootstrapComponents=[],this.destroyCbs=[],this.componentFactoryResolver=new ui(this);let i=Ff(e);this._bootstrapComponents=Bh(i.bootstrap),this._r3Injector=_h(e,n,[{provide:zt,useValue:this},{provide:Kn,useValue:this.componentFactoryResolver},...r],Ee(e),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let e=this._r3Injector;!e.destroyed&&e.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(e){this.destroyCbs.push(e)}},el=class extends Ja{constructor(e){super(),this.moduleType=e}create(e){return new Xa(this.moduleType,e,[])}};var di=class extends zt{constructor(e){super(),this.componentFactoryResolver=new ui(this),this.instance=null;let n=new br([...e.providers,{provide:zt,useValue:this},{provide:Kn,useValue:this.componentFactoryResolver}],e.parent||Ei(),e.debugName,new Set(["environment"]));this.injector=n,e.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(e){this.injector.onDestroy(e)}};function e_(t,e,n=null){return new di({providers:t,parent:e,debugName:n,runEnvironmentInitializers:!0}).injector}function xp(t){return lu(t)?Array.isArray(t)||!(t instanceof Map)&&Symbol.iterator in t:!1}function t_(t,e){if(Array.isArray(t))for(let n=0;n<t.length;n++)e(t[n]);else{let n=t[Symbol.iterator](),r;for(;!(r=n.next()).done;)e(r.value)}}function lu(t){return t!==null&&(typeof t=="function"||typeof t=="object")}function Qt(t,e,n){return t[e]=n}function Bi(t,e){return t[e]}function we(t,e,n){let r=t[e];return Object.is(r,n)?!1:(t[e]=n,!0)}function Yn(t,e,n,r){let o=we(t,e,n);return we(t,e+1,r)||o}function uu(t,e,n,r,o){let i=Yn(t,e,n,r);return we(t,e+2,o)||i}function or(t,e,n,r,o,i){let s=Yn(t,e,n,r);return Yn(t,e+2,o,i)||s}function n_(t){return(t.flags&32)===32}function r_(t,e,n,r,o,i,s,a,l){let u=e.consts,c=In(e,t,4,s||null,a||null);tu(e,n,c,Ht(u,l)),Ti(e,c);let d=c.tView=eu(2,c,r,o,i,e.directiveRegistry,e.pipeRegistry,null,e.schemas,u,null);return e.queries!==null&&(e.queries.template(e,c),d.queries=e.queries.embeddedTView(c)),c}function fi(t,e,n,r,o,i,s,a,l,u){let c=n+ee,d=e.firstCreatePass?r_(c,e,t,r,o,i,s,a,l):e.data[c];Gt(d,!1);let h=i_(e,t,d,n);Si()&&Pi(e,t,h,d),Ut(h,t);let f=sp(h,t,h,d);return t[c]=f,Li(t,f),Aw(f,d,t),Ii(d)&&Jl(e,t,d),l!=null&&Xl(t,d,u),d}function o_(t,e,n,r,o,i,s,a){let l=v(),u=q(),c=Ht(u.consts,i);return fi(l,u,t,e,n,r,o,c,s,a),o_}var i_=s_;function s_(t,e,n,r){return Mi(!0),e[Z].createComment("")}function a_(t,e,n,r){let o=v(),i=wn();if(we(o,i,e)){let s=q(),a=Or();HE(a,o,t,e,n,r)}return a_}function cu(t,e,n,r){return we(t,wn(),n)?e+oe(n)+r:te}function Ap(t,e,n,r,o,i){let s=Ci(),a=Yn(t,s,n,o);return nr(2),a?e+oe(n)+r+oe(o)+i:te}function l_(t,e,n,r,o,i,s,a){let l=Ci(),u=uu(t,l,n,o,s);return nr(3),u?e+oe(n)+r+oe(o)+i+oe(s)+a:te}function Op(t,e,n,r,o,i,s,a,l,u){let c=Ci(),d=or(t,c,n,o,s,l);return nr(4),d?e+oe(n)+r+oe(o)+i+oe(s)+a+oe(l)+u:te}function u_(t,e,n,r,o,i,s,a,l,u,c,d){let h=Ci(),f=or(t,h,n,o,s,l);return f=we(t,h+4,c)||f,nr(5),f?e+oe(n)+r+oe(o)+i+oe(s)+a+oe(l)+u+oe(c)+d:te}function Ro(t,e){return t<<17|e<<2}function vn(t){return t>>17&32767}function c_(t){return(t&2)==2}function d_(t,e){return t&131071|e<<17}function tl(t){return t|2}function Jn(t){return(t&131068)>>2}function ra(t,e){return t&-131069|e<<2}function f_(t){return(t&1)===1}function nl(t){return t|1}function h_(t,e,n,r,o,i){let s=i?e.classBindings:e.styleBindings,a=vn(s),l=Jn(s);t[r]=n;let u=!1,c;if(Array.isArray(n)){let d=n;c=d[1],(c===null||Ar(d,c)>0)&&(u=!0)}else c=n;if(o)if(l!==0){let h=vn(t[a+1]);t[r+1]=Ro(h,a),h!==0&&(t[h+1]=ra(t[h+1],r)),t[a+1]=d_(t[a+1],r)}else t[r+1]=Ro(a,0),a!==0&&(t[a+1]=ra(t[a+1],r)),a=r;else t[r+1]=Ro(l,0),a===0?a=r:t[l+1]=ra(t[l+1],r),l=r;u&&(t[r+1]=tl(t[r+1])),ef(t,c,r,!0),ef(t,c,r,!1),p_(e,c,t,r,i),s=Ro(a,l),i?e.classBindings=s:e.styleBindings=s}function p_(t,e,n,r,o){let i=o?t.residualClasses:t.residualStyles;i!=null&&typeof e=="string"&&Ar(i,e)>=0&&(n[r+1]=nl(n[r+1]))}function ef(t,e,n,r){let o=t[n+1],i=e===null,s=r?vn(o):Jn(o),a=!1;for(;s!==0&&(a===!1||i);){let l=t[s],u=t[s+1];m_(l,e)&&(a=!0,t[s+1]=r?nl(u):tl(u)),s=r?vn(u):Jn(u)}a&&(t[n+1]=r?tl(o):nl(o))}function m_(t,e){return t===null||e==null||(Array.isArray(t)?t[1]:t)===e?!0:Array.isArray(t)&&typeof e=="string"?Ar(t,e)>=0:!1}var ie={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function Pp(t){return t.substring(ie.key,ie.keyEnd)}function g_(t){return t.substring(ie.value,ie.valueEnd)}function y_(t){return kp(t),Fp(t,Xn(t,0,ie.textEnd))}function Fp(t,e){let n=ie.textEnd;return n===e?-1:(e=ie.keyEnd=D_(t,ie.key=e,n),Xn(t,e,n))}function v_(t){return kp(t),Rp(t,Xn(t,0,ie.textEnd))}function Rp(t,e){let n=ie.textEnd,r=ie.key=Xn(t,e,n);return n===r?-1:(r=ie.keyEnd=E_(t,r,n),r=tf(t,r,n,58),r=ie.value=Xn(t,r,n),r=ie.valueEnd=w_(t,r,n),tf(t,r,n,59))}function kp(t){ie.key=0,ie.keyEnd=0,ie.value=0,ie.valueEnd=0,ie.textEnd=t.length}function Xn(t,e,n){for(;e<n&&t.charCodeAt(e)<=32;)e++;return e}function D_(t,e,n){for(;e<n&&t.charCodeAt(e)>32;)e++;return e}function E_(t,e,n){let r;for(;e<n&&((r=t.charCodeAt(e))===45||r===95||(r&-33)>=65&&(r&-33)<=90||r>=48&&r<=57);)e++;return e}function tf(t,e,n,r){return e=Xn(t,e,n),e<n&&e++,e}function w_(t,e,n){let r=-1,o=-1,i=-1,s=e,a=s;for(;s<n;){let l=t.charCodeAt(s++);if(l===59)return a;l===34||l===39?a=s=nf(t,l,s,n):e===s-4&&i===85&&o===82&&r===76&&l===40?a=s=nf(t,41,s,n):l>32&&(a=s),i=o,o=r,r=l&-33}return a}function nf(t,e,n,r){let o=-1,i=n;for(;i<r;){let s=t.charCodeAt(i++);if(s==e&&o!==92)return i;s==92&&o===92?o=0:o=s}throw new Error}function __(t,e,n){let r=v(),o=wn();if(we(r,o,e)){let i=q(),s=Or();ki(i,s,r,t,e,r[Z],n,!1)}return __}function rl(t,e,n,r,o){let i=e.inputs,s=o?"class":"style";nu(t,n,i[s],s,r)}function Lp(t,e,n){return jp(t,e,n,!1),Lp}function I_(t,e){return jp(t,e,null,!0),I_}function UF(t){jr($p,b_,t,!1)}function b_(t,e){for(let n=v_(e);n>=0;n=Rp(e,n))$p(t,Pp(e),g_(e))}function zF(t){jr(x_,$i,t,!0)}function $i(t,e){for(let n=y_(e);n>=0;n=Fp(e,n))Dn(t,Pp(e),!0)}function jp(t,e,n,r){let o=v(),i=q(),s=nr(2);if(i.firstUpdatePass&&Bp(i,t,s,r),e!==te&&we(o,s,e)){let a=i.data[Oe()];Hp(i,a,o,o[Z],t,o[s+1]=O_(e,n),r,s)}}function jr(t,e,n,r){let o=q(),i=nr(2);o.firstUpdatePass&&Bp(o,null,i,r);let s=v();if(n!==te&&we(s,i,n)){let a=o.data[Oe()];if(Up(a,r)&&!Vp(o,i)){let l=r?a.classesWithoutHost:a.stylesWithoutHost;l!==null&&(n=la(l,n||"")),rl(o,a,s,n,r)}else A_(o,a,s,s[Z],s[i+1],s[i+1]=N_(t,e,n),r,i)}}function Vp(t,e){return e>=t.expandoStartIndex}function Bp(t,e,n,r){let o=t.data;if(o[n+1]===null){let i=o[Oe()],s=Vp(t,n);Up(i,r)&&e===null&&!s&&(e=!1),e=C_(o,i,e,r),h_(o,i,e,n,s,r)}}function C_(t,e,n,r){let o=Ll(t),i=r?e.residualClasses:e.residualStyles;if(o===null)(r?e.classBindings:e.styleBindings)===0&&(n=oa(null,t,e,n,r),n=Nr(n,e.attrs,r),i=null);else{let s=e.directiveStylingLast;if(s===-1||t[s]!==o)if(n=oa(o,t,e,n,r),i===null){let l=S_(t,e,r);l!==void 0&&Array.isArray(l)&&(l=oa(null,t,e,l[1],r),l=Nr(l,e.attrs,r),M_(t,e,r,l))}else i=T_(t,e,r)}return i!==void 0&&(r?e.residualClasses=i:e.residualStyles=i),n}function S_(t,e,n){let r=n?e.classBindings:e.styleBindings;if(Jn(r)!==0)return t[vn(r)]}function M_(t,e,n,r){let o=n?e.classBindings:e.styleBindings;t[vn(o)]=r}function T_(t,e,n){let r,o=e.directiveEnd;for(let i=1+e.directiveStylingLast;i<o;i++){let s=t[i].hostAttrs;r=Nr(r,s,n)}return Nr(r,e.attrs,n)}function oa(t,e,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=e[a],r=Nr(r,i.hostAttrs,o),i!==t);)a++;return t!==null&&(n.directiveStylingLast=a),r}function Nr(t,e,n){let r=n?1:2,o=-1;if(e!==null)for(let i=0;i<e.length;i++){let s=e[i];typeof s=="number"?o=s:o===r&&(Array.isArray(t)||(t=t===void 0?[]:["",t]),Dn(t,s,n?!0:e[++i]))}return t===void 0?null:t}function N_(t,e,n){if(n==null||n==="")return Ce;let r=[],o=_n(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)t(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&t(r,i,o[i]);else typeof o=="string"&&e(r,o);return r}function $p(t,e,n){Dn(t,e,_n(n))}function x_(t,e,n){let r=String(e);r!==""&&!r.includes(" ")&&Dn(t,r,n)}function A_(t,e,n,r,o,i,s,a){o===te&&(o=Ce);let l=0,u=0,c=0<o.length?o[0]:null,d=0<i.length?i[0]:null;for(;c!==null||d!==null;){let h=l<o.length?o[l+1]:void 0,f=u<i.length?i[u+1]:void 0,p=null,m;c===d?(l+=2,u+=2,h!==f&&(p=d,m=f)):d===null||c!==null&&c<d?(l+=2,p=c):(u+=2,p=d,m=f),p!==null&&Hp(t,e,n,r,p,m,s,a),c=l<o.length?o[l]:null,d=u<i.length?i[u]:null}}function Hp(t,e,n,r,o,i,s,a){if(!(e.type&3))return;let l=t.data,u=l[a+1],c=f_(u)?rf(l,e,n,o,Jn(u),s):void 0;if(!hi(c)){hi(i)||c_(u)&&(i=rf(l,null,n,o,a,s));let d=Zf(Oe(),n);DE(r,s,d,o,i)}}function rf(t,e,n,r,o,i){let s=e===null,a;for(;o>0;){let l=t[o],u=Array.isArray(l),c=u?l[1]:l,d=c===null,h=n[o+1];h===te&&(h=d?Ce:void 0);let f=d?Zs(h,r):c===r?h:void 0;if(u&&!hi(f)&&(f=Zs(l,r)),hi(f)&&(a=f,s))return a;let p=t[o+1];o=s?vn(p):Jn(p)}if(e!==null){let l=i?e.residualClasses:e.residualStyles;l!=null&&(a=Zs(l,r))}return a}function hi(t){return t!==void 0}function O_(t,e){return t==null||t===""||(typeof e=="string"?t=t+e:typeof t=="object"&&(t=Ee(_n(t)))),t}function Up(t,e){return(t.flags&(e?8:16))!==0}function qF(t,e,n){let r=v(),o=cu(r,t,e,n);jr(Dn,$i,o,!0)}function GF(t,e,n,r,o){let i=v(),s=Ap(i,t,e,n,r,o);jr(Dn,$i,s,!0)}function WF(t,e,n,r,o,i,s,a,l){let u=v(),c=Op(u,t,e,n,r,o,i,s,a,l);jr(Dn,$i,c,!0)}var ol=class{destroy(e){}updateValue(e,n){}swap(e,n){let r=Math.min(e,n),o=Math.max(e,n),i=this.detach(o);if(o-r>1){let s=this.detach(r);this.attach(r,i),this.attach(o,s)}else this.attach(r,i)}move(e,n){this.attach(n,this.detach(e))}};function ia(t,e,n,r,o){return t===n&&Object.is(e,r)?1:Object.is(o(t,e),o(n,r))?-1:0}function P_(t,e,n){let r,o,i=0,s=t.length-1,a=void 0;if(Array.isArray(e)){let l=e.length-1;for(;i<=s&&i<=l;){let u=t.at(i),c=e[i],d=ia(i,u,i,c,n);if(d!==0){d<0&&t.updateValue(i,c),i++;continue}let h=t.at(s),f=e[l],p=ia(s,h,l,f,n);if(p!==0){p<0&&t.updateValue(s,f),s--,l--;continue}let m=n(i,u),D=n(s,h),E=n(i,c);if(Object.is(E,D)){let T=n(l,f);Object.is(T,m)?(t.swap(i,s),t.updateValue(s,f),l--,s--):t.move(s,i),t.updateValue(i,c),i++;continue}if(r??=new pi,o??=sf(t,i,s,n),il(t,r,i,E))t.updateValue(i,c),i++,s++;else if(o.has(E))r.set(m,t.detach(i)),s--;else{let T=t.create(i,e[i]);t.attach(i,T),i++,s++}}for(;i<=l;)of(t,r,n,i,e[i]),i++}else if(e!=null){let l=e[Symbol.iterator](),u=l.next();for(;!u.done&&i<=s;){let c=t.at(i),d=u.value,h=ia(i,c,i,d,n);if(h!==0)h<0&&t.updateValue(i,d),i++,u=l.next();else{r??=new pi,o??=sf(t,i,s,n);let f=n(i,d);if(il(t,r,i,f))t.updateValue(i,d),i++,s++,u=l.next();else if(!o.has(f))t.attach(i,t.create(i,d)),i++,s++,u=l.next();else{let p=n(i,c);r.set(p,t.detach(i)),s--}}}for(;!u.done;)of(t,r,n,t.length,u.value),u=l.next()}for(;i<=s;)t.destroy(t.detach(s--));r?.forEach(l=>{t.destroy(l)})}function il(t,e,n,r){return e!==void 0&&e.has(r)?(t.attach(n,e.get(r)),e.delete(r),!0):!1}function of(t,e,n,r,o){if(il(t,e,r,n(r,o)))t.updateValue(r,o);else{let i=t.create(r,o);t.attach(r,i)}}function sf(t,e,n,r){let o=new Set;for(let i=e;i<=n;i++)o.add(r(i,t.at(i)));return o}var pi=class{constructor(){this.kvMap=new Map,this._vMap=void 0}has(e){return this.kvMap.has(e)}delete(e){if(!this.has(e))return!1;let n=this.kvMap.get(e);return this._vMap!==void 0&&this._vMap.has(n)?(this.kvMap.set(e,this._vMap.get(n)),this._vMap.delete(n)):this.kvMap.delete(e),!0}get(e){return this.kvMap.get(e)}set(e,n){if(this.kvMap.has(e)){let r=this.kvMap.get(e);this._vMap===void 0&&(this._vMap=new Map);let o=this._vMap;for(;o.has(r);)r=o.get(r);o.set(r,n)}else this.kvMap.set(e,n)}forEach(e){for(let[n,r]of this.kvMap)if(e(r,n),this._vMap!==void 0){let o=this._vMap;for(;o.has(r);)r=o.get(r),e(r,n)}}};function QF(t,e){Qe("NgControlFlow");let n=v(),r=wn(),o=n[r]!==te?n[r]:-1,i=o!==-1?mi(n,ee+o):void 0,s=0;if(we(n,r,t)){let a=O(null);try{if(i!==void 0&&hp(i,s),t!==-1){let l=ee+t,u=mi(n,l),c=ul(n[M],l),d=Qn(u,c.tView.ssrId),h=kr(n,c,e,{dehydratedView:d});Lr(u,h,s,Wn(c,d))}}finally{O(a)}}else if(i!==void 0){let a=fp(i,s);a!==void 0&&(a[pe]=e)}}var sl=class{constructor(e,n,r){this.lContainer=e,this.$implicit=n,this.$index=r}get $count(){return this.lContainer.length-me}};function ZF(t){return t}function KF(t,e){return e}var al=class{constructor(e,n,r){this.hasEmptyBlock=e,this.trackByFn=n,this.liveCollection=r}};function YF(t,e,n,r,o,i,s,a,l,u,c,d,h){Qe("NgControlFlow");let f=v(),p=q(),m=l!==void 0,D=v(),E=a?s.bind(D[Ae][pe]):s,T=new al(m,E);D[ee+t]=T,fi(f,p,t+1,e,n,r,o,Ht(p.consts,i)),m&&fi(f,p,t+2,l,u,c,d,Ht(p.consts,h))}var ll=class extends ol{constructor(e,n,r){super(),this.lContainer=e,this.hostLView=n,this.templateTNode=r,this.operationsCounter=void 0,this.needsIndexUpdate=!1}get length(){return this.lContainer.length-me}at(e){return this.getLView(e)[pe].$implicit}attach(e,n){let r=n[zn];this.needsIndexUpdate||=e!==this.length,Lr(this.lContainer,n,e,Wn(this.templateTNode,r))}detach(e){return this.needsIndexUpdate||=e!==this.length-1,F_(this.lContainer,e)}create(e,n){let r=Qn(this.lContainer,this.templateTNode.tView.ssrId),o=kr(this.hostLView,this.templateTNode,new sl(this.lContainer,n,e),{dehydratedView:r});return this.operationsCounter?.recordCreate(),o}destroy(e){Oi(e[M],e),this.operationsCounter?.recordDestroy()}updateValue(e,n){this.getLView(e)[pe].$implicit=n}reset(){this.needsIndexUpdate=!1,this.operationsCounter?.reset()}updateIndexes(){if(this.needsIndexUpdate)for(let e=0;e<this.length;e++)this.getLView(e)[pe].$index=e}getLView(e){return R_(this.lContainer,e)}};function JF(t){let e=O(null),n=Oe();try{let r=v(),o=r[M],i=r[n],s=n+1,a=mi(r,s);if(i.liveCollection===void 0){let u=ul(o,s);i.liveCollection=new ll(a,r,u)}else i.liveCollection.reset();let l=i.liveCollection;if(P_(l,t,i.trackByFn),l.updateIndexes(),i.hasEmptyBlock){let u=wn(),c=l.length===0;if(we(r,u,c)){let d=n+2,h=mi(r,d);if(c){let f=ul(o,d),p=Qn(h,f.tView.ssrId),m=kr(r,f,void 0,{dehydratedView:p});Lr(h,m,0,Wn(f,p))}else hp(h,0)}}}finally{O(e)}}function mi(t,e){return t[e]}function F_(t,e){return Tr(t,e)}function R_(t,e){return fp(t,e)}function ul(t,e){return Pl(t,e)}function k_(t,e,n,r,o,i){let s=e.consts,a=Ht(s,o),l=In(e,t,2,r,a);return tu(e,n,l,Ht(s,i)),l.attrs!==null&&li(l,l.attrs,!1),l.mergedAttrs!==null&&li(l,l.mergedAttrs,!0),e.queries!==null&&e.queries.elementStart(e,l),l}function zp(t,e,n,r){let o=v(),i=q(),s=ee+t,a=o[Z],l=i.firstCreatePass?k_(s,i,o,e,n,r):i.data[s],u=j_(i,o,l,a,e,t);o[s]=u;let c=Ii(l);return Gt(l,!0),Jh(a,u,l),!n_(l)&&Si()&&Pi(i,o,u,l),Ov()===0&&Ut(u,o),Pv(),c&&(Jl(i,o,l),Yl(i,l,o)),r!==null&&Xl(o,l),zp}function qp(){let t=le();Rl()?kl():(t=t.parent,Gt(t,!1));let e=t;Rv(e)&&kv(),Fv();let n=q();return n.firstCreatePass&&(Ti(n,t),Al(t)&&n.queries.elementEnd(t)),e.classesWithoutHost!=null&&Zv(e)&&rl(n,e,v(),e.classesWithoutHost,!0),e.stylesWithoutHost!=null&&Kv(e)&&rl(n,e,v(),e.stylesWithoutHost,!1),qp}function L_(t,e,n,r){return zp(t,e,n,r),qp(),L_}var j_=(t,e,n,r,o,i)=>(Mi(!0),Hh(r,o,qv()));function V_(t,e,n,r,o){let i=e.consts,s=Ht(i,r),a=In(e,t,8,"ng-container",s);s!==null&&li(a,s,!0);let l=Ht(i,o);return tu(e,n,a,l),e.queries!==null&&e.queries.elementStart(e,a),a}function Gp(t,e,n){let r=v(),o=q(),i=t+ee,s=o.firstCreatePass?V_(i,o,r,e,n):o.data[i];Gt(s,!0);let a=$_(o,r,s,t);return r[i]=a,Si()&&Pi(o,r,a,s),Ut(a,r),Ii(s)&&(Jl(o,r,s),Yl(o,s,r)),n!=null&&Xl(r,s),Gp}function Wp(){let t=le(),e=q();return Rl()?kl():(t=t.parent,Gt(t,!1)),e.firstCreatePass&&(Ti(e,t),Al(t)&&e.queries.elementEnd(t)),Wp}function B_(t,e,n){return Gp(t,e,n),Wp(),B_}var $_=(t,e,n,r)=>(Mi(!0),iE(e[Z],""));function XF(){return v()}function H_(t,e,n){let r=v(),o=wn();if(we(r,o,e)){let i=q(),s=Or(),a=Ll(i.data),l=cp(a,s,r);ki(i,s,r,t,e,l,n,!0)}return H_}var sn=void 0;function U_(t){let e=t,n=Math.floor(Math.abs(t)),r=t.toString().replace(/^[^.]*\.?/,"").length;return n===1&&r===0?1:5}var z_=["en",[["a","p"],["AM","PM"],sn],[["AM","PM"],sn,sn],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],sn,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],sn,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",sn,"{1} 'at' {0}",sn],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",U_],Er={};function Qp(t,e,n){typeof e!="string"&&(n=e,e=t[Q.LocaleId]),e=e.toLowerCase().replace(/_/g,"-"),Er[e]=t,n&&(Er[e][Q.ExtraData]=n)}function Te(t){let e=q_(t),n=af(e);if(n)return n;let r=e.split("-")[0];if(n=af(r),n)return n;if(r==="en")return z_;throw new g(701,!1)}function af(t){return t in Er||(Er[t]=Lt.ng&&Lt.ng.common&&Lt.ng.common.locales&&Lt.ng.common.locales[t]),Er[t]}var Q=function(t){return t[t.LocaleId=0]="LocaleId",t[t.DayPeriodsFormat=1]="DayPeriodsFormat",t[t.DayPeriodsStandalone=2]="DayPeriodsStandalone",t[t.DaysFormat=3]="DaysFormat",t[t.DaysStandalone=4]="DaysStandalone",t[t.MonthsFormat=5]="MonthsFormat",t[t.MonthsStandalone=6]="MonthsStandalone",t[t.Eras=7]="Eras",t[t.FirstDayOfWeek=8]="FirstDayOfWeek",t[t.WeekendRange=9]="WeekendRange",t[t.DateFormat=10]="DateFormat",t[t.TimeFormat=11]="TimeFormat",t[t.DateTimeFormat=12]="DateTimeFormat",t[t.NumberSymbols=13]="NumberSymbols",t[t.NumberFormats=14]="NumberFormats",t[t.CurrencyCode=15]="CurrencyCode",t[t.CurrencySymbol=16]="CurrencySymbol",t[t.CurrencyName=17]="CurrencyName",t[t.Currencies=18]="Currencies",t[t.Directionality=19]="Directionality",t[t.PluralCase=20]="PluralCase",t[t.ExtraData=21]="ExtraData",t}(Q||{});function q_(t){return t.toLowerCase().replace(/_/g,"-")}var gi="en-US",G_="USD";var W_=gi;function Q_(t){typeof t=="string"&&(W_=t.toLowerCase().replace(/_/g,"-"))}var Z_=(t,e,n)=>{};function K_(t,e,n,r){let o=v(),i=q(),s=le();return du(i,o,o[Z],s,t,e,r),K_}function Y_(t,e){let n=le(),r=v(),o=q(),i=Ll(o.data),s=cp(i,n,r);return du(o,r,s,n,t,e),Y_}function J_(t,e,n,r){let o=t.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=e[Zo],l=o[i+2];return a.length>l?a[l]:null}typeof s=="string"&&(i+=2)}return null}function du(t,e,n,r,o,i,s){let a=Ii(r),u=t.firstCreatePass&&up(t),c=e[pe],d=lp(e),h=!0;if(r.type&3||s){let m=Ve(r,e),D=s?s(m):m,E=d.length,T=s?V=>s(it(V[r.index])):r.index,k=null;if(!s&&a&&(k=J_(t,e,o,r.index)),k!==null){let V=k.__ngLastListenerFn__||k;V.__ngNextListenerFn__=i,k.__ngLastListenerFn__=i,h=!1}else{i=uf(r,e,c,i),Z_(m,o,i);let V=n.listen(D,o,i);d.push(i,V),u&&u.push(o,T,E,E+1)}}else i=uf(r,e,c,i);let f=r.outputs,p;if(h&&f!==null&&(p=f[o])){let m=p.length;if(m)for(let D=0;D<m;D+=2){let E=p[D],T=p[D+1],de=e[E][T].subscribe(i),Y=d.length;d.push(i,de),u&&u.push(o,r.index,Y,-(Y+1))}}}function lf(t,e,n,r){let o=O(null);try{return nt(6,e,n),n(r)!==!1}catch(i){return dp(t,i),!1}finally{nt(7,e,n),O(o)}}function uf(t,e,n,r){return function o(i){if(i===Function)return r;let s=t.componentOffset>-1?qt(t.index,e):e;ou(s,5);let a=lf(e,n,r,i),l=o.__ngNextListenerFn__;for(;l;)a=lf(e,n,l,i)&&a,l=l.__ngNextListenerFn__;return a}}function eR(t=1){return Uv(t)}function X_(t,e){let n=null,r=Jy(t);for(let o=0;o<e.length;o++){let i=e[o];if(i==="*"){n=o;continue}if(r===null?Af(t,i,!0):tv(r,i))return o}return n}function tR(t){let e=v()[Ae][Me];if(!e.projection){let n=t?t.length:1,r=e.projection=Uy(n,null),o=r.slice(),i=e.child;for(;i!==null;){if(i.type!==128){let s=t?X_(i,t):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function nR(t,e=0,n,r,o,i){let s=v(),a=q(),l=r?t+1:null;l!==null&&fi(s,a,l,r,o,i,null,n);let u=In(a,ee+t,16,null,n||null);u.projection===null&&(u.projection=e),kl();let d=!s[zn]||th();s[Ae][Me].projection[u.projection]===null&&l!==null?eI(s,a,l):d&&(u.flags&32)!==32&&yE(a,s,u)}function eI(t,e,n){let r=ee+n,o=e.data[r],i=t[r],s=Qn(i,o.tView.ssrId),a=kr(t,o,void 0,{dehydratedView:s});Lr(i,a,0,Wn(o,s))}function tI(t,e,n){return Zp(t,"",e,"",n),tI}function Zp(t,e,n,r,o){let i=v(),s=cu(i,e,n,r);if(s!==te){let a=q(),l=Or();ki(a,l,i,t,s,i[Z],o,!1)}return Zp}function rR(t,e,n,r){Vw(t,e,n,r)}function oR(t,e,n){jw(t,e,n)}function iR(t){let e=v(),n=q(),r=ih();jl(r+1);let o=au(n,r);if(t.dirty&&Tv(e)===((o.metadata.flags&2)===2)){if(o.matches===null)t.reset([]);else{let i=Hw(e,r);t.reset(i,vD),t.notifyOnChanges()}return!0}return!1}function sR(){return Lw(v(),ih())}function fu(t,e,n,r){n>=t.data.length&&(t.data[n]=null,t.blueprint[n]=null),e[n]=r}function aR(t){let e=rh();return tr(e,ee+t)}function lR(t,e=""){let n=v(),r=q(),o=t+ee,i=r.firstCreatePass?In(r,o,1,e,null):r.data[o],s=nI(r,n,i,e,t);n[o]=s,Si()&&Pi(r,n,s,i),Gt(i,!1)}var nI=(t,e,n,r,o)=>(Mi(!0),rE(e[Z],r));function rI(t){return Kp("",t,""),rI}function Kp(t,e,n){let r=v(),o=cu(r,t,e,n);return o!==te&&Rr(r,Oe(),o),Kp}function oI(t,e,n,r,o){let i=v(),s=Ap(i,t,e,n,r,o);return s!==te&&Rr(i,Oe(),s),oI}function iI(t,e,n,r,o,i,s){let a=v(),l=l_(a,t,e,n,r,o,i,s);return l!==te&&Rr(a,Oe(),l),iI}function sI(t,e,n,r,o,i,s,a,l){let u=v(),c=Op(u,t,e,n,r,o,i,s,a,l);return c!==te&&Rr(u,Oe(),c),sI}function aI(t,e,n,r,o,i,s,a,l,u,c){let d=v(),h=u_(d,t,e,n,r,o,i,s,a,l,u,c);return h!==te&&Rr(d,Oe(),h),aI}function lI(t,e,n){Tp(e)&&(e=e());let r=v(),o=wn();if(we(r,o,e)){let i=q(),s=Or();ki(i,s,r,t,e,r[Z],n,!1)}return lI}function uR(t,e){let n=Tp(t);return n&&t.set(e),n}function uI(t,e){let n=v(),r=q(),o=le();return du(r,n,n[Z],o,t,e),uI}var Yp={};function cI(t){let e=q(),n=v(),r=t+ee,o=In(e,r,128,null,null);return Gt(o,!1),fu(e,n,r,Yp),cI}function cR(t){Qe("NgLet");let e=q(),n=v(),r=Oe();return fu(e,n,r,t),t}function dR(t){let e=rh(),n=tr(e,ee+t);if(n===Yp)throw new g(314,!1);return n}function dI(t,e,n){let r=q();if(r.firstCreatePass){let o=vt(t);cl(n,r.data,r.blueprint,o,!0),cl(e,r.data,r.blueprint,o,!1)}}function cl(t,e,n,r,o){if(t=ce(t),Array.isArray(t))for(let i=0;i<t.length;i++)cl(t[i],e,n,r,o);else{let i=q(),s=v(),a=le(),l=Un(t)?t:ce(t.provide),u=$f(t),c=a.providerIndexes&1048575,d=a.directiveStart,h=a.providerIndexes>>20;if(Un(t)||!t.multi){let f=new hn(u,o,H),p=aa(l,e,o?c:c+h,d);p===-1?(_a(ti(a,s),i,l),sa(i,t,e.length),e.push(l),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(f),s.push(f)):(n[p]=f,s[p]=f)}else{let f=aa(l,e,c+h,d),p=aa(l,e,c,c+h),m=f>=0&&n[f],D=p>=0&&n[p];if(o&&!D||!o&&!m){_a(ti(a,s),i,l);let E=pI(o?hI:fI,n.length,o,r,u);!o&&D&&(n[p].providerFactory=E),sa(i,t,e.length,0),e.push(l),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(E),s.push(E)}else{let E=Jp(n[o?p:f],u,!o&&r);sa(i,t,f>-1?f:p,E)}!o&&r&&D&&n[p].componentProviders++}}}function sa(t,e,n,r){let o=Un(e),i=pv(e);if(o||i){let l=(i?ce(e.useClass):e).prototype.ngOnDestroy;if(l){let u=t.destroyHooks||(t.destroyHooks=[]);if(!o&&e.multi){let c=u.indexOf(n);c===-1?u.push(n,[r,l]):u[c+1].push(r,l)}else u.push(n,l)}}}function Jp(t,e,n){return n&&t.componentProviders++,t.multi.push(e)-1}function aa(t,e,n,r){for(let o=n;o<r;o++)if(e[o]===t)return o;return-1}function fI(t,e,n,r){return dl(this.multi,[])}function hI(t,e,n,r){let o=this.multi,i;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=pn(n,n[M],this.providerFactory.index,r);i=a.slice(0,s),dl(o,i);for(let l=s;l<a.length;l++)i.push(a[l])}else i=[],dl(o,i);return i}function dl(t,e){for(let n=0;n<t.length;n++){let r=t[n];e.push(r())}return e}function pI(t,e,n,r,o){let i=new hn(t,n,H);return i.multi=[],i.index=e,i.componentProviders=0,Jp(i,o,r&&!n),i}function fR(t,e=[]){return n=>{n.providersResolver=(r,o)=>dI(r,o?o(t):t,e)}}var mI=(()=>{class t{constructor(n){this._injector=n,this.cachedInjectors=new Map}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=Lf(!1,n.type),o=r.length>0?e_([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static{this.\u0275prov=z({token:t,providedIn:"environment",factory:()=>new t(ge($t))})}}return t})();function hR(t){Qe("NgStandalone"),t.getStandaloneInjector=e=>e.get(mI).getOrCreateStandaloneInjector(t)}function pR(t,e,n){let r=Be()+t,o=v();return o[r]===te?Qt(o,r,n?e.call(n):e()):Bi(o,r)}function mR(t,e,n,r){return Xp(v(),Be(),t,e,n,r)}function gR(t,e,n,r,o){return em(v(),Be(),t,e,n,r,o)}function yR(t,e,n,r,o,i){return tm(v(),Be(),t,e,n,r,o,i)}function vR(t,e,n,r,o,i,s){return nm(v(),Be(),t,e,n,r,o,i,s)}function DR(t,e,n,r,o,i,s,a){let l=Be()+t,u=v(),c=or(u,l,n,r,o,i);return we(u,l+4,s)||c?Qt(u,l+5,a?e.call(a,n,r,o,i,s):e(n,r,o,i,s)):Bi(u,l+5)}function ER(t,e,n,r,o,i,s,a,l){let u=Be()+t,c=v(),d=or(c,u,n,r,o,i);return Yn(c,u+4,s,a)||d?Qt(c,u+6,l?e.call(l,n,r,o,i,s,a):e(n,r,o,i,s,a)):Bi(c,u+6)}function wR(t,e,n,r,o,i,s,a,l,u){let c=Be()+t,d=v(),h=or(d,c,n,r,o,i);return uu(d,c+4,s,a,l)||h?Qt(d,c+7,u?e.call(u,n,r,o,i,s,a,l):e(n,r,o,i,s,a,l)):Bi(d,c+7)}function Hi(t,e){let n=t[e];return n===te?void 0:n}function Xp(t,e,n,r,o,i){let s=e+n;return we(t,s,o)?Qt(t,s+1,i?r.call(i,o):r(o)):Hi(t,s+1)}function em(t,e,n,r,o,i,s){let a=e+n;return Yn(t,a,o,i)?Qt(t,a+2,s?r.call(s,o,i):r(o,i)):Hi(t,a+2)}function tm(t,e,n,r,o,i,s,a){let l=e+n;return uu(t,l,o,i,s)?Qt(t,l+3,a?r.call(a,o,i,s):r(o,i,s)):Hi(t,l+3)}function nm(t,e,n,r,o,i,s,a,l){let u=e+n;return or(t,u,o,i,s,a)?Qt(t,u+4,l?r.call(l,o,i,s,a):r(o,i,s,a)):Hi(t,u+4)}function _R(t,e){let n=q(),r,o=t+ee;n.firstCreatePass?(r=gI(e,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=ln(r.type,!0)),s,a=be(H);try{let l=ei(!1),u=i();return ei(l),fu(n,v(),o,u),u}finally{be(a)}}function gI(t,e){if(e)for(let n=e.length-1;n>=0;n--){let r=e[n];if(t===r.name)return r}}function IR(t,e,n){let r=t+ee,o=v(),i=tr(o,r);return Ui(o,r)?Xp(o,Be(),e,i.transform,n,i):i.transform(n)}function bR(t,e,n,r){let o=t+ee,i=v(),s=tr(i,o);return Ui(i,o)?em(i,Be(),e,s.transform,n,r,s):s.transform(n,r)}function CR(t,e,n,r,o){let i=t+ee,s=v(),a=tr(s,i);return Ui(s,i)?tm(s,Be(),e,a.transform,n,r,o,a):a.transform(n,r,o)}function SR(t,e,n,r,o,i){let s=t+ee,a=v(),l=tr(a,s);return Ui(a,s)?nm(a,Be(),e,l.transform,n,r,o,i,l):l.transform(n,r,o,i)}function Ui(t,e){return t[M].data[e].pure}function MR(t,e){return ji(t,e)}var TR=(()=>{class t{log(n){console.log(n)}warn(n){console.warn(n)}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=z({token:t,factory:t.\u0275fac,providedIn:"platform"})}}return t})();var yI=new j("");function zi(t){return!!t&&typeof t.then=="function"}function hu(t){return!!t&&typeof t.subscribe=="function"}var vI=new j(""),rm=(()=>{class t{constructor(){this.initialized=!1,this.done=!1,this.donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r}),this.appInits=A(vI,{optional:!0})??[]}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=o();if(zi(i))n.push(i);else if(hu(i)){let s=new Promise((a,l)=>{i.subscribe({complete:a,error:l})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=z({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),DI=new j("");function EI(){Ac(()=>{throw new g(600,!1)})}function wI(t){return t.isBoundToModule}var _I=10;function II(t,e,n){try{let r=n();return zi(r)?r.catch(o=>{throw e.runOutsideAngular(()=>t.handleError(o)),o}):r}catch(r){throw e.runOutsideAngular(()=>t.handleError(r)),r}}var qi=(()=>{class t{constructor(){this._bootstrapListeners=[],this._runningTick=!1,this._destroyed=!1,this._destroyListeners=[],this._views=[],this.internalErrorHandler=A(gD),this.afterRenderEffectManager=A(su),this.zonelessEnabled=A(iu),this.externalTestViews=new Set,this.beforeRender=new Ie,this.afterTick=new Ie,this.componentTypes=[],this.components=[],this.isStable=A(Pr).hasPendingTasks.pipe(tt(n=>!n)),this._injector=A($t)}get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}get injector(){return this._injector}bootstrap(n,r){let o=n instanceof si;if(!this._injector.get(rm).done){let h=!o&&av(n),f=!1;throw new g(405,f)}let s;o?s=n:s=this._injector.get(Kn).resolveComponentFactory(n),this.componentTypes.push(s.componentType);let a=wI(s)?void 0:this._injector.get(zt),l=r||s.selector,u=s.create(Dt.NULL,[],l,a),c=u.location.nativeElement,d=u.injector.get(yI,null);return d?.registerApplication(c),u.onDestroy(()=>{this.detachView(u.hostView),Ho(this.components,u),d?.unregisterApplication(c)}),this._loadComponent(u),u}tick(){this._tick(!0)}_tick(n){if(this._runningTick)throw new g(101,!1);let r=O(null);try{this._runningTick=!0,this.detectChangesInAttachedViews(n)}catch(o){this.internalErrorHandler(o)}finally{this._runningTick=!1,O(r),this.afterTick.next()}}detectChangesInAttachedViews(n){let r=null;this._injector.destroyed||(r=this._injector.get(ai,null,{optional:!0}));let o=0,i=this.afterRenderEffectManager;for(;o<_I;){let s=o===0;if(n||!s){this.beforeRender.next(s);for(let{_lView:a,notifyErrorHandler:l}of this._views)bI(a,l,s,this.zonelessEnabled)}else r?.begin?.(),r?.end?.();if(o++,i.executeInternalCallbacks(),!this.allViews.some(({_lView:a})=>Sr(a))&&(i.execute(),!this.allViews.some(({_lView:a})=>Sr(a))))break}}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;Ho(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n);let r=this._injector.get(DI,[]);[...this._bootstrapListeners,...r].forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._bootstrapListeners=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>Ho(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new g(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}warnIfDestroyed(){}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=z({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();function Ho(t,e){let n=t.indexOf(e);n>-1&&t.splice(n,1)}function bI(t,e,n,r){if(!n&&!Sr(t))return;gp(t,e,n&&!r?0:1)}var fl=class{constructor(e,n){this.ngModuleFactory=e,this.componentFactories=n}},NR=(()=>{class t{compileModuleSync(n){return new el(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=Ff(n),i=Bh(o.declarations).reduce((s,a)=>{let l=gt(a);return l&&s.push(new yn(l)),s},[]);return new fl(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=z({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();var CI=(()=>{class t{constructor(){this.zone=A(Se),this.changeDetectionScheduler=A(Zn),this.applicationRef=A(qi)}initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=z({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),SI=new j("",{factory:()=>!1});function om({ngZoneFactory:t,ignoreChangesOutsideZone:e}){return t??=()=>new Se(im()),[{provide:Se,useFactory:t},{provide:Wo,multi:!0,useFactory:()=>{let n=A(CI,{optional:!0});return()=>n.initialize()}},{provide:Wo,multi:!0,useFactory:()=>{let n=A(MI);return()=>{n.initialize()}}},e===!0?{provide:Ep,useValue:!0}:[]]}function xR(t){let e=t?.ignoreChangesOutsideZone,n=om({ngZoneFactory:()=>{let r=im(t);return r.shouldCoalesceEventChangeDetection&&Qe("NgZone_CoalesceEvent"),new Se(r)},ignoreChangesOutsideZone:e});return uv([{provide:SI,useValue:!0},{provide:iu,useValue:!1},n])}function im(t){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:t?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:t?.runCoalescing??!1}}var MI=(()=>{class t{constructor(){this.subscription=new X,this.initialized=!1,this.zone=A(Se),this.pendingTasks=A(Pr)}initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{Se.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{Se.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=z({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();var TI=(()=>{class t{constructor(){this.appRef=A(qi),this.taskService=A(Pr),this.ngZone=A(Se),this.zonelessEnabled=A(iu),this.disableScheduling=A(Ep,{optional:!0})??!1,this.zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run,this.schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}],this.subscriptions=new X,this.angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(ri):null,this.cancelScheduledCallback=null,this.shouldRefreshViews=!1,this.useMicrotaskScheduler=!1,this.runningTick=!1,this.pendingRenderTaskId=null,this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Ma||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;switch(n){case 3:case 2:case 0:case 4:case 5:case 1:{this.shouldRefreshViews=!0;break}case 8:case 7:case 6:case 9:default:}if(!this.shouldScheduleTick())return;let r=this.useMicrotaskScheduler?kd:Ih;this.pendingRenderTaskId=this.taskService.add(),this.zoneIsDefined?Zone.root.run(()=>{this.cancelScheduledCallback=r(()=>{this.tick(this.shouldRefreshViews)})}):this.cancelScheduledCallback=r(()=>{this.tick(this.shouldRefreshViews)})}shouldScheduleTick(){return!(this.disableScheduling||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(ri+this.angularZoneId))}tick(n){if(this.runningTick||this.appRef.destroyed)return;let r=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick(n)},void 0,this.schedulerTickApplyArgs)}catch(o){throw this.taskService.remove(r),o}finally{this.cleanup()}this.useMicrotaskScheduler=!0,kd(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(r)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.shouldRefreshViews=!1,this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=z({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();function NI(){return typeof $localize<"u"&&$localize.locale||gi}var ir=new j("",{providedIn:"root",factory:()=>A(ir,F.Optional|F.SkipSelf)||NI()}),sm=new j("",{providedIn:"root",factory:()=>G_});var am=new j("");function ko(t){return!!t.platformInjector}function xI(t){let e=ko(t)?t.r3Injector:t.moduleRef.injector,n=e.get(Se);return n.run(()=>{ko(t)?t.r3Injector.resolveInjectorInitializers():t.moduleRef.resolveInjectorInitializers();let r=e.get(mn,null),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:i=>{r.handleError(i)}})}),ko(t)){let i=()=>e.destroy(),s=t.platformInjector.get(am);s.add(i),e.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else t.moduleRef.onDestroy(()=>{Ho(t.allPlatformModules,t.moduleRef),o.unsubscribe()});return II(r,n,()=>{let i=e.get(rm);return i.runInitializers(),i.donePromise.then(()=>{let s=e.get(ir,gi);if(Q_(s||gi),ko(t)){let a=e.get(qi);return t.rootComponent!==void 0&&a.bootstrap(t.rootComponent),a}else return AI(t.moduleRef,t.allPlatformModules),t.moduleRef})})})}function AI(t,e){let n=t.injector.get(qi);if(t._bootstrapComponents.length>0)t._bootstrapComponents.forEach(r=>n.bootstrap(r));else if(t.instance.ngDoBootstrap)t.instance.ngDoBootstrap(n);else throw new g(-403,!1);e.push(t)}var Uo=null;function OI(t=[],e){return Dt.create({name:e,providers:[{provide:Bf,useValue:"platform"},{provide:am,useValue:new Set([()=>Uo=null])},...t]})}function PI(t=[]){if(Uo)return Uo;let e=OI(t);return Uo=e,EI(),FI(e),e}function FI(t){t.get(CD,null)?.forEach(n=>n())}function AR(){return!1}var Gi=(()=>{class t{static{this.__NG_ELEMENT_ID__=RI}}return t})();function RI(t){return kI(le(),v(),(t&16)===16)}function kI(t,e,n){if(_i(t)&&!n){let r=qt(t.index,e);return new gn(r,r)}else if(t.type&175){let r=e[Ae];return new gn(r,e)}return null}var hl=class{constructor(){}supports(e){return xp(e)}create(e){return new pl(e)}},LI=(t,e)=>e,pl=class{constructor(e){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=e||LI}forEachItem(e){let n;for(n=this._itHead;n!==null;n=n._next)e(n)}forEachOperation(e){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<cf(r,o,i)?n:r,a=cf(s,o,i),l=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let u=a-o,c=l-o;if(u!=c){for(let h=0;h<u;h++){let f=h<i.length?i[h]:i[h]=0,p=f+h;c<=p&&p<u&&(i[h]=f+1)}let d=s.previousIndex;i[d]=c-u}}a!==l&&e(s,a,l)}}forEachPreviousItem(e){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)e(n)}forEachAddedItem(e){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)e(n)}forEachMovedItem(e){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)e(n)}forEachRemovedItem(e){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)e(n)}forEachIdentityChange(e){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)e(n)}diff(e){if(e==null&&(e=[]),!xp(e))throw new g(900,!1);return this.check(e)?this:null}onDestroy(){}check(e){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(e)){this.length=e.length;for(let a=0;a<this.length;a++)i=e[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,t_(e,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=e,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let e;for(e=this._previousItHead=this._itHead;e!==null;e=e._next)e._nextPrevious=e._next;for(e=this._additionsHead;e!==null;e=e._nextAdded)e.previousIndex=e.currentIndex;for(this._additionsHead=this._additionsTail=null,e=this._movesHead;e!==null;e=e._nextMoved)e.previousIndex=e.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(e,n,r,o){let i;return e===null?i=this._itTail:(i=e._prev,this._remove(e)),e=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),e!==null?(Object.is(e.item,n)||this._addIdentityChange(e,n),this._reinsertAfter(e,i,o)):(e=this._linkedRecords===null?null:this._linkedRecords.get(r,o),e!==null?(Object.is(e.item,n)||this._addIdentityChange(e,n),this._moveAfter(e,i,o)):e=this._addAfter(new ml(n,r),i,o)),e}_verifyReinsertion(e,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?e=this._reinsertAfter(i,e._prev,o):e.currentIndex!=o&&(e.currentIndex=o,this._addToMoves(e,o)),e}_truncate(e){for(;e!==null;){let n=e._next;this._addToRemovals(this._unlink(e)),e=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(e,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(e);let o=e._prevRemoved,i=e._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(e,n,r),this._addToMoves(e,r),e}_moveAfter(e,n,r){return this._unlink(e),this._insertAfter(e,n,r),this._addToMoves(e,r),e}_addAfter(e,n,r){return this._insertAfter(e,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=e:this._additionsTail=this._additionsTail._nextAdded=e,e}_insertAfter(e,n,r){let o=n===null?this._itHead:n._next;return e._next=o,e._prev=n,o===null?this._itTail=e:o._prev=e,n===null?this._itHead=e:n._next=e,this._linkedRecords===null&&(this._linkedRecords=new yi),this._linkedRecords.put(e),e.currentIndex=r,e}_remove(e){return this._addToRemovals(this._unlink(e))}_unlink(e){this._linkedRecords!==null&&this._linkedRecords.remove(e);let n=e._prev,r=e._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,e}_addToMoves(e,n){return e.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=e:this._movesTail=this._movesTail._nextMoved=e),e}_addToRemovals(e){return this._unlinkedRecords===null&&(this._unlinkedRecords=new yi),this._unlinkedRecords.put(e),e.currentIndex=null,e._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=e,e._prevRemoved=null):(e._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=e),e}_addIdentityChange(e,n){return e.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=e:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=e,e}},ml=class{constructor(e,n){this.item=e,this.trackById=n,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}},gl=class{constructor(){this._head=null,this._tail=null}add(e){this._head===null?(this._head=this._tail=e,e._nextDup=null,e._prevDup=null):(this._tail._nextDup=e,e._prevDup=this._tail,e._nextDup=null,this._tail=e)}get(e,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,e))return r;return null}remove(e){let n=e._prevDup,r=e._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},yi=class{constructor(){this.map=new Map}put(e){let n=e.trackById,r=this.map.get(n);r||(r=new gl,this.map.set(n,r)),r.add(e)}get(e,n){let r=e,o=this.map.get(r);return o?o.get(e,n):null}remove(e){let n=e.trackById;return this.map.get(n).remove(e)&&this.map.delete(n),e}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function cf(t,e,n){let r=t.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+e+o}var yl=class{constructor(){}supports(e){return e instanceof Map||lu(e)}create(){return new vl}},vl=class{constructor(){this._records=new Map,this._mapHead=null,this._appendAfter=null,this._previousMapHead=null,this._changesHead=null,this._changesTail=null,this._additionsHead=null,this._additionsTail=null,this._removalsHead=null,this._removalsTail=null}get isDirty(){return this._additionsHead!==null||this._changesHead!==null||this._removalsHead!==null}forEachItem(e){let n;for(n=this._mapHead;n!==null;n=n._next)e(n)}forEachPreviousItem(e){let n;for(n=this._previousMapHead;n!==null;n=n._nextPrevious)e(n)}forEachChangedItem(e){let n;for(n=this._changesHead;n!==null;n=n._nextChanged)e(n)}forEachAddedItem(e){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)e(n)}forEachRemovedItem(e){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)e(n)}diff(e){if(!e)e=new Map;else if(!(e instanceof Map||lu(e)))throw new g(900,!1);return this.check(e)?this:null}onDestroy(){}check(e){this._reset();let n=this._mapHead;if(this._appendAfter=null,this._forEach(e,(r,o)=>{if(n&&n.key===o)this._maybeAddToChanges(n,r),this._appendAfter=n,n=n._next;else{let i=this._getOrCreateRecordForKey(o,r);n=this._insertBeforeOrAppend(n,i)}}),n){n._prev&&(n._prev._next=null),this._removalsHead=n;for(let r=n;r!==null;r=r._nextRemoved)r===this._mapHead&&(this._mapHead=null),this._records.delete(r.key),r._nextRemoved=r._next,r.previousValue=r.currentValue,r.currentValue=null,r._prev=null,r._next=null}return this._changesTail&&(this._changesTail._nextChanged=null),this._additionsTail&&(this._additionsTail._nextAdded=null),this.isDirty}_insertBeforeOrAppend(e,n){if(e){let r=e._prev;return n._next=e,n._prev=r,e._prev=n,r&&(r._next=n),e===this._mapHead&&(this._mapHead=n),this._appendAfter=e,e}return this._appendAfter?(this._appendAfter._next=n,n._prev=this._appendAfter):this._mapHead=n,this._appendAfter=n,null}_getOrCreateRecordForKey(e,n){if(this._records.has(e)){let o=this._records.get(e);this._maybeAddToChanges(o,n);let i=o._prev,s=o._next;return i&&(i._next=s),s&&(s._prev=i),o._next=null,o._prev=null,o}let r=new Dl(e);return this._records.set(e,r),r.currentValue=n,this._addToAdditions(r),r}_reset(){if(this.isDirty){let e;for(this._previousMapHead=this._mapHead,e=this._previousMapHead;e!==null;e=e._next)e._nextPrevious=e._next;for(e=this._changesHead;e!==null;e=e._nextChanged)e.previousValue=e.currentValue;for(e=this._additionsHead;e!=null;e=e._nextAdded)e.previousValue=e.currentValue;this._changesHead=this._changesTail=null,this._additionsHead=this._additionsTail=null,this._removalsHead=null}}_maybeAddToChanges(e,n){Object.is(n,e.currentValue)||(e.previousValue=e.currentValue,e.currentValue=n,this._addToChanges(e))}_addToAdditions(e){this._additionsHead===null?this._additionsHead=this._additionsTail=e:(this._additionsTail._nextAdded=e,this._additionsTail=e)}_addToChanges(e){this._changesHead===null?this._changesHead=this._changesTail=e:(this._changesTail._nextChanged=e,this._changesTail=e)}_forEach(e,n){e instanceof Map?e.forEach(n):Object.keys(e).forEach(r=>n(e[r],r))}},Dl=class{constructor(e){this.key=e,this.previousValue=null,this.currentValue=null,this._nextPrevious=null,this._next=null,this._prev=null,this._nextAdded=null,this._nextRemoved=null,this._nextChanged=null}};function df(){return new pu([new hl])}var pu=(()=>{class t{static{this.\u0275prov=z({token:t,providedIn:"root",factory:df})}constructor(n){this.factories=n}static create(n,r){if(r!=null){let o=r.factories.slice();n=n.concat(o)}return new t(n)}static extend(n){return{provide:t,useFactory:r=>t.create(n,r||df()),deps:[[t,new bf,new If]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r!=null)return r;throw new g(901,!1)}}return t})();function ff(){return new Wi([new yl])}var Wi=(()=>{class t{static{this.\u0275prov=z({token:t,providedIn:"root",factory:ff})}constructor(n){this.factories=n}static create(n,r){if(r){let o=r.factories.slice();n=n.concat(o)}return new t(n)}static extend(n){return{provide:t,useFactory:r=>t.create(n,r||ff()),deps:[[t,new bf,new If]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r)return r;throw new g(901,!1)}}return t})();function OR(t){try{let{rootComponent:e,appProviders:n,platformProviders:r}=t,o=PI(r),i=[om({}),{provide:Zn,useExisting:TI},...n||[]],s=new di({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return xI({r3Injector:s.injector,platformInjector:o,rootComponent:e})}catch(e){return Promise.reject(e)}}var PR=new j("");function jI(t){return typeof t=="boolean"?t:t!=null&&t!=="false"}function VI(t,e=NaN){return!isNaN(parseFloat(t))&&!isNaN(Number(t))?Number(t):e}function FR(t,e){Qe("NgSignals");let n=Tc(t);return e?.equal&&(n[Je].equal=e.equal),n}function mu(t){let e=O(null);try{return t()}finally{O(e)}}var BI=new j("",{providedIn:"root",factory:()=>A($I)}),$I=(()=>{class t{static{this.\u0275prov=z({token:t,providedIn:"root",factory:()=>new El})}}return t})(),El=class{constructor(){this.queuedEffectCount=0,this.queues=new Map,this.pendingTasks=A(Pr),this.taskId=null}scheduleEffect(e){if(this.enqueue(e),this.taskId===null){let n=this.taskId=this.pendingTasks.add();queueMicrotask(()=>{this.flush(),this.pendingTasks.remove(n),this.taskId=null})}}enqueue(e){let n=e.creationZone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(e)||(this.queuedEffectCount++,r.add(e))}flush(){for(;this.queuedEffectCount>0;)for(let[e,n]of this.queues)e===null?this.flushQueue(n):e.run(()=>this.flushQueue(n))}flushQueue(e){for(let n of e)e.delete(n),this.queuedEffectCount--,n.run()}},wl=class{constructor(e,n,r,o,i,s){this.scheduler=e,this.effectFn=n,this.creationZone=r,this.injector=i,this.watcher=Rc(a=>this.runEffect(a),()=>this.schedule(),s),this.unregisterOnDestroy=o?.onDestroy(()=>this.destroy())}runEffect(e){try{this.effectFn(e)}catch(n){this.injector.get(mn,null,{optional:!0})?.handleError(n)}}run(){this.watcher.run()}schedule(){this.scheduler.scheduleEffect(this)}destroy(){this.watcher.destroy(),this.unregisterOnDestroy?.()}};function HI(t,e){Qe("NgSignals"),!e?.injector&&Nl(HI);let n=e?.injector??A(Dt),r=e?.manualCleanup!==!0?n.get(Ni):null,o=new wl(n.get(BI),t,typeof Zone>"u"?null:Zone.current,r,n,e?.allowSignalWrites??!1),i=n.get(Gi,null,{optional:!0});return!i||!(i._lView[S]&8)?o.watcher.notify():(i._lView[jo]??=[]).push(o.watcher.notify),o}function RR(t,e){let n=gt(t),r=e.elementInjector||Ei();return new yn(n).create(r,e.projectableNodes,e.hostElement,e.environmentInjector)}function kR(t){let e=gt(t);if(!e)return null;let n=new yn(e);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return e.standalone},get isSignal(){return e.signals}}}var Em=null;function gu(){return Em}function rk(t){Em??=t}var um=class{};var Nu=new j(""),xu=(()=>{class t{historyGo(n){throw new Error("")}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=z({token:t,factory:()=>A(UI),providedIn:"platform"})}}return t})(),ok=new j(""),UI=(()=>{class t extends xu{constructor(){super(),this._doc=A(Nu),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return gu().getBaseHref(this._doc)}onPopState(n){let r=gu().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=gu().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=z({token:t,factory:()=>new t,providedIn:"platform"})}}return t})();function Au(t,e){if(t.length==0)return e;if(e.length==0)return t;let n=0;return t.endsWith("/")&&n++,e.startsWith("/")&&n++,n==2?t+e.substring(1):n==1?t+e:t+"/"+e}function cm(t){let e=t.match(/#|\?|$/),n=e&&e.index||t.length,r=n-(t[n-1]==="/"?1:0);return t.slice(0,r)+t.slice(n)}function Mt(t){return t&&t[0]!=="?"?"?"+t:t}var is=(()=>{class t{historyGo(n){throw new Error("")}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=z({token:t,factory:()=>A(zI),providedIn:"root"})}}return t})(),wm=new j(""),zI=(()=>{class t extends is{constructor(n,r){super(),this._platformLocation=n,this._removeListenerFns=[],this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??A(Nu).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return Au(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+Mt(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+Mt(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+Mt(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static{this.\u0275fac=function(r){return new(r||t)(ge(xu),ge(wm,8))}}static{this.\u0275prov=z({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),ik=(()=>{class t extends is{constructor(n,r){super(),this._platformLocation=n,this._baseHref="",this._removeListenerFns=[],r!=null&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}path(n=!1){let r=this._platformLocation.hash??"#";return r.length>0?r.substring(1):r}prepareExternalUrl(n){let r=Au(this._baseHref,n);return r.length>0?"#"+r:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+Mt(i));s.length==0&&(s=this._platformLocation.pathname),this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+Mt(i));s.length==0&&(s=this._platformLocation.pathname),this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static{this.\u0275fac=function(r){return new(r||t)(ge(xu),ge(wm,8))}}static{this.\u0275prov=z({token:t,factory:t.\u0275fac})}}return t})(),qI=(()=>{class t{constructor(n){this._subject=new Ge,this._urlChangeListeners=[],this._urlChangeSubscription=null,this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=QI(cm(dm(r))),this._locationStrategy.onPopState(o=>{this._subject.emit({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+Mt(r))}normalize(n){return t.stripTrailingSlash(WI(this._basePath,dm(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+Mt(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+Mt(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r,complete:o})}static{this.normalizeQueryParams=Mt}static{this.joinWithSlash=Au}static{this.stripTrailingSlash=cm}static{this.\u0275fac=function(r){return new(r||t)(ge(is))}}static{this.\u0275prov=z({token:t,factory:()=>GI(),providedIn:"root"})}}return t})();function GI(){return new qI(ge(is))}function WI(t,e){if(!t||!e.startsWith(t))return e;let n=e.substring(t.length);return n===""||["/",";","?","#"].includes(n[0])?n:e}function dm(t){return t.replace(/\/index.html$/,"")}function QI(t){if(new RegExp("^(https?:)?//").test(t)){let[,n]=t.split(/\/\/[^\/]+/);return n}return t}var _m={ADP:[void 0,void 0,0],AFN:[void 0,"\u060B",0],ALL:[void 0,void 0,0],AMD:[void 0,"\u058F",2],AOA:[void 0,"Kz"],ARS:[void 0,"$"],AUD:["A$","$"],AZN:[void 0,"\u20BC"],BAM:[void 0,"KM"],BBD:[void 0,"$"],BDT:[void 0,"\u09F3"],BHD:[void 0,void 0,3],BIF:[void 0,void 0,0],BMD:[void 0,"$"],BND:[void 0,"$"],BOB:[void 0,"Bs"],BRL:["R$"],BSD:[void 0,"$"],BWP:[void 0,"P"],BYN:[void 0,void 0,2],BYR:[void 0,void 0,0],BZD:[void 0,"$"],CAD:["CA$","$",2],CHF:[void 0,void 0,2],CLF:[void 0,void 0,4],CLP:[void 0,"$",0],CNY:["CN\xA5","\xA5"],COP:[void 0,"$",2],CRC:[void 0,"\u20A1",2],CUC:[void 0,"$"],CUP:[void 0,"$"],CZK:[void 0,"K\u010D",2],DJF:[void 0,void 0,0],DKK:[void 0,"kr",2],DOP:[void 0,"$"],EGP:[void 0,"E\xA3"],ESP:[void 0,"\u20A7",0],EUR:["\u20AC"],FJD:[void 0,"$"],FKP:[void 0,"\xA3"],GBP:["\xA3"],GEL:[void 0,"\u20BE"],GHS:[void 0,"GH\u20B5"],GIP:[void 0,"\xA3"],GNF:[void 0,"FG",0],GTQ:[void 0,"Q"],GYD:[void 0,"$",2],HKD:["HK$","$"],HNL:[void 0,"L"],HRK:[void 0,"kn"],HUF:[void 0,"Ft",2],IDR:[void 0,"Rp",2],ILS:["\u20AA"],INR:["\u20B9"],IQD:[void 0,void 0,0],IRR:[void 0,void 0,0],ISK:[void 0,"kr",0],ITL:[void 0,void 0,0],JMD:[void 0,"$"],JOD:[void 0,void 0,3],JPY:["\xA5",void 0,0],KHR:[void 0,"\u17DB"],KMF:[void 0,"CF",0],KPW:[void 0,"\u20A9",0],KRW:["\u20A9",void 0,0],KWD:[void 0,void 0,3],KYD:[void 0,"$"],KZT:[void 0,"\u20B8"],LAK:[void 0,"\u20AD",0],LBP:[void 0,"L\xA3",0],LKR:[void 0,"Rs"],LRD:[void 0,"$"],LTL:[void 0,"Lt"],LUF:[void 0,void 0,0],LVL:[void 0,"Ls"],LYD:[void 0,void 0,3],MGA:[void 0,"Ar",0],MGF:[void 0,void 0,0],MMK:[void 0,"K",0],MNT:[void 0,"\u20AE",2],MRO:[void 0,void 0,0],MUR:[void 0,"Rs",2],MXN:["MX$","$"],MYR:[void 0,"RM"],NAD:[void 0,"$"],NGN:[void 0,"\u20A6"],NIO:[void 0,"C$"],NOK:[void 0,"kr",2],NPR:[void 0,"Rs"],NZD:["NZ$","$"],OMR:[void 0,void 0,3],PHP:["\u20B1"],PKR:[void 0,"Rs",2],PLN:[void 0,"z\u0142"],PYG:[void 0,"\u20B2",0],RON:[void 0,"lei"],RSD:[void 0,void 0,0],RUB:[void 0,"\u20BD"],RWF:[void 0,"RF",0],SBD:[void 0,"$"],SEK:[void 0,"kr",2],SGD:[void 0,"$"],SHP:[void 0,"\xA3"],SLE:[void 0,void 0,2],SLL:[void 0,void 0,0],SOS:[void 0,void 0,0],SRD:[void 0,"$"],SSP:[void 0,"\xA3"],STD:[void 0,void 0,0],STN:[void 0,"Db"],SYP:[void 0,"\xA3",0],THB:[void 0,"\u0E3F"],TMM:[void 0,void 0,0],TND:[void 0,void 0,3],TOP:[void 0,"T$"],TRL:[void 0,void 0,0],TRY:[void 0,"\u20BA"],TTD:[void 0,"$"],TWD:["NT$","$",2],TZS:[void 0,void 0,2],UAH:[void 0,"\u20B4"],UGX:[void 0,void 0,0],USD:["$"],UYI:[void 0,void 0,0],UYU:[void 0,"$"],UYW:[void 0,void 0,4],UZS:[void 0,void 0,2],VEF:[void 0,"Bs",2],VND:["\u20AB",void 0,0],VUV:[void 0,void 0,0],XAF:["FCFA",void 0,0],XCD:["EC$","$"],XOF:["F\u202FCFA",void 0,0],XPF:["CFPF",void 0,0],XXX:["\xA4"],YER:[void 0,void 0,0],ZAR:[void 0,"R"],ZMK:[void 0,void 0,0],ZMW:[void 0,"ZK"],ZWD:[void 0,void 0,0]},Ou=function(t){return t[t.Decimal=0]="Decimal",t[t.Percent=1]="Percent",t[t.Currency=2]="Currency",t[t.Scientific=3]="Scientific",t}(Ou||{});var _e=function(t){return t[t.Format=0]="Format",t[t.Standalone=1]="Standalone",t}(_e||{}),G=function(t){return t[t.Narrow=0]="Narrow",t[t.Abbreviated=1]="Abbreviated",t[t.Wide=2]="Wide",t[t.Short=3]="Short",t}(G||{}),Pe=function(t){return t[t.Short=0]="Short",t[t.Medium=1]="Medium",t[t.Long=2]="Long",t[t.Full=3]="Full",t}(Pe||{}),ve={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function ZI(t){return Te(t)[Q.LocaleId]}function KI(t,e,n){let r=Te(t),o=[r[Q.DayPeriodsFormat],r[Q.DayPeriodsStandalone]],i=$e(o,e);return $e(i,n)}function YI(t,e,n){let r=Te(t),o=[r[Q.DaysFormat],r[Q.DaysStandalone]],i=$e(o,e);return $e(i,n)}function JI(t,e,n){let r=Te(t),o=[r[Q.MonthsFormat],r[Q.MonthsStandalone]],i=$e(o,e);return $e(i,n)}function XI(t,e){let r=Te(t)[Q.Eras];return $e(r,e)}function Qi(t,e){let n=Te(t);return $e(n[Q.DateFormat],e)}function Zi(t,e){let n=Te(t);return $e(n[Q.TimeFormat],e)}function Ki(t,e){let r=Te(t)[Q.DateTimeFormat];return $e(r,e)}function at(t,e){let n=Te(t),r=n[Q.NumberSymbols][e];if(typeof r>"u"){if(e===ve.CurrencyDecimal)return n[Q.NumberSymbols][ve.Decimal];if(e===ve.CurrencyGroup)return n[Q.NumberSymbols][ve.Group]}return r}function Im(t,e){return Te(t)[Q.NumberFormats][e]}function eb(t){return Te(t)[Q.Currencies]}function bm(t){if(!t[Q.ExtraData])throw new Error(`Missing extra locale data for the locale "${t[Q.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function tb(t){let e=Te(t);return bm(e),(e[Q.ExtraData][2]||[]).map(r=>typeof r=="string"?yu(r):[yu(r[0]),yu(r[1])])}function nb(t,e,n){let r=Te(t);bm(r);let o=[r[Q.ExtraData][0],r[Q.ExtraData][1]],i=$e(o,e)||[];return $e(i,n)||[]}function $e(t,e){for(let n=e;n>-1;n--)if(typeof t[n]<"u")return t[n];throw new Error("Locale data API: locale data undefined")}function yu(t){let[e,n]=t.split(":");return{hours:+e,minutes:+n}}function rb(t,e,n="en"){let r=eb(n)[t]||_m[t]||[],o=r[1];return e==="narrow"&&typeof o=="string"?o:r[0]||t}var ob=2;function ib(t){let e,n=_m[t];return n&&(e=n[2]),typeof e=="number"?e:ob}var sb=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,Yi={},ab=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/,Tt=function(t){return t[t.Short=0]="Short",t[t.ShortGMT=1]="ShortGMT",t[t.Long=2]="Long",t[t.Extended=3]="Extended",t}(Tt||{}),$=function(t){return t[t.FullYear=0]="FullYear",t[t.Month=1]="Month",t[t.Date=2]="Date",t[t.Hours=3]="Hours",t[t.Minutes=4]="Minutes",t[t.Seconds=5]="Seconds",t[t.FractionalSeconds=6]="FractionalSeconds",t[t.Day=7]="Day",t}($||{}),B=function(t){return t[t.DayPeriods=0]="DayPeriods",t[t.Days=1]="Days",t[t.Months=2]="Months",t[t.Eras=3]="Eras",t}(B||{});function lb(t,e,n,r){let o=yb(t);e=St(n,e)||e;let s=[],a;for(;e;)if(a=ab.exec(e),a){s=s.concat(a.slice(1));let c=s.pop();if(!c)break;e=c}else{s.push(e);break}let l=o.getTimezoneOffset();r&&(l=Sm(r,l),o=gb(o,r,!0));let u="";return s.forEach(c=>{let d=pb(c);u+=d?d(o,n,l):c==="''"?"'":c.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),u}function ns(t,e,n){let r=new Date(0);return r.setFullYear(t,e,n),r.setHours(0,0,0),r}function St(t,e){let n=ZI(t);if(Yi[n]??={},Yi[n][e])return Yi[n][e];let r="";switch(e){case"shortDate":r=Qi(t,Pe.Short);break;case"mediumDate":r=Qi(t,Pe.Medium);break;case"longDate":r=Qi(t,Pe.Long);break;case"fullDate":r=Qi(t,Pe.Full);break;case"shortTime":r=Zi(t,Pe.Short);break;case"mediumTime":r=Zi(t,Pe.Medium);break;case"longTime":r=Zi(t,Pe.Long);break;case"fullTime":r=Zi(t,Pe.Full);break;case"short":let o=St(t,"shortTime"),i=St(t,"shortDate");r=Ji(Ki(t,Pe.Short),[o,i]);break;case"medium":let s=St(t,"mediumTime"),a=St(t,"mediumDate");r=Ji(Ki(t,Pe.Medium),[s,a]);break;case"long":let l=St(t,"longTime"),u=St(t,"longDate");r=Ji(Ki(t,Pe.Long),[l,u]);break;case"full":let c=St(t,"fullTime"),d=St(t,"fullDate");r=Ji(Ki(t,Pe.Full),[c,d]);break}return r&&(Yi[n][e]=r),r}function Ji(t,e){return e&&(t=t.replace(/\{([^}]+)}/g,function(n,r){return e!=null&&r in e?e[r]:n})),t}function Ze(t,e,n="-",r,o){let i="";(t<0||o&&t<=0)&&(o?t=-t+1:(t=-t,i=n));let s=String(t);for(;s.length<e;)s="0"+s;return r&&(s=s.slice(s.length-e)),i+s}function ub(t,e){return Ze(t,3).substring(0,e)}function ne(t,e,n=0,r=!1,o=!1){return function(i,s){let a=cb(t,i);if((n>0||a>-n)&&(a+=n),t===$.Hours)a===0&&n===-12&&(a=12);else if(t===$.FractionalSeconds)return ub(a,e);let l=at(s,ve.MinusSign);return Ze(a,e,l,r,o)}}function cb(t,e){switch(t){case $.FullYear:return e.getFullYear();case $.Month:return e.getMonth();case $.Date:return e.getDate();case $.Hours:return e.getHours();case $.Minutes:return e.getMinutes();case $.Seconds:return e.getSeconds();case $.FractionalSeconds:return e.getMilliseconds();case $.Day:return e.getDay();default:throw new Error(`Unknown DateType value "${t}".`)}}function W(t,e,n=_e.Format,r=!1){return function(o,i){return db(o,i,t,e,n,r)}}function db(t,e,n,r,o,i){switch(n){case B.Months:return JI(e,o,r)[t.getMonth()];case B.Days:return YI(e,o,r)[t.getDay()];case B.DayPeriods:let s=t.getHours(),a=t.getMinutes();if(i){let u=tb(e),c=nb(e,o,r),d=u.findIndex(h=>{if(Array.isArray(h)){let[f,p]=h,m=s>=f.hours&&a>=f.minutes,D=s<p.hours||s===p.hours&&a<p.minutes;if(f.hours<p.hours){if(m&&D)return!0}else if(m||D)return!0}else if(h.hours===s&&h.minutes===a)return!0;return!1});if(d!==-1)return c[d]}return KI(e,o,r)[s<12?0:1];case B.Eras:return XI(e,r)[t.getFullYear()<=0?0:1];default:let l=n;throw new Error(`unexpected translation type ${l}`)}}function Xi(t){return function(e,n,r){let o=-1*r,i=at(n,ve.MinusSign),s=o>0?Math.floor(o/60):Math.ceil(o/60);switch(t){case Tt.Short:return(o>=0?"+":"")+Ze(s,2,i)+Ze(Math.abs(o%60),2,i);case Tt.ShortGMT:return"GMT"+(o>=0?"+":"")+Ze(s,1,i);case Tt.Long:return"GMT"+(o>=0?"+":"")+Ze(s,2,i)+":"+Ze(Math.abs(o%60),2,i);case Tt.Extended:return r===0?"Z":(o>=0?"+":"")+Ze(s,2,i)+":"+Ze(Math.abs(o%60),2,i);default:throw new Error(`Unknown zone width "${t}"`)}}}var fb=0,ts=4;function hb(t){let e=ns(t,fb,1).getDay();return ns(t,0,1+(e<=ts?ts:ts+7)-e)}function Cm(t){let e=t.getDay(),n=e===0?-3:ts-e;return ns(t.getFullYear(),t.getMonth(),t.getDate()+n)}function vu(t,e=!1){return function(n,r){let o;if(e){let i=new Date(n.getFullYear(),n.getMonth(),1).getDay()-1,s=n.getDate();o=1+Math.floor((s+i)/7)}else{let i=Cm(n),s=hb(i.getFullYear()),a=i.getTime()-s.getTime();o=1+Math.round(a/6048e5)}return Ze(o,t,at(r,ve.MinusSign))}}function es(t,e=!1){return function(n,r){let i=Cm(n).getFullYear();return Ze(i,t,at(r,ve.MinusSign),e)}}var Du={};function pb(t){if(Du[t])return Du[t];let e;switch(t){case"G":case"GG":case"GGG":e=W(B.Eras,G.Abbreviated);break;case"GGGG":e=W(B.Eras,G.Wide);break;case"GGGGG":e=W(B.Eras,G.Narrow);break;case"y":e=ne($.FullYear,1,0,!1,!0);break;case"yy":e=ne($.FullYear,2,0,!0,!0);break;case"yyy":e=ne($.FullYear,3,0,!1,!0);break;case"yyyy":e=ne($.FullYear,4,0,!1,!0);break;case"Y":e=es(1);break;case"YY":e=es(2,!0);break;case"YYY":e=es(3);break;case"YYYY":e=es(4);break;case"M":case"L":e=ne($.Month,1,1);break;case"MM":case"LL":e=ne($.Month,2,1);break;case"MMM":e=W(B.Months,G.Abbreviated);break;case"MMMM":e=W(B.Months,G.Wide);break;case"MMMMM":e=W(B.Months,G.Narrow);break;case"LLL":e=W(B.Months,G.Abbreviated,_e.Standalone);break;case"LLLL":e=W(B.Months,G.Wide,_e.Standalone);break;case"LLLLL":e=W(B.Months,G.Narrow,_e.Standalone);break;case"w":e=vu(1);break;case"ww":e=vu(2);break;case"W":e=vu(1,!0);break;case"d":e=ne($.Date,1);break;case"dd":e=ne($.Date,2);break;case"c":case"cc":e=ne($.Day,1);break;case"ccc":e=W(B.Days,G.Abbreviated,_e.Standalone);break;case"cccc":e=W(B.Days,G.Wide,_e.Standalone);break;case"ccccc":e=W(B.Days,G.Narrow,_e.Standalone);break;case"cccccc":e=W(B.Days,G.Short,_e.Standalone);break;case"E":case"EE":case"EEE":e=W(B.Days,G.Abbreviated);break;case"EEEE":e=W(B.Days,G.Wide);break;case"EEEEE":e=W(B.Days,G.Narrow);break;case"EEEEEE":e=W(B.Days,G.Short);break;case"a":case"aa":case"aaa":e=W(B.DayPeriods,G.Abbreviated);break;case"aaaa":e=W(B.DayPeriods,G.Wide);break;case"aaaaa":e=W(B.DayPeriods,G.Narrow);break;case"b":case"bb":case"bbb":e=W(B.DayPeriods,G.Abbreviated,_e.Standalone,!0);break;case"bbbb":e=W(B.DayPeriods,G.Wide,_e.Standalone,!0);break;case"bbbbb":e=W(B.DayPeriods,G.Narrow,_e.Standalone,!0);break;case"B":case"BB":case"BBB":e=W(B.DayPeriods,G.Abbreviated,_e.Format,!0);break;case"BBBB":e=W(B.DayPeriods,G.Wide,_e.Format,!0);break;case"BBBBB":e=W(B.DayPeriods,G.Narrow,_e.Format,!0);break;case"h":e=ne($.Hours,1,-12);break;case"hh":e=ne($.Hours,2,-12);break;case"H":e=ne($.Hours,1);break;case"HH":e=ne($.Hours,2);break;case"m":e=ne($.Minutes,1);break;case"mm":e=ne($.Minutes,2);break;case"s":e=ne($.Seconds,1);break;case"ss":e=ne($.Seconds,2);break;case"S":e=ne($.FractionalSeconds,1);break;case"SS":e=ne($.FractionalSeconds,2);break;case"SSS":e=ne($.FractionalSeconds,3);break;case"Z":case"ZZ":case"ZZZ":e=Xi(Tt.Short);break;case"ZZZZZ":e=Xi(Tt.Extended);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":e=Xi(Tt.ShortGMT);break;case"OOOO":case"ZZZZ":case"zzzz":e=Xi(Tt.Long);break;default:return null}return Du[t]=e,e}function Sm(t,e){t=t.replace(/:/g,"");let n=Date.parse("Jan 01, 1970 00:00:00 "+t)/6e4;return isNaN(n)?e:n}function mb(t,e){return t=new Date(t.getTime()),t.setMinutes(t.getMinutes()+e),t}function gb(t,e,n){let r=n?-1:1,o=t.getTimezoneOffset(),i=Sm(e,o);return mb(t,r*(i-o))}function yb(t){if(fm(t))return t;if(typeof t=="number"&&!isNaN(t))return new Date(t);if(typeof t=="string"){if(t=t.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(t)){let[o,i=1,s=1]=t.split("-").map(a=>+a);return ns(o,i-1,s)}let n=parseFloat(t);if(!isNaN(t-n))return new Date(n);let r;if(r=t.match(sb))return vb(r)}let e=new Date(t);if(!fm(e))throw new Error(`Unable to convert "${t}" into a date`);return e}function vb(t){let e=new Date(0),n=0,r=0,o=t[8]?e.setUTCFullYear:e.setFullYear,i=t[8]?e.setUTCHours:e.setHours;t[9]&&(n=Number(t[9]+t[10]),r=Number(t[9]+t[11])),o.call(e,Number(t[1]),Number(t[2])-1,Number(t[3]));let s=Number(t[4]||0)-n,a=Number(t[5]||0)-r,l=Number(t[6]||0),u=Math.floor(parseFloat("0."+(t[7]||0))*1e3);return i.call(e,s,a,l,u),e}function fm(t){return t instanceof Date&&!isNaN(t.valueOf())}var Db=/^(\d+)?\.((\d+)(-(\d+))?)?$/,hm=22,rs=".",Vr="0",Eb=";",wb=",",Eu="#",pm="\xA4";function Mm(t,e,n,r,o,i,s=!1){let a="",l=!1;if(!isFinite(t))a=at(n,ve.Infinity);else{let u=Cb(t);s&&(u=bb(u));let c=e.minInt,d=e.minFrac,h=e.maxFrac;if(i){let T=i.match(Db);if(T===null)throw new Error(`${i} is not a valid digit info`);let k=T[1],V=T[3],de=T[5];k!=null&&(c=wu(k)),V!=null&&(d=wu(V)),de!=null?h=wu(de):V!=null&&d>h&&(h=d)}Sb(u,d,h);let f=u.digits,p=u.integerLen,m=u.exponent,D=[];for(l=f.every(T=>!T);p<c;p++)f.unshift(0);for(;p<0;p++)f.unshift(0);p>0?D=f.splice(p,f.length):(D=f,f=[0]);let E=[];for(f.length>=e.lgSize&&E.unshift(f.splice(-e.lgSize,f.length).join(""));f.length>e.gSize;)E.unshift(f.splice(-e.gSize,f.length).join(""));f.length&&E.unshift(f.join("")),a=E.join(at(n,r)),D.length&&(a+=at(n,o)+D.join("")),m&&(a+=at(n,ve.Exponential)+"+"+m)}return t<0&&!l?a=e.negPre+a+e.negSuf:a=e.posPre+a+e.posSuf,a}function _b(t,e,n,r,o){let i=Im(e,Ou.Currency),s=Tm(i,at(e,ve.MinusSign));return s.minFrac=ib(r),s.maxFrac=s.minFrac,Mm(t,s,e,ve.CurrencyGroup,ve.CurrencyDecimal,o).replace(pm,n).replace(pm,"").trim()}function Ib(t,e,n){let r=Im(e,Ou.Decimal),o=Tm(r,at(e,ve.MinusSign));return Mm(t,o,e,ve.Group,ve.Decimal,n)}function Tm(t,e="-"){let n={minInt:1,minFrac:0,maxFrac:0,posPre:"",posSuf:"",negPre:"",negSuf:"",gSize:0,lgSize:0},r=t.split(Eb),o=r[0],i=r[1],s=o.indexOf(rs)!==-1?o.split(rs):[o.substring(0,o.lastIndexOf(Vr)+1),o.substring(o.lastIndexOf(Vr)+1)],a=s[0],l=s[1]||"";n.posPre=a.substring(0,a.indexOf(Eu));for(let c=0;c<l.length;c++){let d=l.charAt(c);d===Vr?n.minFrac=n.maxFrac=c+1:d===Eu?n.maxFrac=c+1:n.posSuf+=d}let u=a.split(wb);if(n.gSize=u[1]?u[1].length:0,n.lgSize=u[2]||u[1]?(u[2]||u[1]).length:0,i){let c=o.length-n.posPre.length-n.posSuf.length,d=i.indexOf(Eu);n.negPre=i.substring(0,d).replace(/'/g,""),n.negSuf=i.slice(d+c).replace(/'/g,"")}else n.negPre=e+n.posPre,n.negSuf=n.posSuf;return n}function bb(t){if(t.digits[0]===0)return t;let e=t.digits.length-t.integerLen;return t.exponent?t.exponent+=2:(e===0?t.digits.push(0,0):e===1&&t.digits.push(0),t.integerLen+=2),t}function Cb(t){let e=Math.abs(t)+"",n=0,r,o,i,s,a;for((o=e.indexOf(rs))>-1&&(e=e.replace(rs,"")),(i=e.search(/e/i))>0?(o<0&&(o=i),o+=+e.slice(i+1),e=e.substring(0,i)):o<0&&(o=e.length),i=0;e.charAt(i)===Vr;i++);if(i===(a=e.length))r=[0],o=1;else{for(a--;e.charAt(a)===Vr;)a--;for(o-=i,r=[],s=0;i<=a;i++,s++)r[s]=Number(e.charAt(i))}return o>hm&&(r=r.splice(0,hm-1),n=o-1,o=1),{digits:r,exponent:n,integerLen:o}}function Sb(t,e,n){if(e>n)throw new Error(`The minimum number of digits after fraction (${e}) is higher than the maximum (${n}).`);let r=t.digits,o=r.length-t.integerLen,i=Math.min(Math.max(e,o),n),s=i+t.integerLen,a=r[s];if(s>0){r.splice(Math.max(t.integerLen,s));for(let d=s;d<r.length;d++)r[d]=0}else{o=Math.max(0,o),t.integerLen=1,r.length=Math.max(1,s=i+1),r[0]=0;for(let d=1;d<s;d++)r[d]=0}if(a>=5)if(s-1<0){for(let d=0;d>s;d--)r.unshift(0),t.integerLen++;r.unshift(1),t.integerLen++}else r[s-1]++;for(;o<Math.max(0,i);o++)r.push(0);let l=i!==0,u=e+t.integerLen,c=r.reduceRight(function(d,h,f,p){return h=h+d,p[f]=h<10?h:h-10,l&&(p[f]===0&&f>=u?p.pop():l=!1),h>=10?1:0},0);c&&(r.unshift(c),t.integerLen++)}function wu(t){let e=parseInt(t);if(isNaN(e))throw new Error("Invalid integer literal when parsing "+t);return e}function sk(t,e,n){return Qp(t,e,n)}function ak(t,e){e=encodeURIComponent(e);for(let n of t.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===e)return decodeURIComponent(i)}return null}var _u=/\s+/,mm=[],lk=(()=>{class t{constructor(n,r){this._ngEl=n,this._renderer=r,this.initialClasses=mm,this.stateMap=new Map}set klass(n){this.initialClasses=n!=null?n.trim().split(_u):mm}set ngClass(n){this.rawClass=typeof n=="string"?n.trim().split(_u):n}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let n=this.rawClass;if(Array.isArray(n)||n instanceof Set)for(let r of n)this._updateState(r,!0);else if(n!=null)for(let r of Object.keys(n))this._updateState(r,!!n[r]);this._applyStateDiff()}_updateState(n,r){let o=this.stateMap.get(n);o!==void 0?(o.enabled!==r&&(o.changed=!0,o.enabled=r),o.touched=!0):this.stateMap.set(n,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let n of this.stateMap){let r=n[0],o=n[1];o.changed?(this._toggleClass(r,o.enabled),o.changed=!1):o.touched||(o.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),o.touched=!1}}_toggleClass(n,r){n=n.trim(),n.length>0&&n.split(_u).forEach(o=>{r?this._renderer.addClass(this._ngEl.nativeElement,o):this._renderer.removeClass(this._ngEl.nativeElement,o)})}static{this.\u0275fac=function(r){return new(r||t)(H(Wt),H(Vi))}}static{this.\u0275dir=_t({type:t,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"},standalone:!0})}}return t})();var Iu=class{constructor(e,n,r,o){this.$implicit=e,this.ngForOf=n,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},uk=(()=>{class t{set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}constructor(n,r,o){this._viewContainer=n,this._template=r,this._differs=o,this._ngForOf=null,this._ngForOfDirty=!0,this._differ=null}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;if(!this._differ&&n)if(0)try{}catch{}else this._differ=this._differs.find(n).create(this.ngForTrackBy)}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let r=this._viewContainer;n.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new Iu(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),gm(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}n.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);gm(i,o)})}static ngTemplateContextGuard(n,r){return!0}static{this.\u0275fac=function(r){return new(r||t)(H(Ct),H(wt),H(pu))}}static{this.\u0275dir=_t({type:t,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"},standalone:!0})}}return t})();function gm(t,e){t.context.$implicit=e.item}var ck=(()=>{class t{constructor(n,r){this._viewContainer=n,this._context=new bu,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){ym("ngIfThen",n),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){ym("ngIfElse",n),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(n,r){return!0}static{this.\u0275fac=function(r){return new(r||t)(H(Ct),H(wt))}}static{this.\u0275dir=_t({type:t,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"},standalone:!0})}}return t})(),bu=class{constructor(){this.$implicit=null,this.ngIf=null}};function ym(t,e){if(!!!(!e||e.createEmbeddedView))throw new Error(`${t} must be a TemplateRef, but received '${Ee(e)}'.`)}var os=class{constructor(e,n){this._viewContainerRef=e,this._templateRef=n,this._created=!1}create(){this._created=!0,this._viewContainerRef.createEmbeddedView(this._templateRef)}destroy(){this._created=!1,this._viewContainerRef.clear()}enforceState(e){e&&!this._created?this.create():!e&&this._created&&this.destroy()}},Nm=(()=>{class t{constructor(){this._defaultViews=[],this._defaultUsed=!1,this._caseCount=0,this._lastCaseCheckIndex=0,this._lastCasesMatched=!1}set ngSwitch(n){this._ngSwitch=n,this._caseCount===0&&this._updateDefaultCases(!0)}_addCase(){return this._caseCount++}_addDefault(n){this._defaultViews.push(n)}_matchCase(n){let r=n===this._ngSwitch;return this._lastCasesMatched||=r,this._lastCaseCheckIndex++,this._lastCaseCheckIndex===this._caseCount&&(this._updateDefaultCases(!this._lastCasesMatched),this._lastCaseCheckIndex=0,this._lastCasesMatched=!1),r}_updateDefaultCases(n){if(this._defaultViews.length>0&&n!==this._defaultUsed){this._defaultUsed=n;for(let r of this._defaultViews)r.enforceState(n)}}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275dir=_t({type:t,selectors:[["","ngSwitch",""]],inputs:{ngSwitch:"ngSwitch"},standalone:!0})}}return t})(),dk=(()=>{class t{constructor(n,r,o){this.ngSwitch=o,o._addCase(),this._view=new os(n,r)}ngDoCheck(){this._view.enforceState(this.ngSwitch._matchCase(this.ngSwitchCase))}static{this.\u0275fac=function(r){return new(r||t)(H(Ct),H(wt),H(Nm,9))}}static{this.\u0275dir=_t({type:t,selectors:[["","ngSwitchCase",""]],inputs:{ngSwitchCase:"ngSwitchCase"},standalone:!0})}}return t})(),fk=(()=>{class t{constructor(n,r,o){o._addDefault(new os(n,r))}static{this.\u0275fac=function(r){return new(r||t)(H(Ct),H(wt),H(Nm,9))}}static{this.\u0275dir=_t({type:t,selectors:[["","ngSwitchDefault",""]],standalone:!0})}}return t})();var hk=(()=>{class t{constructor(n,r,o){this._ngEl=n,this._differs=r,this._renderer=o,this._ngStyle=null,this._differ=null}set ngStyle(n){this._ngStyle=n,!this._differ&&n&&(this._differ=this._differs.find(n).create())}ngDoCheck(){if(this._differ){let n=this._differ.diff(this._ngStyle);n&&this._applyChanges(n)}}_setStyle(n,r){let[o,i]=n.split("."),s=o.indexOf("-")===-1?void 0:Mr.DashCase;r!=null?this._renderer.setStyle(this._ngEl.nativeElement,o,i?`${r}${i}`:r,s):this._renderer.removeStyle(this._ngEl.nativeElement,o,s)}_applyChanges(n){n.forEachRemovedItem(r=>this._setStyle(r.key,null)),n.forEachAddedItem(r=>this._setStyle(r.key,r.currentValue)),n.forEachChangedItem(r=>this._setStyle(r.key,r.currentValue))}static{this.\u0275fac=function(r){return new(r||t)(H(Wt),H(Wi),H(Vi))}}static{this.\u0275dir=_t({type:t,selectors:[["","ngStyle",""]],inputs:{ngStyle:"ngStyle"},standalone:!0})}}return t})(),pk=(()=>{class t{constructor(n){this._viewContainerRef=n,this._viewRef=null,this.ngTemplateOutletContext=null,this.ngTemplateOutlet=null,this.ngTemplateOutletInjector=null}ngOnChanges(n){if(this._shouldRecreateView(n)){let r=this._viewContainerRef;if(this._viewRef&&r.remove(r.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let o=this._createContextForwardProxy();this._viewRef=r.createEmbeddedView(this.ngTemplateOutlet,o,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(n){return!!n.ngTemplateOutlet||!!n.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(n,r,o)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,r,o):!1,get:(n,r,o)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,r,o)}})}static{this.\u0275fac=function(r){return new(r||t)(H(Ct))}}static{this.\u0275dir=_t({type:t,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},standalone:!0,features:[Ol]})}}return t})();function Br(t,e){return new g(2100,!1)}var Cu=class{createSubscription(e,n){return mu(()=>e.subscribe({next:n,error:r=>{throw r}}))}dispose(e){mu(()=>e.unsubscribe())}},Su=class{createSubscription(e,n){return e.then(n,r=>{throw r})}dispose(e){}},Mb=new Su,Tb=new Cu,mk=(()=>{class t{constructor(n){this._latestValue=null,this.markForCheckOnValueUpdate=!0,this._subscription=null,this._obj=null,this._strategy=null,this._ref=n}ngOnDestroy(){this._subscription&&this._dispose(),this._ref=null}transform(n){if(!this._obj){if(n)try{this.markForCheckOnValueUpdate=!1,this._subscribe(n)}finally{this.markForCheckOnValueUpdate=!0}return this._latestValue}return n!==this._obj?(this._dispose(),this.transform(n)):this._latestValue}_subscribe(n){this._obj=n,this._strategy=this._selectStrategy(n),this._subscription=this._strategy.createSubscription(n,r=>this._updateLatestValue(n,r))}_selectStrategy(n){if(zi(n))return Mb;if(hu(n))return Tb;throw Br(t,n)}_dispose(){this._strategy.dispose(this._subscription),this._latestValue=null,this._subscription=null,this._obj=null}_updateLatestValue(n,r){n===this._obj&&(this._latestValue=r,this.markForCheckOnValueUpdate&&this._ref?.markForCheck())}static{this.\u0275fac=function(r){return new(r||t)(H(Gi,16))}}static{this.\u0275pipe=En({name:"async",type:t,pure:!1,standalone:!0})}}return t})(),gk=(()=>{class t{transform(n){if(n==null)return null;if(typeof n!="string")throw Br(t,n);return n.toLowerCase()}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275pipe=En({name:"lowercase",type:t,pure:!0,standalone:!0})}}return t})();var Nb="mediumDate",xb=new j(""),Ab=new j(""),yk=(()=>{class t{constructor(n,r,o){this.locale=n,this.defaultTimezone=r,this.defaultOptions=o}transform(n,r,o,i){if(n==null||n===""||n!==n)return null;try{let s=r??this.defaultOptions?.dateFormat??Nb,a=o??this.defaultOptions?.timezone??this.defaultTimezone??void 0;return lb(n,s,i||this.locale,a)}catch(s){throw Br(t,s.message)}}static{this.\u0275fac=function(r){return new(r||t)(H(ir,16),H(xb,24),H(Ab,24))}}static{this.\u0275pipe=En({name:"date",type:t,pure:!0,standalone:!0})}}return t})();function Ob(t,e){return{key:t,value:e}}var vk=(()=>{class t{constructor(n){this.differs=n,this.keyValues=[],this.compareFn=vm}transform(n,r=vm){if(!n||!(n instanceof Map)&&typeof n!="object")return null;this.differ??=this.differs.find(n).create();let o=this.differ.diff(n),i=r!==this.compareFn;return o&&(this.keyValues=[],o.forEachItem(s=>{this.keyValues.push(Ob(s.key,s.currentValue))})),(o||i)&&(this.keyValues.sort(r),this.compareFn=r),this.keyValues}static{this.\u0275fac=function(r){return new(r||t)(H(Wi,16))}}static{this.\u0275pipe=En({name:"keyvalue",type:t,pure:!1,standalone:!0})}}return t})();function vm(t,e){let n=t.key,r=e.key;if(n===r)return 0;if(n===void 0)return 1;if(r===void 0)return-1;if(n===null)return 1;if(r===null)return-1;if(typeof n=="string"&&typeof r=="string")return n<r?-1:1;if(typeof n=="number"&&typeof r=="number")return n-r;if(typeof n=="boolean"&&typeof r=="boolean")return n<r?-1:1;let o=String(n),i=String(r);return o==i?0:o<i?-1:1}var Dk=(()=>{class t{constructor(n){this._locale=n}transform(n,r,o){if(!xm(n))return null;o||=this._locale;try{let i=Am(n);return Ib(i,o,r)}catch(i){throw Br(t,i.message)}}static{this.\u0275fac=function(r){return new(r||t)(H(ir,16))}}static{this.\u0275pipe=En({name:"number",type:t,pure:!0,standalone:!0})}}return t})();var Ek=(()=>{class t{constructor(n,r="USD"){this._locale=n,this._defaultCurrencyCode=r}transform(n,r=this._defaultCurrencyCode,o="symbol",i,s){if(!xm(n))return null;s||=this._locale,typeof o=="boolean"&&(o=o?"symbol":"code");let a=r||this._defaultCurrencyCode;o!=="code"&&(o==="symbol"||o==="symbol-narrow"?a=rb(a,o==="symbol"?"wide":"narrow",s):a=o);try{let l=Am(n);return _b(l,s,a,r,i)}catch(l){throw Br(t,l.message)}}static{this.\u0275fac=function(r){return new(r||t)(H(ir,16),H(sm,16))}}static{this.\u0275pipe=En({name:"currency",type:t,pure:!0,standalone:!0})}}return t})();function xm(t){return!(t==null||t===""||t!==t)}function Am(t){if(typeof t=="string"&&!isNaN(Number(t)-parseFloat(t)))return Number(t);if(typeof t!="number")throw new Error(`${t} is not a number`);return t}var wk=(()=>{class t{static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275mod=Of({type:t})}static{this.\u0275inj=yf({})}}return t})(),Pb="browser",Fb="server";function Rb(t){return t===Pb}function _k(t){return t===Fb}var Ik=(()=>{class t{static{this.\u0275prov=z({token:t,providedIn:"root",factory:()=>Rb(A(zl))?new Mu(A(Nu),window):new Tu})}}return t})(),Mu=class{constructor(e,n){this.document=e,this.window=n,this.offset=()=>[0,0]}setOffset(e){Array.isArray(e)?this.offset=()=>e:this.offset=e}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(e){this.window.scrollTo(e[0],e[1])}scrollToAnchor(e){let n=kb(this.document,e);n&&(this.scrollToElement(n),n.focus())}setHistoryScrollRestoration(e){this.window.history.scrollRestoration=e}scrollToElement(e){let n=e.getBoundingClientRect(),r=n.left+this.window.pageXOffset,o=n.top+this.window.pageYOffset,i=this.offset();this.window.scrollTo(r-i[0],o-i[1])}};function kb(t,e){let n=t.getElementById(e)||t.getElementsByName(e)[0];if(n)return n;if(typeof t.createTreeWalker=="function"&&t.body&&typeof t.body.attachShadow=="function"){let r=t.createTreeWalker(t.body,NodeFilter.SHOW_ELEMENT),o=r.currentNode;for(;o;){let i=o.shadowRoot;if(i){let s=i.getElementById(e)||i.querySelector(`[name="${e}"]`);if(s)return s}o=r.nextNode()}}return null}var Tu=class{setOffset(e){}getScrollPosition(){return[0,0]}scrollToPosition(e){}scrollToAnchor(e){}setHistoryScrollRestoration(e){}},Dm=class{};var N=function(t){return t[t.State=0]="State",t[t.Transition=1]="Transition",t[t.Sequence=2]="Sequence",t[t.Group=3]="Group",t[t.Animate=4]="Animate",t[t.Keyframes=5]="Keyframes",t[t.Style=6]="Style",t[t.Trigger=7]="Trigger",t[t.Reference=8]="Reference",t[t.AnimateChild=9]="AnimateChild",t[t.AnimateRef=10]="AnimateRef",t[t.Query=11]="Query",t[t.Stagger=12]="Stagger",t}(N||{}),lt="*";function Sk(t,e){return{type:N.Trigger,name:t,definitions:e,options:{}}}function Mk(t,e=null){return{type:N.Animate,styles:e,timings:t}}function Om(t,e=null){return{type:N.Sequence,steps:t,options:e}}function Pu(t){return{type:N.Style,styles:t,offset:null}}function Tk(t,e,n){return{type:N.State,name:t,styles:e,options:n}}function Nk(t,e,n=null){return{type:N.Transition,expr:t,animation:e,options:n}}function xk(t,e,n=null){return{type:N.Query,selector:t,animation:e,options:n}}function Ak(t,e){return{type:N.Stagger,timings:t,animation:e}}var Zt=class{constructor(e=0,n=0){this._onDoneFns=[],this._onStartFns=[],this._onDestroyFns=[],this._originalOnDoneFns=[],this._originalOnStartFns=[],this._started=!1,this._destroyed=!1,this._finished=!1,this._position=0,this.parentPlayer=null,this.totalTime=e+n}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(e=>e()),this._onDoneFns=[])}onStart(e){this._originalOnStartFns.push(e),this._onStartFns.push(e)}onDone(e){this._originalOnDoneFns.push(e),this._onDoneFns.push(e)}onDestroy(e){this._onDestroyFns.push(e)}hasStarted(){return this._started}init(){}play(){this.hasStarted()||(this._onStart(),this.triggerMicrotask()),this._started=!0}triggerMicrotask(){queueMicrotask(()=>this._onFinish())}_onStart(){this._onStartFns.forEach(e=>e()),this._onStartFns=[]}pause(){}restart(){}finish(){this._onFinish()}destroy(){this._destroyed||(this._destroyed=!0,this.hasStarted()||this._onStart(),this.finish(),this._onDestroyFns.forEach(e=>e()),this._onDestroyFns=[])}reset(){this._started=!1,this._finished=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}setPosition(e){this._position=this.totalTime?e*this.totalTime:1}getPosition(){return this.totalTime?this._position/this.totalTime:1}triggerCallback(e){let n=e=="start"?this._onStartFns:this._onDoneFns;n.forEach(r=>r()),n.length=0}},$r=class{constructor(e){this._onDoneFns=[],this._onStartFns=[],this._finished=!1,this._started=!1,this._destroyed=!1,this._onDestroyFns=[],this.parentPlayer=null,this.totalTime=0,this.players=e;let n=0,r=0,o=0,i=this.players.length;i==0?queueMicrotask(()=>this._onFinish()):this.players.forEach(s=>{s.onDone(()=>{++n==i&&this._onFinish()}),s.onDestroy(()=>{++r==i&&this._onDestroy()}),s.onStart(()=>{++o==i&&this._onStart()})}),this.totalTime=this.players.reduce((s,a)=>Math.max(s,a.totalTime),0)}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(e=>e()),this._onDoneFns=[])}init(){this.players.forEach(e=>e.init())}onStart(e){this._onStartFns.push(e)}_onStart(){this.hasStarted()||(this._started=!0,this._onStartFns.forEach(e=>e()),this._onStartFns=[])}onDone(e){this._onDoneFns.push(e)}onDestroy(e){this._onDestroyFns.push(e)}hasStarted(){return this._started}play(){this.parentPlayer||this.init(),this._onStart(),this.players.forEach(e=>e.play())}pause(){this.players.forEach(e=>e.pause())}restart(){this.players.forEach(e=>e.restart())}finish(){this._onFinish(),this.players.forEach(e=>e.finish())}destroy(){this._onDestroy()}_onDestroy(){this._destroyed||(this._destroyed=!0,this._onFinish(),this.players.forEach(e=>e.destroy()),this._onDestroyFns.forEach(e=>e()),this._onDestroyFns=[])}reset(){this.players.forEach(e=>e.reset()),this._destroyed=!1,this._finished=!1,this._started=!1}setPosition(e){let n=e*this.totalTime;this.players.forEach(r=>{let o=r.totalTime?Math.min(1,n/r.totalTime):1;r.setPosition(o)})}getPosition(){let e=this.players.reduce((n,r)=>n===null||r.totalTime>n.totalTime?r:n,null);return e!=null?e.getPosition():0}beforeDestroy(){this.players.forEach(e=>{e.beforeDestroy&&e.beforeDestroy()})}triggerCallback(e){let n=e=="start"?this._onStartFns:this._onDoneFns;n.forEach(r=>r()),n.length=0}},ss="!";function Pm(t){return new g(3e3,!1)}function Lb(){return new g(3100,!1)}function jb(){return new g(3101,!1)}function Vb(t){return new g(3001,!1)}function Bb(t){return new g(3003,!1)}function $b(t){return new g(3004,!1)}function Hb(t,e){return new g(3005,!1)}function Ub(){return new g(3006,!1)}function zb(){return new g(3007,!1)}function qb(t,e){return new g(3008,!1)}function Gb(t){return new g(3002,!1)}function Wb(t,e,n,r,o){return new g(3010,!1)}function Qb(){return new g(3011,!1)}function Zb(){return new g(3012,!1)}function Kb(){return new g(3200,!1)}function Yb(){return new g(3202,!1)}function Jb(){return new g(3013,!1)}function Xb(t){return new g(3014,!1)}function eC(t){return new g(3015,!1)}function tC(t){return new g(3016,!1)}function nC(t){return new g(3500,!1)}function rC(t){return new g(3501,!1)}function oC(t,e){return new g(3404,!1)}function iC(t){return new g(3502,!1)}function sC(t){return new g(3503,!1)}function aC(){return new g(3300,!1)}function lC(t){return new g(3504,!1)}function uC(t){return new g(3301,!1)}function cC(t,e){return new g(3302,!1)}function dC(t){return new g(3303,!1)}function fC(t,e){return new g(3400,!1)}function hC(t){return new g(3401,!1)}function pC(t){return new g(3402,!1)}function mC(t,e){return new g(3505,!1)}var gC=new Set(["-moz-outline-radius","-moz-outline-radius-bottomleft","-moz-outline-radius-bottomright","-moz-outline-radius-topleft","-moz-outline-radius-topright","-ms-grid-columns","-ms-grid-rows","-webkit-line-clamp","-webkit-text-fill-color","-webkit-text-stroke","-webkit-text-stroke-color","accent-color","all","backdrop-filter","background","background-color","background-position","background-size","block-size","border","border-block-end","border-block-end-color","border-block-end-width","border-block-start","border-block-start-color","border-block-start-width","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-width","border-color","border-end-end-radius","border-end-start-radius","border-image-outset","border-image-slice","border-image-width","border-inline-end","border-inline-end-color","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-width","border-left","border-left-color","border-left-width","border-radius","border-right","border-right-color","border-right-width","border-start-end-radius","border-start-start-radius","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-width","border-width","bottom","box-shadow","caret-color","clip","clip-path","color","column-count","column-gap","column-rule","column-rule-color","column-rule-width","column-width","columns","filter","flex","flex-basis","flex-grow","flex-shrink","font","font-size","font-size-adjust","font-stretch","font-variation-settings","font-weight","gap","grid-column-gap","grid-gap","grid-row-gap","grid-template-columns","grid-template-rows","height","inline-size","input-security","inset","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","left","letter-spacing","line-clamp","line-height","margin","margin-block-end","margin-block-start","margin-bottom","margin-inline-end","margin-inline-start","margin-left","margin-right","margin-top","mask","mask-border","mask-position","mask-size","max-block-size","max-height","max-inline-size","max-lines","max-width","min-block-size","min-height","min-inline-size","min-width","object-position","offset","offset-anchor","offset-distance","offset-path","offset-position","offset-rotate","opacity","order","outline","outline-color","outline-offset","outline-width","padding","padding-block-end","padding-block-start","padding-bottom","padding-inline-end","padding-inline-start","padding-left","padding-right","padding-top","perspective","perspective-origin","right","rotate","row-gap","scale","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap-coordinate","scroll-snap-destination","scrollbar-color","shape-image-threshold","shape-margin","shape-outside","tab-size","text-decoration","text-decoration-color","text-decoration-thickness","text-emphasis","text-emphasis-color","text-indent","text-shadow","text-underline-offset","top","transform","transform-origin","translate","vertical-align","visibility","width","word-spacing","z-index","zoom"]);function Kt(t){switch(t.length){case 0:return new Zt;case 1:return t[0];default:return new $r(t)}}function Ym(t,e,n=new Map,r=new Map){let o=[],i=[],s=-1,a=null;if(e.forEach(l=>{let u=l.get("offset"),c=u==s,d=c&&a||new Map;l.forEach((h,f)=>{let p=f,m=h;if(f!=="offset")switch(p=t.normalizePropertyName(p,o),m){case ss:m=n.get(f);break;case lt:m=r.get(f);break;default:m=t.normalizeStyleValue(f,p,m,o);break}d.set(p,m)}),c||i.push(d),a=d,s=u}),o.length)throw iC(o);return i}function ic(t,e,n,r){switch(e){case"start":t.onStart(()=>r(n&&Fu(n,"start",t)));break;case"done":t.onDone(()=>r(n&&Fu(n,"done",t)));break;case"destroy":t.onDestroy(()=>r(n&&Fu(n,"destroy",t)));break}}function Fu(t,e,n){let r=n.totalTime,o=!!n.disabled,i=sc(t.element,t.triggerName,t.fromState,t.toState,e||t.phaseName,r??t.totalTime,o),s=t._data;return s!=null&&(i._data=s),i}function sc(t,e,n,r,o="",i=0,s){return{element:t,triggerName:e,fromState:n,toState:r,phaseName:o,totalTime:i,disabled:!!s}}function Re(t,e,n){let r=t.get(e);return r||t.set(e,r=n),r}function Fm(t){let e=t.indexOf(":"),n=t.substring(1,e),r=t.slice(e+1);return[n,r]}var yC=typeof document>"u"?null:document.documentElement;function ac(t){let e=t.parentNode||t.host||null;return e===yC?null:e}function vC(t){return t.substring(1,6)=="ebkit"}var bn=null,Rm=!1;function DC(t){bn||(bn=EC()||{},Rm=bn.style?"WebkitAppearance"in bn.style:!1);let e=!0;return bn.style&&!vC(t)&&(e=t in bn.style,!e&&Rm&&(e="Webkit"+t.charAt(0).toUpperCase()+t.slice(1)in bn.style)),e}function kk(t){return gC.has(t)}function EC(){return typeof document<"u"?document.body:null}function Jm(t,e){for(;e;){if(e===t)return!0;e=ac(e)}return!1}function Xm(t,e,n){if(n)return Array.from(t.querySelectorAll(e));let r=t.querySelector(e);return r?[r]:[]}var eg=(()=>{class t{validateStyleProperty(n){return DC(n)}containsElement(n,r){return Jm(n,r)}getParentElement(n){return ac(n)}query(n,r,o){return Xm(n,r,o)}computeStyle(n,r,o){return o||""}animate(n,r,o,i,s,a=[],l){return new Zt(o,i)}static{this.\u0275fac=function(r){return new(r||t)}}static{this.\u0275prov=z({token:t,factory:t.\u0275fac})}}return t})(),km=class{static{this.NOOP=new eg}},Bu=class{},$u=class{normalizePropertyName(e,n){return e}normalizeStyleValue(e,n,r,o){return r}},wC=1e3,tg="{{",_C="}}",lc="ng-enter",fs="ng-leave",as="ng-trigger",hs=".ng-trigger",Lm="ng-animating",Hu=".ng-animating";function Nt(t){if(typeof t=="number")return t;let e=t.match(/^(-?[\.\d]+)(m?s)/);return!e||e.length<2?0:Uu(parseFloat(e[1]),e[2])}function Uu(t,e){switch(e){case"s":return t*wC;default:return t}}function ps(t,e,n){return t.hasOwnProperty("duration")?t:IC(t,e,n)}function IC(t,e,n){let r=/^(-?[\.\d]+)(m?s)(?:\s+(-?[\.\d]+)(m?s))?(?:\s+([-a-z]+(?:\(.+?\))?))?$/i,o,i=0,s="";if(typeof t=="string"){let a=t.match(r);if(a===null)return e.push(Pm(t)),{duration:0,delay:0,easing:""};o=Uu(parseFloat(a[1]),a[2]);let l=a[3];l!=null&&(i=Uu(parseFloat(l),a[4]));let u=a[5];u&&(s=u)}else o=t;if(!n){let a=!1,l=e.length;o<0&&(e.push(Lb()),a=!0),i<0&&(e.push(jb()),a=!0),a&&e.splice(l,0,Pm(t))}return{duration:o,delay:i,easing:s}}function bC(t){return t.length?t[0]instanceof Map?t:t.map(e=>new Map(Object.entries(e))):[]}function jm(t){return Array.isArray(t)?new Map(...t):new Map(t)}function ut(t,e,n){e.forEach((r,o)=>{let i=uc(o);n&&!n.has(o)&&n.set(o,t.style[i]),t.style[i]=r})}function Sn(t,e){e.forEach((n,r)=>{let o=uc(r);t.style[o]=""})}function Hr(t){return Array.isArray(t)?t.length==1?t[0]:Om(t):t}function CC(t,e,n){let r=e.params||{},o=ng(t);o.length&&o.forEach(i=>{r.hasOwnProperty(i)||n.push(Vb(i))})}var zu=new RegExp(`${tg}\\s*(.+?)\\s*${_C}`,"g");function ng(t){let e=[];if(typeof t=="string"){let n;for(;n=zu.exec(t);)e.push(n[1]);zu.lastIndex=0}return e}function zr(t,e,n){let r=`${t}`,o=r.replace(zu,(i,s)=>{let a=e[s];return a==null&&(n.push(Bb(s)),a=""),a.toString()});return o==r?t:o}var SC=/-+([a-z0-9])/g;function uc(t){return t.replace(SC,(...e)=>e[1].toUpperCase())}function Lk(t){return t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function MC(t,e){return t===0||e===0}function TC(t,e,n){if(n.size&&e.length){let r=e[0],o=[];if(n.forEach((i,s)=>{r.has(s)||o.push(s),r.set(s,i)}),o.length)for(let i=1;i<e.length;i++){let s=e[i];o.forEach(a=>s.set(a,cc(t,a)))}}return e}function Fe(t,e,n){switch(e.type){case N.Trigger:return t.visitTrigger(e,n);case N.State:return t.visitState(e,n);case N.Transition:return t.visitTransition(e,n);case N.Sequence:return t.visitSequence(e,n);case N.Group:return t.visitGroup(e,n);case N.Animate:return t.visitAnimate(e,n);case N.Keyframes:return t.visitKeyframes(e,n);case N.Style:return t.visitStyle(e,n);case N.Reference:return t.visitReference(e,n);case N.AnimateChild:return t.visitAnimateChild(e,n);case N.AnimateRef:return t.visitAnimateRef(e,n);case N.Query:return t.visitQuery(e,n);case N.Stagger:return t.visitStagger(e,n);default:throw $b(e.type)}}function cc(t,e){return window.getComputedStyle(t)[e]}var NC=new Set(["width","height","minWidth","minHeight","maxWidth","maxHeight","left","top","bottom","right","fontSize","outlineWidth","outlineOffset","paddingTop","paddingLeft","paddingBottom","paddingRight","marginTop","marginLeft","marginBottom","marginRight","borderRadius","borderWidth","borderTopWidth","borderLeftWidth","borderRightWidth","borderBottomWidth","textIndent","perspective"]),qu=class extends Bu{normalizePropertyName(e,n){return uc(e)}normalizeStyleValue(e,n,r,o){let i="",s=r.toString().trim();if(NC.has(n)&&r!==0&&r!=="0")if(typeof r=="number")i="px";else{let a=r.match(/^[+-]?[\d\.]+([a-z]*)$/);a&&a[1].length==0&&o.push(Hb(e,r))}return s+i}};var ms="*";function xC(t,e){let n=[];return typeof t=="string"?t.split(/\s*,\s*/).forEach(r=>AC(r,n,e)):n.push(t),n}function AC(t,e,n){if(t[0]==":"){let l=OC(t,n);if(typeof l=="function"){e.push(l);return}t=l}let r=t.match(/^(\*|[-\w]+)\s*(<?[=-]>)\s*(\*|[-\w]+)$/);if(r==null||r.length<4)return n.push(eC(t)),e;let o=r[1],i=r[2],s=r[3];e.push(Vm(o,s));let a=o==ms&&s==ms;i[0]=="<"&&!a&&e.push(Vm(s,o))}function OC(t,e){switch(t){case":enter":return"void => *";case":leave":return"* => void";case":increment":return(n,r)=>parseFloat(r)>parseFloat(n);case":decrement":return(n,r)=>parseFloat(r)<parseFloat(n);default:return e.push(tC(t)),"* => *"}}var ls=new Set(["true","1"]),us=new Set(["false","0"]);function Vm(t,e){let n=ls.has(t)||us.has(t),r=ls.has(e)||us.has(e);return(o,i)=>{let s=t==ms||t==o,a=e==ms||e==i;return!s&&n&&typeof o=="boolean"&&(s=o?ls.has(t):us.has(t)),!a&&r&&typeof i=="boolean"&&(a=i?ls.has(e):us.has(e)),s&&a}}var rg=":self",PC=new RegExp(`s*${rg}s*,?`,"g");function dc(t,e,n,r){return new Gu(t).build(e,n,r)}var Bm="",Gu=class{constructor(e){this._driver=e}build(e,n,r){let o=new Wu(n);return this._resetContextStyleTimingState(o),Fe(this,Hr(e),o)}_resetContextStyleTimingState(e){e.currentQuerySelector=Bm,e.collectedStyles=new Map,e.collectedStyles.set(Bm,new Map),e.currentTime=0}visitTrigger(e,n){let r=n.queryCount=0,o=n.depCount=0,i=[],s=[];return e.name.charAt(0)=="@"&&n.errors.push(Ub()),e.definitions.forEach(a=>{if(this._resetContextStyleTimingState(n),a.type==N.State){let l=a,u=l.name;u.toString().split(/\s*,\s*/).forEach(c=>{l.name=c,i.push(this.visitState(l,n))}),l.name=u}else if(a.type==N.Transition){let l=this.visitTransition(a,n);r+=l.queryCount,o+=l.depCount,s.push(l)}else n.errors.push(zb())}),{type:N.Trigger,name:e.name,states:i,transitions:s,queryCount:r,depCount:o,options:null}}visitState(e,n){let r=this.visitStyle(e.styles,n),o=e.options&&e.options.params||null;if(r.containsDynamicStyles){let i=new Set,s=o||{};r.styles.forEach(a=>{a instanceof Map&&a.forEach(l=>{ng(l).forEach(u=>{s.hasOwnProperty(u)||i.add(u)})})}),i.size&&n.errors.push(qb(e.name,[...i.values()]))}return{type:N.State,name:e.name,style:r,options:o?{params:o}:null}}visitTransition(e,n){n.queryCount=0,n.depCount=0;let r=Fe(this,Hr(e.animation),n),o=xC(e.expr,n.errors);return{type:N.Transition,matchers:o,animation:r,queryCount:n.queryCount,depCount:n.depCount,options:Cn(e.options)}}visitSequence(e,n){return{type:N.Sequence,steps:e.steps.map(r=>Fe(this,r,n)),options:Cn(e.options)}}visitGroup(e,n){let r=n.currentTime,o=0,i=e.steps.map(s=>{n.currentTime=r;let a=Fe(this,s,n);return o=Math.max(o,n.currentTime),a});return n.currentTime=o,{type:N.Group,steps:i,options:Cn(e.options)}}visitAnimate(e,n){let r=LC(e.timings,n.errors);n.currentAnimateTimings=r;let o,i=e.styles?e.styles:Pu({});if(i.type==N.Keyframes)o=this.visitKeyframes(i,n);else{let s=e.styles,a=!1;if(!s){a=!0;let u={};r.easing&&(u.easing=r.easing),s=Pu(u)}n.currentTime+=r.duration+r.delay;let l=this.visitStyle(s,n);l.isEmptyStep=a,o=l}return n.currentAnimateTimings=null,{type:N.Animate,timings:r,style:o,options:null}}visitStyle(e,n){let r=this._makeStyleAst(e,n);return this._validateStyleAst(r,n),r}_makeStyleAst(e,n){let r=[],o=Array.isArray(e.styles)?e.styles:[e.styles];for(let a of o)typeof a=="string"?a===lt?r.push(a):n.errors.push(Gb(a)):r.push(new Map(Object.entries(a)));let i=!1,s=null;return r.forEach(a=>{if(a instanceof Map&&(a.has("easing")&&(s=a.get("easing"),a.delete("easing")),!i)){for(let l of a.values())if(l.toString().indexOf(tg)>=0){i=!0;break}}}),{type:N.Style,styles:r,easing:s,offset:e.offset,containsDynamicStyles:i,options:null}}_validateStyleAst(e,n){let r=n.currentAnimateTimings,o=n.currentTime,i=n.currentTime;r&&i>0&&(i-=r.duration+r.delay),e.styles.forEach(s=>{typeof s!="string"&&s.forEach((a,l)=>{let u=n.collectedStyles.get(n.currentQuerySelector),c=u.get(l),d=!0;c&&(i!=o&&i>=c.startTime&&o<=c.endTime&&(n.errors.push(Wb(l,c.startTime,c.endTime,i,o)),d=!1),i=c.startTime),d&&u.set(l,{startTime:i,endTime:o}),n.options&&CC(a,n.options,n.errors)})})}visitKeyframes(e,n){let r={type:N.Keyframes,styles:[],options:null};if(!n.currentAnimateTimings)return n.errors.push(Qb()),r;let o=1,i=0,s=[],a=!1,l=!1,u=0,c=e.steps.map(E=>{let T=this._makeStyleAst(E,n),k=T.offset!=null?T.offset:kC(T.styles),V=0;return k!=null&&(i++,V=T.offset=k),l=l||V<0||V>1,a=a||V<u,u=V,s.push(V),T});l&&n.errors.push(Zb()),a&&n.errors.push(Kb());let d=e.steps.length,h=0;i>0&&i<d?n.errors.push(Yb()):i==0&&(h=o/(d-1));let f=d-1,p=n.currentTime,m=n.currentAnimateTimings,D=m.duration;return c.forEach((E,T)=>{let k=h>0?T==f?1:h*T:s[T],V=k*D;n.currentTime=p+m.delay+V,m.duration=V,this._validateStyleAst(E,n),E.offset=k,r.styles.push(E)}),r}visitReference(e,n){return{type:N.Reference,animation:Fe(this,Hr(e.animation),n),options:Cn(e.options)}}visitAnimateChild(e,n){return n.depCount++,{type:N.AnimateChild,options:Cn(e.options)}}visitAnimateRef(e,n){return{type:N.AnimateRef,animation:this.visitReference(e.animation,n),options:Cn(e.options)}}visitQuery(e,n){let r=n.currentQuerySelector,o=e.options||{};n.queryCount++,n.currentQuery=e;let[i,s]=FC(e.selector);n.currentQuerySelector=r.length?r+" "+i:i,Re(n.collectedStyles,n.currentQuerySelector,new Map);let a=Fe(this,Hr(e.animation),n);return n.currentQuery=null,n.currentQuerySelector=r,{type:N.Query,selector:i,limit:o.limit||0,optional:!!o.optional,includeSelf:s,animation:a,originalSelector:e.selector,options:Cn(e.options)}}visitStagger(e,n){n.currentQuery||n.errors.push(Jb());let r=e.timings==="full"?{duration:0,delay:0,easing:"full"}:ps(e.timings,n.errors,!0);return{type:N.Stagger,animation:Fe(this,Hr(e.animation),n),timings:r,options:null}}};function FC(t){let e=!!t.split(/\s*,\s*/).find(n=>n==rg);return e&&(t=t.replace(PC,"")),t=t.replace(/@\*/g,hs).replace(/@\w+/g,n=>hs+"-"+n.slice(1)).replace(/:animating/g,Hu),[t,e]}function RC(t){return t?xe({},t):null}var Wu=class{constructor(e){this.errors=e,this.queryCount=0,this.depCount=0,this.currentTransition=null,this.currentQuery=null,this.currentQuerySelector=null,this.currentAnimateTimings=null,this.currentTime=0,this.collectedStyles=new Map,this.options=null,this.unsupportedCSSPropertiesFound=new Set}};function kC(t){if(typeof t=="string")return null;let e=null;if(Array.isArray(t))t.forEach(n=>{if(n instanceof Map&&n.has("offset")){let r=n;e=parseFloat(r.get("offset")),r.delete("offset")}});else if(t instanceof Map&&t.has("offset")){let n=t;e=parseFloat(n.get("offset")),n.delete("offset")}return e}function LC(t,e){if(t.hasOwnProperty("duration"))return t;if(typeof t=="number"){let i=ps(t,e).duration;return Ru(i,0,"")}let n=t;if(n.split(/\s+/).some(i=>i.charAt(0)=="{"&&i.charAt(1)=="{")){let i=Ru(0,0,"");return i.dynamic=!0,i.strValue=n,i}let o=ps(n,e);return Ru(o.duration,o.delay,o.easing)}function Cn(t){return t?(t=xe({},t),t.params&&(t.params=RC(t.params))):t={},t}function Ru(t,e,n){return{duration:t,delay:e,easing:n}}function fc(t,e,n,r,o,i,s=null,a=!1){return{type:1,element:t,keyframes:e,preStyleProps:n,postStyleProps:r,duration:o,delay:i,totalTime:o+i,easing:s,subTimeline:a}}var ar=class{constructor(){this._map=new Map}get(e){return this._map.get(e)||[]}append(e,n){let r=this._map.get(e);r||this._map.set(e,r=[]),r.push(...n)}has(e){return this._map.has(e)}clear(){this._map.clear()}},jC=1,VC=":enter",BC=new RegExp(VC,"g"),$C=":leave",HC=new RegExp($C,"g");function hc(t,e,n,r,o,i=new Map,s=new Map,a,l,u=[]){return new Qu().buildKeyframes(t,e,n,r,o,i,s,a,l,u)}var Qu=class{buildKeyframes(e,n,r,o,i,s,a,l,u,c=[]){u=u||new ar;let d=new Zu(e,n,u,o,i,c,[]);d.options=l;let h=l.delay?Nt(l.delay):0;d.currentTimeline.delayNextStep(h),d.currentTimeline.setStyles([s],null,d.errors,l),Fe(this,r,d);let f=d.timelines.filter(p=>p.containsAnimation());if(f.length&&a.size){let p;for(let m=f.length-1;m>=0;m--){let D=f[m];if(D.element===n){p=D;break}}p&&!p.allowOnlyTimelineStyles()&&p.setStyles([a],null,d.errors,l)}return f.length?f.map(p=>p.buildKeyframes()):[fc(n,[],[],[],0,h,"",!1)]}visitTrigger(e,n){}visitState(e,n){}visitTransition(e,n){}visitAnimateChild(e,n){let r=n.subInstructions.get(n.element);if(r){let o=n.createSubContext(e.options),i=n.currentTimeline.currentTime,s=this._visitSubInstructions(r,o,o.options);i!=s&&n.transformIntoNewTimeline(s)}n.previousNode=e}visitAnimateRef(e,n){let r=n.createSubContext(e.options);r.transformIntoNewTimeline(),this._applyAnimationRefDelays([e.options,e.animation.options],n,r),this.visitReference(e.animation,r),n.transformIntoNewTimeline(r.currentTimeline.currentTime),n.previousNode=e}_applyAnimationRefDelays(e,n,r){for(let o of e){let i=o?.delay;if(i){let s=typeof i=="number"?i:Nt(zr(i,o?.params??{},n.errors));r.delayNextStep(s)}}}_visitSubInstructions(e,n,r){let i=n.currentTimeline.currentTime,s=r.duration!=null?Nt(r.duration):null,a=r.delay!=null?Nt(r.delay):null;return s!==0&&e.forEach(l=>{let u=n.appendInstructionToTimeline(l,s,a);i=Math.max(i,u.duration+u.delay)}),i}visitReference(e,n){n.updateOptions(e.options,!0),Fe(this,e.animation,n),n.previousNode=e}visitSequence(e,n){let r=n.subContextCount,o=n,i=e.options;if(i&&(i.params||i.delay)&&(o=n.createSubContext(i),o.transformIntoNewTimeline(),i.delay!=null)){o.previousNode.type==N.Style&&(o.currentTimeline.snapshotCurrentStyles(),o.previousNode=gs);let s=Nt(i.delay);o.delayNextStep(s)}e.steps.length&&(e.steps.forEach(s=>Fe(this,s,o)),o.currentTimeline.applyStylesToKeyframe(),o.subContextCount>r&&o.transformIntoNewTimeline()),n.previousNode=e}visitGroup(e,n){let r=[],o=n.currentTimeline.currentTime,i=e.options&&e.options.delay?Nt(e.options.delay):0;e.steps.forEach(s=>{let a=n.createSubContext(e.options);i&&a.delayNextStep(i),Fe(this,s,a),o=Math.max(o,a.currentTimeline.currentTime),r.push(a.currentTimeline)}),r.forEach(s=>n.currentTimeline.mergeTimelineCollectedStyles(s)),n.transformIntoNewTimeline(o),n.previousNode=e}_visitTiming(e,n){if(e.dynamic){let r=e.strValue,o=n.params?zr(r,n.params,n.errors):r;return ps(o,n.errors)}else return{duration:e.duration,delay:e.delay,easing:e.easing}}visitAnimate(e,n){let r=n.currentAnimateTimings=this._visitTiming(e.timings,n),o=n.currentTimeline;r.delay&&(n.incrementTime(r.delay),o.snapshotCurrentStyles());let i=e.style;i.type==N.Keyframes?this.visitKeyframes(i,n):(n.incrementTime(r.duration),this.visitStyle(i,n),o.applyStylesToKeyframe()),n.currentAnimateTimings=null,n.previousNode=e}visitStyle(e,n){let r=n.currentTimeline,o=n.currentAnimateTimings;!o&&r.hasCurrentStyleProperties()&&r.forwardFrame();let i=o&&o.easing||e.easing;e.isEmptyStep?r.applyEmptyStep(i):r.setStyles(e.styles,i,n.errors,n.options),n.previousNode=e}visitKeyframes(e,n){let r=n.currentAnimateTimings,o=n.currentTimeline.duration,i=r.duration,a=n.createSubContext().currentTimeline;a.easing=r.easing,e.styles.forEach(l=>{let u=l.offset||0;a.forwardTime(u*i),a.setStyles(l.styles,l.easing,n.errors,n.options),a.applyStylesToKeyframe()}),n.currentTimeline.mergeTimelineCollectedStyles(a),n.transformIntoNewTimeline(o+i),n.previousNode=e}visitQuery(e,n){let r=n.currentTimeline.currentTime,o=e.options||{},i=o.delay?Nt(o.delay):0;i&&(n.previousNode.type===N.Style||r==0&&n.currentTimeline.hasCurrentStyleProperties())&&(n.currentTimeline.snapshotCurrentStyles(),n.previousNode=gs);let s=r,a=n.invokeQuery(e.selector,e.originalSelector,e.limit,e.includeSelf,!!o.optional,n.errors);n.currentQueryTotal=a.length;let l=null;a.forEach((u,c)=>{n.currentQueryIndex=c;let d=n.createSubContext(e.options,u);i&&d.delayNextStep(i),u===n.element&&(l=d.currentTimeline),Fe(this,e.animation,d),d.currentTimeline.applyStylesToKeyframe();let h=d.currentTimeline.currentTime;s=Math.max(s,h)}),n.currentQueryIndex=0,n.currentQueryTotal=0,n.transformIntoNewTimeline(s),l&&(n.currentTimeline.mergeTimelineCollectedStyles(l),n.currentTimeline.snapshotCurrentStyles()),n.previousNode=e}visitStagger(e,n){let r=n.parentContext,o=n.currentTimeline,i=e.timings,s=Math.abs(i.duration),a=s*(n.currentQueryTotal-1),l=s*n.currentQueryIndex;switch(i.duration<0?"reverse":i.easing){case"reverse":l=a-l;break;case"full":l=r.currentStaggerTime;break}let c=n.currentTimeline;l&&c.delayNextStep(l);let d=c.currentTime;Fe(this,e.animation,n),n.previousNode=e,r.currentStaggerTime=o.currentTime-d+(o.startTime-r.currentTimeline.startTime)}},gs={},Zu=class t{constructor(e,n,r,o,i,s,a,l){this._driver=e,this.element=n,this.subInstructions=r,this._enterClassName=o,this._leaveClassName=i,this.errors=s,this.timelines=a,this.parentContext=null,this.currentAnimateTimings=null,this.previousNode=gs,this.subContextCount=0,this.options={},this.currentQueryIndex=0,this.currentQueryTotal=0,this.currentStaggerTime=0,this.currentTimeline=l||new ys(this._driver,n,0),a.push(this.currentTimeline)}get params(){return this.options.params}updateOptions(e,n){if(!e)return;let r=e,o=this.options;r.duration!=null&&(o.duration=Nt(r.duration)),r.delay!=null&&(o.delay=Nt(r.delay));let i=r.params;if(i){let s=o.params;s||(s=this.options.params={}),Object.keys(i).forEach(a=>{(!n||!s.hasOwnProperty(a))&&(s[a]=zr(i[a],s,this.errors))})}}_copyOptions(){let e={};if(this.options){let n=this.options.params;if(n){let r=e.params={};Object.keys(n).forEach(o=>{r[o]=n[o]})}}return e}createSubContext(e=null,n,r){let o=n||this.element,i=new t(this._driver,o,this.subInstructions,this._enterClassName,this._leaveClassName,this.errors,this.timelines,this.currentTimeline.fork(o,r||0));return i.previousNode=this.previousNode,i.currentAnimateTimings=this.currentAnimateTimings,i.options=this._copyOptions(),i.updateOptions(e),i.currentQueryIndex=this.currentQueryIndex,i.currentQueryTotal=this.currentQueryTotal,i.parentContext=this,this.subContextCount++,i}transformIntoNewTimeline(e){return this.previousNode=gs,this.currentTimeline=this.currentTimeline.fork(this.element,e),this.timelines.push(this.currentTimeline),this.currentTimeline}appendInstructionToTimeline(e,n,r){let o={duration:n??e.duration,delay:this.currentTimeline.currentTime+(r??0)+e.delay,easing:""},i=new Ku(this._driver,e.element,e.keyframes,e.preStyleProps,e.postStyleProps,o,e.stretchStartingKeyframe);return this.timelines.push(i),o}incrementTime(e){this.currentTimeline.forwardTime(this.currentTimeline.duration+e)}delayNextStep(e){e>0&&this.currentTimeline.delayNextStep(e)}invokeQuery(e,n,r,o,i,s){let a=[];if(o&&a.push(this.element),e.length>0){e=e.replace(BC,"."+this._enterClassName),e=e.replace(HC,"."+this._leaveClassName);let l=r!=1,u=this._driver.query(this.element,e,l);r!==0&&(u=r<0?u.slice(u.length+r,u.length):u.slice(0,r)),a.push(...u)}return!i&&a.length==0&&s.push(Xb(n)),a}},ys=class t{constructor(e,n,r,o){this._driver=e,this.element=n,this.startTime=r,this._elementTimelineStylesLookup=o,this.duration=0,this.easing=null,this._previousKeyframe=new Map,this._currentKeyframe=new Map,this._keyframes=new Map,this._styleSummary=new Map,this._localTimelineStyles=new Map,this._pendingStyles=new Map,this._backFill=new Map,this._currentEmptyStepKeyframe=null,this._elementTimelineStylesLookup||(this._elementTimelineStylesLookup=new Map),this._globalTimelineStyles=this._elementTimelineStylesLookup.get(n),this._globalTimelineStyles||(this._globalTimelineStyles=this._localTimelineStyles,this._elementTimelineStylesLookup.set(n,this._localTimelineStyles)),this._loadKeyframe()}containsAnimation(){switch(this._keyframes.size){case 0:return!1;case 1:return this.hasCurrentStyleProperties();default:return!0}}hasCurrentStyleProperties(){return this._currentKeyframe.size>0}get currentTime(){return this.startTime+this.duration}delayNextStep(e){let n=this._keyframes.size===1&&this._pendingStyles.size;this.duration||n?(this.forwardTime(this.currentTime+e),n&&this.snapshotCurrentStyles()):this.startTime+=e}fork(e,n){return this.applyStylesToKeyframe(),new t(this._driver,e,n||this.currentTime,this._elementTimelineStylesLookup)}_loadKeyframe(){this._currentKeyframe&&(this._previousKeyframe=this._currentKeyframe),this._currentKeyframe=this._keyframes.get(this.duration),this._currentKeyframe||(this._currentKeyframe=new Map,this._keyframes.set(this.duration,this._currentKeyframe))}forwardFrame(){this.duration+=jC,this._loadKeyframe()}forwardTime(e){this.applyStylesToKeyframe(),this.duration=e,this._loadKeyframe()}_updateStyle(e,n){this._localTimelineStyles.set(e,n),this._globalTimelineStyles.set(e,n),this._styleSummary.set(e,{time:this.currentTime,value:n})}allowOnlyTimelineStyles(){return this._currentEmptyStepKeyframe!==this._currentKeyframe}applyEmptyStep(e){e&&this._previousKeyframe.set("easing",e);for(let[n,r]of this._globalTimelineStyles)this._backFill.set(n,r||lt),this._currentKeyframe.set(n,lt);this._currentEmptyStepKeyframe=this._currentKeyframe}setStyles(e,n,r,o){n&&this._previousKeyframe.set("easing",n);let i=o&&o.params||{},s=UC(e,this._globalTimelineStyles);for(let[a,l]of s){let u=zr(l,i,r);this._pendingStyles.set(a,u),this._localTimelineStyles.has(a)||this._backFill.set(a,this._globalTimelineStyles.get(a)??lt),this._updateStyle(a,u)}}applyStylesToKeyframe(){this._pendingStyles.size!=0&&(this._pendingStyles.forEach((e,n)=>{this._currentKeyframe.set(n,e)}),this._pendingStyles.clear(),this._localTimelineStyles.forEach((e,n)=>{this._currentKeyframe.has(n)||this._currentKeyframe.set(n,e)}))}snapshotCurrentStyles(){for(let[e,n]of this._localTimelineStyles)this._pendingStyles.set(e,n),this._updateStyle(e,n)}getFinalKeyframe(){return this._keyframes.get(this.duration)}get properties(){let e=[];for(let n in this._currentKeyframe)e.push(n);return e}mergeTimelineCollectedStyles(e){e._styleSummary.forEach((n,r)=>{let o=this._styleSummary.get(r);(!o||n.time>o.time)&&this._updateStyle(r,n.value)})}buildKeyframes(){this.applyStylesToKeyframe();let e=new Set,n=new Set,r=this._keyframes.size===1&&this.duration===0,o=[];this._keyframes.forEach((a,l)=>{let u=new Map([...this._backFill,...a]);u.forEach((c,d)=>{c===ss?e.add(d):c===lt&&n.add(d)}),r||u.set("offset",l/this.duration),o.push(u)});let i=[...e.values()],s=[...n.values()];if(r){let a=o[0],l=new Map(a);a.set("offset",0),l.set("offset",1),o=[a,l]}return fc(this.element,o,i,s,this.duration,this.startTime,this.easing,!1)}},Ku=class extends ys{constructor(e,n,r,o,i,s,a=!1){super(e,n,s.delay),this.keyframes=r,this.preStyleProps=o,this.postStyleProps=i,this._stretchStartingKeyframe=a,this.timings={duration:s.duration,delay:s.delay,easing:s.easing}}containsAnimation(){return this.keyframes.length>1}buildKeyframes(){let e=this.keyframes,{delay:n,duration:r,easing:o}=this.timings;if(this._stretchStartingKeyframe&&n){let i=[],s=r+n,a=n/s,l=new Map(e[0]);l.set("offset",0),i.push(l);let u=new Map(e[0]);u.set("offset",$m(a)),i.push(u);let c=e.length-1;for(let d=1;d<=c;d++){let h=new Map(e[d]),f=h.get("offset"),p=n+f*r;h.set("offset",$m(p/s)),i.push(h)}r=s,n=0,o="",e=i}return fc(this.element,e,this.preStyleProps,this.postStyleProps,r,n,o,!0)}};function $m(t,e=3){let n=Math.pow(10,e-1);return Math.round(t*n)/n}function UC(t,e){let n=new Map,r;return t.forEach(o=>{if(o==="*"){r??=e.keys();for(let i of r)n.set(i,lt)}else for(let[i,s]of o)n.set(i,s)}),n}function Hm(t,e,n,r,o,i,s,a,l,u,c,d,h){return{type:0,element:t,triggerName:e,isRemovalTransition:o,fromState:n,fromStyles:i,toState:r,toStyles:s,timelines:a,queriedElements:l,preStyleProps:u,postStyleProps:c,totalTime:d,errors:h}}var ku={},vs=class{constructor(e,n,r){this._triggerName=e,this.ast=n,this._stateStyles=r}match(e,n,r,o){return zC(this.ast.matchers,e,n,r,o)}buildStyles(e,n,r){let o=this._stateStyles.get("*");return e!==void 0&&(o=this._stateStyles.get(e?.toString())||o),o?o.buildStyles(n,r):new Map}build(e,n,r,o,i,s,a,l,u,c){let d=[],h=this.ast.options&&this.ast.options.params||ku,f=a&&a.params||ku,p=this.buildStyles(r,f,d),m=l&&l.params||ku,D=this.buildStyles(o,m,d),E=new Set,T=new Map,k=new Map,V=o==="void",de={params:og(m,h),delay:this.ast.options?.delay},Y=c?[]:hc(e,n,this.ast.animation,i,s,p,D,de,u,d),J=0;return Y.forEach(se=>{J=Math.max(se.duration+se.delay,J)}),d.length?Hm(n,this._triggerName,r,o,V,p,D,[],[],T,k,J,d):(Y.forEach(se=>{let ct=se.element,xt=Re(T,ct,new Set);se.preStyleProps.forEach(Yt=>xt.add(Yt));let pc=Re(k,ct,new Set);se.postStyleProps.forEach(Yt=>pc.add(Yt)),ct!==n&&E.add(ct)}),Hm(n,this._triggerName,r,o,V,p,D,Y,[...E.values()],T,k,J))}};function zC(t,e,n,r,o){return t.some(i=>i(e,n,r,o))}function og(t,e){let n=xe({},e);return Object.entries(t).forEach(([r,o])=>{o!=null&&(n[r]=o)}),n}var Yu=class{constructor(e,n,r){this.styles=e,this.defaultParams=n,this.normalizer=r}buildStyles(e,n){let r=new Map,o=og(e,this.defaultParams);return this.styles.styles.forEach(i=>{typeof i!="string"&&i.forEach((s,a)=>{s&&(s=zr(s,o,n));let l=this.normalizer.normalizePropertyName(a,n);s=this.normalizer.normalizeStyleValue(a,l,s,n),r.set(a,s)})}),r}};function qC(t,e,n){return new Ju(t,e,n)}var Ju=class{constructor(e,n,r){this.name=e,this.ast=n,this._normalizer=r,this.transitionFactories=[],this.states=new Map,n.states.forEach(o=>{let i=o.options&&o.options.params||{};this.states.set(o.name,new Yu(o.style,i,r))}),Um(this.states,"true","1"),Um(this.states,"false","0"),n.transitions.forEach(o=>{this.transitionFactories.push(new vs(e,o,this.states))}),this.fallbackTransition=GC(e,this.states,this._normalizer)}get containsQueries(){return this.ast.queryCount>0}matchTransition(e,n,r,o){return this.transitionFactories.find(s=>s.match(e,n,r,o))||null}matchStyles(e,n,r){return this.fallbackTransition.buildStyles(e,n,r)}};function GC(t,e,n){let r=[(s,a)=>!0],o={type:N.Sequence,steps:[],options:null},i={type:N.Transition,animation:o,matchers:r,options:null,queryCount:0,depCount:0};return new vs(t,i,e)}function Um(t,e,n){t.has(e)?t.has(n)||t.set(n,t.get(e)):t.has(n)&&t.set(e,t.get(n))}var WC=new ar,Xu=class{constructor(e,n,r){this.bodyNode=e,this._driver=n,this._normalizer=r,this._animations=new Map,this._playersById=new Map,this.players=[]}register(e,n){let r=[],o=[],i=dc(this._driver,n,r,o);if(r.length)throw sC(r);o.length&&void 0,this._animations.set(e,i)}_buildPlayer(e,n,r){let o=e.element,i=Ym(this._normalizer,e.keyframes,n,r);return this._driver.animate(o,i,e.duration,e.delay,e.easing,[],!0)}create(e,n,r={}){let o=[],i=this._animations.get(e),s,a=new Map;if(i?(s=hc(this._driver,n,i,lc,fs,new Map,new Map,r,WC,o),s.forEach(c=>{let d=Re(a,c.element,new Map);c.postStyleProps.forEach(h=>d.set(h,null))})):(o.push(aC()),s=[]),o.length)throw lC(o);a.forEach((c,d)=>{c.forEach((h,f)=>{c.set(f,this._driver.computeStyle(d,f,lt))})});let l=s.map(c=>{let d=a.get(c.element);return this._buildPlayer(c,new Map,d)}),u=Kt(l);return this._playersById.set(e,u),u.onDestroy(()=>this.destroy(e)),this.players.push(u),u}destroy(e){let n=this._getPlayer(e);n.destroy(),this._playersById.delete(e);let r=this.players.indexOf(n);r>=0&&this.players.splice(r,1)}_getPlayer(e){let n=this._playersById.get(e);if(!n)throw uC(e);return n}listen(e,n,r,o){let i=sc(n,"","","");return ic(this._getPlayer(e),r,i,o),()=>{}}command(e,n,r,o){if(r=="register"){this.register(e,o[0]);return}if(r=="create"){let s=o[0]||{};this.create(e,n,s);return}let i=this._getPlayer(e);switch(r){case"play":i.play();break;case"pause":i.pause();break;case"reset":i.reset();break;case"restart":i.restart();break;case"finish":i.finish();break;case"init":i.init();break;case"setPosition":i.setPosition(parseFloat(o[0]));break;case"destroy":this.destroy(e);break}}},zm="ng-animate-queued",QC=".ng-animate-queued",Lu="ng-animate-disabled",ZC=".ng-animate-disabled",KC="ng-star-inserted",YC=".ng-star-inserted",JC=[],ig={namespaceId:"",setForRemoval:!1,setForMove:!1,hasAnimation:!1,removedBeforeQueried:!1},XC={namespaceId:"",setForMove:!1,setForRemoval:!1,hasAnimation:!1,removedBeforeQueried:!0},Ke="__ng_removed",qr=class{get params(){return this.options.params}constructor(e,n=""){this.namespaceId=n;let r=e&&e.hasOwnProperty("value"),o=r?e.value:e;if(this.value=tS(o),r){let i=e,{value:s}=i,a=vc(i,["value"]);this.options=a}else this.options={};this.options.params||(this.options.params={})}absorbOptions(e){let n=e.params;if(n){let r=this.options.params;Object.keys(n).forEach(o=>{r[o]==null&&(r[o]=n[o])})}}},Ur="void",ju=new qr(Ur),ec=class{constructor(e,n,r){this.id=e,this.hostElement=n,this._engine=r,this.players=[],this._triggers=new Map,this._queue=[],this._elementListeners=new Map,this._hostClassName="ng-tns-"+e,He(n,this._hostClassName)}listen(e,n,r,o){if(!this._triggers.has(n))throw cC(r,n);if(r==null||r.length==0)throw dC(n);if(!nS(r))throw fC(r,n);let i=Re(this._elementListeners,e,[]),s={name:n,phase:r,callback:o};i.push(s);let a=Re(this._engine.statesByElement,e,new Map);return a.has(n)||(He(e,as),He(e,as+"-"+n),a.set(n,ju)),()=>{this._engine.afterFlush(()=>{let l=i.indexOf(s);l>=0&&i.splice(l,1),this._triggers.has(n)||a.delete(n)})}}register(e,n){return this._triggers.has(e)?!1:(this._triggers.set(e,n),!0)}_getTrigger(e){let n=this._triggers.get(e);if(!n)throw hC(e);return n}trigger(e,n,r,o=!0){let i=this._getTrigger(n),s=new Gr(this.id,n,e),a=this._engine.statesByElement.get(e);a||(He(e,as),He(e,as+"-"+n),this._engine.statesByElement.set(e,a=new Map));let l=a.get(n),u=new qr(r,this.id);if(!(r&&r.hasOwnProperty("value"))&&l&&u.absorbOptions(l.options),a.set(n,u),l||(l=ju),!(u.value===Ur)&&l.value===u.value){if(!iS(l.params,u.params)){let m=[],D=i.matchStyles(l.value,l.params,m),E=i.matchStyles(u.value,u.params,m);m.length?this._engine.reportError(m):this._engine.afterFlush(()=>{Sn(e,D),ut(e,E)})}return}let h=Re(this._engine.playersByElement,e,[]);h.forEach(m=>{m.namespaceId==this.id&&m.triggerName==n&&m.queued&&m.destroy()});let f=i.matchTransition(l.value,u.value,e,u.params),p=!1;if(!f){if(!o)return;f=i.fallbackTransition,p=!0}return this._engine.totalQueuedPlayers++,this._queue.push({element:e,triggerName:n,transition:f,fromState:l,toState:u,player:s,isFallbackTransition:p}),p||(He(e,zm),s.onStart(()=>{sr(e,zm)})),s.onDone(()=>{let m=this.players.indexOf(s);m>=0&&this.players.splice(m,1);let D=this._engine.playersByElement.get(e);if(D){let E=D.indexOf(s);E>=0&&D.splice(E,1)}}),this.players.push(s),h.push(s),s}deregister(e){this._triggers.delete(e),this._engine.statesByElement.forEach(n=>n.delete(e)),this._elementListeners.forEach((n,r)=>{this._elementListeners.set(r,n.filter(o=>o.name!=e))})}clearElementCache(e){this._engine.statesByElement.delete(e),this._elementListeners.delete(e);let n=this._engine.playersByElement.get(e);n&&(n.forEach(r=>r.destroy()),this._engine.playersByElement.delete(e))}_signalRemovalForInnerTriggers(e,n){let r=this._engine.driver.query(e,hs,!0);r.forEach(o=>{if(o[Ke])return;let i=this._engine.fetchNamespacesByElement(o);i.size?i.forEach(s=>s.triggerLeaveAnimation(o,n,!1,!0)):this.clearElementCache(o)}),this._engine.afterFlushAnimationsDone(()=>r.forEach(o=>this.clearElementCache(o)))}triggerLeaveAnimation(e,n,r,o){let i=this._engine.statesByElement.get(e),s=new Map;if(i){let a=[];if(i.forEach((l,u)=>{if(s.set(u,l.value),this._triggers.has(u)){let c=this.trigger(e,u,Ur,o);c&&a.push(c)}}),a.length)return this._engine.markElementAsRemoved(this.id,e,!0,n,s),r&&Kt(a).onDone(()=>this._engine.processLeaveNode(e)),!0}return!1}prepareLeaveAnimationListeners(e){let n=this._elementListeners.get(e),r=this._engine.statesByElement.get(e);if(n&&r){let o=new Set;n.forEach(i=>{let s=i.name;if(o.has(s))return;o.add(s);let l=this._triggers.get(s).fallbackTransition,u=r.get(s)||ju,c=new qr(Ur),d=new Gr(this.id,s,e);this._engine.totalQueuedPlayers++,this._queue.push({element:e,triggerName:s,transition:l,fromState:u,toState:c,player:d,isFallbackTransition:!0})})}}removeNode(e,n){let r=this._engine;if(e.childElementCount&&this._signalRemovalForInnerTriggers(e,n),this.triggerLeaveAnimation(e,n,!0))return;let o=!1;if(r.totalAnimations){let i=r.players.length?r.playersByQueriedElement.get(e):[];if(i&&i.length)o=!0;else{let s=e;for(;s=s.parentNode;)if(r.statesByElement.get(s)){o=!0;break}}}if(this.prepareLeaveAnimationListeners(e),o)r.markElementAsRemoved(this.id,e,!1,n);else{let i=e[Ke];(!i||i===ig)&&(r.afterFlush(()=>this.clearElementCache(e)),r.destroyInnerAnimations(e),r._onRemovalComplete(e,n))}}insertNode(e,n){He(e,this._hostClassName)}drainQueuedTransitions(e){let n=[];return this._queue.forEach(r=>{let o=r.player;if(o.destroyed)return;let i=r.element,s=this._elementListeners.get(i);s&&s.forEach(a=>{if(a.name==r.triggerName){let l=sc(i,r.triggerName,r.fromState.value,r.toState.value);l._data=e,ic(r.player,a.phase,l,a.callback)}}),o.markedForDestroy?this._engine.afterFlush(()=>{o.destroy()}):n.push(r)}),this._queue=[],n.sort((r,o)=>{let i=r.transition.ast.depCount,s=o.transition.ast.depCount;return i==0||s==0?i-s:this._engine.driver.containsElement(r.element,o.element)?1:-1})}destroy(e){this.players.forEach(n=>n.destroy()),this._signalRemovalForInnerTriggers(this.hostElement,e)}},tc=class{_onRemovalComplete(e,n){this.onRemovalComplete(e,n)}constructor(e,n,r){this.bodyNode=e,this.driver=n,this._normalizer=r,this.players=[],this.newHostElements=new Map,this.playersByElement=new Map,this.playersByQueriedElement=new Map,this.statesByElement=new Map,this.disabledNodes=new Set,this.totalAnimations=0,this.totalQueuedPlayers=0,this._namespaceLookup={},this._namespaceList=[],this._flushFns=[],this._whenQuietFns=[],this.namespacesByHostElement=new Map,this.collectedEnterElements=[],this.collectedLeaveElements=[],this.onRemovalComplete=(o,i)=>{}}get queuedPlayers(){let e=[];return this._namespaceList.forEach(n=>{n.players.forEach(r=>{r.queued&&e.push(r)})}),e}createNamespace(e,n){let r=new ec(e,n,this);return this.bodyNode&&this.driver.containsElement(this.bodyNode,n)?this._balanceNamespaceList(r,n):(this.newHostElements.set(n,r),this.collectEnterElement(n)),this._namespaceLookup[e]=r}_balanceNamespaceList(e,n){let r=this._namespaceList,o=this.namespacesByHostElement;if(r.length-1>=0){let s=!1,a=this.driver.getParentElement(n);for(;a;){let l=o.get(a);if(l){let u=r.indexOf(l);r.splice(u+1,0,e),s=!0;break}a=this.driver.getParentElement(a)}s||r.unshift(e)}else r.push(e);return o.set(n,e),e}register(e,n){let r=this._namespaceLookup[e];return r||(r=this.createNamespace(e,n)),r}registerTrigger(e,n,r){let o=this._namespaceLookup[e];o&&o.register(n,r)&&this.totalAnimations++}destroy(e,n){e&&(this.afterFlush(()=>{}),this.afterFlushAnimationsDone(()=>{let r=this._fetchNamespace(e);this.namespacesByHostElement.delete(r.hostElement);let o=this._namespaceList.indexOf(r);o>=0&&this._namespaceList.splice(o,1),r.destroy(n),delete this._namespaceLookup[e]}))}_fetchNamespace(e){return this._namespaceLookup[e]}fetchNamespacesByElement(e){let n=new Set,r=this.statesByElement.get(e);if(r){for(let o of r.values())if(o.namespaceId){let i=this._fetchNamespace(o.namespaceId);i&&n.add(i)}}return n}trigger(e,n,r,o){if(cs(n)){let i=this._fetchNamespace(e);if(i)return i.trigger(n,r,o),!0}return!1}insertNode(e,n,r,o){if(!cs(n))return;let i=n[Ke];if(i&&i.setForRemoval){i.setForRemoval=!1,i.setForMove=!0;let s=this.collectedLeaveElements.indexOf(n);s>=0&&this.collectedLeaveElements.splice(s,1)}if(e){let s=this._fetchNamespace(e);s&&s.insertNode(n,r)}o&&this.collectEnterElement(n)}collectEnterElement(e){this.collectedEnterElements.push(e)}markElementAsDisabled(e,n){n?this.disabledNodes.has(e)||(this.disabledNodes.add(e),He(e,Lu)):this.disabledNodes.has(e)&&(this.disabledNodes.delete(e),sr(e,Lu))}removeNode(e,n,r){if(cs(n)){let o=e?this._fetchNamespace(e):null;o?o.removeNode(n,r):this.markElementAsRemoved(e,n,!1,r);let i=this.namespacesByHostElement.get(n);i&&i.id!==e&&i.removeNode(n,r)}else this._onRemovalComplete(n,r)}markElementAsRemoved(e,n,r,o,i){this.collectedLeaveElements.push(n),n[Ke]={namespaceId:e,setForRemoval:o,hasAnimation:r,removedBeforeQueried:!1,previousTriggersValues:i}}listen(e,n,r,o,i){return cs(n)?this._fetchNamespace(e).listen(n,r,o,i):()=>{}}_buildInstruction(e,n,r,o,i){return e.transition.build(this.driver,e.element,e.fromState.value,e.toState.value,r,o,e.fromState.options,e.toState.options,n,i)}destroyInnerAnimations(e){let n=this.driver.query(e,hs,!0);n.forEach(r=>this.destroyActiveAnimationsForElement(r)),this.playersByQueriedElement.size!=0&&(n=this.driver.query(e,Hu,!0),n.forEach(r=>this.finishActiveQueriedAnimationOnElement(r)))}destroyActiveAnimationsForElement(e){let n=this.playersByElement.get(e);n&&n.forEach(r=>{r.queued?r.markedForDestroy=!0:r.destroy()})}finishActiveQueriedAnimationOnElement(e){let n=this.playersByQueriedElement.get(e);n&&n.forEach(r=>r.finish())}whenRenderingDone(){return new Promise(e=>{if(this.players.length)return Kt(this.players).onDone(()=>e());e()})}processLeaveNode(e){let n=e[Ke];if(n&&n.setForRemoval){if(e[Ke]=ig,n.namespaceId){this.destroyInnerAnimations(e);let r=this._fetchNamespace(n.namespaceId);r&&r.clearElementCache(e)}this._onRemovalComplete(e,n.setForRemoval)}e.classList?.contains(Lu)&&this.markElementAsDisabled(e,!1),this.driver.query(e,ZC,!0).forEach(r=>{this.markElementAsDisabled(r,!1)})}flush(e=-1){let n=[];if(this.newHostElements.size&&(this.newHostElements.forEach((r,o)=>this._balanceNamespaceList(r,o)),this.newHostElements.clear()),this.totalAnimations&&this.collectedEnterElements.length)for(let r=0;r<this.collectedEnterElements.length;r++){let o=this.collectedEnterElements[r];He(o,KC)}if(this._namespaceList.length&&(this.totalQueuedPlayers||this.collectedLeaveElements.length)){let r=[];try{n=this._flushAnimations(r,e)}finally{for(let o=0;o<r.length;o++)r[o]()}}else for(let r=0;r<this.collectedLeaveElements.length;r++){let o=this.collectedLeaveElements[r];this.processLeaveNode(o)}if(this.totalQueuedPlayers=0,this.collectedEnterElements.length=0,this.collectedLeaveElements.length=0,this._flushFns.forEach(r=>r()),this._flushFns=[],this._whenQuietFns.length){let r=this._whenQuietFns;this._whenQuietFns=[],n.length?Kt(n).onDone(()=>{r.forEach(o=>o())}):r.forEach(o=>o())}}reportError(e){throw pC(e)}_flushAnimations(e,n){let r=new ar,o=[],i=new Map,s=[],a=new Map,l=new Map,u=new Map,c=new Set;this.disabledNodes.forEach(y=>{c.add(y);let I=this.driver.query(y,QC,!0);for(let C=0;C<I.length;C++)c.add(I[C])});let d=this.bodyNode,h=Array.from(this.statesByElement.keys()),f=Wm(h,this.collectedEnterElements),p=new Map,m=0;f.forEach((y,I)=>{let C=lc+m++;p.set(I,C),y.forEach(L=>He(L,C))});let D=[],E=new Set,T=new Set;for(let y=0;y<this.collectedLeaveElements.length;y++){let I=this.collectedLeaveElements[y],C=I[Ke];C&&C.setForRemoval&&(D.push(I),E.add(I),C.hasAnimation?this.driver.query(I,YC,!0).forEach(L=>E.add(L)):T.add(I))}let k=new Map,V=Wm(h,Array.from(E));V.forEach((y,I)=>{let C=fs+m++;k.set(I,C),y.forEach(L=>He(L,C))}),e.push(()=>{f.forEach((y,I)=>{let C=p.get(I);y.forEach(L=>sr(L,C))}),V.forEach((y,I)=>{let C=k.get(I);y.forEach(L=>sr(L,C))}),D.forEach(y=>{this.processLeaveNode(y)})});let de=[],Y=[];for(let y=this._namespaceList.length-1;y>=0;y--)this._namespaceList[y].drainQueuedTransitions(n).forEach(C=>{let L=C.player,ae=C.element;if(de.push(L),this.collectedEnterElements.length){let fe=ae[Ke];if(fe&&fe.setForMove){if(fe.previousTriggersValues&&fe.previousTriggersValues.has(C.triggerName)){let Jt=fe.previousTriggersValues.get(C.triggerName),ke=this.statesByElement.get(C.element);if(ke&&ke.has(C.triggerName)){let Wr=ke.get(C.triggerName);Wr.value=Jt,ke.set(C.triggerName,Wr)}}L.destroy();return}}let Ye=!d||!this.driver.containsElement(d,ae),Ne=k.get(ae),At=p.get(ae),K=this._buildInstruction(C,r,At,Ne,Ye);if(K.errors&&K.errors.length){Y.push(K);return}if(Ye){L.onStart(()=>Sn(ae,K.fromStyles)),L.onDestroy(()=>ut(ae,K.toStyles)),o.push(L);return}if(C.isFallbackTransition){L.onStart(()=>Sn(ae,K.fromStyles)),L.onDestroy(()=>ut(ae,K.toStyles)),o.push(L);return}let yc=[];K.timelines.forEach(fe=>{fe.stretchStartingKeyframe=!0,this.disabledNodes.has(fe.element)||yc.push(fe)}),K.timelines=yc,r.append(ae,K.timelines);let lg={instruction:K,player:L,element:ae};s.push(lg),K.queriedElements.forEach(fe=>Re(a,fe,[]).push(L)),K.preStyleProps.forEach((fe,Jt)=>{if(fe.size){let ke=l.get(Jt);ke||l.set(Jt,ke=new Set),fe.forEach((Wr,Is)=>ke.add(Is))}}),K.postStyleProps.forEach((fe,Jt)=>{let ke=u.get(Jt);ke||u.set(Jt,ke=new Set),fe.forEach((Wr,Is)=>ke.add(Is))})});if(Y.length){let y=[];Y.forEach(I=>{y.push(mC(I.triggerName,I.errors))}),de.forEach(I=>I.destroy()),this.reportError(y)}let J=new Map,se=new Map;s.forEach(y=>{let I=y.element;r.has(I)&&(se.set(I,I),this._beforeAnimationBuild(y.player.namespaceId,y.instruction,J))}),o.forEach(y=>{let I=y.element;this._getPreviousPlayers(I,!1,y.namespaceId,y.triggerName,null).forEach(L=>{Re(J,I,[]).push(L),L.destroy()})});let ct=D.filter(y=>Qm(y,l,u)),xt=new Map;Gm(xt,this.driver,T,u,lt).forEach(y=>{Qm(y,l,u)&&ct.push(y)});let Yt=new Map;f.forEach((y,I)=>{Gm(Yt,this.driver,new Set(y),l,ss)}),ct.forEach(y=>{let I=xt.get(y),C=Yt.get(y);xt.set(y,new Map([...I?.entries()??[],...C?.entries()??[]]))});let _s=[],mc=[],gc={};s.forEach(y=>{let{element:I,player:C,instruction:L}=y;if(r.has(I)){if(c.has(I)){C.onDestroy(()=>ut(I,L.toStyles)),C.disabled=!0,C.overrideTotalTime(L.totalTime),o.push(C);return}let ae=gc;if(se.size>1){let Ne=I,At=[];for(;Ne=Ne.parentNode;){let K=se.get(Ne);if(K){ae=K;break}At.push(Ne)}At.forEach(K=>se.set(K,ae))}let Ye=this._buildAnimation(C.namespaceId,L,J,i,Yt,xt);if(C.setRealPlayer(Ye),ae===gc)_s.push(C);else{let Ne=this.playersByElement.get(ae);Ne&&Ne.length&&(C.parentPlayer=Kt(Ne)),o.push(C)}}else Sn(I,L.fromStyles),C.onDestroy(()=>ut(I,L.toStyles)),mc.push(C),c.has(I)&&o.push(C)}),mc.forEach(y=>{let I=i.get(y.element);if(I&&I.length){let C=Kt(I);y.setRealPlayer(C)}}),o.forEach(y=>{y.parentPlayer?y.syncPlayerEvents(y.parentPlayer):y.destroy()});for(let y=0;y<D.length;y++){let I=D[y],C=I[Ke];if(sr(I,fs),C&&C.hasAnimation)continue;let L=[];if(a.size){let Ye=a.get(I);Ye&&Ye.length&&L.push(...Ye);let Ne=this.driver.query(I,Hu,!0);for(let At=0;At<Ne.length;At++){let K=a.get(Ne[At]);K&&K.length&&L.push(...K)}}let ae=L.filter(Ye=>!Ye.destroyed);ae.length?rS(this,I,ae):this.processLeaveNode(I)}return D.length=0,_s.forEach(y=>{this.players.push(y),y.onDone(()=>{y.destroy();let I=this.players.indexOf(y);this.players.splice(I,1)}),y.play()}),_s}afterFlush(e){this._flushFns.push(e)}afterFlushAnimationsDone(e){this._whenQuietFns.push(e)}_getPreviousPlayers(e,n,r,o,i){let s=[];if(n){let a=this.playersByQueriedElement.get(e);a&&(s=a)}else{let a=this.playersByElement.get(e);if(a){let l=!i||i==Ur;a.forEach(u=>{u.queued||!l&&u.triggerName!=o||s.push(u)})}}return(r||o)&&(s=s.filter(a=>!(r&&r!=a.namespaceId||o&&o!=a.triggerName))),s}_beforeAnimationBuild(e,n,r){let o=n.triggerName,i=n.element,s=n.isRemovalTransition?void 0:e,a=n.isRemovalTransition?void 0:o;for(let l of n.timelines){let u=l.element,c=u!==i,d=Re(r,u,[]);this._getPreviousPlayers(u,c,s,a,n.toState).forEach(f=>{let p=f.getRealPlayer();p.beforeDestroy&&p.beforeDestroy(),f.destroy(),d.push(f)})}Sn(i,n.fromStyles)}_buildAnimation(e,n,r,o,i,s){let a=n.triggerName,l=n.element,u=[],c=new Set,d=new Set,h=n.timelines.map(p=>{let m=p.element;c.add(m);let D=m[Ke];if(D&&D.removedBeforeQueried)return new Zt(p.duration,p.delay);let E=m!==l,T=oS((r.get(m)||JC).map(J=>J.getRealPlayer())).filter(J=>{let se=J;return se.element?se.element===m:!1}),k=i.get(m),V=s.get(m),de=Ym(this._normalizer,p.keyframes,k,V),Y=this._buildPlayer(p,de,T);if(p.subTimeline&&o&&d.add(m),E){let J=new Gr(e,a,m);J.setRealPlayer(Y),u.push(J)}return Y});u.forEach(p=>{Re(this.playersByQueriedElement,p.element,[]).push(p),p.onDone(()=>eS(this.playersByQueriedElement,p.element,p))}),c.forEach(p=>He(p,Lm));let f=Kt(h);return f.onDestroy(()=>{c.forEach(p=>sr(p,Lm)),ut(l,n.toStyles)}),d.forEach(p=>{Re(o,p,[]).push(f)}),f}_buildPlayer(e,n,r){return n.length>0?this.driver.animate(e.element,n,e.duration,e.delay,e.easing,r):new Zt(e.duration,e.delay)}},Gr=class{constructor(e,n,r){this.namespaceId=e,this.triggerName=n,this.element=r,this._player=new Zt,this._containsRealPlayer=!1,this._queuedCallbacks=new Map,this.destroyed=!1,this.parentPlayer=null,this.markedForDestroy=!1,this.disabled=!1,this.queued=!0,this.totalTime=0}setRealPlayer(e){this._containsRealPlayer||(this._player=e,this._queuedCallbacks.forEach((n,r)=>{n.forEach(o=>ic(e,r,void 0,o))}),this._queuedCallbacks.clear(),this._containsRealPlayer=!0,this.overrideTotalTime(e.totalTime),this.queued=!1)}getRealPlayer(){return this._player}overrideTotalTime(e){this.totalTime=e}syncPlayerEvents(e){let n=this._player;n.triggerCallback&&e.onStart(()=>n.triggerCallback("start")),e.onDone(()=>this.finish()),e.onDestroy(()=>this.destroy())}_queueEvent(e,n){Re(this._queuedCallbacks,e,[]).push(n)}onDone(e){this.queued&&this._queueEvent("done",e),this._player.onDone(e)}onStart(e){this.queued&&this._queueEvent("start",e),this._player.onStart(e)}onDestroy(e){this.queued&&this._queueEvent("destroy",e),this._player.onDestroy(e)}init(){this._player.init()}hasStarted(){return this.queued?!1:this._player.hasStarted()}play(){!this.queued&&this._player.play()}pause(){!this.queued&&this._player.pause()}restart(){!this.queued&&this._player.restart()}finish(){this._player.finish()}destroy(){this.destroyed=!0,this._player.destroy()}reset(){!this.queued&&this._player.reset()}setPosition(e){this.queued||this._player.setPosition(e)}getPosition(){return this.queued?0:this._player.getPosition()}triggerCallback(e){let n=this._player;n.triggerCallback&&n.triggerCallback(e)}};function eS(t,e,n){let r=t.get(e);if(r){if(r.length){let o=r.indexOf(n);r.splice(o,1)}r.length==0&&t.delete(e)}return r}function tS(t){return t??null}function cs(t){return t&&t.nodeType===1}function nS(t){return t=="start"||t=="done"}function qm(t,e){let n=t.style.display;return t.style.display=e??"none",n}function Gm(t,e,n,r,o){let i=[];n.forEach(l=>i.push(qm(l)));let s=[];r.forEach((l,u)=>{let c=new Map;l.forEach(d=>{let h=e.computeStyle(u,d,o);c.set(d,h),(!h||h.length==0)&&(u[Ke]=XC,s.push(u))}),t.set(u,c)});let a=0;return n.forEach(l=>qm(l,i[a++])),s}function Wm(t,e){let n=new Map;if(t.forEach(a=>n.set(a,[])),e.length==0)return n;let r=1,o=new Set(e),i=new Map;function s(a){if(!a)return r;let l=i.get(a);if(l)return l;let u=a.parentNode;return n.has(u)?l=u:o.has(u)?l=r:l=s(u),i.set(a,l),l}return e.forEach(a=>{let l=s(a);l!==r&&n.get(l).push(a)}),n}function He(t,e){t.classList?.add(e)}function sr(t,e){t.classList?.remove(e)}function rS(t,e,n){Kt(n).onDone(()=>t.processLeaveNode(e))}function oS(t){let e=[];return sg(t,e),e}function sg(t,e){for(let n=0;n<t.length;n++){let r=t[n];r instanceof $r?sg(r.players,e):e.push(r)}}function iS(t,e){let n=Object.keys(t),r=Object.keys(e);if(n.length!=r.length)return!1;for(let o=0;o<n.length;o++){let i=n[o];if(!e.hasOwnProperty(i)||t[i]!==e[i])return!1}return!0}function Qm(t,e,n){let r=n.get(t);if(!r)return!1;let o=e.get(t);return o?r.forEach(i=>o.add(i)):e.set(t,r),n.delete(t),!0}var Ds=class{constructor(e,n,r){this._driver=n,this._normalizer=r,this._triggerCache={},this.onRemovalComplete=(o,i)=>{},this._transitionEngine=new tc(e.body,n,r),this._timelineEngine=new Xu(e.body,n,r),this._transitionEngine.onRemovalComplete=(o,i)=>this.onRemovalComplete(o,i)}registerTrigger(e,n,r,o,i){let s=e+"-"+o,a=this._triggerCache[s];if(!a){let l=[],u=[],c=dc(this._driver,i,l,u);if(l.length)throw oC(o,l);u.length&&void 0,a=qC(o,c,this._normalizer),this._triggerCache[s]=a}this._transitionEngine.registerTrigger(n,o,a)}register(e,n){this._transitionEngine.register(e,n)}destroy(e,n){this._transitionEngine.destroy(e,n)}onInsert(e,n,r,o){this._transitionEngine.insertNode(e,n,r,o)}onRemove(e,n,r){this._transitionEngine.removeNode(e,n,r)}disableAnimations(e,n){this._transitionEngine.markElementAsDisabled(e,n)}process(e,n,r,o){if(r.charAt(0)=="@"){let[i,s]=Fm(r),a=o;this._timelineEngine.command(i,n,s,a)}else this._transitionEngine.trigger(e,n,r,o)}listen(e,n,r,o,i){if(r.charAt(0)=="@"){let[s,a]=Fm(r);return this._timelineEngine.listen(s,n,a,i)}return this._transitionEngine.listen(e,n,r,o,i)}flush(e=-1){this._transitionEngine.flush(e)}get players(){return[...this._transitionEngine.players,...this._timelineEngine.players]}whenRenderingDone(){return this._transitionEngine.whenRenderingDone()}afterFlushAnimationsDone(e){this._transitionEngine.afterFlushAnimationsDone(e)}};function sS(t,e){let n=null,r=null;return Array.isArray(e)&&e.length?(n=Vu(e[0]),e.length>1&&(r=Vu(e[e.length-1]))):e instanceof Map&&(n=Vu(e)),n||r?new nc(t,n,r):null}var nc=class t{static{this.initialStylesByElement=new WeakMap}constructor(e,n,r){this._element=e,this._startStyles=n,this._endStyles=r,this._state=0;let o=t.initialStylesByElement.get(e);o||t.initialStylesByElement.set(e,o=new Map),this._initialStyles=o}start(){this._state<1&&(this._startStyles&&ut(this._element,this._startStyles,this._initialStyles),this._state=1)}finish(){this.start(),this._state<2&&(ut(this._element,this._initialStyles),this._endStyles&&(ut(this._element,this._endStyles),this._endStyles=null),this._state=1)}destroy(){this.finish(),this._state<3&&(t.initialStylesByElement.delete(this._element),this._startStyles&&(Sn(this._element,this._startStyles),this._endStyles=null),this._endStyles&&(Sn(this._element,this._endStyles),this._endStyles=null),ut(this._element,this._initialStyles),this._state=3)}};function Vu(t){let e=null;return t.forEach((n,r)=>{aS(r)&&(e=e||new Map,e.set(r,n))}),e}function aS(t){return t==="display"||t==="position"}var Es=class{constructor(e,n,r,o){this.element=e,this.keyframes=n,this.options=r,this._specialStyles=o,this._onDoneFns=[],this._onStartFns=[],this._onDestroyFns=[],this._initialized=!1,this._finished=!1,this._started=!1,this._destroyed=!1,this._originalOnDoneFns=[],this._originalOnStartFns=[],this.time=0,this.parentPlayer=null,this.currentSnapshot=new Map,this._duration=r.duration,this._delay=r.delay||0,this.time=this._duration+this._delay}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(e=>e()),this._onDoneFns=[])}init(){this._buildPlayer(),this._preparePlayerBeforeStart()}_buildPlayer(){if(this._initialized)return;this._initialized=!0;let e=this.keyframes;this.domPlayer=this._triggerWebAnimation(this.element,e,this.options),this._finalKeyframe=e.length?e[e.length-1]:new Map;let n=()=>this._onFinish();this.domPlayer.addEventListener("finish",n),this.onDestroy(()=>{this.domPlayer.removeEventListener("finish",n)})}_preparePlayerBeforeStart(){this._delay?this._resetDomPlayerState():this.domPlayer.pause()}_convertKeyframesToObject(e){let n=[];return e.forEach(r=>{n.push(Object.fromEntries(r))}),n}_triggerWebAnimation(e,n,r){return e.animate(this._convertKeyframesToObject(n),r)}onStart(e){this._originalOnStartFns.push(e),this._onStartFns.push(e)}onDone(e){this._originalOnDoneFns.push(e),this._onDoneFns.push(e)}onDestroy(e){this._onDestroyFns.push(e)}play(){this._buildPlayer(),this.hasStarted()||(this._onStartFns.forEach(e=>e()),this._onStartFns=[],this._started=!0,this._specialStyles&&this._specialStyles.start()),this.domPlayer.play()}pause(){this.init(),this.domPlayer.pause()}finish(){this.init(),this._specialStyles&&this._specialStyles.finish(),this._onFinish(),this.domPlayer.finish()}reset(){this._resetDomPlayerState(),this._destroyed=!1,this._finished=!1,this._started=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}_resetDomPlayerState(){this.domPlayer&&this.domPlayer.cancel()}restart(){this.reset(),this.play()}hasStarted(){return this._started}destroy(){this._destroyed||(this._destroyed=!0,this._resetDomPlayerState(),this._onFinish(),this._specialStyles&&this._specialStyles.destroy(),this._onDestroyFns.forEach(e=>e()),this._onDestroyFns=[])}setPosition(e){this.domPlayer===void 0&&this.init(),this.domPlayer.currentTime=e*this.time}getPosition(){return+(this.domPlayer.currentTime??0)/this.time}get totalTime(){return this._delay+this._duration}beforeDestroy(){let e=new Map;this.hasStarted()&&this._finalKeyframe.forEach((r,o)=>{o!=="offset"&&e.set(o,this._finished?r:cc(this.element,o))}),this.currentSnapshot=e}triggerCallback(e){let n=e==="start"?this._onStartFns:this._onDoneFns;n.forEach(r=>r()),n.length=0}},rc=class{validateStyleProperty(e){return!0}validateAnimatableStyleProperty(e){return!0}containsElement(e,n){return Jm(e,n)}getParentElement(e){return ac(e)}query(e,n,r){return Xm(e,n,r)}computeStyle(e,n,r){return cc(e,n)}animate(e,n,r,o,i,s=[]){let a=o==0?"both":"forwards",l={duration:r,delay:o,fill:a};i&&(l.easing=i);let u=new Map,c=s.filter(f=>f instanceof Es);MC(r,o)&&c.forEach(f=>{f.currentSnapshot.forEach((p,m)=>u.set(m,p))});let d=bC(n).map(f=>new Map(f));d=TC(e,d,u);let h=sS(e,d);return new Es(e,d,l,h)}};function jk(t,e){return t==="noop"?new Ds(e,new eg,new $u):new Ds(e,new rc,new qu)}var Zm=class{constructor(e,n){this._driver=e;let r=[],o=[],i=dc(e,n,r,o);if(r.length)throw nC(r);o.length&&void 0,this._animationAst=i}buildTimelines(e,n,r,o,i){let s=Array.isArray(n)?jm(n):n,a=Array.isArray(r)?jm(r):r,l=[];i=i||new ar;let u=hc(this._driver,e,this._animationAst,lc,fs,s,a,o,i,l);if(l.length)throw rC(l);return u}},ds="@",ag="@.disabled",ws=class{constructor(e,n,r,o){this.namespaceId=e,this.delegate=n,this.engine=r,this._onDestroy=o,this.\u0275type=0}get data(){return this.delegate.data}destroyNode(e){this.delegate.destroyNode?.(e)}destroy(){this.engine.destroy(this.namespaceId,this.delegate),this.engine.afterFlushAnimationsDone(()=>{queueMicrotask(()=>{this.delegate.destroy()})}),this._onDestroy?.()}createElement(e,n){return this.delegate.createElement(e,n)}createComment(e){return this.delegate.createComment(e)}createText(e){return this.delegate.createText(e)}appendChild(e,n){this.delegate.appendChild(e,n),this.engine.onInsert(this.namespaceId,n,e,!1)}insertBefore(e,n,r,o=!0){this.delegate.insertBefore(e,n,r),this.engine.onInsert(this.namespaceId,n,e,o)}removeChild(e,n,r){this.parentNode(n)&&this.engine.onRemove(this.namespaceId,n,this.delegate)}selectRootElement(e,n){return this.delegate.selectRootElement(e,n)}parentNode(e){return this.delegate.parentNode(e)}nextSibling(e){return this.delegate.nextSibling(e)}setAttribute(e,n,r,o){this.delegate.setAttribute(e,n,r,o)}removeAttribute(e,n,r){this.delegate.removeAttribute(e,n,r)}addClass(e,n){this.delegate.addClass(e,n)}removeClass(e,n){this.delegate.removeClass(e,n)}setStyle(e,n,r,o){this.delegate.setStyle(e,n,r,o)}removeStyle(e,n,r){this.delegate.removeStyle(e,n,r)}setProperty(e,n,r){n.charAt(0)==ds&&n==ag?this.disableAnimations(e,!!r):this.delegate.setProperty(e,n,r)}setValue(e,n){this.delegate.setValue(e,n)}listen(e,n,r){return this.delegate.listen(e,n,r)}disableAnimations(e,n){this.engine.disableAnimations(e,n)}},oc=class extends ws{constructor(e,n,r,o,i){super(n,r,o,i),this.factory=e,this.namespaceId=n}setProperty(e,n,r){n.charAt(0)==ds?n.charAt(1)=="."&&n==ag?(r=r===void 0?!0:!!r,this.disableAnimations(e,r)):this.engine.process(this.namespaceId,e,n.slice(1),r):this.delegate.setProperty(e,n,r)}listen(e,n,r){if(n.charAt(0)==ds){let o=lS(e),i=n.slice(1),s="";return i.charAt(0)!=ds&&([i,s]=uS(i)),this.engine.listen(this.namespaceId,o,i,s,a=>{let l=a._data||-1;this.factory.scheduleListenerCallback(l,r,a)})}return this.delegate.listen(e,n,r)}};function lS(t){switch(t){case"body":return document.body;case"document":return document;case"window":return window;default:return t}}function uS(t){let e=t.indexOf("."),n=t.substring(0,e),r=t.slice(e+1);return[n,r]}var Km=class{constructor(e,n,r){this.delegate=e,this.engine=n,this._zone=r,this._currentId=0,this._microtaskId=1,this._animationCallbacksBuffer=[],this._rendererCache=new Map,this._cdRecurDepth=0,n.onRemovalComplete=(o,i)=>{i?.removeChild(null,o)}}createRenderer(e,n){let r="",o=this.delegate.createRenderer(e,n);if(!e||!n?.data?.animation){let u=this._rendererCache,c=u.get(o);if(!c){let d=()=>u.delete(o);c=new ws(r,o,this.engine,d),u.set(o,c)}return c}let i=n.id,s=n.id+"-"+this._currentId;this._currentId++,this.engine.register(s,e);let a=u=>{Array.isArray(u)?u.forEach(a):this.engine.registerTrigger(i,s,e,u.name,u)};return n.data.animation.forEach(a),new oc(this,s,o,this.engine)}begin(){this._cdRecurDepth++,this.delegate.begin&&this.delegate.begin()}_scheduleCountTask(){queueMicrotask(()=>{this._microtaskId++})}scheduleListenerCallback(e,n,r){if(e>=0&&e<this._microtaskId){this._zone.run(()=>n(r));return}let o=this._animationCallbacksBuffer;o.length==0&&queueMicrotask(()=>{this._zone.run(()=>{o.forEach(i=>{let[s,a]=i;s(a)}),this._animationCallbacksBuffer=[]})}),o.push([n,r])}end(){this._cdRecurDepth--,this._cdRecurDepth==0&&this._zone.runOutsideAngular(()=>{this._scheduleCountTask(),this.engine.flush(this._microtaskId)}),this.delegate.end&&this.delegate.end()}whenRenderingDone(){return this.engine.whenRenderingDone()}};export{X as a,Dg as b,R as c,Ls as d,js as e,Ie as f,dr as g,hr as h,Cg as i,Sg as j,nn as k,fM as l,et as m,Fg as n,Rg as o,kg as p,pt as q,Lg as r,Vg as s,tt as t,Qg as u,Le as v,gr as w,kn as x,Hs as y,Kg as z,Us as A,ry as B,on as C,oy as D,iy as E,Rt as F,sy as G,ld as H,ly as I,uy as J,cd as K,yr as L,Ln as M,zs as N,cy as O,fd as P,hy as Q,py as R,hd as S,Gs as T,my as U,pd as V,gy as W,yy as X,Qs as Y,vy as Z,md as _,gd as $,yd as aa,vd as ba,Dy as ca,Dd as da,Ey as ea,g as fa,mf as ga,z as ha,yf as ia,hF as ja,j as ka,F as la,ge as ma,A as na,pF as oa,If as pa,bf as qa,_r as ra,mF as sa,Of as ta,_t as ua,En as va,uv as wa,cv as xa,Bf as ya,$t as za,wv as Aa,Nl as Ba,gF as Ca,Ol as Da,yF as Ea,vF as Fa,DF as Ga,EF as Ha,wF as Ia,sD as Ja,Dt as Ka,Ni as La,Pr as Ma,Ge as Na,Se as Oa,mn as Pa,Wt as Qa,Ta as Ra,_F as Sa,IF as Ta,CD as Ua,zl as Va,bF as Wa,CF as Xa,_n as Ya,Gl as Za,SF as _a,MF as $a,TF as ab,NF as bb,xF as cb,Fh as db,WD as eb,Ai as fb,AF as gb,ZD as hb,KD as ib,OF as jb,PF as kb,FF as lb,RF as mb,Mr as nb,kF as ob,H as pb,LF as qb,wt as rb,Zn as sb,Kn as tb,ai as ub,Vi as vb,Qe as wb,mw as xb,gw as yb,Ct as zb,Uw as Ab,$F as Bb,Gw as Cb,HF as Db,Xw as Eb,Ja as Fb,e_ as Gb,o_ as Hb,a_ as Ib,__ as Jb,Lp as Kb,I_ as Lb,UF as Mb,zF as Nb,qF as Ob,GF as Pb,WF as Qb,QF as Rb,ZF as Sb,KF as Tb,YF as Ub,JF as Vb,zp as Wb,qp as Xb,L_ as Yb,Gp as Zb,Wp as _b,B_ as $b,XF as ac,H_ as bc,K_ as cc,Y_ as dc,eR as ec,tR as fc,nR as gc,tI as hc,Zp as ic,rR as jc,oR as kc,iR as lc,sR as mc,aR as nc,lR as oc,rI as pc,Kp as qc,oI as rc,iI as sc,sI as tc,aI as uc,lI as vc,uR as wc,uI as xc,cI as yc,cR as zc,dR as Ac,fR as Bc,hR as Cc,pR as Dc,mR as Ec,gR as Fc,yR as Gc,vR as Hc,DR as Ic,ER as Jc,wR as Kc,_R as Lc,IR as Mc,bR as Nc,CR as Oc,SR as Pc,MR as Qc,TR as Rc,zi as Sc,vI as Tc,DI as Uc,qi as Vc,NR as Wc,xR as Xc,ir as Yc,AR as Zc,Gi as _c,pu as $c,OR as ad,PR as bd,jI as cd,VI as dd,FR as ed,mu as fd,HI as gd,RR as hd,kR as id,gu as jd,rk as kd,um as ld,Nu as md,xu as nd,ok as od,is as pd,wm as qd,zI as rd,ik as sd,qI as td,_e as ud,G as vd,KI as wd,lb as xd,_b as yd,Ib as zd,sk as Ad,ak as Bd,lk as Cd,uk as Dd,ck as Ed,Nm as Fd,dk as Gd,fk as Hd,hk as Id,pk as Jd,mk as Kd,gk as Ld,yk as Md,vk as Nd,Dk as Od,Ek as Pd,wk as Qd,Pb as Rd,Rb as Sd,_k as Td,Ik as Ud,Dm as Vd,Sk as Wd,Mk as Xd,Pu as Yd,Tk as Zd,Nk as _d,xk as $d,Ak as ae,ac as be,DC as ce,kk as de,Jm as ee,Xm as fe,eg as ge,km as he,Bu as ie,$u as je,bC as ke,Lk as le,MC as me,qu as ne,Ds as oe,Es as pe,rc as qe,jk as re,Zm as se,ws as te,oc as ue,Km as ve};
