import{f as p}from"./chunk-AASME2FE.js";import{fa as h}from"./chunk-AX4DCRSD.js";import{A as o,Ka as c,Qa as l,ba as n,f as a,na as r,ua as s}from"./chunk-YK6FMNSY.js";var $=(()=>{class i{elementRef=r(l);injector=r(c);control=r(p);helperService=r(h);destroy$=new a;ngOnInit(){o(this.elementRef.nativeElement,"paste").pipe(n(this.destroy$)).subscribe(e=>{if(e&&e.clipboardData){let t=e?.clipboardData.getData("text");this.changeData(t)}}),o(this.elementRef.nativeElement,"keydown").pipe(n(this.destroy$)).subscribe(e=>{let m=e.target.value,u=e.key;if(!this.helperService.allowMetaKey(e)&&(e?.key?.search(/[^+0-9]/)>=0||u!=="0"&&!m?.length)){e.preventDefault(),e.stopPropagation();return}})}convertPhone(e){let t=e;return t=t.replace(/^(\D84|84|0)(\d{0,9})(.*?)$/,"0$2"),t.search(/^0\d{0,9}$/gi)<0?null:t}changeData(e){setTimeout(()=>{this.control&&this.control.control?.setValue(this.convertPhone(e))},0)}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}static \u0275fac=function(t){return new(t||i)};static \u0275dir=s({type:i,selectors:[["","appPhoneNumber",""]],standalone:!0})}return i})();export{$ as a};
