import{d as W}from"./chunk-AX4DCRSD.js";import{J as U,K as b,Pd as B,jf as H}from"./chunk-K5H3SJL5.js";import{$b as f,Ac as d,Cc as S,Cd as L,Ec as A,Fc as M,Gc as E,Hb as C,Jb as c,Jd as k,Mb as x,Nb as g,Qc as V,Qd as O,Rb as p,Wb as s,Xb as l,Yb as m,ec as a,fc as v,gc as h,na as y,nc as u,ob as r,sa as I,yc as z,zc as T}from"./chunk-YK6FMNSY.js";var F=["*"],R=e=>({"icon-wrap":!0,"icon-mute":e}),j=(e,o,t)=>({icon:e,iconVip:o,position:"prefix",mute:t}),N=(e,o)=>({icon:e,position:"suffix",mute:o});function w(e,o){if(e&1&&(s(0,"div",5),m(1,"app-svg",9),l()),e&2){let t=a(),i=t.icon,n=t.iconVip,Z=t.mute,D=d(1),_=a();c("ngClass",A(6,R,Z)),r(),c("src",i)("vipSrc",n)("colorChange",_.iconColorChange)("size",D)("changeWithTheme",_.changeWithTheme)}}function q(e,o){if(e&1&&m(0,"app-avatar",6),e&2){let t=a().icon,i=d(1),n=a();c("size",n.SIZE_AVATAR[n.size])("iconSize",i)("type",n.UI.AvatarType.Bank)("content",t)}}function G(e,o){if(e&1&&m(0,"app-avatar",6),e&2){let t=a().icon,i=d(1),n=a();x(n.UI.AvatarStyle.Bank),c("size",n.SIZE_AVATAR[n.size])("iconSize",i)("type",n.UI.AvatarType.Icon)("content",t)}}function J(e,o){if(e&1&&m(0,"app-avatar",10),e&2){let t=a().icon,i=d(1),n=a();x(n.UI.AvatarStyle.Light),c("size",n.SIZE_AVATAR[n.size])("iconSize",i)("content",t)("type",n.UI.AvatarType.Bank)}}function K(e,o){if(e&1&&(s(0,"div"),z(1),C(2,w,2,8,"div",5)(3,q,1,4,"app-avatar",6)(4,G,1,6,"app-avatar",7)(5,J,1,6,"app-avatar",8),l()),e&2){let t,i=o.position,n=a();g(i==="prefix"?"card-prefix":"card-suffix"),r(),T(n.sizeIcon||n.SIZE_ICON[n.size]),r(),p((t=n.typeIcon)==="icon"?2:t==="bank"?3:t==="bank-icon"?4:5)}}function P(e,o){if(e&1&&(s(0,"div",1),f(1,11),l()),e&2){let t=a();r(),c("ngTemplateOutlet",t.prefixExtra)}}function Q(e,o){if(e&1&&f(0,2),e&2){let t=a(),i=u(1);c("ngTemplateOutlet",i)("ngTemplateOutletContext",E(2,j,t.prefixIcon,t.prefixIconVip,t.prefixIconMute))}}function X(e,o){if(e&1&&f(0,2),e&2){let t=a(),i=u(1);c("ngTemplateOutlet",i)("ngTemplateOutletContext",M(2,N,t.suffixIcon,t.suffixIconMute))}}function Y(e,o){e&1&&(s(0,"div",4),m(1,"app-svg",12),l()),e&2&&(r(),c("src","media/icons/outline/chevron-right.svg"))}var de=(()=>{class e{size=U.Md;sizeIcon;typeIcon="icon";prefixIcon;prefixIconVip;iconColorChange=!1;suffixIcon;prefixIconMute=!1;suffixIconMute=!0;cardLink=!0;cardCenter=!1;cardHorizontal=!0;changeWithTheme=!0;align="middle";type=b.Default;prefixExtra;get hostClass(){return this.initClass()}UI=y(H);SIZE_AVATAR={sm:this.UI.AvatarSize.Sm,md:this.UI.AvatarSize.Md,lg:this.UI.AvatarSize.midMd};SIZE_ICON={sm:4,md:6,lg:8};initClass(){let t=["card-category","card-category-"+this.size.toString(),"card-category-"+this.type,"card-category-"+this.align,this.cardHorizontal?"card-category-horizontal":"card-category-vertical"];return this.cardCenter&&t.push("card-category-center"),this.cardLink&&t.push("card-category-link"),t.join(" ")}static \u0275fac=function(i){return new(i||e)};static \u0275cmp=I({type:e,selectors:[["app-card-category"],["","app-card-category",""]],hostVars:2,hostBindings:function(i,n){i&2&&g(n.hostClass)},inputs:{size:"size",sizeIcon:"sizeIcon",typeIcon:"typeIcon",prefixIcon:"prefixIcon",prefixIconVip:"prefixIconVip",iconColorChange:"iconColorChange",suffixIcon:"suffixIcon",prefixIconMute:"prefixIconMute",suffixIconMute:"suffixIconMute",cardLink:"cardLink",cardCenter:"cardCenter",cardHorizontal:"cardHorizontal",changeWithTheme:"changeWithTheme",align:"align",type:"type",prefixExtra:"prefixExtra"},standalone:!0,features:[S],ngContentSelectors:F,decls:8,vars:4,consts:[["iconTpl",""],[1,"card-prefix-extra"],[3,"ngTemplateOutlet","ngTemplateOutletContext"],[1,"card-category--content"],[1,"card-category--arrow"],[3,"ngClass"],[3,"size","iconSize","type","content"],[3,"size","iconSize","type","style","content"],[3,"size","iconSize","content","type","style"],[3,"src","vipSrc","colorChange","size","changeWithTheme"],[3,"size","iconSize","content","type"],[3,"ngTemplateOutlet"],[3,"src"]],template:function(i,n){i&1&&(v(),C(0,K,6,4,"ng-template",null,0,V)(2,P,2,1,"div",1)(3,Q,1,6,"ng-container",2),s(4,"div",3),h(5),l(),C(6,X,1,5,"ng-container",2)(7,Y,2,1,"div",4)),i&2&&(r(2),p(n.prefixExtra?2:-1),r(),p(n.prefixIcon?3:-1),r(3),p(n.suffixIcon?6:-1),r(),p(n.cardLink?7:-1))},dependencies:[B,W,O,L,k]})}return e})();export{de as a};
