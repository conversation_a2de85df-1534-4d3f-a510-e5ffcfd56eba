import{a as xe}from"./chunk-GEIXMSZ2.js";import{b as ve}from"./chunk-JSW6KUUV.js";import{a as ye}from"./chunk-SOAH2WLW.js";import{j as fe,k as ge,o as _e}from"./chunk-5VBIWGCV.js";import{a as st,d as ae,e as k,g as L,h as pe,i as le,j as $,l as ue,m as at,n as ce,q as de,s as he,u as me,w as z}from"./chunk-AASME2FE.js";import{y as Ce}from"./chunk-AX4DCRSD.js";import{Ad as J,Af as rt,Bd as tt,Bf as se,Cd as et,E as O,Jc as te,Kc as ee,Le as ne,Me as oe,Pd as ie,Pe as it,Te as c,Wa as X,Z as mt,aa as Wt,cb as Zt,eb as Xt,jf as nt,kf as re,mb as Jt,mf as ot,ta as Yt,ud as A}from"./chunk-K5H3SJL5.js";import{A as vt,Bc as j,C as xt,Cb as Ft,Cc as T,Cd as Ut,Da as bt,Dc as Lt,Dd as Kt,Ea as v,Ec as W,Ed as Ht,F as St,Fa as x,Fc as ht,Fd as Gt,Hb as _,Ia as At,Id as Qt,Jb as h,Kd as jt,La as D,Lb as G,Lc as m,M as wt,Mc as g,Na as H,Nc as zt,Ob as ct,Qc as Y,Qd as Z,Rb as y,Wb as a,Xb as l,Yb as b,Zb as F,_b as N,_c as $t,aa as It,ac as w,ba as Mt,ca as Tt,cc as S,da as Ot,ec as p,f as U,g as E,ga as K,ha as Vt,ic as Nt,kc as P,lb as Et,lc as B,mc as R,md as qt,na as d,nc as Q,ob as s,oc as f,pb as Dt,pc as dt,qc as C,rc as Pt,sa as M,t as yt,vc as Bt,wc as Rt,xc as kt}from"./chunk-YK6FMNSY.js";import{a as lt,b as ut}from"./chunk-TSRGIXR5.js";var Me=n=>({"error-input":n});function Te(n,r){if(n&1&&(a(0,"span"),f(1),l()),n&2){let t=p(3);s(),C(" ",t.config.separator," ")}}function Oe(n,r){if(n&1){let t=w();F(0),a(1,"input",5,0),S("paste",function(i){v(t);let o=p(2);return x(o.handlePaste(i))})("keyup",function(i){let o=v(t).index,u=p(2);return x(u.onKeyUp(i,o))})("input",function(i){let o=v(t).index,u=p(2);return x(u.onInput(i,o))})("keydown",function(i){let o=v(t).index,u=p(2);return x(u.onKeyDown(i,o))}),l(),_(3,Te,2,1,"span",6),N()}if(n&2){let t=r.$implicit,e=r.index,i=r.last,o=p(2);s(),ct("otp-input ",o.config.inputClass,""),h("pattern",o.config.allowNumbersOnly?"\\d*":"")("type",o.inputType)("placeholder",(o.config==null?null:o.config.placeholder)||"")("ngStyle",o.config.inputStyles)("formControl",o.otpForm.controls[t])("id",o.getBoxId(e))("ngClass",W(11,Me,(o.config==null?null:o.config.showError)&&(o.formCtrl==null?null:o.formCtrl.invalid)&&((o.formCtrl==null?null:o.formCtrl.dirty)||(o.formCtrl==null?null:o.formCtrl.touched)))),s(2),h("ngIf",o.config.separator&&!i)}}function Ve(n,r){if(n&1){let t=w();a(0,"div",2),S("focusin",function(){v(t);let i=p();return x(i.onFocusIn())})("focusout",function(){v(t);let i=p();return x(i.onFocusOut())}),a(1,"div",3),_(2,Oe,4,13,"ng-container",4),l()()}if(n&2){let t=p();ct("ng-otp-input-wrapper wrapper ",t.config.containerClass,""),Nt("id","c_",t.componentKey,""),h("ngStyle",t.config.containerStyles),s(2),h("ngForOf",t.controlKeys)}}var V=class{static ifTab(r){return this.ifKey(r,"Tab")}static ifDelete(r){return this.ifKey(r,"Delete;Del")}static ifBackspace(r){return this.ifKey(r,"Backspace")}static ifRightArrow(r){return this.ifKey(r,"ArrowRight;Right")}static ifLeftArrow(r){return this.ifKey(r,"ArrowLeft;Left")}static ifSpacebar(r){return this.ifKey(r,"Spacebar; ")}static ifKey(r,t){return t.split(";").some(i=>i===r.key)}},q=class{static keys(r){return r?Object.keys(r):[]}},ft=(()=>{class n{set disabled(t){this.setDisabledState(t)}get inputType(){return this.config?.isPasswordInput?"password":this.config?.allowNumbersOnly?"tel":"text"}get controlKeys(){return q.keys(this.otpForm?.controls)}constructor(t){this.document=t,this.config={length:4},this.onBlur=new U,this.onInputChange=new U,this.inputControls=new Array(this.config.length),this.componentKey=Math.random().toString(36).substring(2)+new Date().getTime().toString(36),this.destroy$=new U,this.activeFocusCount=0,this.onChange=()=>{},this.onTouched=()=>{},this._isDisabled=!1}ngOnInit(){this.otpForm=new le({});for(let t=0;t<this.config.length;t++)this.otpForm.addControl(this.getControlName(t),new $);this.otpForm.valueChanges.pipe(Mt(this.destroy$)).subscribe(t=>{q.keys(this.otpForm.controls).forEach(e=>{var i=this.otpForm.controls[e].value;i&&i.length>1&&(i.length>=this.config.length?this.setValue(i):this.rebuildValue())})})}setDisabledState(t){this._isDisabled=t,this.otpForm&&(t?this.otpForm.disable({emitEvent:!1}):this.otpForm.enable({emitEvent:!1}))}writeValue(t){this.currentVal=this.hasVal(t)?t:null,this.setValue(this.currentVal)}registerOnChange(t){this.onChange=t}registerOnTouched(t){this.onTouched=t}onFocusIn(){this.onTouched(),this.activeFocusCount++}onFocusOut(){setTimeout(()=>{this.activeFocusCount--,this.activeFocusCount===0&&(this.onTouched(),this.onBlur.next())},0)}ngAfterViewInit(){if(!this.config.disableAutoFocus){let t=this.document.getElementById(`c_${this.componentKey}`);if(t){let e=t.getElementsByClassName("otp-input")[0];e&&e.focus&&e.focus()}}}getControlName(t){return`ctrl_${t}`}onKeyDown(t,e){let i=this.getBoxId(e-1),o=this.getBoxId(e),u=this.getBoxId(e+1);if(V.ifSpacebar(t))return t.preventDefault(),!1;if(V.ifBackspace(t)){t.target.value?this.clearInput(o,e):(this.clearInput(i,e-1),this.setSelected(i)),this.rebuildValue();return}if(V.ifDelete(t)){t.target.value?this.clearInput(o,e):(this.clearInput(i,e-1),this.setSelected(i)),this.rebuildValue();return}}hasVal(t){return t!=null&&t!=null&&(!t?.trim||t.trim()!="")}onInput(t,e){let i=this.hasVal(this.currentVal)?`${this.currentVal}${t.target.value}`:t.target.value;if(this.config.allowNumbersOnly&&!this.validateNumber(i)){t.target.value=null,t.stopPropagation(),t.preventDefault(),this.clearInput(null,e);return}if(this.ifValidKeyCode(null,t.target.value)){let o=this.getBoxId(e+1);this.setSelected(o),this.rebuildValue()}else{t.target.value=null;let o=this.getControlName(e);this.otpForm.controls[o]?.setValue(null),this.rebuildValue()}}onKeyUp(t,e){V.ifTab(t)&&(e-=1);let i=this.getBoxId(e+1),o=this.getBoxId(e-1);if(V.ifRightArrow(t)){t.preventDefault(),this.setSelected(i);return}if(V.ifLeftArrow(t)){t.preventDefault(),this.setSelected(o);return}}validateNumber(t){return t&&/^[0-9]+$/.test(t)}getBoxId(t){return`otp_${t}_${this.componentKey}`}clearInput(t,e){let i=this.getControlName(e);if(this.otpForm.controls[i]?.setValue(null),t){let o=this.document.getElementById(t);o&&o instanceof HTMLInputElement&&(o.value=null)}}setSelected(t){this.focusTo(t);let e=this.document.getElementById(t);e&&e.setSelectionRange&&setTimeout(()=>{e.setSelectionRange(0,1)},0)}ifValidKeyCode(t,e){let i=e??t.key;return this.config?.allowNumbersOnly?this.validateNumber(i):/iPhone|iPad|iPod|Android/i.test(navigator.userAgent)||/^[a-zA-Z0-9%*_\-@#$!]$/.test(i)&&i.length==1}focusTo(t){let e=this.document.getElementById(t);e&&e.focus()}setValue(t){if(!(this.config.allowNumbersOnly&&isNaN(t))){if(this.otpForm?.reset(),!this.hasVal(t)){this.rebuildValue();return}t=t.toString().replace(/\s/g,""),Array.from(t).forEach((e,i)=>{this.otpForm.get(this.getControlName(i))&&this.otpForm.get(this.getControlName(i)).setValue(e)}),this.config.disableAutoFocus||setTimeout(()=>{let e=this.document.getElementById(`c_${this.componentKey}`);var i=t.length<this.config.length?t.length:this.config.length-1;let o=e.getElementsByClassName("otp-input")[i];o&&o.focus&&setTimeout(()=>{o.focus()},1)},0),this.rebuildValue()}}rebuildValue(){let t=null;q.keys(this.otpForm.controls).forEach(e=>{let i=this.otpForm.controls[e].value;if(i){let o=i.length>1,u=!this.config.allowNumbersOnly&&this.config.letterCase&&(this.config.letterCase.toLocaleLowerCase()=="upper"||this.config.letterCase.toLocaleLowerCase()=="lower");i=i[0];let I=u?this.config.letterCase.toLocaleLowerCase()=="upper"?i.toUpperCase():i.toLowerCase():i;u&&I==i?u=!1:i=I,t==null?t=i:t+=i,(o||u)&&this.otpForm.controls[e].setValue(i)}}),this.currentVal!=t&&(this.currentVal=t,this.onChange(t),this.formCtrl?.setValue&&this.formCtrl.setValue(t),this.onInputChange.next(t))}handlePaste(t){let e=t.clipboardData||window.clipboardData;if(e)var i=e.getData("Text");t.stopPropagation(),t.preventDefault(),!(!i||this.config.allowNumbersOnly&&!this.validateNumber(i))&&this.setValue(i)}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}static{this.\u0275fac=function(e){return new(e||n)(Dt(qt))}}static{this.\u0275cmp=M({type:n,selectors:[["ng-otp-input"],["ngx-otp-input"]],inputs:{config:"config",formCtrl:"formCtrl",disabled:"disabled"},outputs:{onBlur:"onBlur",onInputChange:"onInputChange"},standalone:!0,features:[j([{provide:st,useExisting:K(()=>n),multi:!0}]),T],decls:1,vars:1,consts:[["inp",""],["tabindex","0",3,"class","id","ngStyle","focusin","focusout",4,"ngIf"],["tabindex","0",3,"focusin","focusout","id","ngStyle"],[1,"n-o-c"],[4,"ngFor","ngForOf"],["autocomplete","one-time-code",3,"paste","keyup","input","keydown","pattern","type","placeholder","ngStyle","formControl","id","ngClass"],[4,"ngIf"]],template:function(e,i){e&1&&_(0,Ve,3,7,"div",1),e&2&&h("ngIf",i.otpForm==null?null:i.otpForm.controls)},dependencies:[z,ae,L,he,at,Ht,Kt,Qt,Ut],styles:[".otp-input[_ngcontent-%COMP%]{width:50px;height:50px;border-radius:4px;border:solid 1px #c5c5c5;text-align:center;font-size:32px}.ng-otp-input-wrapper[_ngcontent-%COMP%]   .otp-input[_ngcontent-%COMP%]{margin:0 .51rem}.ng-otp-input-wrapper[_ngcontent-%COMP%]   .otp-input[_ngcontent-%COMP%]:first-child{margin-left:0}.ng-otp-input-wrapper[_ngcontent-%COMP%]   .otp-input[_ngcontent-%COMP%]:last-child{margin-right:0}.n-o-c[_ngcontent-%COMP%]{display:flex;align-items:center}.error-input[_ngcontent-%COMP%]{border-color:red}@media screen and (max-width: 767px){.otp-input[_ngcontent-%COMP%]{width:40px;font-size:24px;height:40px}}@media screen and (max-width: 420px){.otp-input[_ngcontent-%COMP%]{width:30px;font-size:18px;height:30px}}"]})}}return n})();function be(n,r){if(n&1&&(a(0,"div",3),f(1),l()),n&2){let t=p(2);s(),C(" ",t.hint," ")}}function Ae(n,r){if(n&1&&b(0,"app-validate-error",4),n&2){let t=p(3);h("errors",t.control.errors)("errorMessages",t.errorMessages)("align",t.ValidateErrorAlign.Center)}}function Ee(n,r){if(n&1&&_(0,Ae,1,3,"app-validate-error",4),n&2){let t=p(2);y(t.control.invalid&&t.control.touched&&t.control.errors&&t.errorMessages?0:-1)}}function De(n,r){if(n&1&&(a(0,"div",2),_(1,be,2,1,"div",3)(2,Ee,1,1),l()),n&2){let t=p();s(),y(t.hint&&!(t.control.invalid&&t.control.touched)?1:-1),s(),y(t.showError?2:-1)}}var gt=(()=>{class n extends fe{otp=!0;size=6;digitOnly=!0;hint;showError=!1;errorMessages;otpValue;otpValueChange=new H;ngOtpInputComponent;errors=null;invalid=!1;config={allowNumbersOnly:!0,showError:this.showError,placeholder:"-",length:this.size,inputClass:"input-otp",disableAutoFocus:!1};cdr=d($t);destroyRef=d(D);ValidateErrorAlign=Yt;ngOnInit(){super.ngOnInit()}ngAfterViewInit(){this.cdr.detectChanges();let t=this.ngOtpInputComponent?.getBoxId(0);if(this.ngOtpInputComponent?.focusTo(t),t){let e=document.getElementById(t);e&&vt(e,"blur").pipe(wt(1),It(()=>new Promise(i=>{requestAnimationFrame(()=>i(null))})),St(()=>document.body.contains(e)&&!Array.from(document.querySelectorAll(".input-otp")).some(i=>i===document.activeElement)),A(this.destroyRef)).subscribe(()=>{let i=this.ngOtpInputComponent?.getBoxId(0);this.ngOtpInputComponent?.focusTo(i)})}}ngOnChanges(){}onOtpChange(t){}clear(){this.control.setValue(""),this.ngOtpInputComponent?.setValue("")}static \u0275fac=(()=>{let t;return function(i){return(t||(t=At(n)))(i||n)}})();static \u0275cmp=M({type:n,selectors:[["app-otp"]],viewQuery:function(e,i){if(e&1&&P(ft,5),e&2){let o;B(o=R())&&(i.ngOtpInputComponent=o.first)}},hostVars:2,hostBindings:function(e,i){e&2&&G("app-otp",i.otp)},inputs:{size:"size",digitOnly:"digitOnly",hint:"hint",showError:"showError",errorMessages:"errorMessages",otpValue:"otpValue"},outputs:{otpValueChange:"otpValueChange"},standalone:!0,features:[j([{provide:st,useExisting:K(()=>n),multi:!0}]),Ft,bt,T],decls:3,vars:3,consts:[[1,"input-otps"],[1,"input-otp-wrap",3,"onInputChange","formControl","config"],[1,"explain"],[1,"hint"],[3,"errors","errorMessages","align"]],template:function(e,i){e&1&&(a(0,"div",0)(1,"ng-otp-input",1),S("onInputChange",function(u){return i.onOtpChange(u)}),l()(),_(2,De,3,2,"div",2)),e&2&&(s(),h("formControl",i.control)("config",i.config),s(),y(i.control.invalid&&i.control.touched||i.hint?2:-1))},dependencies:[Z,z,L,at,ge,ft]})}return n})();var Ie=(()=>{class n{count=1;step=1;responseInitAuthMethod;step$=new E(this.step);responseInitStep$=new E(void 0);responseConfirmStep$=new E(void 0);errorConfirmStep$=new E(void 0);stepAction$=new E(void 0);initForm;apiService=d(rt);modalService=d(ot);translate=d(J);cryptoService=d(te);storageService=d(ee);loadingService=d(se);initCount(t){this.count=t}initStep(t=1){this.step=t,this.step$.next(this.step),this.stepAction$.next(void 0),this.toTop()}next(){this.step<this.count&&this.step$.next(++this.step),this.toTop()}prev(){this.step>1&&this.step$.next(--this.step),this.toTop()}getStep(){return this.step$.asObservable()}getStepValue(){return this.step$.getValue()}clearProcessData(){this.responseInitStep$.next(void 0),this.responseConfirmStep$.next(void 0),this.stepAction$.next(void 0),this.errorConfirmStep$.next(void 0),this.responseInitAuthMethod=void 0}resetStep(){this.step=1,this.step$.next(this.step),this.clearProcessData()}toTop(){window.scrollTo(0,0)}setResponseInitStep(t){this.responseInitStep$.next(t)}getResponseInitStep(){return this.responseInitStep$.asObservable()}setResponseConfirmStep(t){this.responseConfirmStep$.next(t)}getResponseConfirmStep(){return this.responseConfirmStep$.asObservable()}getErrorConfirmStep(){return this.errorConfirmStep$.asObservable()}setErrorConfirmStep(t){this.errorConfirmStep$.next(t)}getStepAction(){return this.stepAction$.asObservable()}setStepAction(t){this.stepAction$.next(t),t===Ce.NewTransaction&&(this.responseInitStep$.next(void 0),this.responseConfirmStep$.next(void 0),this.initForm?.reset())}getInitForm(){return this.initForm}setInitForm(t){this.initForm=t}handleResetProcess(t){this.resetStep(),this.initForm?.reset(t)}toggleModalConfirm(t){let{responseInit:e}=t,i=this.modalService.open(we,{maskClosable:!1,data:{responseInitAuthMethod:this.responseInitAuthMethod},confirm:o=>{t?.authSuccess?.(ut(lt({},o),{mainMethod:o.authenType,tranToken:e?.tranToken,authDetail:{subMethod:"",authenValue:""},groupMethod:e?.authDetails[0]?.groupMethod}))},size:Wt.XMedium});i?.componentInstance?.authFailedEvent.subscribe(()=>{t?.authFail?.()}),i?.afterClose.subscribe(o=>{o&&t?.onConfirmResult?.(ut(lt({},o),{mainMethod:o.authenType,tranToken:e?.tranToken,authDetail:{subMethod:"",authenValue:""},groupMethod:e?.authDetails[0]?.groupMethod}))})}initAuthMethod(t){let{responseInit:e}=t,{authDetails:i}=e||{},o=i?.[0],u={user:e?.user,tranToken:e?.tranToken,authDetail:{mainMethod:o?.mainMethod,authenValue:""}};this.apiService.initAuthMethod(u).subscribe({next:I=>{this.responseInitAuthMethod=I,this.toggleModalConfirm(t)},error:I=>{this.modalService.error({message:I?.error?.des||this.translate.instant("that_bai")})}})}processBaseAuthMethod(t){console.log("processBaseAuthMethod",t);try{this.loadingService.showLoading();let{responseInit:e}=t,{authDetails:i}=e||{},o=i?.[0],{mainMethod:u,subMethod:I}=o||{};if(u===c.SOtp||u===c.Password||u===c.FacePay&&I===c.SOtp){this.initAuthMethod(t);return}if(u===c.None){let Ct=this.cryptoService.genDataSign(this.storageService.userInfo,e?.tranToken,this.storageService.browserId,this.storageService.clientId);Ct?t?.authSuccess?.({mainMethod:c.None,authenType:c.None,authenValue:Ct,tranToken:e?.tranToken,authDetail:{subMethod:"",authenValue:""},groupMethod:e?.authDetails[0]?.groupMethod}):this.modalService.error({message:this.translate.instant("that_bai")}),this.loadingService.hideLoading();return}}catch(e){console.error("error auth method",e),t?.authFail?.()}}static \u0275fac=function(e){return new(e||n)};static \u0275prov=Vt({token:n,factory:n.\u0275fac,providedIn:"root"})}return n})();var Ne=(n,r)=>[n,r],Pe=()=>({required:"errors.otp_khong_duong_trong",minlength:"errors.ma_otp_gom_6_ky_tu"}),Be=n=>({method:n}),Re=(n,r)=>({required:n,minlength:r});function ke(n,r){if(n&1){let t=w();F(0),a(1,"form",3)(2,"p",4),f(3),m(4,"translate"),l(),a(5,"app-otp",5),kt("otpValueChange",function(i){v(t);let o=p();return Rt(o.otp,i)||(o.otp=i),x(i)}),l()(),N()}if(n&2){let t=p();s(),h("formGroup",t.form),s(2),C(" ",g(4,6,"verify_authentication.otp_sent_to_phone")," "),s(2),h("showError",t.submitted),Bt("otpValue",t.otp),h("size",t.size)("errorMessages",Lt(8,Pe))}}function Le(n,r){if(n&1&&(a(0,"div",11),f(1),m(2,"translate"),a(3,"span",15),f(4),l(),f(5),m(6,"translate"),l()),n&2){let t=p(3);s(),C(" ",g(2,3,"verify_authentication.availability_time")," "),s(3),dt(t.timeRemaining),s(),C(" ",g(6,5,"verify_authentication.second")," ")}}function ze(n,r){if(n&1&&(a(0,"div",7),b(1,"app-svg",8),l(),a(2,"p",9),f(3),m(4,"translate"),m(5,"translate"),l(),a(6,"div",10),_(7,Le,7,7,"div",11),a(8,"div",12),b(9,"div",13),l(),a(10,"p",14),f(11),m(12,"translate"),l()()),n&2){let t=p(2);s(),h("src","media/icons/outline/loading.svg")("colorChange",!1)("size",16),s(2),C(" ",zt(5,9,"verify_authentication.wait_confirm_on_mb",W(14,Be,g(4,7,"auth_methods."+t.authMethod)))," "),s(4),y(t.timeRemaining?7:-1),s(2),h("qrData",t.qrData),s(2),C(" ",g(12,12,"verify_authentication.do_not_out")," ")}}function $e(n,r){n&1&&(a(0,"div"),f(1),m(2,"translate"),l()),n&2&&(s(),dt(g(2,1,"verify_authentication.fail_to_gen_qr")))}function qe(n,r){if(n&1&&(F(0),a(1,"div",6),_(2,ze,13,16)(3,$e,3,3,"div"),l(),N()),n&2){let t=p();s(2),y(t.qrData?2:3)}}function Ue(n,r){if(n&1){let t=w();a(0,"button",18),S("click",function(){v(t);let i=p(2);return x(i.handleShowHidePassword())}),l()}if(n&2){let t=p(2);h("mute",!0)("iconOnly",!0)("size",t.UI.ButtonSize.Md)("prefixIcon",t.iconEye)("color",t.UI.ButtonColor.Text)}}function Ke(n,r){if(n&1&&(a(0,"form",2)(1,"p",16),f(2),m(3,"translate"),l(),a(4,"app-input",17),m(5,"translate"),m(6,"translate"),m(7,"translate"),_(8,Ue,1,5,"ng-template",null,0,Y),l()()),n&2){let t=Q(9),e=p();h("formGroup",e.form),s(2),C(" ",g(3,8,"verify_authentication.please_enter_password")," "),s(2),h("maxLength",20)("placeholder",g(5,10,"placeholder.nhap_mat_khau"))("size",e.UI.InputSize.Large)("type",e.inputPasswordType)("errorMessages",ht(16,Re,g(6,12,"errors.bo_trong_mk"),g(7,14,"errors.mat_khau_toi_thieu_8_ky_tu")))("suffix",t)}}var _t=(()=>{class n{otpComponent;type;authDetail;size=Jt;otp;qrData="";timeRemaining;auth=!0;authMethod;form;submitted=!1;showPassword=!1;iconEye=this.showPassword?"media/icons/outline/eye-hide.svg":"media/icons/outline/eye.svg";inputPasswordType=this.showPassword?mt.Text:mt.Password;fb=d(me);stepService=d(Ie);destroyRef=d(D);UI=d(nt);AuthType=c;canLeavePage(t){return confirm()&&t.preventDefault(),!1}ngOnInit(){this.form=this.fb.group({otp:new $(null,k.required),password:new $(null,[k.required,k.minLength(X)])}),this.size&&(this.otpControl?.setValidators([k.required,k.minLength(this.size)]),this.otpControl?.updateValueAndValidity()),this.getAuthMethod(),this.stepService.getErrorConfirmStep().pipe(A(this.destroyRef)).subscribe(t=>{if(t){let{code:e,codeFull:i}=t;i===Xt.CLEAR_INPUT?this.form.get("password")?.setValue(""):e===Zt.WRONG_OTP&&(this.form.get("otp")?.setValue(""),this.otp=void 0,this.otpComponent?.clear())}})}get otpControl(){return this.form?.get("otp")}getOtp(){return this.submitted=!0,this.form.get("otp")?.valid?this.form.get("otp")?.value:""}getPassword(){if(this.submitted=!0,this.form.get("password")?.valid)return this.form.get("password")?.value}getAuthMethod(){let t=this.authDetail;if(!t)return;let{mainMethod:e,subMethod:i}=t;e===c.None&&(this.authMethod=O.NONE),e===c.Sms&&(this.authMethod=O.OTP),e===c.SOtp&&(this.authMethod=O.SMART_OTP),e===c.Password&&(this.authMethod=O.PASSWORD),e===c.FacePay&&(this.authMethod=O.FACE_PAY,i===c.SOtp&&(this.authMethod=O.FPAY_SOTP),i===c.Sms&&(this.authMethod=O.FPAY_SMS))}handleShowHidePassword(){this.showPassword=!this.showPassword,this.iconEye=this.showPassword?"media/icons/outline/eye-hide.svg":"media/icons/outline/eye.svg",this.inputPasswordType=this.showPassword?this.UI.InputType.Text:this.UI.InputType.Password}ngOnDestroy(){this.stepService.setErrorConfirmStep()}static \u0275fac=function(e){return new(e||n)};static \u0275cmp=M({type:n,selectors:[["app-auth-method"]],viewQuery:function(e,i){if(e&1&&P(gt,5),e&2){let o;B(o=R())&&(i.otpComponent=o.first)}},hostVars:2,hostBindings:function(e,i){e&1&&S("beforeunload",function(u){return i.canLeavePage(u)},!1,Et),e&2&&G("auth-method",i.auth)},inputs:{type:"type",authDetail:"authDetail",size:"size",otp:"otp",qrData:"qrData",timeRemaining:"timeRemaining"},standalone:!0,features:[T],decls:4,vars:6,consts:[["passSuffix",""],[3,"ngSwitch"],[3,"formGroup"],[1,"otp",3,"formGroup"],[1,"text-body-lg","text-text-secondary","mb-6"],["formControlName","otp",3,"otpValueChange","showError","otpValue","size","errorMessages"],[1,"smart-otp"],[1,"loading"],[3,"src","colorChange","size"],[1,"smart-otp-title"],[1,"smart-otp-content"],[1,"exprid-time-wrap"],[1,"qr"],["app-smart-qr","",3,"qrData"],[1,"text-body-md","text-text-secondary"],[1,"exprid-time"],[1,"otp-guide"],["appNoneUnikey","","appNoneSpace","","formControlName","password",3,"maxLength","placeholder","size","type","errorMessages","suffix"],["app-button","","aria-label","hide/show","type","button",3,"click","mute","iconOnly","size","prefixIcon","color"]],template:function(e,i){e&1&&(F(0,1),_(1,ke,6,9,"ng-container")(2,qe,4,1,"ng-container")(3,Ke,10,19,"form",2),N()),e&2&&(h("ngSwitch",i.type),s(),y(i.type===i.AuthType.Sms?1:ht(3,Ne,i.AuthType.SOtp,i.AuthType.FacePay).includes(i.type)?2:-1),s(2),y(i.type===i.AuthType.Password?3:-1))},dependencies:[z,ue,L,pe,ce,de,Z,Gt,gt,_e,et,tt,ie,xe,ye,it,ve]})}return n})();function He(n,r){if(n&1&&b(0,"app-auth-method",2),n&2){let t=p();h("authDetail",t.responseInitAuthMethod==null||t.responseInitAuthMethod.authDetails==null?null:t.responseInitAuthMethod.authDetails[0])("type",t.authType)("qrData",t.qrData)("size",t.sizeOtp)}}function Ge(n,r){if(n&1&&(a(0,"p",3),f(1),m(2,"translate"),a(3,"span",4),f(4),m(5,"async"),m(6,"translate"),l()()),n&2){let t=p();s(),C(" ",g(2,3,"verify_authentication.note_availability")," "),s(3),Pt(" ",g(5,5,t.timeRemaining$)," ",g(6,7,"verify_authentication.second")," ")}}function Qe(n,r){if(n&1){let t=w();a(0,"a",7),S("click",function(){v(t);let i=p(2);return x(i.handleApprove())}),f(1),m(2,"translate"),l()}n&2&&(s(),C(" ",g(2,1,"verify_authentication.confirm")," "))}function je(n,r){if(n&1){let t=w();a(0,"a",5),S("click",function(){v(t);let i=p();return x(i.handleCancel())}),f(1),m(2,"translate"),l(),_(3,Qe,3,3,"a",6)}if(n&2){let t=p();h("color",t.UI.ButtonColor.Outline),s(),C(" ",g(2,3,"verify_authentication.cancel")," "),s(2),y(t.authType&&(t.authType===t.AuthType.Sms||t.authType===t.AuthType.Password)?3:-1)}}var we=(()=>{class n{authMethodComponent;authFailedEvent=new H;onApprove(t){this.handleApprove()}sizeOtp=6;timeRemaining$;authType;qrData;responseInitAuthMethod;AuthType=c;modalRef=d(oe);modalService=d(ot);translate=d(J);apiService=d(rt);destroyRef=d(D);modalData=d(ne);UI=d(nt);ngOnInit(){let{responseInitAuthMethod:t}=this.modalData?.data,{timeExpireSms:e}=t;this.responseInitAuthMethod=t,this.qrData=t?.dataSotp?.toString(),this.authType=t?.authDetails?.[0]?.mainMethod,e&&this.authType!==c.Password&&(this.timeRemaining$=xt(0,1e3).pipe(A(this.destroyRef),yt(i=>e-i),Tt(i=>i>=0),Ot(i=>{i<=0&&this.handleExpired()}))),this.authType&&[c.SOtp,c.FacePay].includes(this.authType)&&this.catchConfirmResult()}catchConfirmResult(){let t={user:this.responseInitAuthMethod?.user,tranToken:this.responseInitAuthMethod?.tranToken};this.apiService.confirmSmartOTP(t,this.responseInitAuthMethod?.timeExpireSms).pipe(A(this.destroyRef)).subscribe({next:e=>{e&&this.modalRef.close(e)},error:e=>{this.modalService.error({message:e?.error?.des||this.translate.instant("that_bai"),confirm:()=>{this.handleCancel()}})}})}handleApprove(){let t="";if(this.authType===c.Sms){if(t=this.authMethodComponent?.getOtp(),!t||t.length<this.sizeOtp)return}else if(this.authType===c.Password&&(t=this.authMethodComponent?.getPassword(),!t||t.length<X))return;let e={authenType:this.authType,authenValue:t};this.modalData?.confirm(e)}handleExpired(){this.modalService.toggleShowOneAtTime(!0),this.modalService.error({message:this.translate.instant("errors.giao_dich_het_hieu_luc_thu_lai_giao_dich_moi"),confirm:()=>{this.handleCancel()}})}handleCancel(){this.authFailedEvent.emit(),this.modalRef.destroy()}static \u0275fac=function(e){return new(e||n)};static \u0275cmp=M({type:n,selectors:[["app-modal-verify-otp"]],viewQuery:function(e,i){if(e&1&&P(_t,5),e&2){let o;B(o=R())&&(i.authMethodComponent=o.first)}},hostBindings:function(e,i){e&1&&S("keydown.enter",function(u){return i.onApprove(u)})},outputs:{authFailedEvent:"authFailedEvent"},standalone:!0,features:[T],decls:6,vars:6,consts:[["actionsTpl",""],[3,"title","actions"],[3,"authDetail","type","qrData","size"],[1,"text-center","mt-6"],[1,"text-body-lg-bold","text-primary"],["app-button","",3,"click","color"],["app-button",""],["app-button","",3,"click"]],template:function(e,i){if(e&1&&(a(0,"app-modal-base",1),m(1,"translate"),_(2,He,1,4,"app-auth-method",2)(3,Ge,7,9,"p",3),l(),_(4,je,4,5,"ng-template",null,0,Y)),e&2){let o=Q(5);h("title",g(1,4,"verify_authentication.title"))("actions",o),s(2),y(i.authType?2:-1),s(),y(i.authType!==i.AuthType.Password?3:-1)}},dependencies:[re,et,tt,it,_t,jt]})}return n})();export{_t as a,we as b,Ie as c};
