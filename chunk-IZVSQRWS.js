import{P as x,wd as k}from"./chunk-K5H3SJL5.js";import{$b as f,Cc as S,Hb as r,Jb as p,Jd as E,Lb as y,Qd as _,Rb as s,Wb as o,Xb as a,Yb as C,ec as c,fc as u,gc as h,hb as l,ob as i,oc as v,pc as g,sa as d}from"./chunk-YK6FMNSY.js";var b=["*"];function D(e,T){if(e&1&&(o(0,"p",4),v(1),a()),e&2){let t=c();i(),g(t.title)}}function O(e,T){if(e&1&&(o(0,"div",6),f(1,7),a()),e&2){let t=c();i(),p("ngTemplateOutlet",t.moreContent)}}var w=(()=>{class e{type=x.List;title;moreContent;card=!0;mediaSrc;ngOnInit(){switch(this.type){case"broken-link":this.mediaSrc="assets/media/icons/doutone/icon-he-thong/ht-empty-broken-link.svg";break;case"notification":this.mediaSrc="assets/media/icons/doutone/icon-he-thong/ht-empty-notification.svg";break;case"search":this.mediaSrc="assets/media/icons/doutone/icon-he-thong/ht-empty-search.svg";break;default:this.mediaSrc="assets/media/icons/doutone/icon-he-thong/ht-empty-list.svg";break}}static \u0275fac=function(n){return new(n||e)};static \u0275cmp=d({type:e,selectors:[["app-card-empty"],["","app-card-empty",""]],hostVars:2,hostBindings:function(n,m){n&2&&y("card-empty",m.card)},inputs:{type:"type",title:"title",moreContent:"moreContent"},standalone:!0,features:[S],ngContentSelectors:b,decls:8,vars:3,consts:[[1,"card-empty-inner"],[1,"empty-media"],["appVvip","","alt","",1,"media",3,"src"],[1,"content"],[1,"title"],[1,"descriptions"],[1,"more-content"],[3,"ngTemplateOutlet"]],template:function(n,m){n&1&&(u(),o(0,"div",0)(1,"div",1),C(2,"img",2),a(),o(3,"div",3),r(4,D,2,1,"p",4),o(5,"div",5),h(6),a()(),r(7,O,2,1,"div",6),a()),n&2&&(i(2),p("src",m.mediaSrc,l),i(2),s(m.title?4:-1),i(3),s(m.moreContent?7:-1))},dependencies:[_,E,k]})}return e})();export{w as a};
