import{$e as Gi,Ad as Oe,Af as pt,B as It,Bb as Da,Bd as $i,Bf as ht,C as Ia,Cb as wn,Cd as qi,D as Aa,Dd as _r,F as Ma,Ff as Vt,G as _n,Gc as Bi,Ia as di,If as it,Jc as La,Jf as yn,Kc as De,Kf as Ba,Le as bn,Me as Fi,Pb as $t,Pd as qt,Pe as Vi,Sa as Pa,Tb as Oa,Ue as Ra,Xe as Q,Za as Na,_a as tt,aa as Ri,bb as Xe,jf as Ft,kf as Ui,ma as ci,mf as ut,na as At,nf as W,oa as vn,q as zt,rd as gr,td as Ha,v as ka,vd as zi,w as gn,y as dt}from"./chunk-K5H3SJL5.js";import{a as x}from"./chunk-2WPD26RB.js";import{$b as li,Ac as ya,Cb as pa,Cc as Ne,Cd as Ta,Dd as Ea,Ea as ze,Eb as ha,Ec as Li,Ed as xa,F as sa,Fa as $e,Hb as ge,<PERSON>a as la,Jb as ce,Jd as Hi,Kd as Ca,La as ca,Lb as fa,Lc as Ie,M as ra,Mc as Ae,Na as Ai,Nb as Pi,Qc as kt,Qd as ct,Rb as qe,Wb as re,Xb as se,Yb as ye,Zb as ma,_b as ga,ac as Fe,c as aa,cc as Ve,ec as pe,f as Bt,fc as _a,g as ve,gb as da,gc as va,ha as be,hb as Mi,kc as Ni,lc as Di,ma as oa,mc as Oi,n as xt,na as Y,nc as Ct,ob as te,oc as Ge,qc as Ue,sa as Pe,t as oi,td as Sa,ub as ua,va as Ii,yc as wa,zc as ba}from"./chunk-YK6FMNSY.js";import{a as X,b as ie,e as mr,f as mn,g as ri}from"./chunk-TSRGIXR5.js";var Fa=mr((xn,Cn)=>{"use strict";(function(i,e){typeof xn=="object"&&typeof Cn<"u"?Cn.exports=e():typeof define=="function"&&define.amd?define(e):(i=typeof globalThis<"u"?globalThis:i||self).dayjs_plugin_customParseFormat=e()})(xn,function(){"use strict";var i={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},e=/(\[[^[]*\])|([-_:/.,()\s]+)|(A|a|YYYY|YY?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g,t=/\d\d/,n=/\d\d?/,a=/\d*[^-_:/,()\s\d]+/,s={},r=function(f){return(f=+f)+(f>68?1900:2e3)},l=function(f){return function(m){this[f]=+m}},o=[/[+-]\d\d:?(\d\d)?|Z/,function(f){(this.zone||(this.zone={})).offset=function(m){if(!m||m==="Z")return 0;var _=m.match(/([+-]|\d\d)/g),w=60*_[1]+(+_[2]||0);return w===0?0:_[0]==="+"?-w:w}(f)}],u=function(f){var m=s[f];return m&&(m.indexOf?m:m.s.concat(m.f))},c=function(f,m){var _,w=s.meridiem;if(w){for(var b=1;b<=24;b+=1)if(f.indexOf(w(b,0,m))>-1){_=b>12;break}}else _=f===(m?"pm":"PM");return _},d={A:[a,function(f){this.afternoon=c(f,!1)}],a:[a,function(f){this.afternoon=c(f,!0)}],S:[/\d/,function(f){this.milliseconds=100*+f}],SS:[t,function(f){this.milliseconds=10*+f}],SSS:[/\d{3}/,function(f){this.milliseconds=+f}],s:[n,l("seconds")],ss:[n,l("seconds")],m:[n,l("minutes")],mm:[n,l("minutes")],H:[n,l("hours")],h:[n,l("hours")],HH:[n,l("hours")],hh:[n,l("hours")],D:[n,l("day")],DD:[t,l("day")],Do:[a,function(f){var m=s.ordinal,_=f.match(/\d+/);if(this.day=_[0],m)for(var w=1;w<=31;w+=1)m(w).replace(/\[|\]/g,"")===f&&(this.day=w)}],M:[n,l("month")],MM:[t,l("month")],MMM:[a,function(f){var m=u("months"),_=(u("monthsShort")||m.map(function(w){return w.slice(0,3)})).indexOf(f)+1;if(_<1)throw new Error;this.month=_%12||_}],MMMM:[a,function(f){var m=u("months").indexOf(f)+1;if(m<1)throw new Error;this.month=m%12||m}],Y:[/[+-]?\d+/,l("year")],YY:[t,function(f){this.year=r(f)}],YYYY:[/\d{4}/,l("year")],Z:o,ZZ:o};function h(f){var m,_;m=f,_=s&&s.formats;for(var w=(f=m.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,function(L,D,S){var C=S&&S.toUpperCase();return D||_[S]||i[S]||_[C].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(P,A,T){return A||T.slice(1)})})).match(e),b=w.length,v=0;v<b;v+=1){var g=w[v],y=d[g],E=y&&y[0],N=y&&y[1];w[v]=N?{regex:E,parser:N}:g.replace(/^\[|\]$/g,"")}return function(L){for(var D={},S=0,C=0;S<b;S+=1){var P=w[S];if(typeof P=="string")C+=P.length;else{var A=P.regex,T=P.parser,I=L.slice(C),O=A.exec(I)[0];T.call(D,O),L=L.replace(O,"")}}return function(V){var k=V.afternoon;if(k!==void 0){var M=V.hours;k?M<12&&(V.hours+=12):M===12&&(V.hours=0),delete V.afternoon}}(D),D}}return function(f,m,_){_.p.customParseFormat=!0,f&&f.parseTwoDigitYear&&(r=f.parseTwoDigitYear);var w=m.prototype,b=w.parse;w.parse=function(v){var g=v.date,y=v.utc,E=v.args;this.$u=y;var N=E[1];if(typeof N=="string"){var L=E[2]===!0,D=E[3]===!0,S=L||D,C=E[2];D&&(C=E[2]),s=this.$locale(),!L&&C&&(s=_.Ls[C]),this.$d=function(I,O,V){try{if(["x","X"].indexOf(O)>-1)return new Date((O==="X"?1e3:1)*I);var k=h(O)(I),M=k.year,G=k.month,K=k.day,B=k.hours,F=k.minutes,U=k.seconds,ae=k.milliseconds,j=k.zone,ee=new Date,fe=K||(M||G?1:ee.getDate()),We=M||ee.getFullYear(),et=0;M&&!G||(et=G>0?G-1:ee.getMonth());var ke=B||0,Be=F||0,Ht=U||0,Rt=ae||0;return j?new Date(Date.UTC(We,et,fe,ke,Be,Ht,Rt+60*j.offset*1e3)):V?new Date(Date.UTC(We,et,fe,ke,Be,Ht,Rt)):new Date(We,et,fe,ke,Be,Ht,Rt)}catch{return new Date("")}}(g,N,y),this.init(),C&&C!==!0&&(this.$L=this.locale(C).$L),S&&g!=this.format(N)&&(this.$d=new Date("")),s={}}else if(N instanceof Array)for(var P=N.length,A=1;A<=P;A+=1){E[1]=N[A-1];var T=_.apply(this,E);if(T.isValid()){this.$d=T.$d,this.$L=T.$L,this.init();break}A===P&&(this.$d=new Date(""))}else b.call(this,v)}}})});var Yi=function(i){return i.BICInsurance="bao_hiem_BIC",i.MetlifeInsurance="bao_hiem_Metlife",i.PayInsurancePremiums="thanh_toan_phi_bao_hiem",i.OpenStockAccount="mo_tai_khoan_chung_khoan",i.StockTrading="giao_dich_chung_khoan",i.StockServices="dich_vu_chung_khoan",i.InsuranceServices="dich_vu_bao_hiem",i.BankingServices="dich_vu_ngan_hang",i.RegisterServices="dang_ki_dich_vu",i.CustomerSupport="ho_tro_khach_hang",i.Charity="tu_thien",i.GovernmentBudget="ngan_sach_nha_nuoc",i}(Yi||{});var p={dang_nhap:1,quen_mat_khau:2,trang_chu:3,cai_dat_giao_dien:5,danh_sach_tim_kiem:32,tao_ma_qr:16,lich_su_giao_dich_qr:17,lai_suat:52,ty_gia:53,atm_chi_nhanh:54,dang_ky_truc_tuyen:55,dang_ki_goi_dv:59,email_thong_bao:60,doi_han_muc:61,han_muc_giao_dich:108,thong_bao_ott:109,reward:71,rut_tiet_kiem_online:78,dat_lenh_chuyen_tien_dinh_ky:79,tra_soat_khieu_nai:80,danh_sach_tra_soat_khieu_nai:304,gop_y_dich_vu:303,bao_cao_giao_dich:81,tai_khoan:9,nop_tien_giao_thong:Bi.SERVICE_ID.nop_tien_giao_thong,lich_su_giao_dich:10,quan_ly_ds_tai_khoan_giao_thong:136,lien_ket_tai_khoan_giao_thong:137,danh_sach_tuy_chinh:200,cau_hoi_thuong_gap:106,danh_ba_thu_huong:12,mau_chuyen_tien:13,danh_ba_thu_huong_chuyen_tien_quoc_te:221,chuyen_tien_noi_bo:74,chuyen_tien_ngoai_stk:75,chuyen_tien_ngoai_so_the:76,chuyen_tien:42,chuyen_tien_dinh_ky:43,chuyen_tien_quoc_te:219,tao_dien_chuyen_tien_quoc_te:220,tham_khao_ti_gia_va_phi:222,ctqt_danh_ba_thu_huong:221,thanh_toan_hoa_don_quoc_te:240,danh_sach_tai_khoan:6,tai_khoan_thanh_toan:6,tai_khoan_tien_vay:8,tai_khoan_tiet_kiem:7,dich_vu_the:41,chuyen_doi_the_chip:64,phat_hanh_the:65,phat_hanh_the_truc_tuyen_the_ghi_no_quoc_te:21,phat_hanh_the_truc_tuyen_the_tin_dung_quoc_te:22,phat_hanh_the_truc_tuyen_the_tra_truoc_quoc_te:113,lien_ket_the_de_thanh_toan_vien_phi:67,them_lien_ket_the:23,quan_ly_the_da_lien_ket_va_huy_lien_ket_the_y_te_phi_vat_ly:24,phat_hanh_the_tin_dung_tu_dong:230,tra_gop_the_tin_dung:223,thanh_toan_the_tin_dung:82,tai_khoan_so_dep:56,tai_khoan_dinh_danh:90,mo_tai_khoan_dinh_danh:91,tai_khoan_nhu_y:92,cai_dat:201,doi_mat_khau:204,cai_dat_dang_nhap:72,BIDV_home:229,vay_cam_co:83,vay_cam_co_online:68,vay_nhanh:73,theo_doi_no_vay:270,cham_dut_han_muc_thau_chi:69,tien_vay_danh_sach_chuc_nang:87,tiet_kiem:86,gui_tien_online:37,tiet_kiem_tu_dong:289,mo_tk_tich_luy:203,gui_tich_luy_online:66,rut_tich_luy_online:209,van_tin_san_pham_tiet_kiem_tu_dong:290,cham_dut_san_pham_tiet_kiem_tu_dong:291,thay_doi_thong_tin_tk_tich_luy:202,bao_hiem:70,chung_chi_quy_mo:251,chung_khoan:58,trai_phieu:262,dat_mua_trai_phieu:263,cap_nhap_thong_tin_nha_dau_tu:264,lich_su_dat_mua_trai_phieu:265,ve_may_bay:50,khach_san:48,ve_xe:46,ve_tau:45,vnpay_taxi:94,giao_hang:95,dat_hoa:96,vnshop:49,ve_xem_phim:47,mua_sam_hoan_tien:231,ve_su_kien:226,data_4g:271,voucher_deal_today:272,vietlott_sms:276,dich_vu_golf:279,uu_dai_cua_ban:273,the_game:280,ban_ngoai_te:35,mua_ngoai_te_tien_mat:120,van_tin_giao_dich_mua_ngoai_te:121,bo_sung_chung_tu_mua_ngoai_te:123,cap_nhat_giao_dich_mua_ngoai_te:124,nop_phi_truoc_ba:278,nop_phi_ha_tang_cang_bien:225,tao_lenh_nop_phi_ha_tang_cang_bien:225,nop_thue:314,nop_thue_theo_mst:125,nop_tien_chung_khoan:333,bao_hiem_xa_hoi_DN:290,chia_se_bien_dong_so_du:241,quan_ly_cua_hang:256,quan_ly_qr_shop:258,tao_moi_qr_shop:259,tao_moi_qr_don_hang:260,nap_tien_dt:38,vi_dien_tu:130,danh_sach_dich_vu:4,dang_ky_dich_vu_ott:31,tao_lien_ket_vi_dien_tu:131,cap_nhat_lien_ket_vi_dien_tu:132,nap_tien_vi_dien_tu:134,smart_kids:206,dang_ky_smart_kids:207,thong_tin_smart_kids:208,lich_su_giao_dich_smart_kids:210,chuyen_tien_ung_ho:77,chuyen_tien_tu_thien:77,tang_qua:44,ung_ho_quy_vac_xin:84,quan_ly_tai_chinh_ca_nhan:63,quan_ly_dac_quyen:253,game_green_mission:228,chat_bot:227,gioi_thieu_ban:62,doi_qua:71,thanh_toan_hoa_don:39,quan_ly_mau_thanh_toan:15,danh_sach_thanh_toan_hoa_don_nhom_thanh_toan_hoa_don:14,thanh_toan_sao_ke:82,thanh_toan_vien_phi:24,thanh_toan_truyen_hinh:30,thanh_toan_hoa_don_dinh_ky:99,thanh_toan_tu_dong:99,tao_thanh_toan_hoa_don_dinh_ky:100,lich_su_gioi_thieu:33,tien_vay_tra_vo_khoan_vay:34,quet_ma_qr:40,lay_ma_smart_otp_offline:89,cai_dat_van_tay:205,cai_dat_sinh_trac_hoc:224,vay_ung_luong:267,cai_dat_smart_otp:275,mo_tai_khoan_chung_khoan:295,dat_san_golf:93,dang_ky_qua_ekyc:103,cai_dat_widget:266,lich_su_qua_da_tang_da_nhan:51,rut_tien_mat_tai_atm:57,nap_tien_the_thanh_toan_vien_phi:85,dang_ky_dich_vu_bsms:88,dang_ky_cho_KH_da_co_tk_thanh_toan:104,smart_watch:105,dong_mo_cif:215,dau_an_nam:250,apple_pay:252,tong_dai_cskh:257,xac_thuc_giao_dich_tren_cong:261,ung_ho_khac_phuc_hau_qua_thien_tai:274,dinh_danh_the:281,chuyen_nhuong_online:282,tao_yc_chuyen_nhuong_dich_danh:283,tao_yc_chuyen_nhuong_tu_do:284,mua_tai_khoan_tien_gui:285,quan_ly_giao_dich:286,mua_the_dien_thoai_imedia:287,bidv_securities:288,my_rm:292,lien_ket_bidv_securities:293,huy_lien_ket_bidv_securities:294,trang_chu_tien_ich_cuoc_song:296,danh_muc_san_pham:297,menu_theo_doi_no_vay:298,menu_tra_no_vay:299,webview_hst:300,quan_ly_ngan_sach:301,quan_ly_vi:302,faq:307,huong_dan_giao_dich_an_toan:308,huong_dan_su_dung_dich_vu:309,danh_muc_sp_tiet_kiem:310,danh_muc_sp_vay_von:311,danh_muc_sp_qltc:312,danh_muc_sp_tien_ich_cs:313,danh_muc_sp_dv_cong:314,danh_muc_sp_thanh_toan:315,danh_muc_sp_dv_tieu_thuong:316,danh_muc_sp_dv_ngoai_te:317,danh_muc_sp_bao_hiem_dau_tu:318,danh_muc_sp_thien_nguyen:319,danh_muc_sp_dac_quyen:320,danh_muc_sp_smart_kids:321},oe={tien_dien:1,tien_nuoc:12,truyen_hinh:30,vien_thong:9,quan_ly_chung_cu:14,bhxh_doanh_nghiep:290,bhxh_ca_nhan:310,hoc_phi:10,bao_hiem:3,tai_chinh:50,mua_chung_chi_quy_mo:20,nop_tien_chung_khoan:333,dau_thau:679,ve_may_bay:6,ve_tau:130,logistics:190,bat_dong_san:330,golf:170,nong_nghiep:354,thuc_pham_tieu_dung:390,dich_vu_thu_ho:15,ma_the_cao:70,nap_data_dien_thoai:16,thanh_toan_truc_tuyen:7,giao_thong:150,nap_tien_dien_thoai:8};var Sn=[{id:"TTHD",code:Q.thanh_toan_hoa_don,area:"DICH_VU_TAI_CHINH",name:"menu.thanh_toan",link:"/dich-vu-ngan-hang-so/thanh-toan-hoa-don",class:"swiper-slide fav-item",icon:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg",keywords:"DICH_VU_NGAN_HANG",isFav:!1,inSetting:!0,order:2,requireDefaultAccount:!1,menuType:"DVNH",menuTypeOrder:"2",serviceId:p.thanh_toan_hoa_don,subIcon:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg",otherType:"OTHER_TYPE",orderSetting:3},{id:"NTDT",code:Q.nap_tien_dien_thoai,area:"DICH_VU_TAI_CHINH",name:"menu.nap_tien_dien_thoai",link:"/dich-vu-ngan-hang-so/nap-tien-dien-thoai",class:"swiper-slide fav-item",icon:"./assets/media/svg-ic-main/nap-tien-dien-thoai.svg",inSetting:!0,isFav:!0,order:5,menuType:"DVNH",menuTypeOrder:"5",otherType:"OTHER_TYPE",requireDefaultAccount:!1,serviceId:p.nap_tien_dt,keywords:"DICH_VU_NGAN_HANG",subIcon:"./assets/media/svg-ic-main/nap-tien-dien-thoai.svg"},{id:"TKO",name:"menu.tiet_kiem_online",link:"",children:["GTKO","GTLO","RTKO"],class:"quick-item ubg-accent-3",icon:"./assets/media/svg/light/pig.svg",inSetting:!1,isFav:!1,order:4,area:"TRUY_CAP_NHANH",menuType:"DVNH",menuTypeOrder:"4",vipIcon:"./assets/vip-media/vip/vip-custom/home/<USER>/pig.svg",keywords:"DICH_VU_NGAN_HANG",subIcon:"./assets/media/svg-ic-main/tiet-kiem-online.svg"},{id:"PHT",name:"menu.phat_hanh_the_online",link:"dich-vu-the/phat-hanh-the",class:"swiper-slide fav-item",icon:"./assets/media/svg-ic-main/tt-the-tin-dung.svg",inSetting:!0,isFav:!1,order:1,otherType:"OTHER_TYPE",orderSetting:4,serviceId:p.phat_hanh_the},{id:"TTSK",name:"menu.thanh_toan_sao_ke",link:"dich-vu-the/thanh-toan-the-cho-nguoi-khac",class:"swiper-slide fav-item",icon:"./assets/media/svg-ic-main/thanh-toan-sao-ke.svg",inSetting:!0,isFav:!1,order:1,otherType:"OTHER_TYPE",orderSetting:5,serviceId:p.thanh_toan_sao_ke},{id:"TTVP",name:"menu.thanh_toan_vien_phi",link:"/dich-vu-the/nap-tien-the-y-te",class:"swiper-slide fav-item",icon:"./assets/media/svg-ic-main/thanh-toan-vien-phi.svg",inSetting:!0,isFav:!1,order:1,orderSetting:6,serviceId:p.thanh_toan_vien_phi,otherType:"OTHER_TYPE"},{id:"CTNB",code:Q.ck_noi_bo_khac_chu_tk,name:"menu.chuyen_tien_noi_bo",link:"/chuyen-tien/noi-bo",class:"swiper-slide fav-item",icon:"./assets/media/svg-ic-main/chuyen-tien chung.svg",inSetting:!0,isFav:!1,serviceId:p.chuyen_tien_noi_bo,otherType:"OTHER_TYPE",orderSetting:1},{id:"NDTK",code:Q.ck_nhanh_lien_ngan_hang,name:"menu.chuyen_tien_ngoai",link:"/chuyen-tien/ngoai-bidv",class:"swiper-slide fav-item",icon:"./assets/media/svg-ic-main/chuyen-tien chung.svg",inSetting:!0,isFav:!0,otherType:"OTHER_TYPE",requireDefaultAccount:!1,serviceId:p.chuyen_tien_ngoai_stk},{id:"CDST",code:Q.ck_nhanh_lien_ngan_hang_den_so_the,name:"menu.chuyen_tien_ngoai_den_so_the",link:"/chuyen-tien/ngoai-bidv-qua-so-the",class:"swiper-slide fav-item",icon:"./assets/media/svg-ic-main/chuyen-tien chung.svg",inSetting:!0,isFav:!1,requireDefaultAccount:!1,serviceId:p.chuyen_tien_ngoai_so_the,otherType:"OTHER_TYPE",orderSetting:2},{id:"CTUH_QLDV",name:"menu.chuyen_tien_ung_ho",link:"/chuyen-tien-ung-ho",class:"swiper-slide fav-item",icon:"./assets/media/svg-ic-main/chuyen-tien-tu-thien.svg",inSetting:!0,requireDefaultAccount:!1,menuType:"TT",menuTypeOrder:"2",order:2,serviceId:p.chuyen_tien_ung_ho,keywords:"TU_THIEN",otherType:"OTHER_TYPE",orderSetting:8},{id:"MENU_TUY_CHINH",name:"menu.tuy_chinh_yeu_thich",link:"/cai-dat/tuy-chinh",serviceId:p.danh_sach_tuy_chinh,class:"swiper-slide fav-item d-md-none",icon:"./assets/media/svg-ic-main/tuy-chinh.svg",isFav:!1},{id:"M6",area:"TRUY_CAP_NHANH",name:"menu.dich_vu_the",link:"/dich-vu-the",requireDefaultAccount:!1,menuType:"DVNH",menuTypeOrder:"3",keywords:"DICH_VU_NGAN_HANG",serviceId:p.dich_vu_the,class:"quick-item ubg-primary-3",icon:"./assets/media/svg/light/credit card.svg",vipIcon:"./assets/vip-media/vip/vip-custom/home/<USER>/credit card.svg",order:3,subIcon:"./assets/media/svg-ic-main/tt-the-tin-dung.svg"},{id:"CTQT",name:"menu.chuyen_tien_quoc_te",children:["CTQT1","CTQT2","CTQT3","CTQT4"],link:"/chuyen-tien-quoc-te",requireDefaultAccount:!1,menuType:"DVNH",menuTypeOrder:"3",keywords:"DICH_VU_NGAN_HANG",class:"quick-item ubg-primary-3",icon:"./assets/media/svg-ic-main/chuyen-tien-quoc-te.svg",serviceId:p.chuyen_tien_quoc_te,order:3,subIcon:"./assets/media/svg-ic-main/chuyen-tien-quoc-te.svg"},{id:"CTQT1",name:"menu.tao_dien_chuyen_tien_quoc_te",link:"/chuyen-tien-quoc-te/tao-dien-chuyen-tien",icon:"./assets/media/svg/accent/transfer.svg",serviceId:p.tao_dien_chuyen_tien_quoc_te,order:1,inSetting:!0,isFav:!1,parentId:"CTQT"},{id:"CTQT2",name:"menu.van_tin_giao_dich",link:"/chuyen-tien-quoc-te/van-tin-giao-dich",icon:"./assets/media/svg/accent/finance.svg",order:2,inSetting:!0,isFav:!1,parentId:"CTQT"},{id:"CTQT3",name:"menu.danh_ba_chuyen_tien_quoc_te",link:"/chuyen-tien-quoc-te/danh-ba-thu-huong",icon:"./assets/media/svg/accent/contact.svg",serviceId:p.danh_ba_thu_huong_chuyen_tien_quoc_te,order:3,inSetting:!0,isFav:!1,parentId:"CTQT"},{id:"CTQT4",name:"heading.tham_khao_ty_gia_va_phi",link:"/chuyen-tien-quoc-te/tham-khao-ty-gia-va-phi",icon:"./assets/media/svg/accent/stock.svg",serviceId:p.tham_khao_ti_gia_va_phi,order:4,inSetting:!0,isFav:!1,parentId:"CTQT"},{id:"NT",name:"menu.nop_thue",children:["NT1","NT2"],link:"/nop-thue",requireDefaultAccount:!1,menuType:"NSNC",keywords:"NGAN_SACH_NHA_NUOC",class:"quick-item ubg-primary-3",icon:"./assets/vip-media/svg-ic-main/nop-thue.svg",vipIcon:"./assets/vip-media/svg-ic-main/nop-thue.svg",order:5,subIcon:"./assets/media/svg-ic-main/nop-thue.svg"},{id:"NT1",name:"menu.chinh_chu_nop",link:"/nop-thue/cung-chu",icon:"./assets/media/svg-ic-main/nop-thue.svg",order:1,inSetting:!0,isFav:!1,parentId:"NT"},{id:"NT2",name:"menu.nop_thay_cho_nguoi_khac",link:"/nop-thue/khac-chu",icon:"./assets/media/svg-ic-main/nop-thue.svg",order:2,inSetting:!0,isFav:!1,parentId:"NT"},{id:"NPCB",name:"menu.nop_phi_ha_tang_cang_bien",link:"/nop-phi-ha-tang-cang-bien",requireDefaultAccount:!1,menuType:"NSNC",keywords:"NGAN_SACH_NHA_NUOC",class:"quick-item ubg-primary-3",icon:"./assets/media/svg-ic-main/nop-phi-ha-tang-cang-bien.svg",vipIcon:"./assets/vip-media/svg-ic-main/nop-phi-ha-tang-cang-bien.svg",subIcon:"./assets/media/svg-ic-main/nop-phi-ha-tang-cang-bien.svg",order:11,serviceId:p.tao_lenh_nop_phi_ha_tang_cang_bien},{id:"M7",name:"menu.tai_khoan",link:"/tai-khoan",class:"quick-item ubg-primary",icon:"./assets/media/svg/light/wallet.svg",serviceId:p.danh_sach_tai_khoan,otherType:"TTHD",requireDefaultAccount:!0,noNeedCheckRuleDebit:!0},{id:"M8",area:"TRUY_CAP_NHANH",name:"menu.chuyen_tien",link:"/chuyen-tien",class:"quick-item ubg-primary-gradient",requireDefaultAccount:!1,menuType:"DVNH",menuTypeOrder:"1",icon:"./assets/media/svg/light/transfer.svg",vipIcon:"./assets/vip-media/vip/vip-custom/home/<USER>/transfer.svg",order:1,serviceId:p.chuyen_tien,keywords:"DICH_VU_NGAN_HANG",subIcon:"./assets/media/svg-ic-main/chuyen-tien chung.svg"},{id:"M9",area:"DICH_VU",name:"menu.tang_qua",link:"",icon:"./assets/media/svg-ic-main/tang-qua.svg",isHidden:!0},{id:"M10",area:"DICH_VU",name:"menu.nhan_kieu_hoi",link:"",icon:"./assets/media/svg-ic-main/nhan-kieu-hoi.svg",isHidden:!0},{id:"M11",area:"DICH_VU",name:"menu.ban_ngoai_te",link:"/dich-vu-ngan-hang-so/ban-ngoai-te",icon:"./assets/media/svg-ic-main/ban-ngoai-te.svg",order:6,inSetting:!1,isHidden:!0,requireDefaultAccount:!1},{id:"M33",area:"TIEN_ICH",name:"menu.dang_ky_truc_tuyen",link:"https://ebank.bidv.com.vn/DKNHDT/index.htm?request_locale=vi_VN",linkEN:"https://ebank.bidv.com.vn/DKNHDT/index.htm?request_locale=en_US",icon:"./assets/media/svg-ic-main/dang-ky-truc-tuyen.svg",order:10,inSetting:!1,menuType:"DKDV",menuTypeOrder:"7",keywords:"DANG_KI_DICH_VU",serviceId:p.dang_ky_truc_tuyen},{id:"M12",area:"DICH_VU",name:"menu.tich_diem_doi_qua",link:"",icon:"./assets/media/svg-ic-main/tich-diem-doi-qua.svg",order:6,inSetting:!1,isHidden:!1},{id:"M13",area:"TIEN_ICH",name:"menu.tim_kiem_atm",link:"https://www.bidv.com.vn/vn/atm-chi-nhanh",linkEN:"https://www.bidv.com.vn/en/atm-chi-nhanh",icon:"./assets/media/svg-ic-main/tim-kiem-atm.svg",order:6,inSetting:!0,isFav:!1,menuType:"HTKH",menuTypeOrder:"2",serviceId:p.atm_chi_nhanh,keywords:"HO_TRO_KHACH_HANG",otherType:"OTHER_TYPE",orderSetting:13},{id:"M14",code:Q.ck_noi_bo_dinh_ky,area:"DICH_VU_TAI_CHINH",name:"menu.chuyen_tien_dinh_ky",link:"/dich-vu-ngan-hang-so/chuyen-tien-dinh-ky",icon:"./assets/media/svg-ic-main/chuyen-tien-dinh-ky.svg",order:3,inSetting:!0,isFav:!1,requireDefaultAccount:!1,menuType:"DKDV",menuTypeOrder:"3",serviceId:p.chuyen_tien_dinh_ky,keywords:"DANG_KI_DICH_VU",otherType:"OTHER_TYPE",orderSetting:12},{id:"M15",area:"DICH_VU_TAI_CHINH",name:"menu.thanh_toan_hoa_don_dinh_ky",link:"/dich-vu-ngan-hang-so/thanh-toan-hoa-don-dinh-ky",icon:"./assets/media/svg-ic-main/thanh-toan.svg",order:2,menuType:"DKDV",menuTypeOrder:"2",keywords:"DANG_KI_DICH_VU",serviceId:p.thanh_toan_hoa_don_dinh_ky},{id:"M16",area:"TIEN_ICH",name:"menu.lai_suat",link:"https://www.bidv.com.vn/vn/tra-cuu-lai-suat",linkEN:"https://www.bidv.com.vn/en/tra-cuu-lai-suat",icon:"./assets/media/svg-ic-main/lai-suat.svg",order:7,inSetting:!0,isFav:!1,menuType:"HTKH",menuTypeOrder:"3",serviceId:p.lai_suat,keywords:"HO_TRO_KHACH_HANG",otherType:"OTHER_TYPE",orderSetting:14},{id:"M17",area:"TIEN_ICH",name:"menu.ty_gia",link:"https://www.bidv.com.vn/vn/ty-gia-ngoai-te",linkEN:"https://www.bidv.com.vn/en/ty-gia-ngoai-te",icon:"./assets/media/svg-ic-main/ty-gia.svg",order:8,inSetting:!0,isFav:!1,menuType:"HTKH",menuTypeOrder:"4",serviceId:p.ty_gia,keywords:"HO_TRO_KHACH_HANG",otherType:"OTHER_TYPE",orderSetting:11},{id:"M18",area:"TIEN_ICH",name:"menu.tra_soat",link:"",children:["M31","M32"],icon:"./assets/media/svg-ic-main/ho-tro.svg",order:9,inSetting:!0,isFav:!1,menuType:"HTKH",menuTypeOrder:"5",keywords:"HO_TRO_KHACH_HANG",otherType:"OTHER_TYPE",orderSetting:15},{id:"M19",area:"DICH_VU",name:"menu.smart_watch",link:"",icon:"./assets/media/svg-ic-main/smart-watch.svg",isHidden:!0},{id:"M20",area:"DICH_VU",name:"menu.dat_lich_tai_quay",link:"",icon:"./assets/media/svg-ic-main/dat-lich-tai-quay.svg",isHidden:!0},{id:"M21",area:"CAI_DAT",name:"menu.quan_ly_danh_ba",link:"/cai-dat/quan-ly-danh-ba",serviceId:p.danh_ba_thu_huong,icon:"./assets/media/svg-ic-main/quan-ly-danh-ba.svg"},{id:"M22",area:"CAI_DAT",name:"menu.mau_thanh_toan_hoa_don",link:"/cai-dat/quan-ly-mau-thanh-toan",serviceId:p.quan_ly_mau_thanh_toan,icon:"./assets/media/svg-ic-main/mau-thanh-toan-hoa-don.svg"},{id:"BCGD",code:Q.bao_cao_giao_dich,area:"TIEN_ICH",name:"menu.bao_cao_giao_dich",link:"/dich-vu-ngan-hang-so/bao-cao-giao-dich",icon:"./assets/media/svg-ic-main/bao-cao-giao-dich.svg",order:5,inSetting:!0,menuType:"HTKH",menuTypeOrder:"1",serviceId:p.bao_cao_giao_dich,keywords:"HO_TRO_KHACH_HANG",otherType:"OTHER_TYPE",orderSetting:10},{id:"M24",area:"CAI_DAT",name:"menu.smart_otp",link:"",icon:"./assets/media/svg-ic-main/smart-otp.svg",isHidden:!0},{id:"M25",area:"CAI_DAT",name:"menu.bien_dong_ott",link:"",icon:"./assets/media/svg-ic-main/bien-dong-ott.svg",isHidden:!0},{id:"M26",area:"CAI_DAT",name:"menu.doi_mk",link:"/cai-dat/doi-mat-khau",serviceId:p.doi_mat_khau,icon:"./assets/media/svg-ic-main/doi-mk.svg"},{id:"M27",area:"CAI_DAT",name:"menu.tai_khoan_thanh_toan",link:"",icon:"./assets/media/svg-ic-main/tai-khoan-thanh-toan.svg",isHidden:!0},{id:"M28",area:"CAI_DAT",name:"menu.cai_dat_khac",link:"",icon:"./assets/media/svg-ic-main/cai-dat-khac.svg",isHidden:!0},{id:"GTKO",name:"menu.gui_tiet_kiem",link:"/dich-vu-ngan-hang-so/tiet-kiem/gui-tiet-kiem",icon:"./assets/media/svg-ic-main/tiet-kiem-online.svg",iconSecondary:"./assets/media/svg/accent/pig.svg",inSetting:!0,isFav:!0,requireDefaultAccount:!1,serviceId:p.gui_tien_online,parentId:"TKO"},{id:"GTLO",name:"gui_tich_luy_online",link:"/gui-tich-luy-online",icon:"./assets/media/svg-ic-main/tiet-kiem-online.svg",iconSecondary:"./assets/media/svg/accent/plant.svg",inSetting:!0,isFav:!1,requireDefaultAccount:!1,serviceId:p.gui_tich_luy_online,orderSetting:7,parentId:"TKO"},{id:"MTKGTLO",name:"mo_tk_tich_luy",link:"/mo-tai-khoan-tich-luy",icon:"./assets/media/svg-ic-main/tiet-kiem-online.svg",iconSecondary:"./assets/media/svg/accent/plus.svg",serviceId:p.mo_tk_tich_luy},{id:"RGTLO",name:"rut_tich_luy",link:"/rut-tich-luy-online",icon:"./assets/media/svg-ic-main/tiet-kiem-online.svg",iconSecondary:"./assets/media/svg/accent/withdraw.svg",serviceId:p.rut_tich_luy_online},{id:"RTKO",code:Q.rut_tien_online,name:"menu.rut_tien_tiet_kiem_online",link:"/dich-vu-ngan-hang-so/tiet-kiem/rut-tiet-kiem",icon:"./assets/media/svg-ic-main/tiet-kiem-online.svg",iconSecondary:"./assets/media/svg/accent/withdraw.svg",inSetting:!0,isFav:!1,requireDefaultAccount:!1,serviceId:p.rut_tiet_kiem_online,orderSetting:8,parentId:"TKO"},{id:"TKNY",name:"menu.tai_khoan_nhu_y",link:"",children:[],class:"quick-item ubg-accent-3",icon:"./assets/media/svg-ic-main/mo-tk-so-dep.svg",inSetting:!1,isFav:!1,order:4,menuType:"DKDV",menuTypeOrder:"4",vipIcon:"./assets/vip-media/svg-ic-main/mo-tk-so-dep.svg",keywords:"DANG_KI_DICH_VU",subIcon:"./assets/media/svg-ic-main/mo-tk-so-dep.svg"},{id:"M31",name:"menu.tra_soat_giao_dich",serviceId:p.tra_soat_khieu_nai,link:"/dich-vu-ngan-hang-so/tra-soat",icon:".//assets/media/svg/accent/file-search.svg"},{id:"M32",name:"menu.gop_y_dich_vu",link:"/dich-vu-ngan-hang-so/khieu-nai",icon:"./assets/media/svg/accent/bill.svg"},{id:"M33",area:"TIEN_ICH",name:"menu.thay_doi_han_muc",link:"/cai-dat/doi-han-muc-giao-dich",icon:"./assets/media/svg-ic-main/doi-han-muc.svg",requireDefaultAccount:!0,order:6,menuType:"DKDV",menuTypeOrder:"6",serviceId:p.doi_han_muc,keywords:"DANG_KI_DICH_VU"},{id:"M34",area:"TIEN_ICH",name:"menu.doi_goi_dich_vu",link:"/cai-dat/dang-ky-goi-dich-vu",icon:"./assets/media/svg-ic-main/dang-ky-goi-dv.svg",requireDefaultAccount:!0,order:5,menuType:"DKDV",menuTypeOrder:"5",keywords:"DANG_KI_DICH_VU",serviceId:p.dang_ki_goi_dv},{id:"M36",name:"menu.mo_tai_khoan_so_dep",link:"/mo-tai-khoan-so-dep",icon:"./assets/media/svg-ic-main/mo-tk-so-dep.svg",serviceId:p.tai_khoan_so_dep},{id:"M37",area:"TIEN_ICH",name:"menu.dang_ky_email_thong_bao",link:"/cai-dat/dang-ky-email",icon:"./assets/media/svg-ic-main/dang-ky-email.svg",keywords:"DANG_KI_DICH_VU",order:4,menuType:"DKDV",menuTypeOrder:"4",serviceId:p.email_thong_bao},{id:"M38",area:"DICH_VU_TAI_CHINH",name:"menu.chung_khoan",link:"https://dangky.bsc.com.vn/Home/OpenAccountSMB",icon:"./assets/media/svg-ic-main/chung-khoan.svg",order:1,popup:Yi.StockServices},{id:"M39",area:"TRUY_CAP_NHANH",name:"menu.bidv_reward",link:"/rewards",class:"quick-item ubg-accent-2",icon:"./assets/media/svg/light/reward.svg",requireDefaultAccount:!1,favoriteIcon:"./assets/media/svg-ic-main/reward.svg",vipIcon:"./assets/vip-media/vip/vip-custom/home/<USER>/reward.svg",inSetting:!0,isFav:!0,order:4,serviceId:p.reward},{id:"M40",area:"DICH_VU_TAI_CHINH",name:"menu.bao_hiem",link:"",icon:"./assets/media/svg-ic-main/bao-hiem.svg",order:1,popup:Yi.InsuranceServices},{id:"TTTD1",name:"menu.thanh_toan_tien_dien",link:"/dich-vu-ngan-hang-so/thanh-toan-hoa-don",icon:"./assets/media/svg/accent/electronic.svg",iconSecondary:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg",inSetting:!0,isFav:!1,requireDefaultAccount:!1,otherType:"TTHD",favoriteIcon:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg",orderSetting:1},{id:"TTTD2",name:"menu.thanh_toan_tien_nuoc",link:"/dich-vu-ngan-hang-so/thanh-toan-hoa-don",icon:"./assets/media/svg/accent/water.svg",iconSecondary:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg",inSetting:!0,isFav:!1,requireDefaultAccount:!1,otherType:"TTHD",favoriteIcon:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg",orderSetting:2},{id:"TTTD3",name:"menu.thanh_toan_vien_thong",link:"/dich-vu-ngan-hang-so/tiet-kiem/rut-tiet-kiem",icon:"./assets/media/svg/accent/wifi.svg",iconSecondary:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg",inSetting:!0,isFav:!1,requireDefaultAccount:!1,otherType:"TTHD",favoriteIcon:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg",orderSetting:3},{id:"TTTD4",name:"menu.thanh_toan_truyen_hinh",link:"/dich-vu-ngan-hang-so/tiet-kiem/rut-tiet-kiem",icon:"./assets/media/svg/accent/television.svg",iconSecondary:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg",inSetting:!0,isFav:!1,requireDefaultAccount:!1,otherType:"TTHD",favoriteIcon:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg",orderSetting:4},{id:"TTTD5",name:"menu.bao_hiem_xa_hoi_ca_nhan",link:"/dich-vu-ngan-hang-so/tiet-kiem/rut-tiet-kiem",icon:"./assets/media/svg/accent/personal ins.svg",iconSecondary:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg",inSetting:!0,isFav:!1,requireDefaultAccount:!1,otherType:"TTHD",favoriteIcon:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg",orderSetting:5},{id:"TTTD6",name:"menu.nop_tien_chung_khoan",link:"/dich-vu-ngan-hang-so/tiet-kiem/rut-tiet-kiem",icon:"./assets/media/svg/accent/stock.svg",iconSecondary:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg",inSetting:!0,isFav:!1,requireDefaultAccount:!1,otherType:"TTHD",favoriteIcon:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg",orderSetting:6},{id:"TTTD7",name:"menu.nop_thue",link:"/dich-vu-ngan-hang-so/tiet-kiem/rut-tiet-kiem",icon:"./assets/media/svg/accent/tax.svg",iconSecondary:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg",inSetting:!0,isFav:!1,requireDefaultAccount:!1,otherType:"TTHD",favoriteIcon:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg",orderSetting:7},{id:"TTTD8",name:"menu.nop_tien_giao_thong",link:"/tai-khoan-giao-thong",icon:"./assets/media/svg/accent/bus.svg",iconSecondary:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg",inSetting:!0,isFav:!1,requireDefaultAccount:!1,serviceId:p.nop_tien_giao_thong,otherType:"TTHD",favoriteIcon:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg",orderSetting:8},{id:"TTTD9",name:"menu.tai_chinh",link:"/dich-vu-ngan-hang-so/tiet-kiem/rut-tiet-kiem",icon:"./assets/media/svg/accent/finance.svg",iconSecondary:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg",inSetting:!0,isFav:!1,requireDefaultAccount:!1,otherType:"TTHD",favoriteIcon:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg",orderSetting:9},{id:"TTTD10",name:"menu.hoc_phi_le_phi_thi",link:"/dich-vu-ngan-hang-so/tiet-kiem/rut-tiet-kiem",icon:"./assets/media/svg/accent/school fee.svg",iconSecondary:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg",inSetting:!0,isFav:!1,requireDefaultAccount:!1,otherType:"TTHD",favoriteIcon:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg",orderSetting:10},{id:"TTTD11",name:"menu.bao_hiemm",link:"/dich-vu-ngan-hang-so/tiet-kiem/rut-tiet-kiem",icon:"./assets/media/svg/accent/insurance.svg",iconSecondary:"./assets/media/svg/accent/withdraw.svg",inSetting:!0,isFav:!1,requireDefaultAccount:!1,otherType:"TTHD",favoriteIcon:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg",orderSetting:11},{id:"TTTD13",name:"menu.ve_may_bay",link:"/dich-vu-ngan-hang-so/tiet-kiem/rut-tiet-kiem",icon:"./assets/media/svg/accent/plane.svg",iconSecondary:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg",inSetting:!0,isFav:!1,requireDefaultAccount:!1,otherType:"TTHD",favoriteIcon:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg",orderSetting:13},{id:"TTTD14",name:"menu.bao_hiem_xa_hoi_doanh_nghiep",link:"/dich-vu-ngan-hang-so/tiet-kiem/rut-tiet-kiem",icon:"./assets/media/svg/accent/company ins.svg",iconSecondary:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg",inSetting:!0,isFav:!1,requireDefaultAccount:!1,otherType:"TTHD",favoriteIcon:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg",orderSetting:14},{id:"MTKDN",name:"heading.mo_tai_khoan_dinh_danh",link:"/tai-khoan-dinh-danh/mo-tai-khoan-dinh-danh",icon:"./assets/media/svg-ic-main/tai-khoan-dinh-danh.svg",serviceId:p.mo_tai_khoan_dinh_danh},{id:"QLCH",name:"quan_ly_cua_hang",link:"/quan-ly-cua-hang",inSetting:!0,isFav:!1,requireDefaultAccount:!1,serviceId:p.quan_ly_qr_shop,order:9,favoriteIcon:"./assets/media/svg-ic-main/qr-shop.svg",area:"DICH_VU_TAI_CHINH",class:"quick-item ubg-accent-2",keywords:"DICH_VU_NGAN_HANG",menuType:"DVNH",menuTypeOrder:"9",otherType:"OTHER_TYPE",icon:"./assets/media/svg-ic-main/qr-shop.svg",vipIcon:"./assets/media/svg-ic-main/qr-shop.svg",subIcon:"./assets/media/svg-ic-main/qr-shop.svg"},{id:"TKGT",name:"giao_thong",link:"",inSetting:!0,isFav:!1,requireDefaultAccount:!1,orderSetting:8,order:8,children:[],favoriteIcon:"./assets/media/svg-ic-main/tai-khoan-giao-thong.svg",area:"DICH_VU_TAI_CHINH",class:"swiper-slide fav-item",keywords:"DANG_KI_DICH_VU",menuType:"DKDV",menuTypeOrder:"8",otherType:"OTHER_TYPE",icon:"./assets/media/svg-ic-main/tai-khoan-giao-thong.svg",vipIcon:"./assets/vip-media/svg-ic-main/tai-khoan-giao-thong.svg",subIcon:"./assets/media/svg-ic-main/tai-khoan-giao-thong.svg"},{id:"QLTKGT",name:"quan_ly_tai_khoan_giao_thong",link:"/tai-khoan-giao-thong",serviceId:p.quan_ly_ds_tai_khoan_giao_thong,icon:"./assets/media/svg/accent/contact.svg"},{id:"LKTKGT",name:"tai_khoan_giao_thong",link:"/tai-khoan-giao-thong/lien-ket-vi-dien-tu",serviceId:p.lien_ket_tai_khoan_giao_thong,icon:"./assets/media/svg-ic-main/tai-khoan-giao-thong.svg"},{id:"TGTTD",name:"heading.tra_gop_the_tin_dung",link:"/dich-vu-the/tra-gop-the-tin-dung",serviceId:p.tra_gop_the_tin_dung,icon:"./assets/media/svg-ic-main/tra-gop-the-tin-dung.svg"},{id:"PHTTDQT",name:"menu.phat_hanh_the_truc_tuyen_the_tin_dung_quoc_te",link:"/dich-vu-the/phat-hanh-the/tin-dung-quoc-te",serviceId:p.phat_hanh_the_truc_tuyen_the_tin_dung_quoc_te,icon:"./assets/media/svg-ic-main/tt-the-tin-dung.svg"},{id:"PHTGNQT",name:"menu.phat_hanh_the_truc_tuyen_the_ghi_no_quoc_te",link:"/dich-vu-the/phat-hanh-the/ghi-noi-quoc-te",serviceId:p.phat_hanh_the_truc_tuyen_the_ghi_no_quoc_te,icon:"./assets/media/svg-ic-main/tt-the-tin-dung.svg"},{id:"PHTTTQT",name:"menu.phat_hanh_the_truc_tuyen_the_tra_truoc_quoc_te",link:"/dich-vu-the/phat-hanh-the/tra-truoc-quoc-te",serviceId:p.phat_hanh_the_truc_tuyen_the_tra_truoc_quoc_te,icon:"./assets/media/svg-ic-main/tt-the-tin-dung.svg"},{id:"TTTHDDK",name:"menu.thanh_toan_hoa_don_dinh_ky",link:"/dich-vu-ngan-hang-so/thanh-toan-hoa-don-dinh-ky/dang-ky-moi",serviceId:p.tao_thanh_toan_hoa_don_dinh_ky,icon:"./assets/media/svg-ic-main/thanh-toan.svg"},{id:"DLCTDK",name:"heading.dat_lenh_chuyen_tien",link:"/dich-vu-ngan-hang-so/chuyen-tien-dinh-ky",serviceId:p.dat_lenh_chuyen_tien_dinh_ky,icon:"./assets/media/svg-ic-main/chuyen-tien-dinh-ky.svg"},{id:"CDTTSTC",name:"menu.chuyen_doi_the_tu_sang_the_chip",link:"/dich-vu-the/chuyen-doi-the-tu-sang-the-chip/danh-sach-the",serviceId:p.chuyen_doi_the_chip,icon:"./assets/media/svg/accent/transfer.svg"},{id:"HDSDDV",name:"huong_dan_su_dung_dich_vu",link:"https://omni.bidv.com.vn/static/bidv/IB_BIDV_Omni_HDSD_Med.pdf",serviceId:p.huong_dan_su_dung_dich_vu,icon:"./assets/media/svg/accent/book.svg"},{id:"NTTHD",name:"menu.thanh_toan",link:"/dich-vu-ngan-hang-so/thanh-toan-hoa-don",serviceId:p.danh_sach_thanh_toan_hoa_don_nhom_thanh_toan_hoa_don,icon:"./assets/media/svg-ic-main/thanh-toan-hoa-don.svg"},{id:"CHTG",name:"cau_hoi_thuong_gap",link:"/thong-tin/qa",serviceId:p.cau_hoi_thuong_gap,icon:"./assets/media/svg/accent/question.svg"},{id:"TMQRS",name:"tao_qr_cua_hang",link:"/quan-ly-cua-hang/tao-qr-cua-hang",serviceId:p.tao_moi_qr_shop,icon:"./assets/media/svg-ic-main/qr-shop.svg"},{id:"TMQRDH",name:"tao_don_hang",link:"/quan-ly-cua-hang/tao-don-hang",serviceId:p.tao_moi_qr_don_hang,icon:"./assets/media/svg-ic-main/qr-shop.svg"}];var za=(()=>{class i{transform(t,n){if(!t)return"";if(n===2){if(t.split(" ").length<2)return t.slice(0,1).toUpperCase();let s=t.split(" ")[0],r=t.split(" ")[t.split(" ").length-1];return s.slice(0,1).toUpperCase()+r.slice(0,1).toUpperCase()}return t?t.split(" ").map(a=>a.slice(0,1)).join("").toUpperCase():""}static \u0275fac=function(n){return new(n||i)};static \u0275pipe=Ii({name:"shortenName",type:i,pure:!0,standalone:!0})}return i})();var vr=i=>({"app-avt--wrapper":!0,"app-avt--wrapper-change":i});function wr(i,e){if(i&1&&(re(0,"div",1),Ge(1),Ie(2,"shortenName"),se()),i&2){let t=pe();te(),Ue(" ",Ae(2,1,t.content)," ")}}function br(i,e){if(i&1&&ye(0,"app-svg",2),i&2){let t=pe(),n=ya(1);ce("src",t.content)("size",n)("colorChange",t.changeColorIcon)}}function yr(i,e){if(i&1){let t=Fe();re(0,"button",4),Ve("click",function(){ze(t);let a=pe();return $e(a.handleChangeAvt())}),ye(1,"app-svg",5),se()}if(i&2){let t=pe();te(),ce("src",t.iconChange)("colorChange",t.iconChange2Color)}}function Sr(i){let e=Object.keys(It).find(t=>It[t]===i);return e?It[e]:It.Md}var bc=(()=>{class i{rank=vn.None;size=It.Md;type=Ia.Icon;content="assets/media/icons/solid/profile.svg";round=!1;style=Aa.Bank;changeColorIcon=!1;iconChange="media/icons/doutone/icon-he-thong/ht-edit.svg";enableChange=!1;iconChange2Color=!0;iconSize;active=!1;changeAvt=new Ai;get hostClass(){return this.initClass()}SIZE_ICON_CONTENT={bank:{xs:4,sm:6,md:8,midMd:8,lg:12,xxl:14},light:{xs:4,sm:4,md:6,midMd:5,lg:8,xxl:10}};initClass(){let t=["app-avt","app-avt-"+this.type,"app-avt-bg--"+this.style];return this.size!==It.Md&&t.push("app-avt-"+this.size),this.rank!==vn.None&&t.push("app-avt-rank--"+this.rank),this.round&&t.push("app-avt-rounded"),this.active&&t.push("app-avt-active"),t.join(" ")}UI=Y(Ft);handleChangeAvt(){this.changeAvt.emit()}static \u0275fac=function(n){return new(n||i)};static \u0275cmp=Pe({type:i,selectors:[["app-avatar"],["","app-avatar",""]],hostVars:2,hostBindings:function(n,a){n&2&&Pi(a.hostClass)},inputs:{rank:"rank",size:[2,"size","size",Sr],type:"type",content:"content",round:"round",style:"style",changeColorIcon:"changeColorIcon",iconChange:"iconChange",enableChange:"enableChange",iconChange2Color:"iconChange2Color",iconSize:"iconSize",active:"active"},outputs:{changeAvt:"changeAvt"},standalone:!0,features:[ha,Ne],decls:5,vars:6,consts:[[3,"ngClass"],[1,"app-avt--title"],[3,"src","size","colorChange"],["type","button","aria-label","button change avt",1,"app-avt--change-btn"],["type","button","aria-label","button change avt",1,"app-avt--change-btn",3,"click"],[3,"src","colorChange"]],template:function(n,a){if(n&1&&(re(0,"div",0),wa(1),ge(2,wr,3,3,"div",1)(3,br,1,3,"app-svg",2),se(),ge(4,yr,2,2,"button",3)),n&2){let s;ce("ngClass",Li(4,vr,a.enableChange)),te(),ba(a.iconSize||a.SIZE_ICON_CONTENT[a.style][a.size]),te(),qe((s=a.type)==="text"?2:3),te(2),qe(a.enableChange?4:-1)}},dependencies:[ct,Ta,qt,za]})}return i})();var me=function(i){return i.Month="MA",i.Day="DA",i.Week="WA",i.Quarter="QA",i.Year="YA",i.PeriodicallyYearEnd="YAE",i.EndOfMonth="MAE",i.EndOfQuarter="QAE",i.EndOfyear="YEAREND",i.EveryMonthOn="MN",i.HalfMonth="SA",i.EndOfQuarterDay10="QA10",i}(me||{}),Tr=function(i){return i.Register="1",i.NotRegister="0",i}(Tr||{}),Er=function(i){return i.Saving="0",i.Cumulative="1",i}(Er||{});var xr=function(i){return i.Year="Y",i.Month="M",i.Week="W",i.Quarter="P",i.Day="D",i}(xr||{}),Cr=function(i){return i.NonRenewal="0",i.RenewalPrincipal="1",i.RenewalPrincipalAndInterest="2",i.AutoRenewal="3",i}(Cr||{});var qa=(()=>{class i{apiService=Y(pt);storageService=Y(De);modalService=Y(ut);loadingService=Y(ht);router=Y(zt);translate=Y(Oe);expandSearch$=new ve(!1);orderPaymentServices$=new ve(null);expandSearch=this.expandSearch$.asObservable();changeExpandSearch(t){this.expandSearch$.next(t)}getListPayment(t,n){return this.apiService.requestProcess(t,W.LAY_DANH_SACH_THANH_TOAN,n)}getListPaymentIb(t,n){return this.apiService.requestProcess(t,W.LAY_DANH_SACH_THANH_TOAN_TREN_IB,n)}getListPaymentServiceTemplate(t,n){return this.apiService.requestProcess(t,W.LAY_DANH_SACH_MAU_THANH_TOAN,n)}getListPaymentServiceByOrder(t,n){return this.apiService.requestProcess(t,W.LAY_DANH_SACH_DV_THANH_TOAN_CO_THU_TU,n)}getListPaymentServiceDetail(t,n){return this.apiService.requestProcess(t,W.CHI_TIET_DV_THANH_TOAN,n)}getInvoiceDetail(t){return this.apiService.requestProcess(t,W.LAY_THONG_TIN_HOA_DON)}initBillPayment(t){return this.apiService.requestProcess(t,W.KHOI_TAO_THANH_TOAN_HOA_DON)}confirmBillPayment(t){return this.apiService.requestProcess(t,W.XAC_NHAN_THANH_TOAN_HOA_DON)}getSavingTerm(t){return this.apiService.requestProcess(t,W.KY_HAN_TIET_KIEM)}getListSavingProduct(t){return this.apiService.requestProcess(t,W.LAY_DANH_SACH_SAN_PHAM_TIET_KIEM)}getListDepositProducts(t){return this.apiService.requestProcess(t,W.LAY_DANH_SACH_SAN_PHAM_TIEN_GUI)}getListCumulativeDepositProduct(t){return this.apiService.requestProcess(t,W.LAY_DANH_SACH_SAN_PHAM_TICH_LUY)}querySavingsInformationSolutions(t){return this.apiService.requestProcess(t,W.TRUY_VAN_THONG_TIN_GIAI_PHAP_TIET_KIEM_TU_DONG)}queryAccountInformationSolutions(t){return this.apiService.requestProcess(t,W.TRUY_VAN_THONG_TIN_TAI_KHOAN_DANG_KY_TIET_KIEM_TU_DONG)}queryEndSavingAutomatic(t){return this.apiService.requestProcess(t,W.TRUY_VAN_THONG_TIN_CHAM_DUT_TIET_KIEM_TU_DONG)}querySavingAutomaticHistory(t){return this.apiService.requestProcess(t,W.TRUY_VAN_THONG_TIN_LOI_NHUAN_TIET_KIEM_TU_DONG)}initSavingAutomatic(t){return this.apiService.requestProcess(t,W.KHOI_TAO_TIET_KIEM_TU_DONG)}initEndSavingAutomatic(t){return this.apiService.requestProcess(t,W.KHOI_TAO_CHAM_DUT_TIET_KIEM_TU_DONG)}confirmEndSavingAutomatic(t){return this.apiService.requestProcess(t,W.XAC_NHAN_CHAM_DUT_TIET_KIEM_TU_DONG)}querySavingsInterestRateInfor(t){return this.apiService.requestProcess(t,W.TRUY_VAN_THONG_TIN_LAI_SUAT_TIET_KIEM)}queryMinimumSavingsAmountInfor(t){return this.apiService.requestProcess(t,W.TRUY_VAN_THONG_TIN_SO_TIEN_TOI_THIEU_TIET_KIEM)}getMaturityMethod(t){return this.apiService.requestProcess(t,W.PHUONG_THUC_DAO_HAN,!0)}initOpenSavings(t){return this.apiService.requestProcess(t,W.KHOI_TAO_MO_TIET_KIEM)}confirmOpenSavings(t){return this.apiService.requestProcess(t,W.XAC_NHAN_MO_TIET_KIEM)}initSavingsWithdrawal(t){return this.apiService.requestProcess(t,W.KHOI_TAO_TAT_TOAN_TIET_KIEM)}confirmSavingsWithdrawal(t){return this.apiService.requestProcess(t,W.XAC_NHAN_TAT_TOAN_TIET_KIEM)}formatFrequency(t){let n=t.match(/\d+/)?.[0],a=n?+n:"",s=t.match(/[A-Z]+$/)?.[0],r;if(a&&s)switch(s){case me.Day:a===1?r={des:"hang_ngay"}:r={des:"x_ngay_mot_lan",data:{number:a}};break;case me.Month:a===1?r={des:"hang_thang"}:r={des:"x_thang_mot_lan",data:{number:a}};break;case me.Week:a===1?r={des:"hang_tuan"}:r={des:"x_tuan_mot_lan",data:{number:a}};break;case me.Quarter:a===1?r={des:"hang_quy"}:r={des:"x_quy_mot_lan",data:{number:a}};break;case me.Year:a===1?r={des:"hang_nam"}:r={des:"x_nam_mot_lan",data:{number:a}};break;case me.EndOfQuarter:a===1?r={des:"cuoi_moi_quy"}:r={des:"x_cuoi_quy_mot_lan",data:{number:a}};break;case me.EndOfMonth:a===1?r={des:"cuoi_moi_thang"}:r={des:"x_cuoi_thang_mot_lan",data:{number:a}};break;case me.PeriodicallyYearEnd:a===1?r={des:"cuoi_moi_nam"}:r={des:"x_cuoi_nam_mot_lan",data:{number:a}};break;case me.EndOfQuarterDay10:r={des:"ngay_10_hang_quy"};break}else if(s)!a&&s===me.EndOfyear&&(r={des:"cuoi_nam_tai_chinh"});else{let l=/(\d+)([A-Za-z]+)(\d+[-+]\d+)?/,o=t.match(l);if(o){s=o[2];let u=o[3];switch(s){case me.Month:if(u){let d=u.match(/(\d+)([-+])(\d+)/);if(d?.length){let h=d[1],f=d[2],m=d[3];+h==1&&f==="-"&&a===1?r={des:"1_thang_vao_ngay_1_truoc_z",data:{number:a,beforeNum:m}}:f==="-"?r={des:"x_thang_vao_ngay_y_truoc_z",data:{number:a,beforeNum:m,date:h}}:f==="+"&&(a===1?r={des:"1_thang_vao_ngay_y_sau_z",data:{date:h,afterNum:m}}:r={des:"x_thang_vao_ngay_y_sau_z",data:{number:a,date:h,afterNum:m}})}}else t==="1MA1"?r={des:"hang_thang_vao_ngay_dau_tien"}:r={des:"x_thang_vao_ngay_y",data:{number:a,subUnit:n?n[0]:""}};break;case me.EveryMonthOn:r={des:"moi_thang_vao_ngay_cua_thang_dau",data:{number:a,subUnit:n?n[0]:""}};break;case me.Quarter:r={des:"x_quy_vao_ngay_y",data:{number:a,subUnit:n?n[0]:""}};break;case me.HalfMonth:let c=u.match(/(\d+)([-+])(\d+)/);if(c?.length){let d=c[1],h=c[3];r={des:"nua_thang_vao_ngay_y_va_z",data:{date:d,beforeNum:h}}}break;case me.Year:r={des:"x_nam_vao_ngay_y",data:{number:a,subUnit:n?n[0]:""}};break}}}return r}getListPeriodicalPayment(t){return this.apiService.requestProcess(t,W.DANH_SACH_THANH_TOAN_DINH_KY,!0)}initPeriodicalPayment(t){return this.apiService.requestProcess(t,W.KHOI_TAO_THANH_TOAN_DINH_KY)}confirmPeriodicalPayment(t){return this.apiService.requestProcess(t,W.XAC_NHAN_THANH_TOAN_DINH_KY)}getListPeriodicalPaymentBill(t){return this.apiService.requestProcess(t,W.DANH_SACH_HOA_DON_THANH_TOAN_DINH_KY)}getListTransaction(t){return this.apiService.requestProcess(t,W.DANH_SACH_GIAO_DICH)}initUpdateDebitAccount(t){return this.apiService.requestProcess(t,W.CAP_NHAT_TAI_KHOAN_TRICH_NO_DINH_KY.KHOI_TAO)}confirmUpdateDebitAccount(t){return this.apiService.requestProcess(t,W.CAP_NHAT_TAI_KHOAN_TRICH_NO_DINH_KY.XAC_NHAN)}confirmUnsubscribePeriodicalPayment(t){return this.apiService.requestProcess(t,W.XAC_NHAN_HUY_THANH_TOAN_DINH_KY)}getListPaymentTemplate(t,n){return this.apiService.requestProcess(t,W.DANH_SACH_MAU_THANH_TOAN,n)}addPaymentTemplate(t){return this.apiService.requestProcess(t,W.THEM_MAU_THANH_TOAN)}updatePaymentTemplate(t){return this.apiService.requestProcess(t,W.SUA_MAU_THANH_TOAN)}deletePaymentTemplate(t){return this.apiService.requestProcess(t,W.XOA_MAU_THANH_TOAN)}updateFeeNotice(t){return this.apiService.requestProcess(t,W.CAP_NHAT_THONG_BAO_CUOC)}getListStock(t,n){return this.apiService.requestProcess({source:di,function:p.chung_khoan},W.LAY_DANH_SACH_CHUNG_KHOAN,t,n)}getListInsuranceGroup(t,n){return this.apiService.requestProcess({},W.LAY_DANH_SACH_NHOM_BAO_HIEM,t,n)}getListInsurance(t,n,a){return this.apiService.requestProcess(t,W.LAY_DANH_SACH_BAO_HIEM,n,a)}decryptInsuranceData(t){return this.apiService.requestProcess(t,W.GIAI_MA_DATA_BAO_HIEM)}insuranceInterestNote(t){return this.apiService.requestProcess(t,W.GHI_NHAN_QUAN_TAM_BAO_HIEM)}fetchOrderPaymentServices(){this.loadingService.showLoading();let t={serviceId:"",providerId:""};this.getListPaymentServiceByOrder(t).pipe(oi(n=>(n?.data&&n.data.sort((a,s)=>a?.orderServiceType-s?.orderServiceType),n))).subscribe({next:n=>{n?.data&&(this.storageService.templateServicePaymentByOrder=n?.data,this.orderPaymentServices$.next(n?.data))},error:n=>{this.modalService.error(n?.error?.des)}})}getOrderPaymentServices(){let t=this.storageService.templateServicePaymentByOrder;return t?.length?this.orderPaymentServices$.next(t):this.fetchOrderPaymentServices(),this.orderPaymentServices$.asObservable()}setOrderPaymentServices(t){this.orderPaymentServices$.next(t??null)}checkRedirectBillPayment(t){let[n,a]=t.split("_");if(Number(n??0)===p.tao_thanh_toan_hoa_don_dinh_ky){let s={selectedServiceTypeId:a};this.router.navigate([x.Payment,x.PaymentRecurring,x.PaymentRecurringSubcribe],{state:s});return}this.getOrderPaymentServices().pipe(sa(s=>!!s),ra(1)).subscribe(s=>{if(s?.length){let r=s?.find(l=>l.serviceTypeId===a);if(r){let l={selectedPaymentService:r};this.router.navigate([`/${x.Payment}/${x.PaymentBill}`],{state:l});return}this.modalService.error({message:this.translate.instant("errors.khong_ton_tai_dich_vu_duoc_thuc_hien")})}else this.modalService.error({message:this.translate.instant("errors.khong_ton_tai_dich_vu_duoc_thuc_hien")})})}static \u0275fac=function(n){return new(n||i)};static \u0275prov=be({token:i,factory:i.\u0275fac,providedIn:"root"})}return i})();var kr=function(i){return i.vnd="VND",i.usd="USD",i.eur="EUR",i}(kr||{});var ft=function(i){return i.Quick="1",i.Normal="2",i}(ft||{}),Ir=function(i){return i.Save="1",i.NotSave="0",i}(Ir||{});var Ar=function(i){return i.CustomerCodeAndPrice="1",i.FullOption="I1",i.FullOptionWithoutPrice="I2",i.OnlyCustomerCode="0",i.CustomerCodeAndDenomination="A",i}(Ar||{});var Mr=function(i){return i.OldDesign="0",i.NewDesign="1",i}(Mr||{}),Pr=function(i){return i.Yes="1",i.No="0",i}(Pr||{});var Nr=function(i){return i[i.all=0]="all",i[i.income=1]="income",i[i.expenditure=2]="expenditure",i}(Nr||{}),zc="003051",Dr=function(i){return i.noReceived="1",i.wrongInfo="2",i.cheating="3",i.reconfirmContent="4",i.other="5",i}(Dr||{}),Or=function(i){return i.Subscribe="1",i.Unsubscribe="0",i}(Or||{}),Gt=function(i){return i.Individual="0",i.HouseHoldBusiness="1",i}(Gt||{}),Lr=function(i){return i[i.CreateQRAndOrder=0]="CreateQRAndOrder",i[i.RevenueTracking=1]="RevenueTracking",i}(Lr||{}),Hr=function(i){return i.Create="I",i.Edit="U",i}(Hr||{}),Rr=function(i){return i[i.Last7Days=0]="Last7Days",i[i.Last30Days=1]="Last30Days",i[i.Last90Days=2]="Last90Days",i[i.ThisMonth=3]="ThisMonth",i[i.ThisQuarter=4]="ThisQuarter",i[i.Custom=5]="Custom",i}(Rr||{}),Br=function(i){return i.Day="0",i.Week="1",i.Month="2",i.Quarter="3",i}(Br||{});var ui=function(i){return i.NotAuth="",i.Locked="0",i.Active="1",i.Cancel="2",i.WaitUnlock="3",i.WaitLock="4",i.WaitCancel="5",i.AutoLock="6",i.ActiveForBusiness="7",i}(ui||{}),Tn=function(i){return i.Auth="1",i.TimeOut="2",i.NotAuth="3",i}(Tn||{}),zr=function(i){return i.NewTransaction="NEW_TRANSACTION",i}(zr||{});var $r=function(i){return i[i.no=0]="no",i[i.yes=1]="yes",i}($r||{}),ji=function(i){return i.cardLength19="970418",i.cardLengthMin16="********",i}(ji||{});var qr=function(i){return i.DAY="D",i.MONTH="M",i}(qr||{}),Fr=function(i){return i.YES="Y",i.NO="N",i}(Fr||{}),En=function(i){return i[i.Internal=1]="Internal",i[i.ExternalWithAccountNumber=2]="ExternalWithAccountNumber",i[i.ExternalWithCardNumber=3]="ExternalWithCardNumber",i[i.Support=4]="Support",i[i.SellForeignCurrency=5]="SellForeignCurrency",i[i.Periodic=6]="Periodic",i[i.Charity=7]="Charity",i[i.OverseaTransfer=8]="OverseaTransfer",i}(En||{}),Vr=function(i){return i.ALL="",i.TRANSFER_IN="TRANFER_IN",i.TRANSFER_OUT_TO_ACC="TRANFER_OUT_TO_ACC",i.TRANSFER_OUT_TO_CARD="TRANFER_OUT_TO_CARD",i.PAY_CREDIT_CARD="PAY_CREDIT_CARD",i}(Vr||{}),Gr=function(i){return i[i.RecentTransaction=0]="RecentTransaction",i[i.Contact=1]="Contact",i}(Gr||{}),Ur=function(i){return i.Account="1",i.Card="2",i}(Ur||{}),Yr=function(i){return i.Transfer="0",i.Payment="1",i}(Yr||{}),jr=function(i){return i.Smb="SMB",i.IBank="IBANK",i}(jr||{});var Wr=function(i){return i.Active="0",i.Closed="1",i}(Wr||{}),Xr=function(i){return i.Debt="DEBT",i.Cred="CRED",i.Shar="SHAR",i}(Xr||{}),Kr=function(i){return i.CalculateFee="1",i.ReferenceFee="2",i}(Kr||{}),Qr=function(i){return i.Init="0",i.SuggestCancellation="1",i.SuggestConfirmExchangeRate="2",i.ApprovedExchangeRate="3",i.LackDocument="4",i.AddedDocument="5",i.CancelledTransaction="6",i.ApproveAndReturn="7",i.ApproveToProcess="8",i.Successful="9",i}(Qr||{}),Zr=function(i){return i.NotAddYet="0",i.Added="1",i.NotAdd="2",i.AddedButWait="3",i.InvalidRecordAndWaitAdd="4",i.AddedAndWaitApprove="5",i}(Zr||{}),Jr=function(i){return i.Success="3",i.Failed="2",i}(Jr||{});var Wi=function(i){return i.FEATURES="features",i.BENEFICIARIES="beneficiaries",i.TRANSFER_TEMPLATES="transferTemplates",i.RECENTLY_TRANSACTION="recentlyTransaction",i.TEMPLATES="templates",i.BILL_FEATURES="billFeatures",i.BANKS="banks",i.TOP_UPS="topUps",i}(Wi||{}),eo=function(i){return i.LIKE="like",i.EQUAL="equal",i.ADVANCE="advance",i}(eo||{});var mt=(()=>{class i{toast$=new Bt;translate=Y(Oe);getToast(){return this.toast$.asObservable()}success(t){let n=ci.Success,a=ie(X({},typeof t=="object"?t:{}),{message:typeof t=="string"?t:t?.message,type:n});this.show(a)}error(t){let n=ci.Danger,a=ie(X({},typeof t=="object"?t:{}),{message:typeof t=="string"?t:t?.message,title:typeof t=="object"?t?.title??this.translate.instant("that_bai"):"",type:n});this.show(a)}warning(t){let n=ci.Warning,a=ie(X({},typeof t=="object"?t:{}),{icon:"assets/media/icons/solid/alert-warning-circle.svg",message:typeof t=="string"?t:t?.message,type:n});this.show(a)}info(t){let n=ci.Info,a=ie(X({},typeof t=="object"?t:{}),{message:typeof t=="string"?t:t?.message,type:n});this.show(a)}show(t){this.toast$.next(t)}static \u0275fac=function(n){return new(n||i)};static \u0275prov=be({token:i,factory:i.\u0275fac,providedIn:"root"})}return i})();var Kc=(()=>{class i{transform(t){return t==null||t===""?"":Number(t).toLocaleString("en-US")}static \u0275fac=function(n){return new(n||i)};static \u0275pipe=Ii({name:"thousandSeparator",type:i,pure:!0,standalone:!0})}return i})();var kn=[{id:"1",name:"T\xE0i kho\u1EA3n Nh\u01B0 \xFD",nameEn:"Elite Account",active:!0,items:[{id:"1_1",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-tai-khoan-nhu-y.svg",functionName:"T\xE0i kho\u1EA3n Nh\u01B0 \xFD",functionNameEn:"Elite Account",serviceId:p.tai_khoan_nhu_y}]},{id:"3",name:"D\u1ECBch v\u1EE5 th\u1EBB",nameEn:"Card Services",active:!0,items:[{id:"3_1",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-dang-ky-tra-gop.svg",functionName:"\u0110\u0103ng k\xFD tr\u1EA3 g\xF3p",functionNameEn:"Installment Registration",serviceId:p.tra_gop_the_tin_dung},{id:"3_3",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-phat-hanh-the-online.svg",functionName:"Ph\xE1t h\xE0nh th\u1EBB Online",functionNameEn:"Online Card Issuance",serviceId:p.phat_hanh_the},{id:"3_4",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-thanh-toan-the-tin-dung.svg",functionName:"Thanh to\xE1n th\u1EBB t\xEDn d\u1EE5ng",functionNameEn:"Credit Card Payment",serviceId:p.thanh_toan_the_tin_dung}]},{id:"5",name:"Ti\u1EBFt ki\u1EC7m",nameEn:"Savings",active:!0,items:[{id:"5_1",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-tiet-kiem-online.svg",functionName:"Ti\u1EC1n g\u1EEDi Online",functionNameEn:"Online Deposit",serviceId:p.tiet_kiem},{id:"5_289",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-dau-tu-tu-dong.svg",functionName:"\u0110\u1EA7u t\u01B0 t\u1EF1 \u0111\u1ED9ng",functionNameEn:"Auto Deposit",serviceId:p.tiet_kiem_tu_dong}]},{id:"6",name:"Thanh to\xE1n",nameEn:"Bill Payment",active:!0,serviceId:p.thanh_toan_hoa_don,items:[{id:"6_1",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-tien-dien.svg",functionName:"Ti\u1EC1n \u0111i\u1EC7n",functionNameEn:"Electricity Bill",serviceId:`${p.thanh_toan_hoa_don}_${oe.tien_dien}`},{id:"6_2",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-tien-nuoc.svg",functionName:"Ti\u1EC1n n\u01B0\u1EDBc",functionNameEn:"Water Bill",serviceId:`${p.thanh_toan_hoa_don}_${oe.tien_nuoc}`},{id:"6_3",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-truyen-hinh.svg",functionName:"Truy\u1EC1n h\xECnh",functionNameEn:"Television Bill",serviceId:`${p.thanh_toan_hoa_don}_${oe.truyen_hinh}`},{id:"6_4",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-vien-thong.svg",functionName:"Vi\u1EC5n th\xF4ng",functionNameEn:"Telecom Services",serviceId:`${p.thanh_toan_hoa_don}_${oe.vien_thong}`},{id:"6_5",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-phi-quan-ly-chung-cu.svg",functionName:"Ph\xED qu\u1EA3n l\xFD chung c\u01B0",functionNameEn:"Apartment Management Fee",serviceId:`${p.thanh_toan_hoa_don}_${oe.quan_ly_chung_cu}`},{id:"6_6",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-bhxh-dn.svg",functionName:"B\u1EA3o hi\u1EC3m XH cho doanh nghi\u1EC7p",functionNameEn:"Corporate Social Insurance",serviceId:`${p.thanh_toan_hoa_don}_${oe.bhxh_doanh_nghiep}`},{id:"6_7",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-bhxh-cn.svg",functionName:"B\u1EA3o hi\u1EC3m XH cho c\xE1 nh\xE2n",functionNameEn:"Personal Social Insurance",serviceId:`${p.thanh_toan_hoa_don}_${oe.bhxh_ca_nhan}`},{id:"6_8",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-hoc-phi.svg",functionName:"H\u1ECDc ph\xED - L\u1EC7 Ph\xED",functionNameEn:"Tuition Fee",serviceId:`${p.thanh_toan_hoa_don}_${oe.hoc_phi}`},{id:"6_9",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-bao-hiem.svg",functionName:"B\u1EA3o hi\u1EC3m",functionNameEn:"Insurance",serviceId:`${p.thanh_toan_hoa_don}_${oe.bao_hiem}`},{id:"6_10",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-tai-chinh.svg",functionName:"T\xE0i ch\xEDnh",functionNameEn:"Finance",serviceId:`${p.thanh_toan_hoa_don}_${oe.tai_chinh}`},{id:"6_11",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-mua-chung-chi-quy-mo.svg",functionName:"Mua ch\u1EE9ng ch\u1EC9 qu\u1EF9 m\u1EDF",functionNameEn:"Open-Ended Fund",serviceId:`${p.thanh_toan_hoa_don}_${oe.mua_chung_chi_quy_mo}`},{id:"6_12",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-nop-tien-chung-khoan.svg",functionName:"N\u1ED9p ti\u1EC1n ch\u1EE9ng kho\xE1n",functionNameEn:"Stock Account Top-Up",serviceId:`${p.thanh_toan_hoa_don}_${oe.nop_tien_chung_khoan}`},{id:"6_13",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-dau-thau.svg",functionName:"\u0110\u1EA5u th\u1EA7u",functionNameEn:"Bidding Services",serviceId:`${p.thanh_toan_hoa_don}_${oe.dau_thau}`},{id:"6_14",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-ve-may-bay.svg",functionName:"V\xE9 m\xE1y bay",functionNameEn:"Flight Tickets",serviceId:`${p.thanh_toan_hoa_don}_${oe.ve_may_bay}`},{id:"6_15",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-ve-tau.svg",functionName:"V\xE9 t\xE0u",functionNameEn:"Train Tickets",serviceId:`${p.thanh_toan_hoa_don}_${oe.ve_tau}`},{id:"6_16",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-dich-vu-logistic.svg",functionName:"D\u1ECBch v\u1EE5 logistics",functionNameEn:"Logistics Services",serviceId:`${p.thanh_toan_hoa_don}_${oe.logistics}`},{id:"6_330",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-bat-dong-san.svg",functionName:"B\u1EA5t \u0111\u1ED9ng s\u1EA3n",functionNameEn:"Real Estate",serviceId:`${p.thanh_toan_hoa_don}_${oe.bat_dong_san}`},{id:"6_18",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-dich-vu-golf.svg",functionName:"D\u1ECBch v\u1EE5 Goft",functionNameEn:"Golf Services",serviceId:`${p.thanh_toan_hoa_don}_${oe.golf}`},{id:"6_19",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-nong-nghiep.svg",functionName:"N\xF4ng nghi\u1EC7p",functionNameEn:"Agriculture",serviceId:`${p.thanh_toan_hoa_don}_${oe.nong_nghiep}`},{id:"6_20",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-thuc-pham-tieu-dung.svg",functionName:"Th\u1EF1c ph\u1EA9m ti\xEAu d\xF9ng",functionNameEn:"Consumer Food",serviceId:`${p.thanh_toan_hoa_don}_${oe.thuc_pham_tieu_dung}`},{id:"6_21",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-thu-ho.svg",functionName:"D\u1ECBch v\u1EE5 thu h\u1ED9",functionNameEn:"Collection Services",serviceId:`${p.thanh_toan_hoa_don}_${oe.dich_vu_thu_ho}`},{id:"6_22",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-ma-the-cao.svg",functionName:"M\xE3 th\u1EBB c\xE0o",functionNameEn:"Top-up Cards",serviceId:`${p.thanh_toan_hoa_don}_${oe.ma_the_cao}`},{id:"6_23",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-nap-data-dien-thoai.svg",functionName:"N\u1EA1p Data \u0111i\u1EC7n tho\u1EA1i",functionNameEn:"Mobile Data Top-Up",serviceId:`${p.thanh_toan_hoa_don}_${oe.nap_data_dien_thoai}`},{id:"6_24",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-thanh-toan-truc-tuyen.svg",functionName:"Thanh to\xE1n tr\u1EF1c tuy\u1EBFn",functionNameEn:"Online Payments",serviceId:`${p.thanh_toan_hoa_don}_${oe.thanh_toan_truc_tuyen}`},{id:"6_29",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-giao-thong.svg",functionName:"Giao th\xF4ng",functionNameEn:"Transportation",serviceId:`${p.thanh_toan_hoa_don}_${oe.giao_thong}`},{id:"6_25",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-nap-tien-dien-thoai.svg",functionName:"N\u1EA1p ti\u1EC1n \u0111i\u1EC7n tho\u1EA1i",functionNameEn:"Mobile Top-Up",serviceId:p.nap_tien_dt},{id:"6_26",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-thanh-toan-dinh-ky.svg",functionName:"Thanh to\xE1n \u0111\u1ECBnh k\u1EF3",functionNameEn:"Recurring Payments",serviceId:p.thanh_toan_hoa_don_dinh_ky}]},{id:"7",name:"B\u1EA3o hi\u1EC3m & \u0110\u1EA7u t\u01B0",nameEn:"Insurance & Investment",active:!0,items:[{id:"7_1",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-bao-hiem.svg",functionName:"B\u1EA3o hi\u1EC3m",functionNameEn:"Insurance",serviceId:p.bao_hiem},{id:"7_2",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-chung-chi-quy-mo.svg",functionName:"Ch\u1EE9ng ch\u1EC9 qu\u1EF9 m\u1EDF",functionNameEn:"Open-End Funds",serviceId:p.chung_chi_quy_mo},{id:"7_3",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-chung-khoan.svg",functionName:"Ch\u1EE9ng kho\xE1n",functionNameEn:"Securities",serviceId:p.chung_khoan}]},{id:"10",name:"D\u1ECBch v\u1EE5 c\xF4ng",nameEn:"Public Services",active:!0,items:[{id:"10_1",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-nop-le-phi-truoc-ba.svg",functionName:"N\u1ED9p l\u1EC7 ph\xED tr\u01B0\u1EDBc b\u1EA1",functionNameEn:"Pay registration fee",serviceId:p.nop_phi_truoc_ba},{id:"10_2",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-phi-ha-tang-cang-bien.svg",functionName:"N\u1ED9p ph\xED h\u1EA1 t\u1EA7ng c\u1EA3ng bi\u1EC3n",functionNameEn:"Port Infrastructure Fee",serviceId:p.nop_phi_ha_tang_cang_bien},{id:"10_3",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-nop-thue.svg",functionName:"N\u1ED9p thu\u1EBF",functionNameEn:"Tax Payment",serviceId:p.nop_thue_theo_mst}]},{id:"11",name:"D\u1ECBch v\u1EE5 cho KH ti\u1EC3u th\u01B0\u01A1ng",nameEn:"Merchant Services",active:!0,items:[{id:"11_2",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-quan-ly-cua-hang.svg",functionName:"Qu\u1EA3n l\xFD c\u1EEDa h\xE0ng",functionNameEn:"Store Management",serviceId:p.quan_ly_cua_hang}]},{id:"13",name:"Thi\u1EC7n nguy\u1EC7n & T\u1EB7ng qu\xE0",nameEn:"Society Connection & Gifting",active:!0,items:[{id:"13_1",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-tu-thien.svg",functionName:"Chuy\u1EC3n ti\u1EC1n t\u1EEB thi\u1EC7n",functionNameEn:"Society Connection",serviceId:p.chuyen_tien_tu_thien}]},{id:"14",name:"\u0110\u1EB7c quy\u1EC1n & \u01AFu \u0111\xE3i",nameEn:"Privileges & Offers",active:!0,items:[{id:"14_5",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-doi-qua.svg",functionName:"\u0110\u1ED5i qu\xE0",functionNameEn:"Rewards",serviceId:p.doi_qua}]}],Jc=[{id:"base_1",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-tai-khoan-nhu-y.svg",functionName:"M\u1EDF s\u1ED1 \u0111\u1EB9p nh\u01B0 \xFD",functionNameEn:"Open Lucky number account",serviceId:p.tai_khoan_so_dep,fixByClient:!0},{id:"base_2",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-tiet-kiem-online.svg",functionName:"Ti\u1EBFt ki\u1EC7m",functionNameEn:"Savings",serviceId:p.tiet_kiem,fixByClient:!0},{id:"base_3",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-nap-tien-dien-thoai.svg",functionName:"N\u1EA1p ti\u1EC1n \u0111i\u1EC7n tho\u1EA1i",functionNameEn:"Top-up",serviceId:p.nap_tien_dt,fixByClient:!0},{id:"base_4",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-dau-tu-tu-dong.svg",functionName:"\u0110\u1EA7u t\u01B0 t\u1EF1 \u0111\u1ED9ng",functionNameEn:"Auto Deposit",serviceId:p.tiet_kiem_tu_dong,fixByClient:!0},{id:"base_5",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-dang-ky-tra-gop.svg",functionName:"\u0110\u0103ng k\xFD tr\u1EA3 g\xF3p",functionNameEn:"Installment Registration",serviceId:p.tra_gop_the_tin_dung,fixByClient:!0},{id:"base_6",iconUrl:"media/icons/doutone/icon-chuc-nang/cn-chuyen-tien-quoc-te.svg",functionName:"Chuy\u1EC3n ti\u1EC1n qu\u1ED1c t\u1EBF",functionNameEn:"Overseas Transfer",serviceId:p.chuyen_tien_quoc_te,fixByClient:!0}];var Va=(()=>{class i{featureGroupsData=kn;featuresGroup$=new ve([]);favouriteServiceIds=[];storageService=Y(De);processData(){let{favoriteFunction:t}=this.storageService.userInfo;this.favouriteServiceIds=t?.split(",")||[],this.featureGroupsData=kn.map(n=>ie(X({},n),{items:n.items.map(a=>{let s=this.favouriteServiceIds.findIndex(r=>r===a.serviceId.toString());if(![null,void 0,-1,NaN].includes(s)){let r=this.favouriteServiceIds.indexOf(a.serviceId.toString());return ie(X({},a),{order:r,favorite:!0})}return ie(X({},a),{order:void 0,favorite:!1})})})),this.featuresGroup$.next(this.featureGroupsData)}getFeatures(){return this.featuresGroup$.pipe(oi(t=>t.map(n=>ie(X({},n),{items:n.items}))))}updateFavoriteFeatures(t){this.favouriteServiceIds=t,this.processData()}getFavouriteFeatures(){return this.featuresGroup$.pipe(oi(t=>t.reduce((n,a)=>{let s=a.items.filter(r=>r.favorite).map(r=>ie(X({},r),{groupId:a.id,fixByClient:!0}));return[...n,...s]},[]).sort((n,a)=>(n?.order||0)-(a?.order||0))))}toggleFavourite(t){this.featuresGroup$.next(t)}cancelChange(){this.processData()}static \u0275fac=function(n){return new(n||i)};static \u0275prov=be({token:i,factory:i.\u0275fac,providedIn:"root"})}return i})();var to=["*"],Ga=(()=>{class i{color=Ma.Primary;size=_n.Md;get hostClass(){return this.initClass()}initClass(){let t=["badge"];return t.push("badge-"+this.color),this.size!==_n.Md&&t.push("badge-"+this.size),t.join(" ")}static \u0275fac=function(n){return new(n||i)};static \u0275cmp=Pe({type:i,selectors:[["app-badge"],["","app-badge",""]],hostVars:2,hostBindings:function(n,a){n&2&&Pi(a.hostClass)},inputs:{color:"color",size:"size"},standalone:!0,features:[Ne],ngContentSelectors:to,decls:1,vars:0,template:function(n,a){n&1&&(_a(),va(0))},dependencies:[ct]})}return i})();var Ua=(()=>{class i{env=Bi;lang;language$;store$;baseDestroy$=new Bt;userInfo;storeService=Y(De);translate=Y(Oe);constructor(){this.lang=this.storeService.language,this.translate.addLangs(["vi","en"]),this.translate.setDefaultLang("vi"),this.translate.use(this.lang),this.userInfo=this.storeService.userInfo,this.store$=this.storeService.changes.subscribe(t=>{t.key===gn.LANGUAGE&&(this.translate.use(t.value),this.lang=t.value),t.key===gn.USER_INFO&&(this.userInfo=X({},this.storeService.userInfo))})}changeLang(){this.lang=this.lang===dt.En||!this.lang?dt.Vi:dt.En}ngOnDestroy(){this.language$&&this.language$.unsubscribe(),this.store$&&this.store$.unsubscribe(),this.baseDestroy$.next(),this.baseDestroy$.complete()}static \u0275fac=function(n){return new(n||i)};static \u0275cmp=Pe({type:i,selectors:[["app-base"]],standalone:!0,features:[Ne],decls:0,vars:0,template:function(n,a){},encapsulation:2})}return i})();var Ya=(()=>{class i{apiService;constructor(t){this.apiService=t}getListService(t){return this.apiService.requestProcess(t,W.GOI_DICH_VU.DANH_SACH_GOI)}initServiceChange(t){return this.apiService.requestProcess(t,W.GOI_DICH_VU.KHOI_TAO_DOI_GOI)}confirmServiceChange(t){return this.apiService.requestProcess(t,W.GOI_DICH_VU.XAC_NHAN_DOI_GOI)}getTransactionLimit(t){return this.apiService.requestProcess(t,W.HAN_MUC_GIAO_DICH)}getCurrentTransactionLimit(t){return this.apiService.requestProcess(t,W.THONG_TIN_HAN_MUC_HIEN_TAI)}getListLimit(){return this.apiService.requestProcess({},W.THAY_DOI_HAN_MUC.DANH_SACH_HAN_MUC)}initLimitChange(t){return this.apiService.requestProcess(t,W.THAY_DOI_HAN_MUC.KHOI_TAO)}confirmLimitChange(t){return this.apiService.requestProcess(t,W.THAY_DOI_HAN_MUC.XAC_NHAN)}initEmailRegister(t){return this.apiService.requestProcess(t,W.DANG_KY_EMAIL.KHOI_TAO)}confirmEmailRegister(t){return this.apiService.requestProcess(t,W.DANG_KY_EMAIL.XAC_NHAN)}updateEmail(t){return this.apiService.requestProcess(t,W.DANG_KY_EMAIL.CAP_NHAT)}updateTheme(t){return this.apiService.requestProcess(t,W.CAP_NHAT_GIAO_DIEN)}static \u0275fac=function(n){return new(n||i)(oa(pt))};static \u0275prov=be({token:i,factory:i.\u0275fac,providedIn:"root"})}return i})();var ja="1",Wa="32";function Xa(i){return i!==null&&typeof i=="object"&&"constructor"in i&&i.constructor===Object}function In(i,e){i===void 0&&(i={}),e===void 0&&(e={}),Object.keys(e).forEach(t=>{typeof i[t]>"u"?i[t]=e[t]:Xa(e[t])&&Xa(i[t])&&Object.keys(e[t]).length>0&&In(i[t],e[t])})}var Ka={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector(){return null},querySelectorAll(){return[]},getElementById(){return null},createEvent(){return{initEvent(){}}},createElement(){return{children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName(){return[]}}},createElementNS(){return{}},importNode(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function le(){let i=typeof document<"u"?document:{};return In(i,Ka),i}var io={document:Ka,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle(){return{getPropertyValue(){return""}}},Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia(){return{}},requestAnimationFrame(i){return typeof setTimeout>"u"?(i(),null):setTimeout(i,0)},cancelAnimationFrame(i){typeof setTimeout>"u"||clearTimeout(i)}};function ne(){let i=typeof window<"u"?window:{};return In(i,io),i}function nt(i){return i===void 0&&(i=""),i.trim().split(" ").filter(e=>!!e.trim())}function Qa(i){let e=i;Object.keys(e).forEach(t=>{try{e[t]=null}catch{}try{delete e[t]}catch{}})}function Ye(i,e){return e===void 0&&(e=0),setTimeout(i,e)}function we(){return Date.now()}function no(i){let e=ne(),t;return e.getComputedStyle&&(t=e.getComputedStyle(i,null)),!t&&i.currentStyle&&(t=i.currentStyle),t||(t=i.style),t}function pi(i,e){e===void 0&&(e="x");let t=ne(),n,a,s,r=no(i);return t.WebKitCSSMatrix?(a=r.transform||r.webkitTransform,a.split(",").length>6&&(a=a.split(", ").map(l=>l.replace(",",".")).join(", ")),s=new t.WebKitCSSMatrix(a==="none"?"":a)):(s=r.MozTransform||r.OTransform||r.MsTransform||r.msTransform||r.transform||r.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),n=s.toString().split(",")),e==="x"&&(t.WebKitCSSMatrix?a=s.m41:n.length===16?a=parseFloat(n[12]):a=parseFloat(n[4])),e==="y"&&(t.WebKitCSSMatrix?a=s.m42:n.length===16?a=parseFloat(n[13]):a=parseFloat(n[5])),a||0}function Ut(i){return typeof i=="object"&&i!==null&&i.constructor&&Object.prototype.toString.call(i).slice(8,-1)==="Object"}function ao(i){return typeof window<"u"&&typeof window.HTMLElement<"u"?i instanceof HTMLElement:i&&(i.nodeType===1||i.nodeType===11)}function Te(){let i=Object(arguments.length<=0?void 0:arguments[0]),e=["__proto__","constructor","prototype"];for(let t=1;t<arguments.length;t+=1){let n=t<0||arguments.length<=t?void 0:arguments[t];if(n!=null&&!ao(n)){let a=Object.keys(Object(n)).filter(s=>e.indexOf(s)<0);for(let s=0,r=a.length;s<r;s+=1){let l=a[s],o=Object.getOwnPropertyDescriptor(n,l);o!==void 0&&o.enumerable&&(Ut(i[l])&&Ut(n[l])?n[l].__swiper__?i[l]=n[l]:Te(i[l],n[l]):!Ut(i[l])&&Ut(n[l])?(i[l]={},n[l].__swiper__?i[l]=n[l]:Te(i[l],n[l])):i[l]=n[l])}}}return i}function Mt(i,e,t){i.style.setProperty(e,t)}function An(i){let{swiper:e,targetPosition:t,side:n}=i,a=ne(),s=-e.translate,r=null,l,o=e.params.speed;e.wrapperEl.style.scrollSnapType="none",a.cancelAnimationFrame(e.cssModeFrameID);let u=t>s?"next":"prev",c=(h,f)=>u==="next"&&h>=f||u==="prev"&&h<=f,d=()=>{l=new Date().getTime(),r===null&&(r=l);let h=Math.max(Math.min((l-r)/o,1),0),f=.5-Math.cos(h*Math.PI)/2,m=s+f*(t-s);if(c(m,t)&&(m=t),e.wrapperEl.scrollTo({[n]:m}),c(m,t)){e.wrapperEl.style.overflow="hidden",e.wrapperEl.style.scrollSnapType="",setTimeout(()=>{e.wrapperEl.style.overflow="",e.wrapperEl.scrollTo({[n]:m})}),a.cancelAnimationFrame(e.cssModeFrameID);return}e.cssModeFrameID=a.requestAnimationFrame(d)};d()}function Ee(i){return i.querySelector(".swiper-slide-transform")||i.shadowRoot&&i.shadowRoot.querySelector(".swiper-slide-transform")||i}function de(i,e){e===void 0&&(e="");let t=[...i.children];return i instanceof HTMLSlotElement&&t.push(...i.assignedElements()),e?t.filter(n=>n.matches(e)):t}function Za(i,e){let t=e.contains(i);return!t&&e instanceof HTMLSlotElement?[...e.assignedElements()].includes(i):t}function hi(i){try{console.warn(i);return}catch{}}function he(i,e){e===void 0&&(e=[]);let t=document.createElement(i);return t.classList.add(...Array.isArray(e)?e:nt(e)),t}function Pt(i){let e=ne(),t=le(),n=i.getBoundingClientRect(),a=t.body,s=i.clientTop||a.clientTop||0,r=i.clientLeft||a.clientLeft||0,l=i===e?e.scrollY:i.scrollTop,o=i===e?e.scrollX:i.scrollLeft;return{top:n.top+l-s,left:n.left+o-r}}function Ja(i,e){let t=[];for(;i.previousElementSibling;){let n=i.previousElementSibling;e?n.matches(e)&&t.push(n):t.push(n),i=n}return t}function es(i,e){let t=[];for(;i.nextElementSibling;){let n=i.nextElementSibling;e?n.matches(e)&&t.push(n):t.push(n),i=n}return t}function at(i,e){return ne().getComputedStyle(i,null).getPropertyValue(e)}function gt(i){let e=i,t;if(e){for(t=0;(e=e.previousSibling)!==null;)e.nodeType===1&&(t+=1);return t}}function je(i,e){let t=[],n=i.parentElement;for(;n;)e?n.matches(e)&&t.push(n):t.push(n),n=n.parentElement;return t}function _t(i,e){function t(n){n.target===i&&(e.call(i,n),i.removeEventListener("transitionend",t))}e&&i.addEventListener("transitionend",t)}function fi(i,e,t){let n=ne();return t?i[e==="width"?"offsetWidth":"offsetHeight"]+parseFloat(n.getComputedStyle(i,null).getPropertyValue(e==="width"?"margin-right":"margin-top"))+parseFloat(n.getComputedStyle(i,null).getPropertyValue(e==="width"?"margin-left":"margin-bottom")):i.offsetWidth}function Z(i){return(Array.isArray(i)?i:[i]).filter(e=>!!e)}function vt(i){return e=>Math.abs(e)>0&&i.browser&&i.browser.need3dFix&&Math.abs(e)%90===0?e+.001:e}var Mn;function so(){let i=ne(),e=le();return{smoothScroll:e.documentElement&&e.documentElement.style&&"scrollBehavior"in e.documentElement.style,touch:!!("ontouchstart"in i||i.DocumentTouch&&e instanceof i.DocumentTouch)}}function ss(){return Mn||(Mn=so()),Mn}var Pn;function ro(i){let{userAgent:e}=i===void 0?{}:i,t=ss(),n=ne(),a=n.navigator.platform,s=e||n.navigator.userAgent,r={ios:!1,android:!1},l=n.screen.width,o=n.screen.height,u=s.match(/(Android);?[\s\/]+([\d.]+)?/),c=s.match(/(iPad).*OS\s([\d_]+)/),d=s.match(/(iPod)(.*OS\s([\d_]+))?/),h=!c&&s.match(/(iPhone\sOS|iOS)\s([\d_]+)/),f=a==="Win32",m=a==="MacIntel",_=["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"];return!c&&m&&t.touch&&_.indexOf(`${l}x${o}`)>=0&&(c=s.match(/(Version)\/([\d.]+)/),c||(c=[0,1,"13_0_0"]),m=!1),u&&!f&&(r.os="android",r.android=!0),(c||h||d)&&(r.os="ios",r.ios=!0),r}function rs(i){return i===void 0&&(i={}),Pn||(Pn=ro(i)),Pn}var Nn;function oo(){let i=ne(),e=rs(),t=!1;function n(){let l=i.navigator.userAgent.toLowerCase();return l.indexOf("safari")>=0&&l.indexOf("chrome")<0&&l.indexOf("android")<0}if(n()){let l=String(i.navigator.userAgent);if(l.includes("Version/")){let[o,u]=l.split("Version/")[1].split(" ")[0].split(".").map(c=>Number(c));t=o<16||o===16&&u<2}}let a=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(i.navigator.userAgent),s=n(),r=s||a&&e.ios;return{isSafari:t||s,needPerspectiveFix:t,need3dFix:r,isWebView:a}}function lo(){return Nn||(Nn=oo()),Nn}function co(i){let{swiper:e,on:t,emit:n}=i,a=ne(),s=null,r=null,l=()=>{!e||e.destroyed||!e.initialized||(n("beforeResize"),n("resize"))},o=()=>{!e||e.destroyed||!e.initialized||(s=new ResizeObserver(d=>{r=a.requestAnimationFrame(()=>{let{width:h,height:f}=e,m=h,_=f;d.forEach(w=>{let{contentBoxSize:b,contentRect:v,target:g}=w;g&&g!==e.el||(m=v?v.width:(b[0]||b).inlineSize,_=v?v.height:(b[0]||b).blockSize)}),(m!==h||_!==f)&&l()})}),s.observe(e.el))},u=()=>{r&&a.cancelAnimationFrame(r),s&&s.unobserve&&e.el&&(s.unobserve(e.el),s=null)},c=()=>{!e||e.destroyed||!e.initialized||n("orientationchange")};t("init",()=>{if(e.params.resizeObserver&&typeof a.ResizeObserver<"u"){o();return}a.addEventListener("resize",l),a.addEventListener("orientationchange",c)}),t("destroy",()=>{u(),a.removeEventListener("resize",l),a.removeEventListener("orientationchange",c)})}function uo(i){let{swiper:e,extendParams:t,on:n,emit:a}=i,s=[],r=ne(),l=function(c,d){d===void 0&&(d={});let h=r.MutationObserver||r.WebkitMutationObserver,f=new h(m=>{if(e.__preventObserver__)return;if(m.length===1){a("observerUpdate",m[0]);return}let _=function(){a("observerUpdate",m[0])};r.requestAnimationFrame?r.requestAnimationFrame(_):r.setTimeout(_,0)});f.observe(c,{attributes:typeof d.attributes>"u"?!0:d.attributes,childList:e.isElement||(typeof d.childList>"u"?!0:d).childList,characterData:typeof d.characterData>"u"?!0:d.characterData}),s.push(f)},o=()=>{if(e.params.observer){if(e.params.observeParents){let c=je(e.hostEl);for(let d=0;d<c.length;d+=1)l(c[d])}l(e.hostEl,{childList:e.params.observeSlideChildren}),l(e.wrapperEl,{attributes:!1})}},u=()=>{s.forEach(c=>{c.disconnect()}),s.splice(0,s.length)};t({observer:!1,observeParents:!1,observeSlideChildren:!1}),n("init",o),n("destroy",u)}var po={on(i,e,t){let n=this;if(!n.eventsListeners||n.destroyed||typeof e!="function")return n;let a=t?"unshift":"push";return i.split(" ").forEach(s=>{n.eventsListeners[s]||(n.eventsListeners[s]=[]),n.eventsListeners[s][a](e)}),n},once(i,e,t){let n=this;if(!n.eventsListeners||n.destroyed||typeof e!="function")return n;function a(){n.off(i,a),a.__emitterProxy&&delete a.__emitterProxy;for(var s=arguments.length,r=new Array(s),l=0;l<s;l++)r[l]=arguments[l];e.apply(n,r)}return a.__emitterProxy=e,n.on(i,a,t)},onAny(i,e){let t=this;if(!t.eventsListeners||t.destroyed||typeof i!="function")return t;let n=e?"unshift":"push";return t.eventsAnyListeners.indexOf(i)<0&&t.eventsAnyListeners[n](i),t},offAny(i){let e=this;if(!e.eventsListeners||e.destroyed||!e.eventsAnyListeners)return e;let t=e.eventsAnyListeners.indexOf(i);return t>=0&&e.eventsAnyListeners.splice(t,1),e},off(i,e){let t=this;return!t.eventsListeners||t.destroyed||!t.eventsListeners||i.split(" ").forEach(n=>{typeof e>"u"?t.eventsListeners[n]=[]:t.eventsListeners[n]&&t.eventsListeners[n].forEach((a,s)=>{(a===e||a.__emitterProxy&&a.__emitterProxy===e)&&t.eventsListeners[n].splice(s,1)})}),t},emit(){let i=this;if(!i.eventsListeners||i.destroyed||!i.eventsListeners)return i;let e,t,n;for(var a=arguments.length,s=new Array(a),r=0;r<a;r++)s[r]=arguments[r];return typeof s[0]=="string"||Array.isArray(s[0])?(e=s[0],t=s.slice(1,s.length),n=i):(e=s[0].events,t=s[0].data,n=s[0].context||i),t.unshift(n),(Array.isArray(e)?e:e.split(" ")).forEach(o=>{i.eventsAnyListeners&&i.eventsAnyListeners.length&&i.eventsAnyListeners.forEach(u=>{u.apply(n,[o,...t])}),i.eventsListeners&&i.eventsListeners[o]&&i.eventsListeners[o].forEach(u=>{u.apply(n,t)})}),i}};function ho(){let i=this,e,t,n=i.el;typeof i.params.width<"u"&&i.params.width!==null?e=i.params.width:e=n.clientWidth,typeof i.params.height<"u"&&i.params.height!==null?t=i.params.height:t=n.clientHeight,!(e===0&&i.isHorizontal()||t===0&&i.isVertical())&&(e=e-parseInt(at(n,"padding-left")||0,10)-parseInt(at(n,"padding-right")||0,10),t=t-parseInt(at(n,"padding-top")||0,10)-parseInt(at(n,"padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(t)&&(t=0),Object.assign(i,{width:e,height:t,size:i.isHorizontal()?e:t}))}function fo(){let i=this;function e(C,P){return parseFloat(C.getPropertyValue(i.getDirectionLabel(P))||0)}let t=i.params,{wrapperEl:n,slidesEl:a,size:s,rtlTranslate:r,wrongRTL:l}=i,o=i.virtual&&t.virtual.enabled,u=o?i.virtual.slides.length:i.slides.length,c=de(a,`.${i.params.slideClass}, swiper-slide`),d=o?i.virtual.slides.length:c.length,h=[],f=[],m=[],_=t.slidesOffsetBefore;typeof _=="function"&&(_=t.slidesOffsetBefore.call(i));let w=t.slidesOffsetAfter;typeof w=="function"&&(w=t.slidesOffsetAfter.call(i));let b=i.snapGrid.length,v=i.slidesGrid.length,g=t.spaceBetween,y=-_,E=0,N=0;if(typeof s>"u")return;typeof g=="string"&&g.indexOf("%")>=0?g=parseFloat(g.replace("%",""))/100*s:typeof g=="string"&&(g=parseFloat(g)),i.virtualSize=-g,c.forEach(C=>{r?C.style.marginLeft="":C.style.marginRight="",C.style.marginBottom="",C.style.marginTop=""}),t.centeredSlides&&t.cssMode&&(Mt(n,"--swiper-centered-offset-before",""),Mt(n,"--swiper-centered-offset-after",""));let L=t.grid&&t.grid.rows>1&&i.grid;L?i.grid.initSlides(c):i.grid&&i.grid.unsetSlides();let D,S=t.slidesPerView==="auto"&&t.breakpoints&&Object.keys(t.breakpoints).filter(C=>typeof t.breakpoints[C].slidesPerView<"u").length>0;for(let C=0;C<d;C+=1){D=0;let P;if(c[C]&&(P=c[C]),L&&i.grid.updateSlide(C,P,c),!(c[C]&&at(P,"display")==="none")){if(t.slidesPerView==="auto"){S&&(c[C].style[i.getDirectionLabel("width")]="");let A=getComputedStyle(P),T=P.style.transform,I=P.style.webkitTransform;if(T&&(P.style.transform="none"),I&&(P.style.webkitTransform="none"),t.roundLengths)D=i.isHorizontal()?fi(P,"width",!0):fi(P,"height",!0);else{let O=e(A,"width"),V=e(A,"padding-left"),k=e(A,"padding-right"),M=e(A,"margin-left"),G=e(A,"margin-right"),K=A.getPropertyValue("box-sizing");if(K&&K==="border-box")D=O+M+G;else{let{clientWidth:B,offsetWidth:F}=P;D=O+V+k+M+G+(F-B)}}T&&(P.style.transform=T),I&&(P.style.webkitTransform=I),t.roundLengths&&(D=Math.floor(D))}else D=(s-(t.slidesPerView-1)*g)/t.slidesPerView,t.roundLengths&&(D=Math.floor(D)),c[C]&&(c[C].style[i.getDirectionLabel("width")]=`${D}px`);c[C]&&(c[C].swiperSlideSize=D),m.push(D),t.centeredSlides?(y=y+D/2+E/2+g,E===0&&C!==0&&(y=y-s/2-g),C===0&&(y=y-s/2-g),Math.abs(y)<1/1e3&&(y=0),t.roundLengths&&(y=Math.floor(y)),N%t.slidesPerGroup===0&&h.push(y),f.push(y)):(t.roundLengths&&(y=Math.floor(y)),(N-Math.min(i.params.slidesPerGroupSkip,N))%i.params.slidesPerGroup===0&&h.push(y),f.push(y),y=y+D+g),i.virtualSize+=D+g,E=D,N+=1}}if(i.virtualSize=Math.max(i.virtualSize,s)+w,r&&l&&(t.effect==="slide"||t.effect==="coverflow")&&(n.style.width=`${i.virtualSize+g}px`),t.setWrapperSize&&(n.style[i.getDirectionLabel("width")]=`${i.virtualSize+g}px`),L&&i.grid.updateWrapperSize(D,h),!t.centeredSlides){let C=[];for(let P=0;P<h.length;P+=1){let A=h[P];t.roundLengths&&(A=Math.floor(A)),h[P]<=i.virtualSize-s&&C.push(A)}h=C,Math.floor(i.virtualSize-s)-Math.floor(h[h.length-1])>1&&h.push(i.virtualSize-s)}if(o&&t.loop){let C=m[0]+g;if(t.slidesPerGroup>1){let P=Math.ceil((i.virtual.slidesBefore+i.virtual.slidesAfter)/t.slidesPerGroup),A=C*t.slidesPerGroup;for(let T=0;T<P;T+=1)h.push(h[h.length-1]+A)}for(let P=0;P<i.virtual.slidesBefore+i.virtual.slidesAfter;P+=1)t.slidesPerGroup===1&&h.push(h[h.length-1]+C),f.push(f[f.length-1]+C),i.virtualSize+=C}if(h.length===0&&(h=[0]),g!==0){let C=i.isHorizontal()&&r?"marginLeft":i.getDirectionLabel("marginRight");c.filter((P,A)=>!t.cssMode||t.loop?!0:A!==c.length-1).forEach(P=>{P.style[C]=`${g}px`})}if(t.centeredSlides&&t.centeredSlidesBounds){let C=0;m.forEach(A=>{C+=A+(g||0)}),C-=g;let P=C-s;h=h.map(A=>A<=0?-_:A>P?P+w:A)}if(t.centerInsufficientSlides){let C=0;m.forEach(A=>{C+=A+(g||0)}),C-=g;let P=(t.slidesOffsetBefore||0)+(t.slidesOffsetAfter||0);if(C+P<s){let A=(s-C-P)/2;h.forEach((T,I)=>{h[I]=T-A}),f.forEach((T,I)=>{f[I]=T+A})}}if(Object.assign(i,{slides:c,snapGrid:h,slidesGrid:f,slidesSizesGrid:m}),t.centeredSlides&&t.cssMode&&!t.centeredSlidesBounds){Mt(n,"--swiper-centered-offset-before",`${-h[0]}px`),Mt(n,"--swiper-centered-offset-after",`${i.size/2-m[m.length-1]/2}px`);let C=-i.snapGrid[0],P=-i.slidesGrid[0];i.snapGrid=i.snapGrid.map(A=>A+C),i.slidesGrid=i.slidesGrid.map(A=>A+P)}if(d!==u&&i.emit("slidesLengthChange"),h.length!==b&&(i.params.watchOverflow&&i.checkOverflow(),i.emit("snapGridLengthChange")),f.length!==v&&i.emit("slidesGridLengthChange"),t.watchSlidesProgress&&i.updateSlidesOffset(),i.emit("slidesUpdated"),!o&&!t.cssMode&&(t.effect==="slide"||t.effect==="fade")){let C=`${t.containerModifierClass}backface-hidden`,P=i.el.classList.contains(C);d<=t.maxBackfaceHiddenSlides?P||i.el.classList.add(C):P&&i.el.classList.remove(C)}}function mo(i){let e=this,t=[],n=e.virtual&&e.params.virtual.enabled,a=0,s;typeof i=="number"?e.setTransition(i):i===!0&&e.setTransition(e.params.speed);let r=l=>n?e.slides[e.getSlideIndexByData(l)]:e.slides[l];if(e.params.slidesPerView!=="auto"&&e.params.slidesPerView>1)if(e.params.centeredSlides)(e.visibleSlides||[]).forEach(l=>{t.push(l)});else for(s=0;s<Math.ceil(e.params.slidesPerView);s+=1){let l=e.activeIndex+s;if(l>e.slides.length&&!n)break;t.push(r(l))}else t.push(r(e.activeIndex));for(s=0;s<t.length;s+=1)if(typeof t[s]<"u"){let l=t[s].offsetHeight;a=l>a?l:a}(a||a===0)&&(e.wrapperEl.style.height=`${a}px`)}function go(){let i=this,e=i.slides,t=i.isElement?i.isHorizontal()?i.wrapperEl.offsetLeft:i.wrapperEl.offsetTop:0;for(let n=0;n<e.length;n+=1)e[n].swiperSlideOffset=(i.isHorizontal()?e[n].offsetLeft:e[n].offsetTop)-t-i.cssOverflowAdjustment()}var ts=(i,e,t)=>{e&&!i.classList.contains(t)?i.classList.add(t):!e&&i.classList.contains(t)&&i.classList.remove(t)};function _o(i){i===void 0&&(i=this&&this.translate||0);let e=this,t=e.params,{slides:n,rtlTranslate:a,snapGrid:s}=e;if(n.length===0)return;typeof n[0].swiperSlideOffset>"u"&&e.updateSlidesOffset();let r=-i;a&&(r=i),e.visibleSlidesIndexes=[],e.visibleSlides=[];let l=t.spaceBetween;typeof l=="string"&&l.indexOf("%")>=0?l=parseFloat(l.replace("%",""))/100*e.size:typeof l=="string"&&(l=parseFloat(l));for(let o=0;o<n.length;o+=1){let u=n[o],c=u.swiperSlideOffset;t.cssMode&&t.centeredSlides&&(c-=n[0].swiperSlideOffset);let d=(r+(t.centeredSlides?e.minTranslate():0)-c)/(u.swiperSlideSize+l),h=(r-s[0]+(t.centeredSlides?e.minTranslate():0)-c)/(u.swiperSlideSize+l),f=-(r-c),m=f+e.slidesSizesGrid[o],_=f>=0&&f<=e.size-e.slidesSizesGrid[o],w=f>=0&&f<e.size-1||m>1&&m<=e.size||f<=0&&m>=e.size;w&&(e.visibleSlides.push(u),e.visibleSlidesIndexes.push(o)),ts(u,w,t.slideVisibleClass),ts(u,_,t.slideFullyVisibleClass),u.progress=a?-d:d,u.originalProgress=a?-h:h}}function vo(i){let e=this;if(typeof i>"u"){let c=e.rtlTranslate?-1:1;i=e&&e.translate&&e.translate*c||0}let t=e.params,n=e.maxTranslate()-e.minTranslate(),{progress:a,isBeginning:s,isEnd:r,progressLoop:l}=e,o=s,u=r;if(n===0)a=0,s=!0,r=!0;else{a=(i-e.minTranslate())/n;let c=Math.abs(i-e.minTranslate())<1,d=Math.abs(i-e.maxTranslate())<1;s=c||a<=0,r=d||a>=1,c&&(a=0),d&&(a=1)}if(t.loop){let c=e.getSlideIndexByData(0),d=e.getSlideIndexByData(e.slides.length-1),h=e.slidesGrid[c],f=e.slidesGrid[d],m=e.slidesGrid[e.slidesGrid.length-1],_=Math.abs(i);_>=h?l=(_-h)/m:l=(_+m-f)/m,l>1&&(l-=1)}Object.assign(e,{progress:a,progressLoop:l,isBeginning:s,isEnd:r}),(t.watchSlidesProgress||t.centeredSlides&&t.autoHeight)&&e.updateSlidesProgress(i),s&&!o&&e.emit("reachBeginning toEdge"),r&&!u&&e.emit("reachEnd toEdge"),(o&&!s||u&&!r)&&e.emit("fromEdge"),e.emit("progress",a)}var Dn=(i,e,t)=>{e&&!i.classList.contains(t)?i.classList.add(t):!e&&i.classList.contains(t)&&i.classList.remove(t)};function wo(){let i=this,{slides:e,params:t,slidesEl:n,activeIndex:a}=i,s=i.virtual&&t.virtual.enabled,r=i.grid&&t.grid&&t.grid.rows>1,l=d=>de(n,`.${t.slideClass}${d}, swiper-slide${d}`)[0],o,u,c;if(s)if(t.loop){let d=a-i.virtual.slidesBefore;d<0&&(d=i.virtual.slides.length+d),d>=i.virtual.slides.length&&(d-=i.virtual.slides.length),o=l(`[data-swiper-slide-index="${d}"]`)}else o=l(`[data-swiper-slide-index="${a}"]`);else r?(o=e.filter(d=>d.column===a)[0],c=e.filter(d=>d.column===a+1)[0],u=e.filter(d=>d.column===a-1)[0]):o=e[a];o&&(r||(c=es(o,`.${t.slideClass}, swiper-slide`)[0],t.loop&&!c&&(c=e[0]),u=Ja(o,`.${t.slideClass}, swiper-slide`)[0],t.loop&&!u===0&&(u=e[e.length-1]))),e.forEach(d=>{Dn(d,d===o,t.slideActiveClass),Dn(d,d===c,t.slideNextClass),Dn(d,d===u,t.slidePrevClass)}),i.emitSlidesClasses()}var Xi=(i,e)=>{if(!i||i.destroyed||!i.params)return;let t=()=>i.isElement?"swiper-slide":`.${i.params.slideClass}`,n=e.closest(t());if(n){let a=n.querySelector(`.${i.params.lazyPreloaderClass}`);!a&&i.isElement&&(n.shadowRoot?a=n.shadowRoot.querySelector(`.${i.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{n.shadowRoot&&(a=n.shadowRoot.querySelector(`.${i.params.lazyPreloaderClass}`),a&&a.remove())})),a&&a.remove()}},On=(i,e)=>{if(!i.slides[e])return;let t=i.slides[e].querySelector('[loading="lazy"]');t&&t.removeAttribute("loading")},Rn=i=>{if(!i||i.destroyed||!i.params)return;let e=i.params.lazyPreloadPrevNext,t=i.slides.length;if(!t||!e||e<0)return;e=Math.min(e,t);let n=i.params.slidesPerView==="auto"?i.slidesPerViewDynamic():Math.ceil(i.params.slidesPerView),a=i.activeIndex;if(i.params.grid&&i.params.grid.rows>1){let r=a,l=[r-e];l.push(...Array.from({length:e}).map((o,u)=>r+n+u)),i.slides.forEach((o,u)=>{l.includes(o.column)&&On(i,u)});return}let s=a+n-1;if(i.params.rewind||i.params.loop)for(let r=a-e;r<=s+e;r+=1){let l=(r%t+t)%t;(l<a||l>s)&&On(i,l)}else for(let r=Math.max(a-e,0);r<=Math.min(s+e,t-1);r+=1)r!==a&&(r>s||r<a)&&On(i,r)};function bo(i){let{slidesGrid:e,params:t}=i,n=i.rtlTranslate?i.translate:-i.translate,a;for(let s=0;s<e.length;s+=1)typeof e[s+1]<"u"?n>=e[s]&&n<e[s+1]-(e[s+1]-e[s])/2?a=s:n>=e[s]&&n<e[s+1]&&(a=s+1):n>=e[s]&&(a=s);return t.normalizeSlideIndex&&(a<0||typeof a>"u")&&(a=0),a}function yo(i){let e=this,t=e.rtlTranslate?e.translate:-e.translate,{snapGrid:n,params:a,activeIndex:s,realIndex:r,snapIndex:l}=e,o=i,u,c=f=>{let m=f-e.virtual.slidesBefore;return m<0&&(m=e.virtual.slides.length+m),m>=e.virtual.slides.length&&(m-=e.virtual.slides.length),m};if(typeof o>"u"&&(o=bo(e)),n.indexOf(t)>=0)u=n.indexOf(t);else{let f=Math.min(a.slidesPerGroupSkip,o);u=f+Math.floor((o-f)/a.slidesPerGroup)}if(u>=n.length&&(u=n.length-1),o===s&&!e.params.loop){u!==l&&(e.snapIndex=u,e.emit("snapIndexChange"));return}if(o===s&&e.params.loop&&e.virtual&&e.params.virtual.enabled){e.realIndex=c(o);return}let d=e.grid&&a.grid&&a.grid.rows>1,h;if(e.virtual&&a.virtual.enabled&&a.loop)h=c(o);else if(d){let f=e.slides.filter(_=>_.column===o)[0],m=parseInt(f.getAttribute("data-swiper-slide-index"),10);Number.isNaN(m)&&(m=Math.max(e.slides.indexOf(f),0)),h=Math.floor(m/a.grid.rows)}else if(e.slides[o]){let f=e.slides[o].getAttribute("data-swiper-slide-index");f?h=parseInt(f,10):h=o}else h=o;Object.assign(e,{previousSnapIndex:l,snapIndex:u,previousRealIndex:r,realIndex:h,previousIndex:s,activeIndex:o}),e.initialized&&Rn(e),e.emit("activeIndexChange"),e.emit("snapIndexChange"),(e.initialized||e.params.runCallbacksOnInit)&&(r!==h&&e.emit("realIndexChange"),e.emit("slideChange"))}function So(i,e){let t=this,n=t.params,a=i.closest(`.${n.slideClass}, swiper-slide`);!a&&t.isElement&&e&&e.length>1&&e.includes(i)&&[...e.slice(e.indexOf(i)+1,e.length)].forEach(l=>{!a&&l.matches&&l.matches(`.${n.slideClass}, swiper-slide`)&&(a=l)});let s=!1,r;if(a){for(let l=0;l<t.slides.length;l+=1)if(t.slides[l]===a){s=!0,r=l;break}}if(a&&s)t.clickedSlide=a,t.virtual&&t.params.virtual.enabled?t.clickedIndex=parseInt(a.getAttribute("data-swiper-slide-index"),10):t.clickedIndex=r;else{t.clickedSlide=void 0,t.clickedIndex=void 0;return}n.slideToClickedSlide&&t.clickedIndex!==void 0&&t.clickedIndex!==t.activeIndex&&t.slideToClickedSlide()}var To={updateSize:ho,updateSlides:fo,updateAutoHeight:mo,updateSlidesOffset:go,updateSlidesProgress:_o,updateProgress:vo,updateSlidesClasses:wo,updateActiveIndex:yo,updateClickedSlide:So};function Eo(i){i===void 0&&(i=this.isHorizontal()?"x":"y");let e=this,{params:t,rtlTranslate:n,translate:a,wrapperEl:s}=e;if(t.virtualTranslate)return n?-a:a;if(t.cssMode)return a;let r=pi(s,i);return r+=e.cssOverflowAdjustment(),n&&(r=-r),r||0}function xo(i,e){let t=this,{rtlTranslate:n,params:a,wrapperEl:s,progress:r}=t,l=0,o=0,u=0;t.isHorizontal()?l=n?-i:i:o=i,a.roundLengths&&(l=Math.floor(l),o=Math.floor(o)),t.previousTranslate=t.translate,t.translate=t.isHorizontal()?l:o,a.cssMode?s[t.isHorizontal()?"scrollLeft":"scrollTop"]=t.isHorizontal()?-l:-o:a.virtualTranslate||(t.isHorizontal()?l-=t.cssOverflowAdjustment():o-=t.cssOverflowAdjustment(),s.style.transform=`translate3d(${l}px, ${o}px, ${u}px)`);let c,d=t.maxTranslate()-t.minTranslate();d===0?c=0:c=(i-t.minTranslate())/d,c!==r&&t.updateProgress(i),t.emit("setTranslate",t.translate,e)}function Co(){return-this.snapGrid[0]}function ko(){return-this.snapGrid[this.snapGrid.length-1]}function Io(i,e,t,n,a){i===void 0&&(i=0),e===void 0&&(e=this.params.speed),t===void 0&&(t=!0),n===void 0&&(n=!0);let s=this,{params:r,wrapperEl:l}=s;if(s.animating&&r.preventInteractionOnTransition)return!1;let o=s.minTranslate(),u=s.maxTranslate(),c;if(n&&i>o?c=o:n&&i<u?c=u:c=i,s.updateProgress(c),r.cssMode){let d=s.isHorizontal();if(e===0)l[d?"scrollLeft":"scrollTop"]=-c;else{if(!s.support.smoothScroll)return An({swiper:s,targetPosition:-c,side:d?"left":"top"}),!0;l.scrollTo({[d?"left":"top"]:-c,behavior:"smooth"})}return!0}return e===0?(s.setTransition(0),s.setTranslate(c),t&&(s.emit("beforeTransitionStart",e,a),s.emit("transitionEnd"))):(s.setTransition(e),s.setTranslate(c),t&&(s.emit("beforeTransitionStart",e,a),s.emit("transitionStart")),s.animating||(s.animating=!0,s.onTranslateToWrapperTransitionEnd||(s.onTranslateToWrapperTransitionEnd=function(h){!s||s.destroyed||h.target===this&&(s.wrapperEl.removeEventListener("transitionend",s.onTranslateToWrapperTransitionEnd),s.onTranslateToWrapperTransitionEnd=null,delete s.onTranslateToWrapperTransitionEnd,s.animating=!1,t&&s.emit("transitionEnd"))}),s.wrapperEl.addEventListener("transitionend",s.onTranslateToWrapperTransitionEnd))),!0}var Ao={getTranslate:Eo,setTranslate:xo,minTranslate:Co,maxTranslate:ko,translateTo:Io};function Mo(i,e){let t=this;t.params.cssMode||(t.wrapperEl.style.transitionDuration=`${i}ms`,t.wrapperEl.style.transitionDelay=i===0?"0ms":""),t.emit("setTransition",i,e)}function os(i){let{swiper:e,runCallbacks:t,direction:n,step:a}=i,{activeIndex:s,previousIndex:r}=e,l=n;if(l||(s>r?l="next":s<r?l="prev":l="reset"),e.emit(`transition${a}`),t&&s!==r){if(l==="reset"){e.emit(`slideResetTransition${a}`);return}e.emit(`slideChangeTransition${a}`),l==="next"?e.emit(`slideNextTransition${a}`):e.emit(`slidePrevTransition${a}`)}}function Po(i,e){i===void 0&&(i=!0);let t=this,{params:n}=t;n.cssMode||(n.autoHeight&&t.updateAutoHeight(),os({swiper:t,runCallbacks:i,direction:e,step:"Start"}))}function No(i,e){i===void 0&&(i=!0);let t=this,{params:n}=t;t.animating=!1,!n.cssMode&&(t.setTransition(0),os({swiper:t,runCallbacks:i,direction:e,step:"End"}))}var Do={setTransition:Mo,transitionStart:Po,transitionEnd:No};function Oo(i,e,t,n,a){i===void 0&&(i=0),t===void 0&&(t=!0),typeof i=="string"&&(i=parseInt(i,10));let s=this,r=i;r<0&&(r=0);let{params:l,snapGrid:o,slidesGrid:u,previousIndex:c,activeIndex:d,rtlTranslate:h,wrapperEl:f,enabled:m}=s;if(!m&&!n&&!a||s.destroyed||s.animating&&l.preventInteractionOnTransition)return!1;typeof e>"u"&&(e=s.params.speed);let _=Math.min(s.params.slidesPerGroupSkip,r),w=_+Math.floor((r-_)/s.params.slidesPerGroup);w>=o.length&&(w=o.length-1);let b=-o[w];if(l.normalizeSlideIndex)for(let g=0;g<u.length;g+=1){let y=-Math.floor(b*100),E=Math.floor(u[g]*100),N=Math.floor(u[g+1]*100);typeof u[g+1]<"u"?y>=E&&y<N-(N-E)/2?r=g:y>=E&&y<N&&(r=g+1):y>=E&&(r=g)}if(s.initialized&&r!==d&&(!s.allowSlideNext&&(h?b>s.translate&&b>s.minTranslate():b<s.translate&&b<s.minTranslate())||!s.allowSlidePrev&&b>s.translate&&b>s.maxTranslate()&&(d||0)!==r))return!1;r!==(c||0)&&t&&s.emit("beforeSlideChangeStart"),s.updateProgress(b);let v;if(r>d?v="next":r<d?v="prev":v="reset",h&&-b===s.translate||!h&&b===s.translate)return s.updateActiveIndex(r),l.autoHeight&&s.updateAutoHeight(),s.updateSlidesClasses(),l.effect!=="slide"&&s.setTranslate(b),v!=="reset"&&(s.transitionStart(t,v),s.transitionEnd(t,v)),!1;if(l.cssMode){let g=s.isHorizontal(),y=h?b:-b;if(e===0){let E=s.virtual&&s.params.virtual.enabled;E&&(s.wrapperEl.style.scrollSnapType="none",s._immediateVirtual=!0),E&&!s._cssModeVirtualInitialSet&&s.params.initialSlide>0?(s._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{f[g?"scrollLeft":"scrollTop"]=y})):f[g?"scrollLeft":"scrollTop"]=y,E&&requestAnimationFrame(()=>{s.wrapperEl.style.scrollSnapType="",s._immediateVirtual=!1})}else{if(!s.support.smoothScroll)return An({swiper:s,targetPosition:y,side:g?"left":"top"}),!0;f.scrollTo({[g?"left":"top"]:y,behavior:"smooth"})}return!0}return s.setTransition(e),s.setTranslate(b),s.updateActiveIndex(r),s.updateSlidesClasses(),s.emit("beforeTransitionStart",e,n),s.transitionStart(t,v),e===0?s.transitionEnd(t,v):s.animating||(s.animating=!0,s.onSlideToWrapperTransitionEnd||(s.onSlideToWrapperTransitionEnd=function(y){!s||s.destroyed||y.target===this&&(s.wrapperEl.removeEventListener("transitionend",s.onSlideToWrapperTransitionEnd),s.onSlideToWrapperTransitionEnd=null,delete s.onSlideToWrapperTransitionEnd,s.transitionEnd(t,v))}),s.wrapperEl.addEventListener("transitionend",s.onSlideToWrapperTransitionEnd)),!0}function Lo(i,e,t,n){i===void 0&&(i=0),t===void 0&&(t=!0),typeof i=="string"&&(i=parseInt(i,10));let a=this;if(a.destroyed)return;typeof e>"u"&&(e=a.params.speed);let s=a.grid&&a.params.grid&&a.params.grid.rows>1,r=i;if(a.params.loop)if(a.virtual&&a.params.virtual.enabled)r=r+a.virtual.slidesBefore;else{let l;if(s){let h=r*a.params.grid.rows;l=a.slides.filter(f=>f.getAttribute("data-swiper-slide-index")*1===h)[0].column}else l=a.getSlideIndexByData(r);let o=s?Math.ceil(a.slides.length/a.params.grid.rows):a.slides.length,{centeredSlides:u}=a.params,c=a.params.slidesPerView;c==="auto"?c=a.slidesPerViewDynamic():(c=Math.ceil(parseFloat(a.params.slidesPerView,10)),u&&c%2===0&&(c=c+1));let d=o-l<c;if(u&&(d=d||l<Math.ceil(c/2)),n&&u&&a.params.slidesPerView!=="auto"&&!s&&(d=!1),d){let h=u?l<a.activeIndex?"prev":"next":l-a.activeIndex-1<a.params.slidesPerView?"next":"prev";a.loopFix({direction:h,slideTo:!0,activeSlideIndex:h==="next"?l+1:l-o+1,slideRealIndex:h==="next"?a.realIndex:void 0})}if(s){let h=r*a.params.grid.rows;r=a.slides.filter(f=>f.getAttribute("data-swiper-slide-index")*1===h)[0].column}else r=a.getSlideIndexByData(r)}return requestAnimationFrame(()=>{a.slideTo(r,e,t,n)}),a}function Ho(i,e,t){e===void 0&&(e=!0);let n=this,{enabled:a,params:s,animating:r}=n;if(!a||n.destroyed)return n;typeof i>"u"&&(i=n.params.speed);let l=s.slidesPerGroup;s.slidesPerView==="auto"&&s.slidesPerGroup===1&&s.slidesPerGroupAuto&&(l=Math.max(n.slidesPerViewDynamic("current",!0),1));let o=n.activeIndex<s.slidesPerGroupSkip?1:l,u=n.virtual&&s.virtual.enabled;if(s.loop){if(r&&!u&&s.loopPreventsSliding)return!1;if(n.loopFix({direction:"next"}),n._clientLeft=n.wrapperEl.clientLeft,n.activeIndex===n.slides.length-1&&s.cssMode)return requestAnimationFrame(()=>{n.slideTo(n.activeIndex+o,i,e,t)}),!0}return s.rewind&&n.isEnd?n.slideTo(0,i,e,t):n.slideTo(n.activeIndex+o,i,e,t)}function Ro(i,e,t){e===void 0&&(e=!0);let n=this,{params:a,snapGrid:s,slidesGrid:r,rtlTranslate:l,enabled:o,animating:u}=n;if(!o||n.destroyed)return n;typeof i>"u"&&(i=n.params.speed);let c=n.virtual&&a.virtual.enabled;if(a.loop){if(u&&!c&&a.loopPreventsSliding)return!1;n.loopFix({direction:"prev"}),n._clientLeft=n.wrapperEl.clientLeft}let d=l?n.translate:-n.translate;function h(b){return b<0?-Math.floor(Math.abs(b)):Math.floor(b)}let f=h(d),m=s.map(b=>h(b)),_=s[m.indexOf(f)-1];if(typeof _>"u"&&a.cssMode){let b;s.forEach((v,g)=>{f>=v&&(b=g)}),typeof b<"u"&&(_=s[b>0?b-1:b])}let w=0;if(typeof _<"u"&&(w=r.indexOf(_),w<0&&(w=n.activeIndex-1),a.slidesPerView==="auto"&&a.slidesPerGroup===1&&a.slidesPerGroupAuto&&(w=w-n.slidesPerViewDynamic("previous",!0)+1,w=Math.max(w,0))),a.rewind&&n.isBeginning){let b=n.params.virtual&&n.params.virtual.enabled&&n.virtual?n.virtual.slides.length-1:n.slides.length-1;return n.slideTo(b,i,e,t)}else if(a.loop&&n.activeIndex===0&&a.cssMode)return requestAnimationFrame(()=>{n.slideTo(w,i,e,t)}),!0;return n.slideTo(w,i,e,t)}function Bo(i,e,t){e===void 0&&(e=!0);let n=this;if(!n.destroyed)return typeof i>"u"&&(i=n.params.speed),n.slideTo(n.activeIndex,i,e,t)}function zo(i,e,t,n){e===void 0&&(e=!0),n===void 0&&(n=.5);let a=this;if(a.destroyed)return;typeof i>"u"&&(i=a.params.speed);let s=a.activeIndex,r=Math.min(a.params.slidesPerGroupSkip,s),l=r+Math.floor((s-r)/a.params.slidesPerGroup),o=a.rtlTranslate?a.translate:-a.translate;if(o>=a.snapGrid[l]){let u=a.snapGrid[l],c=a.snapGrid[l+1];o-u>(c-u)*n&&(s+=a.params.slidesPerGroup)}else{let u=a.snapGrid[l-1],c=a.snapGrid[l];o-u<=(c-u)*n&&(s-=a.params.slidesPerGroup)}return s=Math.max(s,0),s=Math.min(s,a.slidesGrid.length-1),a.slideTo(s,i,e,t)}function $o(){let i=this;if(i.destroyed)return;let{params:e,slidesEl:t}=i,n=e.slidesPerView==="auto"?i.slidesPerViewDynamic():e.slidesPerView,a=i.clickedIndex,s,r=i.isElement?"swiper-slide":`.${e.slideClass}`;if(e.loop){if(i.animating)return;s=parseInt(i.clickedSlide.getAttribute("data-swiper-slide-index"),10),e.centeredSlides?a<i.loopedSlides-n/2||a>i.slides.length-i.loopedSlides+n/2?(i.loopFix(),a=i.getSlideIndex(de(t,`${r}[data-swiper-slide-index="${s}"]`)[0]),Ye(()=>{i.slideTo(a)})):i.slideTo(a):a>i.slides.length-n?(i.loopFix(),a=i.getSlideIndex(de(t,`${r}[data-swiper-slide-index="${s}"]`)[0]),Ye(()=>{i.slideTo(a)})):i.slideTo(a)}else i.slideTo(a)}var qo={slideTo:Oo,slideToLoop:Lo,slideNext:Ho,slidePrev:Ro,slideReset:Bo,slideToClosest:zo,slideToClickedSlide:$o};function Fo(i){let e=this,{params:t,slidesEl:n}=e;if(!t.loop||e.virtual&&e.params.virtual.enabled)return;let a=()=>{de(n,`.${t.slideClass}, swiper-slide`).forEach((d,h)=>{d.setAttribute("data-swiper-slide-index",h)})},s=e.grid&&t.grid&&t.grid.rows>1,r=t.slidesPerGroup*(s?t.grid.rows:1),l=e.slides.length%r!==0,o=s&&e.slides.length%t.grid.rows!==0,u=c=>{for(let d=0;d<c;d+=1){let h=e.isElement?he("swiper-slide",[t.slideBlankClass]):he("div",[t.slideClass,t.slideBlankClass]);e.slidesEl.append(h)}};if(l){if(t.loopAddBlankSlides){let c=r-e.slides.length%r;u(c),e.recalcSlides(),e.updateSlides()}else hi("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");a()}else if(o){if(t.loopAddBlankSlides){let c=t.grid.rows-e.slides.length%t.grid.rows;u(c),e.recalcSlides(),e.updateSlides()}else hi("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");a()}else a();e.loopFix({slideRealIndex:i,direction:t.centeredSlides?void 0:"next"})}function Vo(i){let{slideRealIndex:e,slideTo:t=!0,direction:n,setTranslate:a,activeSlideIndex:s,byController:r,byMousewheel:l}=i===void 0?{}:i,o=this;if(!o.params.loop)return;o.emit("beforeLoopFix");let{slides:u,allowSlidePrev:c,allowSlideNext:d,slidesEl:h,params:f}=o,{centeredSlides:m}=f;if(o.allowSlidePrev=!0,o.allowSlideNext=!0,o.virtual&&f.virtual.enabled){t&&(!f.centeredSlides&&o.snapIndex===0?o.slideTo(o.virtual.slides.length,0,!1,!0):f.centeredSlides&&o.snapIndex<f.slidesPerView?o.slideTo(o.virtual.slides.length+o.snapIndex,0,!1,!0):o.snapIndex===o.snapGrid.length-1&&o.slideTo(o.virtual.slidesBefore,0,!1,!0)),o.allowSlidePrev=c,o.allowSlideNext=d,o.emit("loopFix");return}let _=f.slidesPerView;_==="auto"?_=o.slidesPerViewDynamic():(_=Math.ceil(parseFloat(f.slidesPerView,10)),m&&_%2===0&&(_=_+1));let w=f.slidesPerGroupAuto?_:f.slidesPerGroup,b=w;b%w!==0&&(b+=w-b%w),b+=f.loopAdditionalSlides,o.loopedSlides=b;let v=o.grid&&f.grid&&f.grid.rows>1;u.length<_+b?hi("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled and not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):v&&f.grid.fill==="row"&&hi("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");let g=[],y=[],E=o.activeIndex;typeof s>"u"?s=o.getSlideIndex(u.filter(T=>T.classList.contains(f.slideActiveClass))[0]):E=s;let N=n==="next"||!n,L=n==="prev"||!n,D=0,S=0,C=v?Math.ceil(u.length/f.grid.rows):u.length,A=(v?u[s].column:s)+(m&&typeof a>"u"?-_/2+.5:0);if(A<b){D=Math.max(b-A,w);for(let T=0;T<b-A;T+=1){let I=T-Math.floor(T/C)*C;if(v){let O=C-I-1;for(let V=u.length-1;V>=0;V-=1)u[V].column===O&&g.push(V)}else g.push(C-I-1)}}else if(A+_>C-b){S=Math.max(A-(C-b*2),w);for(let T=0;T<S;T+=1){let I=T-Math.floor(T/C)*C;v?u.forEach((O,V)=>{O.column===I&&y.push(V)}):y.push(I)}}if(o.__preventObserver__=!0,requestAnimationFrame(()=>{o.__preventObserver__=!1}),L&&g.forEach(T=>{u[T].swiperLoopMoveDOM=!0,h.prepend(u[T]),u[T].swiperLoopMoveDOM=!1}),N&&y.forEach(T=>{u[T].swiperLoopMoveDOM=!0,h.append(u[T]),u[T].swiperLoopMoveDOM=!1}),o.recalcSlides(),f.slidesPerView==="auto"?o.updateSlides():v&&(g.length>0&&L||y.length>0&&N)&&o.slides.forEach((T,I)=>{o.grid.updateSlide(I,T,o.slides)}),f.watchSlidesProgress&&o.updateSlidesOffset(),t){if(g.length>0&&L){if(typeof e>"u"){let T=o.slidesGrid[E],O=o.slidesGrid[E+D]-T;l?o.setTranslate(o.translate-O):(o.slideTo(E+Math.ceil(D),0,!1,!0),a&&(o.touchEventsData.startTranslate=o.touchEventsData.startTranslate-O,o.touchEventsData.currentTranslate=o.touchEventsData.currentTranslate-O))}else if(a){let T=v?g.length/f.grid.rows:g.length;o.slideTo(o.activeIndex+T,0,!1,!0),o.touchEventsData.currentTranslate=o.translate}}else if(y.length>0&&N)if(typeof e>"u"){let T=o.slidesGrid[E],O=o.slidesGrid[E-S]-T;l?o.setTranslate(o.translate-O):(o.slideTo(E-S,0,!1,!0),a&&(o.touchEventsData.startTranslate=o.touchEventsData.startTranslate-O,o.touchEventsData.currentTranslate=o.touchEventsData.currentTranslate-O))}else{let T=v?y.length/f.grid.rows:y.length;o.slideTo(o.activeIndex-T,0,!1,!0)}}if(o.allowSlidePrev=c,o.allowSlideNext=d,o.controller&&o.controller.control&&!r){let T={slideRealIndex:e,direction:n,setTranslate:a,activeSlideIndex:s,byController:!0};Array.isArray(o.controller.control)?o.controller.control.forEach(I=>{!I.destroyed&&I.params.loop&&I.loopFix(ie(X({},T),{slideTo:I.params.slidesPerView===f.slidesPerView?t:!1}))}):o.controller.control instanceof o.constructor&&o.controller.control.params.loop&&o.controller.control.loopFix(ie(X({},T),{slideTo:o.controller.control.params.slidesPerView===f.slidesPerView?t:!1}))}o.emit("loopFix")}function Go(){let i=this,{params:e,slidesEl:t}=i;if(!e.loop||i.virtual&&i.params.virtual.enabled)return;i.recalcSlides();let n=[];i.slides.forEach(a=>{let s=typeof a.swiperSlideIndex>"u"?a.getAttribute("data-swiper-slide-index")*1:a.swiperSlideIndex;n[s]=a}),i.slides.forEach(a=>{a.removeAttribute("data-swiper-slide-index")}),n.forEach(a=>{t.append(a)}),i.recalcSlides(),i.slideTo(i.realIndex,0)}var Uo={loopCreate:Fo,loopFix:Vo,loopDestroy:Go};function Yo(i){let e=this;if(!e.params.simulateTouch||e.params.watchOverflow&&e.isLocked||e.params.cssMode)return;let t=e.params.touchEventsTarget==="container"?e.el:e.wrapperEl;e.isElement&&(e.__preventObserver__=!0),t.style.cursor="move",t.style.cursor=i?"grabbing":"grab",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1})}function jo(){let i=this;i.params.watchOverflow&&i.isLocked||i.params.cssMode||(i.isElement&&(i.__preventObserver__=!0),i[i.params.touchEventsTarget==="container"?"el":"wrapperEl"].style.cursor="",i.isElement&&requestAnimationFrame(()=>{i.__preventObserver__=!1}))}var Wo={setGrabCursor:Yo,unsetGrabCursor:jo};function Xo(i,e){e===void 0&&(e=this);function t(n){if(!n||n===le()||n===ne())return null;n.assignedSlot&&(n=n.assignedSlot);let a=n.closest(i);return!a&&!n.getRootNode?null:a||t(n.getRootNode().host)}return t(e)}function is(i,e,t){let n=ne(),{params:a}=i,s=a.edgeSwipeDetection,r=a.edgeSwipeThreshold;return s&&(t<=r||t>=n.innerWidth-r)?s==="prevent"?(e.preventDefault(),!0):!1:!0}function Ko(i){let e=this,t=le(),n=i;n.originalEvent&&(n=n.originalEvent);let a=e.touchEventsData;if(n.type==="pointerdown"){if(a.pointerId!==null&&a.pointerId!==n.pointerId)return;a.pointerId=n.pointerId}else n.type==="touchstart"&&n.targetTouches.length===1&&(a.touchId=n.targetTouches[0].identifier);if(n.type==="touchstart"){is(e,n,n.targetTouches[0].pageX);return}let{params:s,touches:r,enabled:l}=e;if(!l||!s.simulateTouch&&n.pointerType==="mouse"||e.animating&&s.preventInteractionOnTransition)return;!e.animating&&s.cssMode&&s.loop&&e.loopFix();let o=n.target;if(s.touchEventsTarget==="wrapper"&&!Za(o,e.wrapperEl)||"which"in n&&n.which===3||"button"in n&&n.button>0||a.isTouched&&a.isMoved)return;let u=!!s.noSwipingClass&&s.noSwipingClass!=="",c=n.composedPath?n.composedPath():n.path;u&&n.target&&n.target.shadowRoot&&c&&(o=c[0]);let d=s.noSwipingSelector?s.noSwipingSelector:`.${s.noSwipingClass}`,h=!!(n.target&&n.target.shadowRoot);if(s.noSwiping&&(h?Xo(d,o):o.closest(d))){e.allowClick=!0;return}if(s.swipeHandler&&!o.closest(s.swipeHandler))return;r.currentX=n.pageX,r.currentY=n.pageY;let f=r.currentX,m=r.currentY;if(!is(e,n,f))return;Object.assign(a,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),r.startX=f,r.startY=m,a.touchStartTime=we(),e.allowClick=!0,e.updateSize(),e.swipeDirection=void 0,s.threshold>0&&(a.allowThresholdMove=!1);let _=!0;o.matches(a.focusableElements)&&(_=!1,o.nodeName==="SELECT"&&(a.isTouched=!1)),t.activeElement&&t.activeElement.matches(a.focusableElements)&&t.activeElement!==o&&t.activeElement.blur();let w=_&&e.allowTouchMove&&s.touchStartPreventDefault;(s.touchStartForcePreventDefault||w)&&!o.isContentEditable&&n.preventDefault(),s.freeMode&&s.freeMode.enabled&&e.freeMode&&e.animating&&!s.cssMode&&e.freeMode.onTouchStart(),e.emit("touchStart",n)}function Qo(i){let e=le(),t=this,n=t.touchEventsData,{params:a,touches:s,rtlTranslate:r,enabled:l}=t;if(!l||!a.simulateTouch&&i.pointerType==="mouse")return;let o=i;if(o.originalEvent&&(o=o.originalEvent),o.type==="pointermove"&&(n.touchId!==null||o.pointerId!==n.pointerId))return;let u;if(o.type==="touchmove"){if(u=[...o.changedTouches].filter(N=>N.identifier===n.touchId)[0],!u||u.identifier!==n.touchId)return}else u=o;if(!n.isTouched){n.startMoving&&n.isScrolling&&t.emit("touchMoveOpposite",o);return}let c=u.pageX,d=u.pageY;if(o.preventedByNestedSwiper){s.startX=c,s.startY=d;return}if(!t.allowTouchMove){o.target.matches(n.focusableElements)||(t.allowClick=!1),n.isTouched&&(Object.assign(s,{startX:c,startY:d,currentX:c,currentY:d}),n.touchStartTime=we());return}if(a.touchReleaseOnEdges&&!a.loop){if(t.isVertical()){if(d<s.startY&&t.translate<=t.maxTranslate()||d>s.startY&&t.translate>=t.minTranslate()){n.isTouched=!1,n.isMoved=!1;return}}else if(c<s.startX&&t.translate<=t.maxTranslate()||c>s.startX&&t.translate>=t.minTranslate())return}if(e.activeElement&&o.target===e.activeElement&&o.target.matches(n.focusableElements)){n.isMoved=!0,t.allowClick=!1;return}n.allowTouchCallbacks&&t.emit("touchMove",o),s.previousX=s.currentX,s.previousY=s.currentY,s.currentX=c,s.currentY=d;let h=s.currentX-s.startX,f=s.currentY-s.startY;if(t.params.threshold&&Math.sqrt(h**2+f**2)<t.params.threshold)return;if(typeof n.isScrolling>"u"){let N;t.isHorizontal()&&s.currentY===s.startY||t.isVertical()&&s.currentX===s.startX?n.isScrolling=!1:h*h+f*f>=25&&(N=Math.atan2(Math.abs(f),Math.abs(h))*180/Math.PI,n.isScrolling=t.isHorizontal()?N>a.touchAngle:90-N>a.touchAngle)}if(n.isScrolling&&t.emit("touchMoveOpposite",o),typeof n.startMoving>"u"&&(s.currentX!==s.startX||s.currentY!==s.startY)&&(n.startMoving=!0),n.isScrolling||o.type==="touchmove"&&n.preventTouchMoveFromPointerMove){n.isTouched=!1;return}if(!n.startMoving)return;t.allowClick=!1,!a.cssMode&&o.cancelable&&o.preventDefault(),a.touchMoveStopPropagation&&!a.nested&&o.stopPropagation();let m=t.isHorizontal()?h:f,_=t.isHorizontal()?s.currentX-s.previousX:s.currentY-s.previousY;a.oneWayMovement&&(m=Math.abs(m)*(r?1:-1),_=Math.abs(_)*(r?1:-1)),s.diff=m,m*=a.touchRatio,r&&(m=-m,_=-_);let w=t.touchesDirection;t.swipeDirection=m>0?"prev":"next",t.touchesDirection=_>0?"prev":"next";let b=t.params.loop&&!a.cssMode,v=t.touchesDirection==="next"&&t.allowSlideNext||t.touchesDirection==="prev"&&t.allowSlidePrev;if(!n.isMoved){if(b&&v&&t.loopFix({direction:t.swipeDirection}),n.startTranslate=t.getTranslate(),t.setTransition(0),t.animating){let N=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});t.wrapperEl.dispatchEvent(N)}n.allowMomentumBounce=!1,a.grabCursor&&(t.allowSlideNext===!0||t.allowSlidePrev===!0)&&t.setGrabCursor(!0),t.emit("sliderFirstMove",o)}let g;if(new Date().getTime(),n.isMoved&&n.allowThresholdMove&&w!==t.touchesDirection&&b&&v&&Math.abs(m)>=1){Object.assign(s,{startX:c,startY:d,currentX:c,currentY:d,startTranslate:n.currentTranslate}),n.loopSwapReset=!0,n.startTranslate=n.currentTranslate;return}t.emit("sliderMove",o),n.isMoved=!0,n.currentTranslate=m+n.startTranslate;let y=!0,E=a.resistanceRatio;if(a.touchReleaseOnEdges&&(E=0),m>0?(b&&v&&!g&&n.allowThresholdMove&&n.currentTranslate>(a.centeredSlides?t.minTranslate()-t.slidesSizesGrid[t.activeIndex+1]-(a.slidesPerView!=="auto"&&t.slides.length-a.slidesPerView>=2?t.slidesSizesGrid[t.activeIndex+1]+t.params.spaceBetween:0)-t.params.spaceBetween:t.minTranslate())&&t.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),n.currentTranslate>t.minTranslate()&&(y=!1,a.resistance&&(n.currentTranslate=t.minTranslate()-1+(-t.minTranslate()+n.startTranslate+m)**E))):m<0&&(b&&v&&!g&&n.allowThresholdMove&&n.currentTranslate<(a.centeredSlides?t.maxTranslate()+t.slidesSizesGrid[t.slidesSizesGrid.length-1]+t.params.spaceBetween+(a.slidesPerView!=="auto"&&t.slides.length-a.slidesPerView>=2?t.slidesSizesGrid[t.slidesSizesGrid.length-1]+t.params.spaceBetween:0):t.maxTranslate())&&t.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:t.slides.length-(a.slidesPerView==="auto"?t.slidesPerViewDynamic():Math.ceil(parseFloat(a.slidesPerView,10)))}),n.currentTranslate<t.maxTranslate()&&(y=!1,a.resistance&&(n.currentTranslate=t.maxTranslate()+1-(t.maxTranslate()-n.startTranslate-m)**E))),y&&(o.preventedByNestedSwiper=!0),!t.allowSlideNext&&t.swipeDirection==="next"&&n.currentTranslate<n.startTranslate&&(n.currentTranslate=n.startTranslate),!t.allowSlidePrev&&t.swipeDirection==="prev"&&n.currentTranslate>n.startTranslate&&(n.currentTranslate=n.startTranslate),!t.allowSlidePrev&&!t.allowSlideNext&&(n.currentTranslate=n.startTranslate),a.threshold>0)if(Math.abs(m)>a.threshold||n.allowThresholdMove){if(!n.allowThresholdMove){n.allowThresholdMove=!0,s.startX=s.currentX,s.startY=s.currentY,n.currentTranslate=n.startTranslate,s.diff=t.isHorizontal()?s.currentX-s.startX:s.currentY-s.startY;return}}else{n.currentTranslate=n.startTranslate;return}!a.followFinger||a.cssMode||((a.freeMode&&a.freeMode.enabled&&t.freeMode||a.watchSlidesProgress)&&(t.updateActiveIndex(),t.updateSlidesClasses()),a.freeMode&&a.freeMode.enabled&&t.freeMode&&t.freeMode.onTouchMove(),t.updateProgress(n.currentTranslate),t.setTranslate(n.currentTranslate))}function Zo(i){let e=this,t=e.touchEventsData,n=i;n.originalEvent&&(n=n.originalEvent);let a;if(n.type==="touchend"||n.type==="touchcancel"){if(a=[...n.changedTouches].filter(E=>E.identifier===t.touchId)[0],!a||a.identifier!==t.touchId)return}else{if(t.touchId!==null||n.pointerId!==t.pointerId)return;a=n}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(n.type)&&!(["pointercancel","contextmenu"].includes(n.type)&&(e.browser.isSafari||e.browser.isWebView)))return;t.pointerId=null,t.touchId=null;let{params:r,touches:l,rtlTranslate:o,slidesGrid:u,enabled:c}=e;if(!c||!r.simulateTouch&&n.pointerType==="mouse")return;if(t.allowTouchCallbacks&&e.emit("touchEnd",n),t.allowTouchCallbacks=!1,!t.isTouched){t.isMoved&&r.grabCursor&&e.setGrabCursor(!1),t.isMoved=!1,t.startMoving=!1;return}r.grabCursor&&t.isMoved&&t.isTouched&&(e.allowSlideNext===!0||e.allowSlidePrev===!0)&&e.setGrabCursor(!1);let d=we(),h=d-t.touchStartTime;if(e.allowClick){let E=n.path||n.composedPath&&n.composedPath();e.updateClickedSlide(E&&E[0]||n.target,E),e.emit("tap click",n),h<300&&d-t.lastClickTime<300&&e.emit("doubleTap doubleClick",n)}if(t.lastClickTime=we(),Ye(()=>{e.destroyed||(e.allowClick=!0)}),!t.isTouched||!t.isMoved||!e.swipeDirection||l.diff===0&&!t.loopSwapReset||t.currentTranslate===t.startTranslate&&!t.loopSwapReset){t.isTouched=!1,t.isMoved=!1,t.startMoving=!1;return}t.isTouched=!1,t.isMoved=!1,t.startMoving=!1;let f;if(r.followFinger?f=o?e.translate:-e.translate:f=-t.currentTranslate,r.cssMode)return;if(r.freeMode&&r.freeMode.enabled){e.freeMode.onTouchEnd({currentPos:f});return}let m=f>=-e.maxTranslate()&&!e.params.loop,_=0,w=e.slidesSizesGrid[0];for(let E=0;E<u.length;E+=E<r.slidesPerGroupSkip?1:r.slidesPerGroup){let N=E<r.slidesPerGroupSkip-1?1:r.slidesPerGroup;typeof u[E+N]<"u"?(m||f>=u[E]&&f<u[E+N])&&(_=E,w=u[E+N]-u[E]):(m||f>=u[E])&&(_=E,w=u[u.length-1]-u[u.length-2])}let b=null,v=null;r.rewind&&(e.isBeginning?v=r.virtual&&r.virtual.enabled&&e.virtual?e.virtual.slides.length-1:e.slides.length-1:e.isEnd&&(b=0));let g=(f-u[_])/w,y=_<r.slidesPerGroupSkip-1?1:r.slidesPerGroup;if(h>r.longSwipesMs){if(!r.longSwipes){e.slideTo(e.activeIndex);return}e.swipeDirection==="next"&&(g>=r.longSwipesRatio?e.slideTo(r.rewind&&e.isEnd?b:_+y):e.slideTo(_)),e.swipeDirection==="prev"&&(g>1-r.longSwipesRatio?e.slideTo(_+y):v!==null&&g<0&&Math.abs(g)>r.longSwipesRatio?e.slideTo(v):e.slideTo(_))}else{if(!r.shortSwipes){e.slideTo(e.activeIndex);return}e.navigation&&(n.target===e.navigation.nextEl||n.target===e.navigation.prevEl)?n.target===e.navigation.nextEl?e.slideTo(_+y):e.slideTo(_):(e.swipeDirection==="next"&&e.slideTo(b!==null?b:_+y),e.swipeDirection==="prev"&&e.slideTo(v!==null?v:_))}}function ns(){let i=this,{params:e,el:t}=i;if(t&&t.offsetWidth===0)return;e.breakpoints&&i.setBreakpoint();let{allowSlideNext:n,allowSlidePrev:a,snapGrid:s}=i,r=i.virtual&&i.params.virtual.enabled;i.allowSlideNext=!0,i.allowSlidePrev=!0,i.updateSize(),i.updateSlides(),i.updateSlidesClasses();let l=r&&e.loop;(e.slidesPerView==="auto"||e.slidesPerView>1)&&i.isEnd&&!i.isBeginning&&!i.params.centeredSlides&&!l?i.slideTo(i.slides.length-1,0,!1,!0):i.params.loop&&!r?i.slideToLoop(i.realIndex,0,!1,!0):i.slideTo(i.activeIndex,0,!1,!0),i.autoplay&&i.autoplay.running&&i.autoplay.paused&&(clearTimeout(i.autoplay.resizeTimeout),i.autoplay.resizeTimeout=setTimeout(()=>{i.autoplay&&i.autoplay.running&&i.autoplay.paused&&i.autoplay.resume()},500)),i.allowSlidePrev=a,i.allowSlideNext=n,i.params.watchOverflow&&s!==i.snapGrid&&i.checkOverflow()}function Jo(i){let e=this;e.enabled&&(e.allowClick||(e.params.preventClicks&&i.preventDefault(),e.params.preventClicksPropagation&&e.animating&&(i.stopPropagation(),i.stopImmediatePropagation())))}function el(){let i=this,{wrapperEl:e,rtlTranslate:t,enabled:n}=i;if(!n)return;i.previousTranslate=i.translate,i.isHorizontal()?i.translate=-e.scrollLeft:i.translate=-e.scrollTop,i.translate===0&&(i.translate=0),i.updateActiveIndex(),i.updateSlidesClasses();let a,s=i.maxTranslate()-i.minTranslate();s===0?a=0:a=(i.translate-i.minTranslate())/s,a!==i.progress&&i.updateProgress(t?-i.translate:i.translate),i.emit("setTranslate",i.translate,!1)}function tl(i){let e=this;Xi(e,i.target),!(e.params.cssMode||e.params.slidesPerView!=="auto"&&!e.params.autoHeight)&&e.update()}function il(){let i=this;i.documentTouchHandlerProceeded||(i.documentTouchHandlerProceeded=!0,i.params.touchReleaseOnEdges&&(i.el.style.touchAction="auto"))}var ls=(i,e)=>{let t=le(),{params:n,el:a,wrapperEl:s,device:r}=i,l=!!n.nested,o=e==="on"?"addEventListener":"removeEventListener",u=e;!a||typeof a=="string"||(t[o]("touchstart",i.onDocumentTouchStart,{passive:!1,capture:l}),a[o]("touchstart",i.onTouchStart,{passive:!1}),a[o]("pointerdown",i.onTouchStart,{passive:!1}),t[o]("touchmove",i.onTouchMove,{passive:!1,capture:l}),t[o]("pointermove",i.onTouchMove,{passive:!1,capture:l}),t[o]("touchend",i.onTouchEnd,{passive:!0}),t[o]("pointerup",i.onTouchEnd,{passive:!0}),t[o]("pointercancel",i.onTouchEnd,{passive:!0}),t[o]("touchcancel",i.onTouchEnd,{passive:!0}),t[o]("pointerout",i.onTouchEnd,{passive:!0}),t[o]("pointerleave",i.onTouchEnd,{passive:!0}),t[o]("contextmenu",i.onTouchEnd,{passive:!0}),(n.preventClicks||n.preventClicksPropagation)&&a[o]("click",i.onClick,!0),n.cssMode&&s[o]("scroll",i.onScroll),n.updateOnWindowResize?i[u](r.ios||r.android?"resize orientationchange observerUpdate":"resize observerUpdate",ns,!0):i[u]("observerUpdate",ns,!0),a[o]("load",i.onLoad,{capture:!0}))};function nl(){let i=this,{params:e}=i;i.onTouchStart=Ko.bind(i),i.onTouchMove=Qo.bind(i),i.onTouchEnd=Zo.bind(i),i.onDocumentTouchStart=il.bind(i),e.cssMode&&(i.onScroll=el.bind(i)),i.onClick=Jo.bind(i),i.onLoad=tl.bind(i),ls(i,"on")}function al(){ls(this,"off")}var sl={attachEvents:nl,detachEvents:al},as=(i,e)=>i.grid&&e.grid&&e.grid.rows>1;function rl(){let i=this,{realIndex:e,initialized:t,params:n,el:a}=i,s=n.breakpoints;if(!s||s&&Object.keys(s).length===0)return;let r=i.getBreakpoint(s,i.params.breakpointsBase,i.el);if(!r||i.currentBreakpoint===r)return;let o=(r in s?s[r]:void 0)||i.originalParams,u=as(i,n),c=as(i,o),d=i.params.grabCursor,h=o.grabCursor,f=n.enabled;u&&!c?(a.classList.remove(`${n.containerModifierClass}grid`,`${n.containerModifierClass}grid-column`),i.emitContainerClasses()):!u&&c&&(a.classList.add(`${n.containerModifierClass}grid`),(o.grid.fill&&o.grid.fill==="column"||!o.grid.fill&&n.grid.fill==="column")&&a.classList.add(`${n.containerModifierClass}grid-column`),i.emitContainerClasses()),d&&!h?i.unsetGrabCursor():!d&&h&&i.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(g=>{if(typeof o[g]>"u")return;let y=n[g]&&n[g].enabled,E=o[g]&&o[g].enabled;y&&!E&&i[g].disable(),!y&&E&&i[g].enable()});let m=o.direction&&o.direction!==n.direction,_=n.loop&&(o.slidesPerView!==n.slidesPerView||m),w=n.loop;m&&t&&i.changeDirection(),Te(i.params,o);let b=i.params.enabled,v=i.params.loop;Object.assign(i,{allowTouchMove:i.params.allowTouchMove,allowSlideNext:i.params.allowSlideNext,allowSlidePrev:i.params.allowSlidePrev}),f&&!b?i.disable():!f&&b&&i.enable(),i.currentBreakpoint=r,i.emit("_beforeBreakpoint",o),t&&(_?(i.loopDestroy(),i.loopCreate(e),i.updateSlides()):!w&&v?(i.loopCreate(e),i.updateSlides()):w&&!v&&i.loopDestroy()),i.emit("breakpoint",o)}function ol(i,e,t){if(e===void 0&&(e="window"),!i||e==="container"&&!t)return;let n=!1,a=ne(),s=e==="window"?a.innerHeight:t.clientHeight,r=Object.keys(i).map(l=>{if(typeof l=="string"&&l.indexOf("@")===0){let o=parseFloat(l.substr(1));return{value:s*o,point:l}}return{value:l,point:l}});r.sort((l,o)=>parseInt(l.value,10)-parseInt(o.value,10));for(let l=0;l<r.length;l+=1){let{point:o,value:u}=r[l];e==="window"?a.matchMedia(`(min-width: ${u}px)`).matches&&(n=o):u<=t.clientWidth&&(n=o)}return n||"max"}var ll={setBreakpoint:rl,getBreakpoint:ol};function cl(i,e){let t=[];return i.forEach(n=>{typeof n=="object"?Object.keys(n).forEach(a=>{n[a]&&t.push(e+a)}):typeof n=="string"&&t.push(e+n)}),t}function dl(){let i=this,{classNames:e,params:t,rtl:n,el:a,device:s}=i,r=cl(["initialized",t.direction,{"free-mode":i.params.freeMode&&t.freeMode.enabled},{autoheight:t.autoHeight},{rtl:n},{grid:t.grid&&t.grid.rows>1},{"grid-column":t.grid&&t.grid.rows>1&&t.grid.fill==="column"},{android:s.android},{ios:s.ios},{"css-mode":t.cssMode},{centered:t.cssMode&&t.centeredSlides},{"watch-progress":t.watchSlidesProgress}],t.containerModifierClass);e.push(...r),a.classList.add(...e),i.emitContainerClasses()}function ul(){let i=this,{el:e,classNames:t}=i;!e||typeof e=="string"||(e.classList.remove(...t),i.emitContainerClasses())}var pl={addClasses:dl,removeClasses:ul};function hl(){let i=this,{isLocked:e,params:t}=i,{slidesOffsetBefore:n}=t;if(n){let a=i.slides.length-1,s=i.slidesGrid[a]+i.slidesSizesGrid[a]+n*2;i.isLocked=i.size>s}else i.isLocked=i.snapGrid.length===1;t.allowSlideNext===!0&&(i.allowSlideNext=!i.isLocked),t.allowSlidePrev===!0&&(i.allowSlidePrev=!i.isLocked),e&&e!==i.isLocked&&(i.isEnd=!1),e!==i.isLocked&&i.emit(i.isLocked?"lock":"unlock")}var fl={checkOverflow:hl},Ki={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function ml(i,e){return function(n){n===void 0&&(n={});let a=Object.keys(n)[0],s=n[a];if(typeof s!="object"||s===null){Te(e,n);return}if(i[a]===!0&&(i[a]={enabled:!0}),a==="navigation"&&i[a]&&i[a].enabled&&!i[a].prevEl&&!i[a].nextEl&&(i[a].auto=!0),["pagination","scrollbar"].indexOf(a)>=0&&i[a]&&i[a].enabled&&!i[a].el&&(i[a].auto=!0),!(a in i&&"enabled"in s)){Te(e,n);return}typeof i[a]=="object"&&!("enabled"in i[a])&&(i[a].enabled=!0),i[a]||(i[a]={enabled:!1}),Te(e,n)}}var Ln={eventsEmitter:po,update:To,translate:Ao,transition:Do,slide:qo,loop:Uo,grabCursor:Wo,events:sl,breakpoints:ll,checkOverflow:fl,classes:pl},Hn={},wt=class i{constructor(){let e,t;for(var n=arguments.length,a=new Array(n),s=0;s<n;s++)a[s]=arguments[s];a.length===1&&a[0].constructor&&Object.prototype.toString.call(a[0]).slice(8,-1)==="Object"?t=a[0]:[e,t]=a,t||(t={}),t=Te({},t),e&&!t.el&&(t.el=e);let r=le();if(t.el&&typeof t.el=="string"&&r.querySelectorAll(t.el).length>1){let c=[];return r.querySelectorAll(t.el).forEach(d=>{let h=Te({},t,{el:d});c.push(new i(h))}),c}let l=this;l.__swiper__=!0,l.support=ss(),l.device=rs({userAgent:t.userAgent}),l.browser=lo(),l.eventsListeners={},l.eventsAnyListeners=[],l.modules=[...l.__modules__],t.modules&&Array.isArray(t.modules)&&l.modules.push(...t.modules);let o={};l.modules.forEach(c=>{c({params:t,swiper:l,extendParams:ml(t,o),on:l.on.bind(l),once:l.once.bind(l),off:l.off.bind(l),emit:l.emit.bind(l)})});let u=Te({},Ki,o);return l.params=Te({},u,Hn,t),l.originalParams=Te({},l.params),l.passedParams=Te({},t),l.params&&l.params.on&&Object.keys(l.params.on).forEach(c=>{l.on(c,l.params.on[c])}),l.params&&l.params.onAny&&l.onAny(l.params.onAny),Object.assign(l,{enabled:l.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal(){return l.params.direction==="horizontal"},isVertical(){return l.params.direction==="vertical"},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:l.params.allowSlideNext,allowSlidePrev:l.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:l.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:l.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),l.emit("_swiper"),l.params.init&&l.init(),l}getDirectionLabel(e){return this.isHorizontal()?e:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[e]}getSlideIndex(e){let{slidesEl:t,params:n}=this,a=de(t,`.${n.slideClass}, swiper-slide`),s=gt(a[0]);return gt(e)-s}getSlideIndexByData(e){return this.getSlideIndex(this.slides.filter(t=>t.getAttribute("data-swiper-slide-index")*1===e)[0])}recalcSlides(){let e=this,{slidesEl:t,params:n}=e;e.slides=de(t,`.${n.slideClass}, swiper-slide`)}enable(){let e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){let e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,t){let n=this;e=Math.min(Math.max(e,0),1);let a=n.minTranslate(),r=(n.maxTranslate()-a)*e+a;n.translateTo(r,typeof t>"u"?0:t),n.updateActiveIndex(),n.updateSlidesClasses()}emitContainerClasses(){let e=this;if(!e.params._emitClasses||!e.el)return;let t=e.el.className.split(" ").filter(n=>n.indexOf("swiper")===0||n.indexOf(e.params.containerModifierClass)===0);e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){let t=this;return t.destroyed?"":e.className.split(" ").filter(n=>n.indexOf("swiper-slide")===0||n.indexOf(t.params.slideClass)===0).join(" ")}emitSlidesClasses(){let e=this;if(!e.params._emitClasses||!e.el)return;let t=[];e.slides.forEach(n=>{let a=e.getSlideClasses(n);t.push({slideEl:n,classNames:a}),e.emit("_slideClass",n,a)}),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){e===void 0&&(e="current"),t===void 0&&(t=!1);let n=this,{params:a,slides:s,slidesGrid:r,slidesSizesGrid:l,size:o,activeIndex:u}=n,c=1;if(typeof a.slidesPerView=="number")return a.slidesPerView;if(a.centeredSlides){let d=s[u]?Math.ceil(s[u].swiperSlideSize):0,h;for(let f=u+1;f<s.length;f+=1)s[f]&&!h&&(d+=Math.ceil(s[f].swiperSlideSize),c+=1,d>o&&(h=!0));for(let f=u-1;f>=0;f-=1)s[f]&&!h&&(d+=s[f].swiperSlideSize,c+=1,d>o&&(h=!0))}else if(e==="current")for(let d=u+1;d<s.length;d+=1)(t?r[d]+l[d]-r[u]<o:r[d]-r[u]<o)&&(c+=1);else for(let d=u-1;d>=0;d-=1)r[u]-r[d]<o&&(c+=1);return c}update(){let e=this;if(!e||e.destroyed)return;let{snapGrid:t,params:n}=e;n.breakpoints&&e.setBreakpoint(),[...e.el.querySelectorAll('[loading="lazy"]')].forEach(r=>{r.complete&&Xi(e,r)}),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses();function a(){let r=e.rtlTranslate?e.translate*-1:e.translate,l=Math.min(Math.max(r,e.maxTranslate()),e.minTranslate());e.setTranslate(l),e.updateActiveIndex(),e.updateSlidesClasses()}let s;if(n.freeMode&&n.freeMode.enabled&&!n.cssMode)a(),n.autoHeight&&e.updateAutoHeight();else{if((n.slidesPerView==="auto"||n.slidesPerView>1)&&e.isEnd&&!n.centeredSlides){let r=e.virtual&&n.virtual.enabled?e.virtual.slides:e.slides;s=e.slideTo(r.length-1,0,!1,!0)}else s=e.slideTo(e.activeIndex,0,!1,!0);s||a()}n.watchOverflow&&t!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,t){t===void 0&&(t=!0);let n=this,a=n.params.direction;return e||(e=a==="horizontal"?"vertical":"horizontal"),e===a||e!=="horizontal"&&e!=="vertical"||(n.el.classList.remove(`${n.params.containerModifierClass}${a}`),n.el.classList.add(`${n.params.containerModifierClass}${e}`),n.emitContainerClasses(),n.params.direction=e,n.slides.forEach(s=>{e==="vertical"?s.style.width="":s.style.height=""}),n.emit("changeDirection"),t&&n.update()),n}changeLanguageDirection(e){let t=this;t.rtl&&e==="rtl"||!t.rtl&&e==="ltr"||(t.rtl=e==="rtl",t.rtlTranslate=t.params.direction==="horizontal"&&t.rtl,t.rtl?(t.el.classList.add(`${t.params.containerModifierClass}rtl`),t.el.dir="rtl"):(t.el.classList.remove(`${t.params.containerModifierClass}rtl`),t.el.dir="ltr"),t.update())}mount(e){let t=this;if(t.mounted)return!0;let n=e||t.params.el;if(typeof n=="string"&&(n=document.querySelector(n)),!n)return!1;n.swiper=t,n.parentNode&&n.parentNode.host&&n.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);let a=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`,r=n&&n.shadowRoot&&n.shadowRoot.querySelector?n.shadowRoot.querySelector(a()):de(n,a())[0];return!r&&t.params.createElements&&(r=he("div",t.params.wrapperClass),n.append(r),de(n,`.${t.params.slideClass}`).forEach(l=>{r.append(l)})),Object.assign(t,{el:n,wrapperEl:r,slidesEl:t.isElement&&!n.parentNode.host.slideSlots?n.parentNode.host:r,hostEl:t.isElement?n.parentNode.host:n,mounted:!0,rtl:n.dir.toLowerCase()==="rtl"||at(n,"direction")==="rtl",rtlTranslate:t.params.direction==="horizontal"&&(n.dir.toLowerCase()==="rtl"||at(n,"direction")==="rtl"),wrongRTL:at(r,"display")==="-webkit-box"}),!0}init(e){let t=this;if(t.initialized||t.mount(e)===!1)return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(),t.attachEvents();let a=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&a.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),a.forEach(s=>{s.complete?Xi(t,s):s.addEventListener("load",r=>{Xi(t,r.target)})}),Rn(t),t.initialized=!0,Rn(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){e===void 0&&(e=!0),t===void 0&&(t=!0);let n=this,{params:a,el:s,wrapperEl:r,slides:l}=n;return typeof n.params>"u"||n.destroyed||(n.emit("beforeDestroy"),n.initialized=!1,n.detachEvents(),a.loop&&n.loopDestroy(),t&&(n.removeClasses(),s&&typeof s!="string"&&s.removeAttribute("style"),r&&r.removeAttribute("style"),l&&l.length&&l.forEach(o=>{o.classList.remove(a.slideVisibleClass,a.slideFullyVisibleClass,a.slideActiveClass,a.slideNextClass,a.slidePrevClass),o.removeAttribute("style"),o.removeAttribute("data-swiper-slide-index")})),n.emit("destroy"),Object.keys(n.eventsListeners).forEach(o=>{n.off(o)}),e!==!1&&(n.el&&typeof n.el!="string"&&(n.el.swiper=null),Qa(n)),n.destroyed=!0),null}static extendDefaults(e){Te(Hn,e)}static get extendedDefaults(){return Hn}static get defaults(){return Ki}static installModule(e){i.prototype.__modules__||(i.prototype.__modules__=[]);let t=i.prototype.__modules__;typeof e=="function"&&t.indexOf(e)<0&&t.push(e)}static use(e){return Array.isArray(e)?(e.forEach(t=>i.installModule(t)),i):(i.installModule(e),i)}};Object.keys(Ln).forEach(i=>{Object.keys(Ln[i]).forEach(e=>{wt.prototype[e]=Ln[i][e]})});wt.use([co,uo]);function cs(i){let{swiper:e,extendParams:t,on:n,emit:a}=i;t({virtual:{enabled:!1,slides:[],cache:!0,renderSlide:null,renderExternal:null,renderExternalUpdate:!0,addSlidesBefore:0,addSlidesAfter:0}});let s,r=le();e.virtual={cache:{},from:void 0,to:void 0,slides:[],offset:0,slidesGrid:[]};let l=r.createElement("div");function o(m,_){let w=e.params.virtual;if(w.cache&&e.virtual.cache[_])return e.virtual.cache[_];let b;return w.renderSlide?(b=w.renderSlide.call(e,m,_),typeof b=="string"&&(l.innerHTML=b,b=l.children[0])):e.isElement?b=he("swiper-slide"):b=he("div",e.params.slideClass),b.setAttribute("data-swiper-slide-index",_),w.renderSlide||(b.innerHTML=m),w.cache&&(e.virtual.cache[_]=b),b}function u(m,_){let{slidesPerView:w,slidesPerGroup:b,centeredSlides:v,loop:g,initialSlide:y}=e.params;if(_&&!g&&y>0)return;let{addSlidesBefore:E,addSlidesAfter:N}=e.params.virtual,{from:L,to:D,slides:S,slidesGrid:C,offset:P}=e.virtual;e.params.cssMode||e.updateActiveIndex();let A=e.activeIndex||0,T;e.rtlTranslate?T="right":T=e.isHorizontal()?"left":"top";let I,O;v?(I=Math.floor(w/2)+b+N,O=Math.floor(w/2)+b+E):(I=w+(b-1)+N,O=(g?w:b)+E);let V=A-O,k=A+I;g||(V=Math.max(V,0),k=Math.min(k,S.length-1));let M=(e.slidesGrid[V]||0)-(e.slidesGrid[0]||0);g&&A>=O?(V-=O,v||(M+=e.slidesGrid[0])):g&&A<O&&(V=-O,v&&(M+=e.slidesGrid[0])),Object.assign(e.virtual,{from:V,to:k,offset:M,slidesGrid:e.slidesGrid,slidesBefore:O,slidesAfter:I});function G(){e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),a("virtualUpdate")}if(L===V&&D===k&&!m){e.slidesGrid!==C&&M!==P&&e.slides.forEach(j=>{j.style[T]=`${M-Math.abs(e.cssOverflowAdjustment())}px`}),e.updateProgress(),a("virtualUpdate");return}if(e.params.virtual.renderExternal){e.params.virtual.renderExternal.call(e,{offset:M,from:V,to:k,slides:function(){let ee=[];for(let fe=V;fe<=k;fe+=1)ee.push(S[fe]);return ee}()}),e.params.virtual.renderExternalUpdate?G():a("virtualUpdate");return}let K=[],B=[],F=j=>{let ee=j;return j<0?ee=S.length+j:ee>=S.length&&(ee=ee-S.length),ee};if(m)e.slides.filter(j=>j.matches(`.${e.params.slideClass}, swiper-slide`)).forEach(j=>{j.remove()});else for(let j=L;j<=D;j+=1)if(j<V||j>k){let ee=F(j);e.slides.filter(fe=>fe.matches(`.${e.params.slideClass}[data-swiper-slide-index="${ee}"], swiper-slide[data-swiper-slide-index="${ee}"]`)).forEach(fe=>{fe.remove()})}let U=g?-S.length:0,ae=g?S.length*2:S.length;for(let j=U;j<ae;j+=1)if(j>=V&&j<=k){let ee=F(j);typeof D>"u"||m?B.push(ee):(j>D&&B.push(ee),j<L&&K.push(ee))}if(B.forEach(j=>{e.slidesEl.append(o(S[j],j))}),g)for(let j=K.length-1;j>=0;j-=1){let ee=K[j];e.slidesEl.prepend(o(S[ee],ee))}else K.sort((j,ee)=>ee-j),K.forEach(j=>{e.slidesEl.prepend(o(S[j],j))});de(e.slidesEl,".swiper-slide, swiper-slide").forEach(j=>{j.style[T]=`${M-Math.abs(e.cssOverflowAdjustment())}px`}),G()}function c(m){if(typeof m=="object"&&"length"in m)for(let _=0;_<m.length;_+=1)m[_]&&e.virtual.slides.push(m[_]);else e.virtual.slides.push(m);u(!0)}function d(m){let _=e.activeIndex,w=_+1,b=1;if(Array.isArray(m)){for(let v=0;v<m.length;v+=1)m[v]&&e.virtual.slides.unshift(m[v]);w=_+m.length,b=m.length}else e.virtual.slides.unshift(m);if(e.params.virtual.cache){let v=e.virtual.cache,g={};Object.keys(v).forEach(y=>{let E=v[y],N=E.getAttribute("data-swiper-slide-index");N&&E.setAttribute("data-swiper-slide-index",parseInt(N,10)+b),g[parseInt(y,10)+b]=E}),e.virtual.cache=g}u(!0),e.slideTo(w,0)}function h(m){if(typeof m>"u"||m===null)return;let _=e.activeIndex;if(Array.isArray(m))for(let w=m.length-1;w>=0;w-=1)e.params.virtual.cache&&(delete e.virtual.cache[m[w]],Object.keys(e.virtual.cache).forEach(b=>{b>m&&(e.virtual.cache[b-1]=e.virtual.cache[b],e.virtual.cache[b-1].setAttribute("data-swiper-slide-index",b-1),delete e.virtual.cache[b])})),e.virtual.slides.splice(m[w],1),m[w]<_&&(_-=1),_=Math.max(_,0);else e.params.virtual.cache&&(delete e.virtual.cache[m],Object.keys(e.virtual.cache).forEach(w=>{w>m&&(e.virtual.cache[w-1]=e.virtual.cache[w],e.virtual.cache[w-1].setAttribute("data-swiper-slide-index",w-1),delete e.virtual.cache[w])})),e.virtual.slides.splice(m,1),m<_&&(_-=1),_=Math.max(_,0);u(!0),e.slideTo(_,0)}function f(){e.virtual.slides=[],e.params.virtual.cache&&(e.virtual.cache={}),u(!0),e.slideTo(0,0)}n("beforeInit",()=>{if(!e.params.virtual.enabled)return;let m;if(typeof e.passedParams.virtual.slides>"u"){let _=[...e.slidesEl.children].filter(w=>w.matches(`.${e.params.slideClass}, swiper-slide`));_&&_.length&&(e.virtual.slides=[..._],m=!0,_.forEach((w,b)=>{w.setAttribute("data-swiper-slide-index",b),e.virtual.cache[b]=w,w.remove()}))}m||(e.virtual.slides=e.params.virtual.slides),e.classNames.push(`${e.params.containerModifierClass}virtual`),e.params.watchSlidesProgress=!0,e.originalParams.watchSlidesProgress=!0,u(!1,!0)}),n("setTranslate",()=>{e.params.virtual.enabled&&(e.params.cssMode&&!e._immediateVirtual?(clearTimeout(s),s=setTimeout(()=>{u()},100)):u())}),n("init update resize",()=>{e.params.virtual.enabled&&e.params.cssMode&&Mt(e.wrapperEl,"--swiper-virtual-size",`${e.virtualSize}px`)}),Object.assign(e.virtual,{appendSlide:c,prependSlide:d,removeSlide:h,removeAllSlides:f,update:u})}function ds(i){let{swiper:e,extendParams:t,on:n,emit:a}=i,s=le(),r=ne();e.keyboard={enabled:!1},t({keyboard:{enabled:!1,onlyInViewport:!0,pageUpDown:!0}});function l(c){if(!e.enabled)return;let{rtlTranslate:d}=e,h=c;h.originalEvent&&(h=h.originalEvent);let f=h.keyCode||h.charCode,m=e.params.keyboard.pageUpDown,_=m&&f===33,w=m&&f===34,b=f===37,v=f===39,g=f===38,y=f===40;if(!e.allowSlideNext&&(e.isHorizontal()&&v||e.isVertical()&&y||w)||!e.allowSlidePrev&&(e.isHorizontal()&&b||e.isVertical()&&g||_))return!1;if(!(h.shiftKey||h.altKey||h.ctrlKey||h.metaKey)&&!(s.activeElement&&s.activeElement.nodeName&&(s.activeElement.nodeName.toLowerCase()==="input"||s.activeElement.nodeName.toLowerCase()==="textarea"))){if(e.params.keyboard.onlyInViewport&&(_||w||b||v||g||y)){let E=!1;if(je(e.el,`.${e.params.slideClass}, swiper-slide`).length>0&&je(e.el,`.${e.params.slideActiveClass}`).length===0)return;let N=e.el,L=N.clientWidth,D=N.clientHeight,S=r.innerWidth,C=r.innerHeight,P=Pt(N);d&&(P.left-=N.scrollLeft);let A=[[P.left,P.top],[P.left+L,P.top],[P.left,P.top+D],[P.left+L,P.top+D]];for(let T=0;T<A.length;T+=1){let I=A[T];if(I[0]>=0&&I[0]<=S&&I[1]>=0&&I[1]<=C){if(I[0]===0&&I[1]===0)continue;E=!0}}if(!E)return}e.isHorizontal()?((_||w||b||v)&&(h.preventDefault?h.preventDefault():h.returnValue=!1),((w||v)&&!d||(_||b)&&d)&&e.slideNext(),((_||b)&&!d||(w||v)&&d)&&e.slidePrev()):((_||w||g||y)&&(h.preventDefault?h.preventDefault():h.returnValue=!1),(w||y)&&e.slideNext(),(_||g)&&e.slidePrev()),a("keyPress",f)}}function o(){e.keyboard.enabled||(s.addEventListener("keydown",l),e.keyboard.enabled=!0)}function u(){e.keyboard.enabled&&(s.removeEventListener("keydown",l),e.keyboard.enabled=!1)}n("init",()=>{e.params.keyboard.enabled&&o()}),n("destroy",()=>{e.keyboard.enabled&&u()}),Object.assign(e.keyboard,{enable:o,disable:u})}function us(i){let{swiper:e,extendParams:t,on:n,emit:a}=i,s=ne();t({mousewheel:{enabled:!1,releaseOnEdges:!1,invert:!1,forceToAxis:!1,sensitivity:1,eventsTarget:"container",thresholdDelta:null,thresholdTime:null,noMousewheelClass:"swiper-no-mousewheel"}}),e.mousewheel={enabled:!1};let r,l=we(),o,u=[];function c(g){let L=0,D=0,S=0,C=0;return"detail"in g&&(D=g.detail),"wheelDelta"in g&&(D=-g.wheelDelta/120),"wheelDeltaY"in g&&(D=-g.wheelDeltaY/120),"wheelDeltaX"in g&&(L=-g.wheelDeltaX/120),"axis"in g&&g.axis===g.HORIZONTAL_AXIS&&(L=D,D=0),S=L*10,C=D*10,"deltaY"in g&&(C=g.deltaY),"deltaX"in g&&(S=g.deltaX),g.shiftKey&&!S&&(S=C,C=0),(S||C)&&g.deltaMode&&(g.deltaMode===1?(S*=40,C*=40):(S*=800,C*=800)),S&&!L&&(L=S<1?-1:1),C&&!D&&(D=C<1?-1:1),{spinX:L,spinY:D,pixelX:S,pixelY:C}}function d(){e.enabled&&(e.mouseEntered=!0)}function h(){e.enabled&&(e.mouseEntered=!1)}function f(g){return e.params.mousewheel.thresholdDelta&&g.delta<e.params.mousewheel.thresholdDelta||e.params.mousewheel.thresholdTime&&we()-l<e.params.mousewheel.thresholdTime?!1:g.delta>=6&&we()-l<60?!0:(g.direction<0?(!e.isEnd||e.params.loop)&&!e.animating&&(e.slideNext(),a("scroll",g.raw)):(!e.isBeginning||e.params.loop)&&!e.animating&&(e.slidePrev(),a("scroll",g.raw)),l=new s.Date().getTime(),!1)}function m(g){let y=e.params.mousewheel;if(g.direction<0){if(e.isEnd&&!e.params.loop&&y.releaseOnEdges)return!0}else if(e.isBeginning&&!e.params.loop&&y.releaseOnEdges)return!0;return!1}function _(g){let y=g,E=!0;if(!e.enabled||g.target.closest(`.${e.params.mousewheel.noMousewheelClass}`))return;let N=e.params.mousewheel;e.params.cssMode&&y.preventDefault();let L=e.el;e.params.mousewheel.eventsTarget!=="container"&&(L=document.querySelector(e.params.mousewheel.eventsTarget));let D=L&&L.contains(y.target);if(!e.mouseEntered&&!D&&!N.releaseOnEdges)return!0;y.originalEvent&&(y=y.originalEvent);let S=0,C=e.rtlTranslate?-1:1,P=c(y);if(N.forceToAxis)if(e.isHorizontal())if(Math.abs(P.pixelX)>Math.abs(P.pixelY))S=-P.pixelX*C;else return!0;else if(Math.abs(P.pixelY)>Math.abs(P.pixelX))S=-P.pixelY;else return!0;else S=Math.abs(P.pixelX)>Math.abs(P.pixelY)?-P.pixelX*C:-P.pixelY;if(S===0)return!0;N.invert&&(S=-S);let A=e.getTranslate()+S*N.sensitivity;if(A>=e.minTranslate()&&(A=e.minTranslate()),A<=e.maxTranslate()&&(A=e.maxTranslate()),E=e.params.loop?!0:!(A===e.minTranslate()||A===e.maxTranslate()),E&&e.params.nested&&y.stopPropagation(),!e.params.freeMode||!e.params.freeMode.enabled){let T={time:we(),delta:Math.abs(S),direction:Math.sign(S),raw:g};u.length>=2&&u.shift();let I=u.length?u[u.length-1]:void 0;if(u.push(T),I?(T.direction!==I.direction||T.delta>I.delta||T.time>I.time+150)&&f(T):f(T),m(T))return!0}else{let T={time:we(),delta:Math.abs(S),direction:Math.sign(S)},I=o&&T.time<o.time+500&&T.delta<=o.delta&&T.direction===o.direction;if(!I){o=void 0;let O=e.getTranslate()+S*N.sensitivity,V=e.isBeginning,k=e.isEnd;if(O>=e.minTranslate()&&(O=e.minTranslate()),O<=e.maxTranslate()&&(O=e.maxTranslate()),e.setTransition(0),e.setTranslate(O),e.updateProgress(),e.updateActiveIndex(),e.updateSlidesClasses(),(!V&&e.isBeginning||!k&&e.isEnd)&&e.updateSlidesClasses(),e.params.loop&&e.loopFix({direction:T.direction<0?"next":"prev",byMousewheel:!0}),e.params.freeMode.sticky){clearTimeout(r),r=void 0,u.length>=15&&u.shift();let M=u.length?u[u.length-1]:void 0,G=u[0];if(u.push(T),M&&(T.delta>M.delta||T.direction!==M.direction))u.splice(0);else if(u.length>=15&&T.time-G.time<500&&G.delta-T.delta>=1&&T.delta<=6){let K=S>0?.8:.2;o=T,u.splice(0),r=Ye(()=>{e.destroyed||!e.params||e.slideToClosest(e.params.speed,!0,void 0,K)},0)}r||(r=Ye(()=>{if(e.destroyed||!e.params)return;let K=.5;o=T,u.splice(0),e.slideToClosest(e.params.speed,!0,void 0,K)},500))}if(I||a("scroll",y),e.params.autoplay&&e.params.autoplayDisableOnInteraction&&e.autoplay.stop(),N.releaseOnEdges&&(O===e.minTranslate()||O===e.maxTranslate()))return!0}}return y.preventDefault?y.preventDefault():y.returnValue=!1,!1}function w(g){let y=e.el;e.params.mousewheel.eventsTarget!=="container"&&(y=document.querySelector(e.params.mousewheel.eventsTarget)),y[g]("mouseenter",d),y[g]("mouseleave",h),y[g]("wheel",_)}function b(){return e.params.cssMode?(e.wrapperEl.removeEventListener("wheel",_),!0):e.mousewheel.enabled?!1:(w("addEventListener"),e.mousewheel.enabled=!0,!0)}function v(){return e.params.cssMode?(e.wrapperEl.addEventListener(event,_),!0):e.mousewheel.enabled?(w("removeEventListener"),e.mousewheel.enabled=!1,!0):!1}n("init",()=>{!e.params.mousewheel.enabled&&e.params.cssMode&&v(),e.params.mousewheel.enabled&&b()}),n("destroy",()=>{e.params.cssMode&&b(),e.mousewheel.enabled&&v()}),Object.assign(e.mousewheel,{enable:b,disable:v})}function Yt(i,e,t,n){return i.params.createElements&&Object.keys(n).forEach(a=>{if(!t[a]&&t.auto===!0){let s=de(i.el,`.${n[a]}`)[0];s||(s=he("div",n[a]),s.className=n[a],i.el.append(s)),t[a]=s,e[a]=s}}),t}function ps(i){let{swiper:e,extendParams:t,on:n,emit:a}=i;t({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),e.navigation={nextEl:null,prevEl:null};function s(m){let _;return m&&typeof m=="string"&&e.isElement&&(_=e.el.querySelector(m),_)?_:(m&&(typeof m=="string"&&(_=[...document.querySelectorAll(m)]),e.params.uniqueNavElements&&typeof m=="string"&&_&&_.length>1&&e.el.querySelectorAll(m).length===1?_=e.el.querySelector(m):_&&_.length===1&&(_=_[0])),m&&!_?m:_)}function r(m,_){let w=e.params.navigation;m=Z(m),m.forEach(b=>{b&&(b.classList[_?"add":"remove"](...w.disabledClass.split(" ")),b.tagName==="BUTTON"&&(b.disabled=_),e.params.watchOverflow&&e.enabled&&b.classList[e.isLocked?"add":"remove"](w.lockClass))})}function l(){let{nextEl:m,prevEl:_}=e.navigation;if(e.params.loop){r(_,!1),r(m,!1);return}r(_,e.isBeginning&&!e.params.rewind),r(m,e.isEnd&&!e.params.rewind)}function o(m){m.preventDefault(),!(e.isBeginning&&!e.params.loop&&!e.params.rewind)&&(e.slidePrev(),a("navigationPrev"))}function u(m){m.preventDefault(),!(e.isEnd&&!e.params.loop&&!e.params.rewind)&&(e.slideNext(),a("navigationNext"))}function c(){let m=e.params.navigation;if(e.params.navigation=Yt(e,e.originalParams.navigation,e.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!(m.nextEl||m.prevEl))return;let _=s(m.nextEl),w=s(m.prevEl);Object.assign(e.navigation,{nextEl:_,prevEl:w}),_=Z(_),w=Z(w);let b=(v,g)=>{v&&v.addEventListener("click",g==="next"?u:o),!e.enabled&&v&&v.classList.add(...m.lockClass.split(" "))};_.forEach(v=>b(v,"next")),w.forEach(v=>b(v,"prev"))}function d(){let{nextEl:m,prevEl:_}=e.navigation;m=Z(m),_=Z(_);let w=(b,v)=>{b.removeEventListener("click",v==="next"?u:o),b.classList.remove(...e.params.navigation.disabledClass.split(" "))};m.forEach(b=>w(b,"next")),_.forEach(b=>w(b,"prev"))}n("init",()=>{e.params.navigation.enabled===!1?f():(c(),l())}),n("toEdge fromEdge lock unlock",()=>{l()}),n("destroy",()=>{d()}),n("enable disable",()=>{let{nextEl:m,prevEl:_}=e.navigation;if(m=Z(m),_=Z(_),e.enabled){l();return}[...m,..._].filter(w=>!!w).forEach(w=>w.classList.add(e.params.navigation.lockClass))}),n("click",(m,_)=>{let{nextEl:w,prevEl:b}=e.navigation;w=Z(w),b=Z(b);let v=_.target,g=b.includes(v)||w.includes(v);if(e.isElement&&!g){let y=_.path||_.composedPath&&_.composedPath();y&&(g=y.find(E=>w.includes(E)||b.includes(E)))}if(e.params.navigation.hideOnClick&&!g){if(e.pagination&&e.params.pagination&&e.params.pagination.clickable&&(e.pagination.el===v||e.pagination.el.contains(v)))return;let y;w.length?y=w[0].classList.contains(e.params.navigation.hiddenClass):b.length&&(y=b[0].classList.contains(e.params.navigation.hiddenClass)),a(y===!0?"navigationShow":"navigationHide"),[...w,...b].filter(E=>!!E).forEach(E=>E.classList.toggle(e.params.navigation.hiddenClass))}});let h=()=>{e.el.classList.remove(...e.params.navigation.navigationDisabledClass.split(" ")),c(),l()},f=()=>{e.el.classList.add(...e.params.navigation.navigationDisabledClass.split(" ")),d()};Object.assign(e.navigation,{enable:h,disable:f,update:l,init:c,destroy:d})}function Le(i){return i===void 0&&(i=""),`.${i.trim().replace(/([\.:!+\/])/g,"\\$1").replace(/ /g,".")}`}function hs(i){let{swiper:e,extendParams:t,on:n,emit:a}=i,s="swiper-pagination";t({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:v=>v,formatFractionTotal:v=>v,bulletClass:`${s}-bullet`,bulletActiveClass:`${s}-bullet-active`,modifierClass:`${s}-`,currentClass:`${s}-current`,totalClass:`${s}-total`,hiddenClass:`${s}-hidden`,progressbarFillClass:`${s}-progressbar-fill`,progressbarOppositeClass:`${s}-progressbar-opposite`,clickableClass:`${s}-clickable`,lockClass:`${s}-lock`,horizontalClass:`${s}-horizontal`,verticalClass:`${s}-vertical`,paginationDisabledClass:`${s}-disabled`}}),e.pagination={el:null,bullets:[]};let r,l=0;function o(){return!e.params.pagination.el||!e.pagination.el||Array.isArray(e.pagination.el)&&e.pagination.el.length===0}function u(v,g){let{bulletActiveClass:y}=e.params.pagination;v&&(v=v[`${g==="prev"?"previous":"next"}ElementSibling`],v&&(v.classList.add(`${y}-${g}`),v=v[`${g==="prev"?"previous":"next"}ElementSibling`],v&&v.classList.add(`${y}-${g}-${g}`)))}function c(v,g,y){if(v=v%y,g=g%y,g===v+1)return"next";if(g===v-1)return"previous"}function d(v){let g=v.target.closest(Le(e.params.pagination.bulletClass));if(!g)return;v.preventDefault();let y=gt(g)*e.params.slidesPerGroup;if(e.params.loop){if(e.realIndex===y)return;let E=c(e.realIndex,y,e.slides.length);E==="next"?e.slideNext():E==="previous"?e.slidePrev():e.slideToLoop(y)}else e.slideTo(y)}function h(){let v=e.rtl,g=e.params.pagination;if(o())return;let y=e.pagination.el;y=Z(y);let E,N,L=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.slides.length,D=e.params.loop?Math.ceil(L/e.params.slidesPerGroup):e.snapGrid.length;if(e.params.loop?(N=e.previousRealIndex||0,E=e.params.slidesPerGroup>1?Math.floor(e.realIndex/e.params.slidesPerGroup):e.realIndex):typeof e.snapIndex<"u"?(E=e.snapIndex,N=e.previousSnapIndex):(N=e.previousIndex||0,E=e.activeIndex||0),g.type==="bullets"&&e.pagination.bullets&&e.pagination.bullets.length>0){let S=e.pagination.bullets,C,P,A;if(g.dynamicBullets&&(r=fi(S[0],e.isHorizontal()?"width":"height",!0),y.forEach(T=>{T.style[e.isHorizontal()?"width":"height"]=`${r*(g.dynamicMainBullets+4)}px`}),g.dynamicMainBullets>1&&N!==void 0&&(l+=E-(N||0),l>g.dynamicMainBullets-1?l=g.dynamicMainBullets-1:l<0&&(l=0)),C=Math.max(E-l,0),P=C+(Math.min(S.length,g.dynamicMainBullets)-1),A=(P+C)/2),S.forEach(T=>{let I=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(O=>`${g.bulletActiveClass}${O}`)].map(O=>typeof O=="string"&&O.includes(" ")?O.split(" "):O).flat();T.classList.remove(...I)}),y.length>1)S.forEach(T=>{let I=gt(T);I===E?T.classList.add(...g.bulletActiveClass.split(" ")):e.isElement&&T.setAttribute("part","bullet"),g.dynamicBullets&&(I>=C&&I<=P&&T.classList.add(...`${g.bulletActiveClass}-main`.split(" ")),I===C&&u(T,"prev"),I===P&&u(T,"next"))});else{let T=S[E];if(T&&T.classList.add(...g.bulletActiveClass.split(" ")),e.isElement&&S.forEach((I,O)=>{I.setAttribute("part",O===E?"bullet-active":"bullet")}),g.dynamicBullets){let I=S[C],O=S[P];for(let V=C;V<=P;V+=1)S[V]&&S[V].classList.add(...`${g.bulletActiveClass}-main`.split(" "));u(I,"prev"),u(O,"next")}}if(g.dynamicBullets){let T=Math.min(S.length,g.dynamicMainBullets+4),I=(r*T-r)/2-A*r,O=v?"right":"left";S.forEach(V=>{V.style[e.isHorizontal()?O:"top"]=`${I}px`})}}y.forEach((S,C)=>{if(g.type==="fraction"&&(S.querySelectorAll(Le(g.currentClass)).forEach(P=>{P.textContent=g.formatFractionCurrent(E+1)}),S.querySelectorAll(Le(g.totalClass)).forEach(P=>{P.textContent=g.formatFractionTotal(D)})),g.type==="progressbar"){let P;g.progressbarOpposite?P=e.isHorizontal()?"vertical":"horizontal":P=e.isHorizontal()?"horizontal":"vertical";let A=(E+1)/D,T=1,I=1;P==="horizontal"?T=A:I=A,S.querySelectorAll(Le(g.progressbarFillClass)).forEach(O=>{O.style.transform=`translate3d(0,0,0) scaleX(${T}) scaleY(${I})`,O.style.transitionDuration=`${e.params.speed}ms`})}g.type==="custom"&&g.renderCustom?(S.innerHTML=g.renderCustom(e,E+1,D),C===0&&a("paginationRender",S)):(C===0&&a("paginationRender",S),a("paginationUpdate",S)),e.params.watchOverflow&&e.enabled&&S.classList[e.isLocked?"add":"remove"](g.lockClass)})}function f(){let v=e.params.pagination;if(o())return;let g=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.grid&&e.params.grid.rows>1?e.slides.length/Math.ceil(e.params.grid.rows):e.slides.length,y=e.pagination.el;y=Z(y);let E="";if(v.type==="bullets"){let N=e.params.loop?Math.ceil(g/e.params.slidesPerGroup):e.snapGrid.length;e.params.freeMode&&e.params.freeMode.enabled&&N>g&&(N=g);for(let L=0;L<N;L+=1)v.renderBullet?E+=v.renderBullet.call(e,L,v.bulletClass):E+=`<${v.bulletElement} ${e.isElement?'part="bullet"':""} class="${v.bulletClass}"></${v.bulletElement}>`}v.type==="fraction"&&(v.renderFraction?E=v.renderFraction.call(e,v.currentClass,v.totalClass):E=`<span class="${v.currentClass}"></span> / <span class="${v.totalClass}"></span>`),v.type==="progressbar"&&(v.renderProgressbar?E=v.renderProgressbar.call(e,v.progressbarFillClass):E=`<span class="${v.progressbarFillClass}"></span>`),e.pagination.bullets=[],y.forEach(N=>{v.type!=="custom"&&(N.innerHTML=E||""),v.type==="bullets"&&e.pagination.bullets.push(...N.querySelectorAll(Le(v.bulletClass)))}),v.type!=="custom"&&a("paginationRender",y[0])}function m(){e.params.pagination=Yt(e,e.originalParams.pagination,e.params.pagination,{el:"swiper-pagination"});let v=e.params.pagination;if(!v.el)return;let g;typeof v.el=="string"&&e.isElement&&(g=e.el.querySelector(v.el)),!g&&typeof v.el=="string"&&(g=[...document.querySelectorAll(v.el)]),g||(g=v.el),!(!g||g.length===0)&&(e.params.uniqueNavElements&&typeof v.el=="string"&&Array.isArray(g)&&g.length>1&&(g=[...e.el.querySelectorAll(v.el)],g.length>1&&(g=g.filter(y=>je(y,".swiper")[0]===e.el)[0])),Array.isArray(g)&&g.length===1&&(g=g[0]),Object.assign(e.pagination,{el:g}),g=Z(g),g.forEach(y=>{v.type==="bullets"&&v.clickable&&y.classList.add(...(v.clickableClass||"").split(" ")),y.classList.add(v.modifierClass+v.type),y.classList.add(e.isHorizontal()?v.horizontalClass:v.verticalClass),v.type==="bullets"&&v.dynamicBullets&&(y.classList.add(`${v.modifierClass}${v.type}-dynamic`),l=0,v.dynamicMainBullets<1&&(v.dynamicMainBullets=1)),v.type==="progressbar"&&v.progressbarOpposite&&y.classList.add(v.progressbarOppositeClass),v.clickable&&y.addEventListener("click",d),e.enabled||y.classList.add(v.lockClass)}))}function _(){let v=e.params.pagination;if(o())return;let g=e.pagination.el;g&&(g=Z(g),g.forEach(y=>{y.classList.remove(v.hiddenClass),y.classList.remove(v.modifierClass+v.type),y.classList.remove(e.isHorizontal()?v.horizontalClass:v.verticalClass),v.clickable&&(y.classList.remove(...(v.clickableClass||"").split(" ")),y.removeEventListener("click",d))})),e.pagination.bullets&&e.pagination.bullets.forEach(y=>y.classList.remove(...v.bulletActiveClass.split(" ")))}n("changeDirection",()=>{if(!e.pagination||!e.pagination.el)return;let v=e.params.pagination,{el:g}=e.pagination;g=Z(g),g.forEach(y=>{y.classList.remove(v.horizontalClass,v.verticalClass),y.classList.add(e.isHorizontal()?v.horizontalClass:v.verticalClass)})}),n("init",()=>{e.params.pagination.enabled===!1?b():(m(),f(),h())}),n("activeIndexChange",()=>{typeof e.snapIndex>"u"&&h()}),n("snapIndexChange",()=>{h()}),n("snapGridLengthChange",()=>{f(),h()}),n("destroy",()=>{_()}),n("enable disable",()=>{let{el:v}=e.pagination;v&&(v=Z(v),v.forEach(g=>g.classList[e.enabled?"remove":"add"](e.params.pagination.lockClass)))}),n("lock unlock",()=>{h()}),n("click",(v,g)=>{let y=g.target,E=Z(e.pagination.el);if(e.params.pagination.el&&e.params.pagination.hideOnClick&&E&&E.length>0&&!y.classList.contains(e.params.pagination.bulletClass)){if(e.navigation&&(e.navigation.nextEl&&y===e.navigation.nextEl||e.navigation.prevEl&&y===e.navigation.prevEl))return;let N=E[0].classList.contains(e.params.pagination.hiddenClass);a(N===!0?"paginationShow":"paginationHide"),E.forEach(L=>L.classList.toggle(e.params.pagination.hiddenClass))}});let w=()=>{e.el.classList.remove(e.params.pagination.paginationDisabledClass);let{el:v}=e.pagination;v&&(v=Z(v),v.forEach(g=>g.classList.remove(e.params.pagination.paginationDisabledClass))),m(),f(),h()},b=()=>{e.el.classList.add(e.params.pagination.paginationDisabledClass);let{el:v}=e.pagination;v&&(v=Z(v),v.forEach(g=>g.classList.add(e.params.pagination.paginationDisabledClass))),_()};Object.assign(e.pagination,{enable:w,disable:b,render:f,update:h,init:m,destroy:_})}function fs(i){let{swiper:e,extendParams:t,on:n,emit:a}=i,s=le(),r=!1,l=null,o=null,u,c,d,h;t({scrollbar:{el:null,dragSize:"auto",hide:!1,draggable:!1,snapOnRelease:!0,lockClass:"swiper-scrollbar-lock",dragClass:"swiper-scrollbar-drag",scrollbarDisabledClass:"swiper-scrollbar-disabled",horizontalClass:"swiper-scrollbar-horizontal",verticalClass:"swiper-scrollbar-vertical"}}),e.scrollbar={el:null,dragEl:null};function f(){if(!e.params.scrollbar.el||!e.scrollbar.el)return;let{scrollbar:A,rtlTranslate:T}=e,{dragEl:I,el:O}=A,V=e.params.scrollbar,k=e.params.loop?e.progressLoop:e.progress,M=c,G=(d-c)*k;T?(G=-G,G>0?(M=c-G,G=0):-G+c>d&&(M=d+G)):G<0?(M=c+G,G=0):G+c>d&&(M=d-G),e.isHorizontal()?(I.style.transform=`translate3d(${G}px, 0, 0)`,I.style.width=`${M}px`):(I.style.transform=`translate3d(0px, ${G}px, 0)`,I.style.height=`${M}px`),V.hide&&(clearTimeout(l),O.style.opacity=1,l=setTimeout(()=>{O.style.opacity=0,O.style.transitionDuration="400ms"},1e3))}function m(A){!e.params.scrollbar.el||!e.scrollbar.el||(e.scrollbar.dragEl.style.transitionDuration=`${A}ms`)}function _(){if(!e.params.scrollbar.el||!e.scrollbar.el)return;let{scrollbar:A}=e,{dragEl:T,el:I}=A;T.style.width="",T.style.height="",d=e.isHorizontal()?I.offsetWidth:I.offsetHeight,h=e.size/(e.virtualSize+e.params.slidesOffsetBefore-(e.params.centeredSlides?e.snapGrid[0]:0)),e.params.scrollbar.dragSize==="auto"?c=d*h:c=parseInt(e.params.scrollbar.dragSize,10),e.isHorizontal()?T.style.width=`${c}px`:T.style.height=`${c}px`,h>=1?I.style.display="none":I.style.display="",e.params.scrollbar.hide&&(I.style.opacity=0),e.params.watchOverflow&&e.enabled&&A.el.classList[e.isLocked?"add":"remove"](e.params.scrollbar.lockClass)}function w(A){return e.isHorizontal()?A.clientX:A.clientY}function b(A){let{scrollbar:T,rtlTranslate:I}=e,{el:O}=T,V;V=(w(A)-Pt(O)[e.isHorizontal()?"left":"top"]-(u!==null?u:c/2))/(d-c),V=Math.max(Math.min(V,1),0),I&&(V=1-V);let k=e.minTranslate()+(e.maxTranslate()-e.minTranslate())*V;e.updateProgress(k),e.setTranslate(k),e.updateActiveIndex(),e.updateSlidesClasses()}function v(A){let T=e.params.scrollbar,{scrollbar:I,wrapperEl:O}=e,{el:V,dragEl:k}=I;r=!0,u=A.target===k?w(A)-A.target.getBoundingClientRect()[e.isHorizontal()?"left":"top"]:null,A.preventDefault(),A.stopPropagation(),O.style.transitionDuration="100ms",k.style.transitionDuration="100ms",b(A),clearTimeout(o),V.style.transitionDuration="0ms",T.hide&&(V.style.opacity=1),e.params.cssMode&&(e.wrapperEl.style["scroll-snap-type"]="none"),a("scrollbarDragStart",A)}function g(A){let{scrollbar:T,wrapperEl:I}=e,{el:O,dragEl:V}=T;r&&(A.preventDefault&&A.cancelable?A.preventDefault():A.returnValue=!1,b(A),I.style.transitionDuration="0ms",O.style.transitionDuration="0ms",V.style.transitionDuration="0ms",a("scrollbarDragMove",A))}function y(A){let T=e.params.scrollbar,{scrollbar:I,wrapperEl:O}=e,{el:V}=I;r&&(r=!1,e.params.cssMode&&(e.wrapperEl.style["scroll-snap-type"]="",O.style.transitionDuration=""),T.hide&&(clearTimeout(o),o=Ye(()=>{V.style.opacity=0,V.style.transitionDuration="400ms"},1e3)),a("scrollbarDragEnd",A),T.snapOnRelease&&e.slideToClosest())}function E(A){let{scrollbar:T,params:I}=e,O=T.el;if(!O)return;let V=O,k=I.passiveListeners?{passive:!1,capture:!1}:!1,M=I.passiveListeners?{passive:!0,capture:!1}:!1;if(!V)return;let G=A==="on"?"addEventListener":"removeEventListener";V[G]("pointerdown",v,k),s[G]("pointermove",g,k),s[G]("pointerup",y,M)}function N(){!e.params.scrollbar.el||!e.scrollbar.el||E("on")}function L(){!e.params.scrollbar.el||!e.scrollbar.el||E("off")}function D(){let{scrollbar:A,el:T}=e;e.params.scrollbar=Yt(e,e.originalParams.scrollbar,e.params.scrollbar,{el:"swiper-scrollbar"});let I=e.params.scrollbar;if(!I.el)return;let O;if(typeof I.el=="string"&&e.isElement&&(O=e.el.querySelector(I.el)),!O&&typeof I.el=="string"){if(O=s.querySelectorAll(I.el),!O.length)return}else O||(O=I.el);e.params.uniqueNavElements&&typeof I.el=="string"&&O.length>1&&T.querySelectorAll(I.el).length===1&&(O=T.querySelector(I.el)),O.length>0&&(O=O[0]),O.classList.add(e.isHorizontal()?I.horizontalClass:I.verticalClass);let V;O&&(V=O.querySelector(Le(e.params.scrollbar.dragClass)),V||(V=he("div",e.params.scrollbar.dragClass),O.append(V))),Object.assign(A,{el:O,dragEl:V}),I.draggable&&N(),O&&O.classList[e.enabled?"remove":"add"](...nt(e.params.scrollbar.lockClass))}function S(){let A=e.params.scrollbar,T=e.scrollbar.el;T&&T.classList.remove(...nt(e.isHorizontal()?A.horizontalClass:A.verticalClass)),L()}n("changeDirection",()=>{if(!e.scrollbar||!e.scrollbar.el)return;let A=e.params.scrollbar,{el:T}=e.scrollbar;T=Z(T),T.forEach(I=>{I.classList.remove(A.horizontalClass,A.verticalClass),I.classList.add(e.isHorizontal()?A.horizontalClass:A.verticalClass)})}),n("init",()=>{e.params.scrollbar.enabled===!1?P():(D(),_(),f())}),n("update resize observerUpdate lock unlock changeDirection",()=>{_()}),n("setTranslate",()=>{f()}),n("setTransition",(A,T)=>{m(T)}),n("enable disable",()=>{let{el:A}=e.scrollbar;A&&A.classList[e.enabled?"remove":"add"](...nt(e.params.scrollbar.lockClass))}),n("destroy",()=>{S()});let C=()=>{e.el.classList.remove(...nt(e.params.scrollbar.scrollbarDisabledClass)),e.scrollbar.el&&e.scrollbar.el.classList.remove(...nt(e.params.scrollbar.scrollbarDisabledClass)),D(),_(),f()},P=()=>{e.el.classList.add(...nt(e.params.scrollbar.scrollbarDisabledClass)),e.scrollbar.el&&e.scrollbar.el.classList.add(...nt(e.params.scrollbar.scrollbarDisabledClass)),S()};Object.assign(e.scrollbar,{enable:C,disable:P,updateSize:_,setTranslate:f,init:D,destroy:S})}function ms(i){let{swiper:e,extendParams:t,on:n}=i;t({parallax:{enabled:!1}});let a="[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]",s=(o,u)=>{let{rtl:c}=e,d=c?-1:1,h=o.getAttribute("data-swiper-parallax")||"0",f=o.getAttribute("data-swiper-parallax-x"),m=o.getAttribute("data-swiper-parallax-y"),_=o.getAttribute("data-swiper-parallax-scale"),w=o.getAttribute("data-swiper-parallax-opacity"),b=o.getAttribute("data-swiper-parallax-rotate");if(f||m?(f=f||"0",m=m||"0"):e.isHorizontal()?(f=h,m="0"):(m=h,f="0"),f.indexOf("%")>=0?f=`${parseInt(f,10)*u*d}%`:f=`${f*u*d}px`,m.indexOf("%")>=0?m=`${parseInt(m,10)*u}%`:m=`${m*u}px`,typeof w<"u"&&w!==null){let g=w-(w-1)*(1-Math.abs(u));o.style.opacity=g}let v=`translate3d(${f}, ${m}, 0px)`;if(typeof _<"u"&&_!==null){let g=_-(_-1)*(1-Math.abs(u));v+=` scale(${g})`}if(b&&typeof b<"u"&&b!==null){let g=b*u*-1;v+=` rotate(${g}deg)`}o.style.transform=v},r=()=>{let{el:o,slides:u,progress:c,snapGrid:d,isElement:h}=e,f=de(o,a);e.isElement&&f.push(...de(e.hostEl,a)),f.forEach(m=>{s(m,c)}),u.forEach((m,_)=>{let w=m.progress;e.params.slidesPerGroup>1&&e.params.slidesPerView!=="auto"&&(w+=Math.ceil(_/2)-c*(d.length-1)),w=Math.min(Math.max(w,-1),1),m.querySelectorAll(`${a}, [data-swiper-parallax-rotate]`).forEach(b=>{s(b,w)})})},l=function(o){o===void 0&&(o=e.params.speed);let{el:u,hostEl:c}=e,d=[...u.querySelectorAll(a)];e.isElement&&d.push(...c.querySelectorAll(a)),d.forEach(h=>{let f=parseInt(h.getAttribute("data-swiper-parallax-duration"),10)||o;o===0&&(f=0),h.style.transitionDuration=`${f}ms`})};n("beforeInit",()=>{e.params.parallax.enabled&&(e.params.watchSlidesProgress=!0,e.originalParams.watchSlidesProgress=!0)}),n("init",()=>{e.params.parallax.enabled&&r()}),n("setTranslate",()=>{e.params.parallax.enabled&&r()}),n("setTransition",(o,u)=>{e.params.parallax.enabled&&l(u)})}function gs(i){let{swiper:e,extendParams:t,on:n,emit:a}=i,s=ne();t({zoom:{enabled:!1,limitToOriginalSize:!1,maxRatio:3,minRatio:1,toggle:!0,containerClass:"swiper-zoom-container",zoomedSlideClass:"swiper-slide-zoomed"}}),e.zoom={enabled:!1};let r=1,l=!1,o,u,c=[],d={originX:0,originY:0,slideEl:void 0,slideWidth:void 0,slideHeight:void 0,imageEl:void 0,imageWrapEl:void 0,maxRatio:3},h={isTouched:void 0,isMoved:void 0,currentX:void 0,currentY:void 0,minX:void 0,minY:void 0,maxX:void 0,maxY:void 0,width:void 0,height:void 0,startX:void 0,startY:void 0,touchesStart:{},touchesCurrent:{}},f={x:void 0,y:void 0,prevPositionX:void 0,prevPositionY:void 0,prevTime:void 0},m=1;Object.defineProperty(e.zoom,"scale",{get(){return m},set(B){if(m!==B){let F=d.imageEl,U=d.slideEl;a("zoomChange",B,F,U)}m=B}});function _(){if(c.length<2)return 1;let B=c[0].pageX,F=c[0].pageY,U=c[1].pageX,ae=c[1].pageY;return Math.sqrt((U-B)**2+(ae-F)**2)}function w(){let B=e.params.zoom,F=d.imageWrapEl.getAttribute("data-swiper-zoom")||B.maxRatio;if(B.limitToOriginalSize&&d.imageEl&&d.imageEl.naturalWidth){let U=d.imageEl.naturalWidth/d.imageEl.offsetWidth;return Math.min(U,F)}return F}function b(){if(c.length<2)return{x:null,y:null};let B=d.imageEl.getBoundingClientRect();return[(c[0].pageX+(c[1].pageX-c[0].pageX)/2-B.x-s.scrollX)/r,(c[0].pageY+(c[1].pageY-c[0].pageY)/2-B.y-s.scrollY)/r]}function v(){return e.isElement?"swiper-slide":`.${e.params.slideClass}`}function g(B){let F=v();return!!(B.target.matches(F)||e.slides.filter(U=>U.contains(B.target)).length>0)}function y(B){let F=`.${e.params.zoom.containerClass}`;return!!(B.target.matches(F)||[...e.hostEl.querySelectorAll(F)].filter(U=>U.contains(B.target)).length>0)}function E(B){if(B.pointerType==="mouse"&&c.splice(0,c.length),!g(B))return;let F=e.params.zoom;if(o=!1,u=!1,c.push(B),!(c.length<2)){if(o=!0,d.scaleStart=_(),!d.slideEl){d.slideEl=B.target.closest(`.${e.params.slideClass}, swiper-slide`),d.slideEl||(d.slideEl=e.slides[e.activeIndex]);let U=d.slideEl.querySelector(`.${F.containerClass}`);if(U&&(U=U.querySelectorAll("picture, img, svg, canvas, .swiper-zoom-target")[0]),d.imageEl=U,U?d.imageWrapEl=je(d.imageEl,`.${F.containerClass}`)[0]:d.imageWrapEl=void 0,!d.imageWrapEl){d.imageEl=void 0;return}d.maxRatio=w()}if(d.imageEl){let[U,ae]=b();d.originX=U,d.originY=ae,d.imageEl.style.transitionDuration="0ms"}l=!0}}function N(B){if(!g(B))return;let F=e.params.zoom,U=e.zoom,ae=c.findIndex(j=>j.pointerId===B.pointerId);ae>=0&&(c[ae]=B),!(c.length<2)&&(u=!0,d.scaleMove=_(),d.imageEl&&(U.scale=d.scaleMove/d.scaleStart*r,U.scale>d.maxRatio&&(U.scale=d.maxRatio-1+(U.scale-d.maxRatio+1)**.5),U.scale<F.minRatio&&(U.scale=F.minRatio+1-(F.minRatio-U.scale+1)**.5),d.imageEl.style.transform=`translate3d(0,0,0) scale(${U.scale})`))}function L(B){if(!g(B)||B.pointerType==="mouse"&&B.type==="pointerout")return;let F=e.params.zoom,U=e.zoom,ae=c.findIndex(j=>j.pointerId===B.pointerId);ae>=0&&c.splice(ae,1),!(!o||!u)&&(o=!1,u=!1,d.imageEl&&(U.scale=Math.max(Math.min(U.scale,d.maxRatio),F.minRatio),d.imageEl.style.transitionDuration=`${e.params.speed}ms`,d.imageEl.style.transform=`translate3d(0,0,0) scale(${U.scale})`,r=U.scale,l=!1,U.scale>1&&d.slideEl?d.slideEl.classList.add(`${F.zoomedSlideClass}`):U.scale<=1&&d.slideEl&&d.slideEl.classList.remove(`${F.zoomedSlideClass}`),U.scale===1&&(d.originX=0,d.originY=0,d.slideEl=void 0)))}let D;function S(){e.touchEventsData.preventTouchMoveFromPointerMove=!1}function C(){clearTimeout(D),e.touchEventsData.preventTouchMoveFromPointerMove=!0,D=setTimeout(()=>{S()})}function P(B){let F=e.device;if(!d.imageEl||h.isTouched)return;F.android&&B.cancelable&&B.preventDefault(),h.isTouched=!0;let U=c.length>0?c[0]:B;h.touchesStart.x=U.pageX,h.touchesStart.y=U.pageY}function A(B){if(!g(B)||!y(B))return;let F=e.zoom;if(!d.imageEl||!h.isTouched||!d.slideEl)return;h.isMoved||(h.width=d.imageEl.offsetWidth||d.imageEl.clientWidth,h.height=d.imageEl.offsetHeight||d.imageEl.clientHeight,h.startX=pi(d.imageWrapEl,"x")||0,h.startY=pi(d.imageWrapEl,"y")||0,d.slideWidth=d.slideEl.offsetWidth,d.slideHeight=d.slideEl.offsetHeight,d.imageWrapEl.style.transitionDuration="0ms");let U=h.width*F.scale,ae=h.height*F.scale;if(h.minX=Math.min(d.slideWidth/2-U/2,0),h.maxX=-h.minX,h.minY=Math.min(d.slideHeight/2-ae/2,0),h.maxY=-h.minY,h.touchesCurrent.x=c.length>0?c[0].pageX:B.pageX,h.touchesCurrent.y=c.length>0?c[0].pageY:B.pageY,Math.max(Math.abs(h.touchesCurrent.x-h.touchesStart.x),Math.abs(h.touchesCurrent.y-h.touchesStart.y))>5&&(e.allowClick=!1),!h.isMoved&&!l){if(e.isHorizontal()&&(Math.floor(h.minX)===Math.floor(h.startX)&&h.touchesCurrent.x<h.touchesStart.x||Math.floor(h.maxX)===Math.floor(h.startX)&&h.touchesCurrent.x>h.touchesStart.x)){h.isTouched=!1,S();return}if(!e.isHorizontal()&&(Math.floor(h.minY)===Math.floor(h.startY)&&h.touchesCurrent.y<h.touchesStart.y||Math.floor(h.maxY)===Math.floor(h.startY)&&h.touchesCurrent.y>h.touchesStart.y)){h.isTouched=!1,S();return}}B.cancelable&&B.preventDefault(),B.stopPropagation(),C(),h.isMoved=!0;let ee=(F.scale-r)/(d.maxRatio-e.params.zoom.minRatio),{originX:fe,originY:We}=d;h.currentX=h.touchesCurrent.x-h.touchesStart.x+h.startX+ee*(h.width-fe*2),h.currentY=h.touchesCurrent.y-h.touchesStart.y+h.startY+ee*(h.height-We*2),h.currentX<h.minX&&(h.currentX=h.minX+1-(h.minX-h.currentX+1)**.8),h.currentX>h.maxX&&(h.currentX=h.maxX-1+(h.currentX-h.maxX+1)**.8),h.currentY<h.minY&&(h.currentY=h.minY+1-(h.minY-h.currentY+1)**.8),h.currentY>h.maxY&&(h.currentY=h.maxY-1+(h.currentY-h.maxY+1)**.8),f.prevPositionX||(f.prevPositionX=h.touchesCurrent.x),f.prevPositionY||(f.prevPositionY=h.touchesCurrent.y),f.prevTime||(f.prevTime=Date.now()),f.x=(h.touchesCurrent.x-f.prevPositionX)/(Date.now()-f.prevTime)/2,f.y=(h.touchesCurrent.y-f.prevPositionY)/(Date.now()-f.prevTime)/2,Math.abs(h.touchesCurrent.x-f.prevPositionX)<2&&(f.x=0),Math.abs(h.touchesCurrent.y-f.prevPositionY)<2&&(f.y=0),f.prevPositionX=h.touchesCurrent.x,f.prevPositionY=h.touchesCurrent.y,f.prevTime=Date.now(),d.imageWrapEl.style.transform=`translate3d(${h.currentX}px, ${h.currentY}px,0)`}function T(){let B=e.zoom;if(!d.imageEl)return;if(!h.isTouched||!h.isMoved){h.isTouched=!1,h.isMoved=!1;return}h.isTouched=!1,h.isMoved=!1;let F=300,U=300,ae=f.x*F,j=h.currentX+ae,ee=f.y*U,fe=h.currentY+ee;f.x!==0&&(F=Math.abs((j-h.currentX)/f.x)),f.y!==0&&(U=Math.abs((fe-h.currentY)/f.y));let We=Math.max(F,U);h.currentX=j,h.currentY=fe;let et=h.width*B.scale,ke=h.height*B.scale;h.minX=Math.min(d.slideWidth/2-et/2,0),h.maxX=-h.minX,h.minY=Math.min(d.slideHeight/2-ke/2,0),h.maxY=-h.minY,h.currentX=Math.max(Math.min(h.currentX,h.maxX),h.minX),h.currentY=Math.max(Math.min(h.currentY,h.maxY),h.minY),d.imageWrapEl.style.transitionDuration=`${We}ms`,d.imageWrapEl.style.transform=`translate3d(${h.currentX}px, ${h.currentY}px,0)`}function I(){let B=e.zoom;d.slideEl&&e.activeIndex!==e.slides.indexOf(d.slideEl)&&(d.imageEl&&(d.imageEl.style.transform="translate3d(0,0,0) scale(1)"),d.imageWrapEl&&(d.imageWrapEl.style.transform="translate3d(0,0,0)"),d.slideEl.classList.remove(`${e.params.zoom.zoomedSlideClass}`),B.scale=1,r=1,d.slideEl=void 0,d.imageEl=void 0,d.imageWrapEl=void 0,d.originX=0,d.originY=0)}function O(B){let F=e.zoom,U=e.params.zoom;if(!d.slideEl){B&&B.target&&(d.slideEl=B.target.closest(`.${e.params.slideClass}, swiper-slide`)),d.slideEl||(e.params.virtual&&e.params.virtual.enabled&&e.virtual?d.slideEl=de(e.slidesEl,`.${e.params.slideActiveClass}`)[0]:d.slideEl=e.slides[e.activeIndex]);let si=d.slideEl.querySelector(`.${U.containerClass}`);si&&(si=si.querySelectorAll("picture, img, svg, canvas, .swiper-zoom-target")[0]),d.imageEl=si,si?d.imageWrapEl=je(d.imageEl,`.${U.containerClass}`)[0]:d.imageWrapEl=void 0}if(!d.imageEl||!d.imageWrapEl)return;e.params.cssMode&&(e.wrapperEl.style.overflow="hidden",e.wrapperEl.style.touchAction="none"),d.slideEl.classList.add(`${U.zoomedSlideClass}`);let ae,j,ee,fe,We,et,ke,Be,Ht,Rt,dn,ia,Ci,ki,un,pn,hn,fn;typeof h.touchesStart.x>"u"&&B?(ae=B.pageX,j=B.pageY):(ae=h.touchesStart.x,j=h.touchesStart.y);let ai=typeof B=="number"?B:null;r===1&&ai&&(ae=void 0,j=void 0,h.touchesStart.x=void 0,h.touchesStart.y=void 0);let na=w();F.scale=ai||na,r=ai||na,B&&!(r===1&&ai)?(hn=d.slideEl.offsetWidth,fn=d.slideEl.offsetHeight,ee=Pt(d.slideEl).left+s.scrollX,fe=Pt(d.slideEl).top+s.scrollY,We=ee+hn/2-ae,et=fe+fn/2-j,Ht=d.imageEl.offsetWidth||d.imageEl.clientWidth,Rt=d.imageEl.offsetHeight||d.imageEl.clientHeight,dn=Ht*F.scale,ia=Rt*F.scale,Ci=Math.min(hn/2-dn/2,0),ki=Math.min(fn/2-ia/2,0),un=-Ci,pn=-ki,ke=We*F.scale,Be=et*F.scale,ke<Ci&&(ke=Ci),ke>un&&(ke=un),Be<ki&&(Be=ki),Be>pn&&(Be=pn)):(ke=0,Be=0),ai&&F.scale===1&&(d.originX=0,d.originY=0),d.imageWrapEl.style.transitionDuration="300ms",d.imageWrapEl.style.transform=`translate3d(${ke}px, ${Be}px,0)`,d.imageEl.style.transitionDuration="300ms",d.imageEl.style.transform=`translate3d(0,0,0) scale(${F.scale})`}function V(){let B=e.zoom,F=e.params.zoom;if(!d.slideEl){e.params.virtual&&e.params.virtual.enabled&&e.virtual?d.slideEl=de(e.slidesEl,`.${e.params.slideActiveClass}`)[0]:d.slideEl=e.slides[e.activeIndex];let U=d.slideEl.querySelector(`.${F.containerClass}`);U&&(U=U.querySelectorAll("picture, img, svg, canvas, .swiper-zoom-target")[0]),d.imageEl=U,U?d.imageWrapEl=je(d.imageEl,`.${F.containerClass}`)[0]:d.imageWrapEl=void 0}!d.imageEl||!d.imageWrapEl||(e.params.cssMode&&(e.wrapperEl.style.overflow="",e.wrapperEl.style.touchAction=""),B.scale=1,r=1,h.touchesStart.x=void 0,h.touchesStart.y=void 0,d.imageWrapEl.style.transitionDuration="300ms",d.imageWrapEl.style.transform="translate3d(0,0,0)",d.imageEl.style.transitionDuration="300ms",d.imageEl.style.transform="translate3d(0,0,0) scale(1)",d.slideEl.classList.remove(`${F.zoomedSlideClass}`),d.slideEl=void 0,d.originX=0,d.originY=0)}function k(B){let F=e.zoom;F.scale&&F.scale!==1?V():O(B)}function M(){let B=e.params.passiveListeners?{passive:!0,capture:!1}:!1,F=e.params.passiveListeners?{passive:!1,capture:!0}:!0;return{passiveListener:B,activeListenerWithCapture:F}}function G(){let B=e.zoom;if(B.enabled)return;B.enabled=!0;let{passiveListener:F,activeListenerWithCapture:U}=M();e.wrapperEl.addEventListener("pointerdown",E,F),e.wrapperEl.addEventListener("pointermove",N,U),["pointerup","pointercancel","pointerout"].forEach(ae=>{e.wrapperEl.addEventListener(ae,L,F)}),e.wrapperEl.addEventListener("pointermove",A,U)}function K(){let B=e.zoom;if(!B.enabled)return;B.enabled=!1;let{passiveListener:F,activeListenerWithCapture:U}=M();e.wrapperEl.removeEventListener("pointerdown",E,F),e.wrapperEl.removeEventListener("pointermove",N,U),["pointerup","pointercancel","pointerout"].forEach(ae=>{e.wrapperEl.removeEventListener(ae,L,F)}),e.wrapperEl.removeEventListener("pointermove",A,U)}n("init",()=>{e.params.zoom.enabled&&G()}),n("destroy",()=>{K()}),n("touchStart",(B,F)=>{e.zoom.enabled&&P(F)}),n("touchEnd",(B,F)=>{e.zoom.enabled&&T()}),n("doubleTap",(B,F)=>{!e.animating&&e.params.zoom.enabled&&e.zoom.enabled&&e.params.zoom.toggle&&k(F)}),n("transitionEnd",()=>{e.zoom.enabled&&e.params.zoom.enabled&&I()}),n("slideChange",()=>{e.zoom.enabled&&e.params.zoom.enabled&&e.params.cssMode&&I()}),Object.assign(e.zoom,{enable:G,disable:K,in:O,out:V,toggle:k})}function _s(i){let{swiper:e,extendParams:t,on:n}=i;t({controller:{control:void 0,inverse:!1,by:"slide"}}),e.controller={control:void 0};function a(u,c){let d=function(){let _,w,b;return(v,g)=>{for(w=-1,_=v.length;_-w>1;)b=_+w>>1,v[b]<=g?w=b:_=b;return _}}();this.x=u,this.y=c,this.lastIndex=u.length-1;let h,f;return this.interpolate=function(_){return _?(f=d(this.x,_),h=f-1,(_-this.x[h])*(this.y[f]-this.y[h])/(this.x[f]-this.x[h])+this.y[h]):0},this}function s(u){e.controller.spline=e.params.loop?new a(e.slidesGrid,u.slidesGrid):new a(e.snapGrid,u.snapGrid)}function r(u,c){let d=e.controller.control,h,f,m=e.constructor;function _(w){if(w.destroyed)return;let b=e.rtlTranslate?-e.translate:e.translate;e.params.controller.by==="slide"&&(s(w),f=-e.controller.spline.interpolate(-b)),(!f||e.params.controller.by==="container")&&(h=(w.maxTranslate()-w.minTranslate())/(e.maxTranslate()-e.minTranslate()),(Number.isNaN(h)||!Number.isFinite(h))&&(h=1),f=(b-e.minTranslate())*h+w.minTranslate()),e.params.controller.inverse&&(f=w.maxTranslate()-f),w.updateProgress(f),w.setTranslate(f,e),w.updateActiveIndex(),w.updateSlidesClasses()}if(Array.isArray(d))for(let w=0;w<d.length;w+=1)d[w]!==c&&d[w]instanceof m&&_(d[w]);else d instanceof m&&c!==d&&_(d)}function l(u,c){let d=e.constructor,h=e.controller.control,f;function m(_){_.destroyed||(_.setTransition(u,e),u!==0&&(_.transitionStart(),_.params.autoHeight&&Ye(()=>{_.updateAutoHeight()}),_t(_.wrapperEl,()=>{h&&_.transitionEnd()})))}if(Array.isArray(h))for(f=0;f<h.length;f+=1)h[f]!==c&&h[f]instanceof d&&m(h[f]);else h instanceof d&&c!==h&&m(h)}function o(){e.controller.control&&e.controller.spline&&(e.controller.spline=void 0,delete e.controller.spline)}n("beforeInit",()=>{if(typeof window<"u"&&(typeof e.params.controller.control=="string"||e.params.controller.control instanceof HTMLElement)){(typeof e.params.controller.control=="string"?[...document.querySelectorAll(e.params.controller.control)]:[e.params.controller.control]).forEach(c=>{if(e.controller.control||(e.controller.control=[]),c&&c.swiper)e.controller.control.push(c.swiper);else if(c){let d=`${e.params.eventsPrefix}init`,h=f=>{e.controller.control.push(f.detail[0]),e.update(),c.removeEventListener(d,h)};c.addEventListener(d,h)}});return}e.controller.control=e.params.controller.control}),n("update",()=>{o()}),n("resize",()=>{o()}),n("observerUpdate",()=>{o()}),n("setTranslate",(u,c,d)=>{!e.controller.control||e.controller.control.destroyed||e.controller.setTranslate(c,d)}),n("setTransition",(u,c,d)=>{!e.controller.control||e.controller.control.destroyed||e.controller.setTransition(c,d)}),Object.assign(e.controller,{setTranslate:r,setTransition:l})}function vs(i){let{swiper:e,extendParams:t,on:n}=i;t({a11y:{enabled:!0,notificationClass:"swiper-notification",prevSlideMessage:"Previous slide",nextSlideMessage:"Next slide",firstSlideMessage:"This is the first slide",lastSlideMessage:"This is the last slide",paginationBulletMessage:"Go to slide {{index}}",slideLabelMessage:"{{index}} / {{slidesLength}}",containerMessage:null,containerRoleDescriptionMessage:null,itemRoleDescriptionMessage:null,slideRole:"group",id:null,scrollOnFocus:!0}}),e.a11y={clicked:!1};let a=null,s,r,l=new Date().getTime();function o(k){let M=a;M.length!==0&&(M.innerHTML="",M.innerHTML=k)}function u(k){k===void 0&&(k=16);let M=()=>Math.round(16*Math.random()).toString(16);return"x".repeat(k).replace(/x/g,M)}function c(k){k=Z(k),k.forEach(M=>{M.setAttribute("tabIndex","0")})}function d(k){k=Z(k),k.forEach(M=>{M.setAttribute("tabIndex","-1")})}function h(k,M){k=Z(k),k.forEach(G=>{G.setAttribute("role",M)})}function f(k,M){k=Z(k),k.forEach(G=>{G.setAttribute("aria-roledescription",M)})}function m(k,M){k=Z(k),k.forEach(G=>{G.setAttribute("aria-controls",M)})}function _(k,M){k=Z(k),k.forEach(G=>{G.setAttribute("aria-label",M)})}function w(k,M){k=Z(k),k.forEach(G=>{G.setAttribute("id",M)})}function b(k,M){k=Z(k),k.forEach(G=>{G.setAttribute("aria-live",M)})}function v(k){k=Z(k),k.forEach(M=>{M.setAttribute("aria-disabled",!0)})}function g(k){k=Z(k),k.forEach(M=>{M.setAttribute("aria-disabled",!1)})}function y(k){if(k.keyCode!==13&&k.keyCode!==32)return;let M=e.params.a11y,G=k.target;if(!(e.pagination&&e.pagination.el&&(G===e.pagination.el||e.pagination.el.contains(k.target))&&!k.target.matches(Le(e.params.pagination.bulletClass)))){if(e.navigation&&e.navigation.prevEl&&e.navigation.nextEl){let K=Z(e.navigation.prevEl);Z(e.navigation.nextEl).includes(G)&&(e.isEnd&&!e.params.loop||e.slideNext(),e.isEnd?o(M.lastSlideMessage):o(M.nextSlideMessage)),K.includes(G)&&(e.isBeginning&&!e.params.loop||e.slidePrev(),e.isBeginning?o(M.firstSlideMessage):o(M.prevSlideMessage))}e.pagination&&G.matches(Le(e.params.pagination.bulletClass))&&G.click()}}function E(){if(e.params.loop||e.params.rewind||!e.navigation)return;let{nextEl:k,prevEl:M}=e.navigation;M&&(e.isBeginning?(v(M),d(M)):(g(M),c(M))),k&&(e.isEnd?(v(k),d(k)):(g(k),c(k)))}function N(){return e.pagination&&e.pagination.bullets&&e.pagination.bullets.length}function L(){return N()&&e.params.pagination.clickable}function D(){let k=e.params.a11y;N()&&e.pagination.bullets.forEach(M=>{e.params.pagination.clickable&&(c(M),e.params.pagination.renderBullet||(h(M,"button"),_(M,k.paginationBulletMessage.replace(/\{\{index\}\}/,gt(M)+1)))),M.matches(Le(e.params.pagination.bulletActiveClass))?M.setAttribute("aria-current","true"):M.removeAttribute("aria-current")})}let S=(k,M,G)=>{c(k),k.tagName!=="BUTTON"&&(h(k,"button"),k.addEventListener("keydown",y)),_(k,G),m(k,M)},C=k=>{r&&r!==k.target&&!r.contains(k.target)&&(s=!0),e.a11y.clicked=!0},P=()=>{s=!1,requestAnimationFrame(()=>{requestAnimationFrame(()=>{e.destroyed||(e.a11y.clicked=!1)})})},A=k=>{l=new Date().getTime()},T=k=>{if(e.a11y.clicked||!e.params.a11y.scrollOnFocus||new Date().getTime()-l<100)return;let M=k.target.closest(`.${e.params.slideClass}, swiper-slide`);if(!M||!e.slides.includes(M))return;r=M;let G=e.slides.indexOf(M)===e.activeIndex,K=e.params.watchSlidesProgress&&e.visibleSlides&&e.visibleSlides.includes(M);G||K||k.sourceCapabilities&&k.sourceCapabilities.firesTouchEvents||(e.isHorizontal()?e.el.scrollLeft=0:e.el.scrollTop=0,requestAnimationFrame(()=>{s||(e.params.loop?e.slideToLoop(parseInt(M.getAttribute("data-swiper-slide-index")),0):e.slideTo(e.slides.indexOf(M),0),s=!1)}))},I=()=>{let k=e.params.a11y;k.itemRoleDescriptionMessage&&f(e.slides,k.itemRoleDescriptionMessage),k.slideRole&&h(e.slides,k.slideRole);let M=e.slides.length;k.slideLabelMessage&&e.slides.forEach((G,K)=>{let B=e.params.loop?parseInt(G.getAttribute("data-swiper-slide-index"),10):K,F=k.slideLabelMessage.replace(/\{\{index\}\}/,B+1).replace(/\{\{slidesLength\}\}/,M);_(G,F)})},O=()=>{let k=e.params.a11y;e.el.append(a);let M=e.el;k.containerRoleDescriptionMessage&&f(M,k.containerRoleDescriptionMessage),k.containerMessage&&_(M,k.containerMessage);let G=e.wrapperEl,K=k.id||G.getAttribute("id")||`swiper-wrapper-${u(16)}`,B=e.params.autoplay&&e.params.autoplay.enabled?"off":"polite";w(G,K),b(G,B),I();let{nextEl:F,prevEl:U}=e.navigation?e.navigation:{};F=Z(F),U=Z(U),F&&F.forEach(j=>S(j,K,k.nextSlideMessage)),U&&U.forEach(j=>S(j,K,k.prevSlideMessage)),L()&&Z(e.pagination.el).forEach(ee=>{ee.addEventListener("keydown",y)}),le().addEventListener("visibilitychange",A),e.el.addEventListener("focus",T,!0),e.el.addEventListener("focus",T,!0),e.el.addEventListener("pointerdown",C,!0),e.el.addEventListener("pointerup",P,!0)};function V(){a&&a.remove();let{nextEl:k,prevEl:M}=e.navigation?e.navigation:{};k=Z(k),M=Z(M),k&&k.forEach(K=>K.removeEventListener("keydown",y)),M&&M.forEach(K=>K.removeEventListener("keydown",y)),L()&&Z(e.pagination.el).forEach(B=>{B.removeEventListener("keydown",y)}),le().removeEventListener("visibilitychange",A),e.el&&typeof e.el!="string"&&(e.el.removeEventListener("focus",T,!0),e.el.removeEventListener("pointerdown",C,!0),e.el.removeEventListener("pointerup",P,!0))}n("beforeInit",()=>{a=he("span",e.params.a11y.notificationClass),a.setAttribute("aria-live","assertive"),a.setAttribute("aria-atomic","true")}),n("afterInit",()=>{e.params.a11y.enabled&&O()}),n("slidesLengthChange snapGridLengthChange slidesGridLengthChange",()=>{e.params.a11y.enabled&&I()}),n("fromEdge toEdge afterInit lock unlock",()=>{e.params.a11y.enabled&&E()}),n("paginationUpdate",()=>{e.params.a11y.enabled&&D()}),n("destroy",()=>{e.params.a11y.enabled&&V()})}function ws(i){let{swiper:e,extendParams:t,on:n}=i;t({history:{enabled:!1,root:"",replaceState:!1,key:"slides",keepQuery:!1}});let a=!1,s={},r=f=>f.toString().replace(/\s+/g,"-").replace(/[^\w-]+/g,"").replace(/--+/g,"-").replace(/^-+/,"").replace(/-+$/,""),l=f=>{let m=ne(),_;f?_=new URL(f):_=m.location;let w=_.pathname.slice(1).split("/").filter(y=>y!==""),b=w.length,v=w[b-2],g=w[b-1];return{key:v,value:g}},o=(f,m)=>{let _=ne();if(!a||!e.params.history.enabled)return;let w;e.params.url?w=new URL(e.params.url):w=_.location;let b=e.virtual&&e.params.virtual.enabled?e.slidesEl.querySelector(`[data-swiper-slide-index="${m}"]`):e.slides[m],v=r(b.getAttribute("data-history"));if(e.params.history.root.length>0){let y=e.params.history.root;y[y.length-1]==="/"&&(y=y.slice(0,y.length-1)),v=`${y}/${f?`${f}/`:""}${v}`}else w.pathname.includes(f)||(v=`${f?`${f}/`:""}${v}`);e.params.history.keepQuery&&(v+=w.search);let g=_.history.state;g&&g.value===v||(e.params.history.replaceState?_.history.replaceState({value:v},null,v):_.history.pushState({value:v},null,v))},u=(f,m,_)=>{if(m)for(let w=0,b=e.slides.length;w<b;w+=1){let v=e.slides[w];if(r(v.getAttribute("data-history"))===m){let y=e.getSlideIndex(v);e.slideTo(y,f,_)}}else e.slideTo(0,f,_)},c=()=>{s=l(e.params.url),u(e.params.speed,s.value,!1)},d=()=>{let f=ne();if(e.params.history){if(!f.history||!f.history.pushState){e.params.history.enabled=!1,e.params.hashNavigation.enabled=!0;return}if(a=!0,s=l(e.params.url),!s.key&&!s.value){e.params.history.replaceState||f.addEventListener("popstate",c);return}u(0,s.value,e.params.runCallbacksOnInit),e.params.history.replaceState||f.addEventListener("popstate",c)}},h=()=>{let f=ne();e.params.history.replaceState||f.removeEventListener("popstate",c)};n("init",()=>{e.params.history.enabled&&d()}),n("destroy",()=>{e.params.history.enabled&&h()}),n("transitionEnd _freeModeNoMomentumRelease",()=>{a&&o(e.params.history.key,e.activeIndex)}),n("slideChange",()=>{a&&e.params.cssMode&&o(e.params.history.key,e.activeIndex)})}function bs(i){let{swiper:e,extendParams:t,emit:n,on:a}=i,s=!1,r=le(),l=ne();t({hashNavigation:{enabled:!1,replaceState:!1,watchState:!1,getSlideIndex(h,f){if(e.virtual&&e.params.virtual.enabled){let m=e.slides.filter(w=>w.getAttribute("data-hash")===f)[0];return m?parseInt(m.getAttribute("data-swiper-slide-index"),10):0}return e.getSlideIndex(de(e.slidesEl,`.${e.params.slideClass}[data-hash="${f}"], swiper-slide[data-hash="${f}"]`)[0])}}});let o=()=>{n("hashChange");let h=r.location.hash.replace("#",""),f=e.virtual&&e.params.virtual.enabled?e.slidesEl.querySelector(`[data-swiper-slide-index="${e.activeIndex}"]`):e.slides[e.activeIndex],m=f?f.getAttribute("data-hash"):"";if(h!==m){let _=e.params.hashNavigation.getSlideIndex(e,h);if(typeof _>"u"||Number.isNaN(_))return;e.slideTo(_)}},u=()=>{if(!s||!e.params.hashNavigation.enabled)return;let h=e.virtual&&e.params.virtual.enabled?e.slidesEl.querySelector(`[data-swiper-slide-index="${e.activeIndex}"]`):e.slides[e.activeIndex],f=h?h.getAttribute("data-hash")||h.getAttribute("data-history"):"";e.params.hashNavigation.replaceState&&l.history&&l.history.replaceState?(l.history.replaceState(null,null,`#${f}`||""),n("hashSet")):(r.location.hash=f||"",n("hashSet"))},c=()=>{if(!e.params.hashNavigation.enabled||e.params.history&&e.params.history.enabled)return;s=!0;let h=r.location.hash.replace("#","");if(h){let m=e.params.hashNavigation.getSlideIndex(e,h);e.slideTo(m||0,0,e.params.runCallbacksOnInit,!0)}e.params.hashNavigation.watchState&&l.addEventListener("hashchange",o)},d=()=>{e.params.hashNavigation.watchState&&l.removeEventListener("hashchange",o)};a("init",()=>{e.params.hashNavigation.enabled&&c()}),a("destroy",()=>{e.params.hashNavigation.enabled&&d()}),a("transitionEnd _freeModeNoMomentumRelease",()=>{s&&u()}),a("slideChange",()=>{s&&e.params.cssMode&&u()})}function ys(i){let{swiper:e,extendParams:t,on:n,emit:a,params:s}=i;e.autoplay={running:!1,paused:!1,timeLeft:0},t({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!1,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let r,l,o=s&&s.autoplay?s.autoplay.delay:3e3,u=s&&s.autoplay?s.autoplay.delay:3e3,c,d=new Date().getTime(),h,f,m,_,w,b,v;function g(M){!e||e.destroyed||!e.wrapperEl||M.target===e.wrapperEl&&(e.wrapperEl.removeEventListener("transitionend",g),!(v||M.detail&&M.detail.bySwiperTouchMove)&&C())}let y=()=>{if(e.destroyed||!e.autoplay.running)return;e.autoplay.paused?h=!0:h&&(u=c,h=!1);let M=e.autoplay.paused?c:d+u-new Date().getTime();e.autoplay.timeLeft=M,a("autoplayTimeLeft",M,M/o),l=requestAnimationFrame(()=>{y()})},E=()=>{let M;return e.virtual&&e.params.virtual.enabled?M=e.slides.filter(K=>K.classList.contains("swiper-slide-active"))[0]:M=e.slides[e.activeIndex],M?parseInt(M.getAttribute("data-swiper-autoplay"),10):void 0},N=M=>{if(e.destroyed||!e.autoplay.running)return;cancelAnimationFrame(l),y();let G=typeof M>"u"?e.params.autoplay.delay:M;o=e.params.autoplay.delay,u=e.params.autoplay.delay;let K=E();!Number.isNaN(K)&&K>0&&typeof M>"u"&&(G=K,o=K,u=K),c=G;let B=e.params.speed,F=()=>{!e||e.destroyed||(e.params.autoplay.reverseDirection?!e.isBeginning||e.params.loop||e.params.rewind?(e.slidePrev(B,!0,!0),a("autoplay")):e.params.autoplay.stopOnLastSlide||(e.slideTo(e.slides.length-1,B,!0,!0),a("autoplay")):!e.isEnd||e.params.loop||e.params.rewind?(e.slideNext(B,!0,!0),a("autoplay")):e.params.autoplay.stopOnLastSlide||(e.slideTo(0,B,!0,!0),a("autoplay")),e.params.cssMode&&(d=new Date().getTime(),requestAnimationFrame(()=>{N()})))};return G>0?(clearTimeout(r),r=setTimeout(()=>{F()},G)):requestAnimationFrame(()=>{F()}),G},L=()=>{d=new Date().getTime(),e.autoplay.running=!0,N(),a("autoplayStart")},D=()=>{e.autoplay.running=!1,clearTimeout(r),cancelAnimationFrame(l),a("autoplayStop")},S=(M,G)=>{if(e.destroyed||!e.autoplay.running)return;clearTimeout(r),M||(b=!0);let K=()=>{a("autoplayPause"),e.params.autoplay.waitForTransition?e.wrapperEl.addEventListener("transitionend",g):C()};if(e.autoplay.paused=!0,G){w&&(c=e.params.autoplay.delay),w=!1,K();return}c=(c||e.params.autoplay.delay)-(new Date().getTime()-d),!(e.isEnd&&c<0&&!e.params.loop)&&(c<0&&(c=0),K())},C=()=>{e.isEnd&&c<0&&!e.params.loop||e.destroyed||!e.autoplay.running||(d=new Date().getTime(),b?(b=!1,N(c)):N(),e.autoplay.paused=!1,a("autoplayResume"))},P=()=>{if(e.destroyed||!e.autoplay.running)return;let M=le();M.visibilityState==="hidden"&&(b=!0,S(!0)),M.visibilityState==="visible"&&C()},A=M=>{M.pointerType==="mouse"&&(b=!0,v=!0,!(e.animating||e.autoplay.paused)&&S(!0))},T=M=>{M.pointerType==="mouse"&&(v=!1,e.autoplay.paused&&C())},I=()=>{e.params.autoplay.pauseOnMouseEnter&&(e.el.addEventListener("pointerenter",A),e.el.addEventListener("pointerleave",T))},O=()=>{e.el&&typeof e.el!="string"&&(e.el.removeEventListener("pointerenter",A),e.el.removeEventListener("pointerleave",T))},V=()=>{le().addEventListener("visibilitychange",P)},k=()=>{le().removeEventListener("visibilitychange",P)};n("init",()=>{e.params.autoplay.enabled&&(I(),V(),L())}),n("destroy",()=>{O(),k(),e.autoplay.running&&D()}),n("_freeModeStaticRelease",()=>{(m||b)&&C()}),n("_freeModeNoMomentumRelease",()=>{e.params.autoplay.disableOnInteraction?D():S(!0,!0)}),n("beforeTransitionStart",(M,G,K)=>{e.destroyed||!e.autoplay.running||(K||!e.params.autoplay.disableOnInteraction?S(!0,!0):D())}),n("sliderFirstMove",()=>{if(!(e.destroyed||!e.autoplay.running)){if(e.params.autoplay.disableOnInteraction){D();return}f=!0,m=!1,b=!1,_=setTimeout(()=>{b=!0,m=!0,S(!0)},200)}}),n("touchEnd",()=>{if(!(e.destroyed||!e.autoplay.running||!f)){if(clearTimeout(_),clearTimeout(r),e.params.autoplay.disableOnInteraction){m=!1,f=!1;return}m&&e.params.cssMode&&C(),m=!1,f=!1}}),n("slideChange",()=>{e.destroyed||!e.autoplay.running||(w=!0)}),Object.assign(e.autoplay,{start:L,stop:D,pause:S,resume:C})}function Ss(i){let{swiper:e,extendParams:t,on:n}=i;t({thumbs:{swiper:null,multipleActiveThumbs:!0,autoScrollOffset:0,slideThumbActiveClass:"swiper-slide-thumb-active",thumbsContainerClass:"swiper-thumbs"}});let a=!1,s=!1;e.thumbs={swiper:null};function r(){let u=e.thumbs.swiper;if(!u||u.destroyed)return;let c=u.clickedIndex,d=u.clickedSlide;if(d&&d.classList.contains(e.params.thumbs.slideThumbActiveClass)||typeof c>"u"||c===null)return;let h;u.params.loop?h=parseInt(u.clickedSlide.getAttribute("data-swiper-slide-index"),10):h=c,e.params.loop?e.slideToLoop(h):e.slideTo(h)}function l(){let{thumbs:u}=e.params;if(a)return!1;a=!0;let c=e.constructor;if(u.swiper instanceof c)e.thumbs.swiper=u.swiper,Object.assign(e.thumbs.swiper.originalParams,{watchSlidesProgress:!0,slideToClickedSlide:!1}),Object.assign(e.thumbs.swiper.params,{watchSlidesProgress:!0,slideToClickedSlide:!1}),e.thumbs.swiper.update();else if(Ut(u.swiper)){let d=Object.assign({},u.swiper);Object.assign(d,{watchSlidesProgress:!0,slideToClickedSlide:!1}),e.thumbs.swiper=new c(d),s=!0}return e.thumbs.swiper.el.classList.add(e.params.thumbs.thumbsContainerClass),e.thumbs.swiper.on("tap",r),!0}function o(u){let c=e.thumbs.swiper;if(!c||c.destroyed)return;let d=c.params.slidesPerView==="auto"?c.slidesPerViewDynamic():c.params.slidesPerView,h=1,f=e.params.thumbs.slideThumbActiveClass;if(e.params.slidesPerView>1&&!e.params.centeredSlides&&(h=e.params.slidesPerView),e.params.thumbs.multipleActiveThumbs||(h=1),h=Math.floor(h),c.slides.forEach(w=>w.classList.remove(f)),c.params.loop||c.params.virtual&&c.params.virtual.enabled)for(let w=0;w<h;w+=1)de(c.slidesEl,`[data-swiper-slide-index="${e.realIndex+w}"]`).forEach(b=>{b.classList.add(f)});else for(let w=0;w<h;w+=1)c.slides[e.realIndex+w]&&c.slides[e.realIndex+w].classList.add(f);let m=e.params.thumbs.autoScrollOffset,_=m&&!c.params.loop;if(e.realIndex!==c.realIndex||_){let w=c.activeIndex,b,v;if(c.params.loop){let g=c.slides.filter(y=>y.getAttribute("data-swiper-slide-index")===`${e.realIndex}`)[0];b=c.slides.indexOf(g),v=e.activeIndex>e.previousIndex?"next":"prev"}else b=e.realIndex,v=b>e.previousIndex?"next":"prev";_&&(b+=v==="next"?m:-1*m),c.visibleSlidesIndexes&&c.visibleSlidesIndexes.indexOf(b)<0&&(c.params.centeredSlides?b>w?b=b-Math.floor(d/2)+1:b=b+Math.floor(d/2)-1:b>w&&c.params.slidesPerGroup,c.slideTo(b,u?0:void 0))}}n("beforeInit",()=>{let{thumbs:u}=e.params;if(!(!u||!u.swiper))if(typeof u.swiper=="string"||u.swiper instanceof HTMLElement){let c=le(),d=()=>{let f=typeof u.swiper=="string"?c.querySelector(u.swiper):u.swiper;if(f&&f.swiper)u.swiper=f.swiper,l(),o(!0);else if(f){let m=`${e.params.eventsPrefix}init`,_=w=>{u.swiper=w.detail[0],f.removeEventListener(m,_),l(),o(!0),u.swiper.update(),e.update()};f.addEventListener(m,_)}return f},h=()=>{if(e.destroyed)return;d()||requestAnimationFrame(h)};requestAnimationFrame(h)}else l(),o(!0)}),n("slideChange update resize observerUpdate",()=>{o()}),n("setTransition",(u,c)=>{let d=e.thumbs.swiper;!d||d.destroyed||d.setTransition(c)}),n("beforeDestroy",()=>{let u=e.thumbs.swiper;!u||u.destroyed||s&&u.destroy()}),Object.assign(e.thumbs,{init:l,update:o})}function Ts(i){let{swiper:e,extendParams:t,emit:n,once:a}=i;t({freeMode:{enabled:!1,momentum:!0,momentumRatio:1,momentumBounce:!0,momentumBounceRatio:1,momentumVelocityRatio:1,sticky:!1,minimumVelocity:.02}});function s(){if(e.params.cssMode)return;let o=e.getTranslate();e.setTranslate(o),e.setTransition(0),e.touchEventsData.velocities.length=0,e.freeMode.onTouchEnd({currentPos:e.rtl?e.translate:-e.translate})}function r(){if(e.params.cssMode)return;let{touchEventsData:o,touches:u}=e;o.velocities.length===0&&o.velocities.push({position:u[e.isHorizontal()?"startX":"startY"],time:o.touchStartTime}),o.velocities.push({position:u[e.isHorizontal()?"currentX":"currentY"],time:we()})}function l(o){let{currentPos:u}=o;if(e.params.cssMode)return;let{params:c,wrapperEl:d,rtlTranslate:h,snapGrid:f,touchEventsData:m}=e,w=we()-m.touchStartTime;if(u<-e.minTranslate()){e.slideTo(e.activeIndex);return}if(u>-e.maxTranslate()){e.slides.length<f.length?e.slideTo(f.length-1):e.slideTo(e.slides.length-1);return}if(c.freeMode.momentum){if(m.velocities.length>1){let D=m.velocities.pop(),S=m.velocities.pop(),C=D.position-S.position,P=D.time-S.time;e.velocity=C/P,e.velocity/=2,Math.abs(e.velocity)<c.freeMode.minimumVelocity&&(e.velocity=0),(P>150||we()-D.time>300)&&(e.velocity=0)}else e.velocity=0;e.velocity*=c.freeMode.momentumVelocityRatio,m.velocities.length=0;let b=1e3*c.freeMode.momentumRatio,v=e.velocity*b,g=e.translate+v;h&&(g=-g);let y=!1,E,N=Math.abs(e.velocity)*20*c.freeMode.momentumBounceRatio,L;if(g<e.maxTranslate())c.freeMode.momentumBounce?(g+e.maxTranslate()<-N&&(g=e.maxTranslate()-N),E=e.maxTranslate(),y=!0,m.allowMomentumBounce=!0):g=e.maxTranslate(),c.loop&&c.centeredSlides&&(L=!0);else if(g>e.minTranslate())c.freeMode.momentumBounce?(g-e.minTranslate()>N&&(g=e.minTranslate()+N),E=e.minTranslate(),y=!0,m.allowMomentumBounce=!0):g=e.minTranslate(),c.loop&&c.centeredSlides&&(L=!0);else if(c.freeMode.sticky){let D;for(let S=0;S<f.length;S+=1)if(f[S]>-g){D=S;break}Math.abs(f[D]-g)<Math.abs(f[D-1]-g)||e.swipeDirection==="next"?g=f[D]:g=f[D-1],g=-g}if(L&&a("transitionEnd",()=>{e.loopFix()}),e.velocity!==0){if(h?b=Math.abs((-g-e.translate)/e.velocity):b=Math.abs((g-e.translate)/e.velocity),c.freeMode.sticky){let D=Math.abs((h?-g:g)-e.translate),S=e.slidesSizesGrid[e.activeIndex];D<S?b=c.speed:D<2*S?b=c.speed*1.5:b=c.speed*2.5}}else if(c.freeMode.sticky){e.slideToClosest();return}c.freeMode.momentumBounce&&y?(e.updateProgress(E),e.setTransition(b),e.setTranslate(g),e.transitionStart(!0,e.swipeDirection),e.animating=!0,_t(d,()=>{!e||e.destroyed||!m.allowMomentumBounce||(n("momentumBounce"),e.setTransition(c.speed),setTimeout(()=>{e.setTranslate(E),_t(d,()=>{!e||e.destroyed||e.transitionEnd()})},0))})):e.velocity?(n("_freeModeNoMomentumRelease"),e.updateProgress(g),e.setTransition(b),e.setTranslate(g),e.transitionStart(!0,e.swipeDirection),e.animating||(e.animating=!0,_t(d,()=>{!e||e.destroyed||e.transitionEnd()}))):e.updateProgress(g),e.updateActiveIndex(),e.updateSlidesClasses()}else if(c.freeMode.sticky){e.slideToClosest();return}else c.freeMode&&n("_freeModeNoMomentumRelease");(!c.freeMode.momentum||w>=c.longSwipesMs)&&(n("_freeModeStaticRelease"),e.updateProgress(),e.updateActiveIndex(),e.updateSlidesClasses())}Object.assign(e,{freeMode:{onTouchStart:s,onTouchMove:r,onTouchEnd:l}})}function Es(i){let{swiper:e,extendParams:t,on:n}=i;t({grid:{rows:1,fill:"column"}});let a,s,r,l,o=()=>{let _=e.params.spaceBetween;return typeof _=="string"&&_.indexOf("%")>=0?_=parseFloat(_.replace("%",""))/100*e.size:typeof _=="string"&&(_=parseFloat(_)),_},u=_=>{let{slidesPerView:w}=e.params,{rows:b,fill:v}=e.params.grid,g=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:_.length;r=Math.floor(g/b),Math.floor(g/b)===g/b?a=g:a=Math.ceil(g/b)*b,w!=="auto"&&v==="row"&&(a=Math.max(a,w*b)),s=a/b},c=()=>{e.slides&&e.slides.forEach(_=>{_.swiperSlideGridSet&&(_.style.height="",_.style[e.getDirectionLabel("margin-top")]="")})},d=(_,w,b)=>{let{slidesPerGroup:v}=e.params,g=o(),{rows:y,fill:E}=e.params.grid,N=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:b.length,L,D,S;if(E==="row"&&v>1){let C=Math.floor(_/(v*y)),P=_-y*v*C,A=C===0?v:Math.min(Math.ceil((N-C*y*v)/y),v);S=Math.floor(P/A),D=P-S*A+C*v,L=D+S*a/y,w.style.order=L}else E==="column"?(D=Math.floor(_/y),S=_-D*y,(D>r||D===r&&S===y-1)&&(S+=1,S>=y&&(S=0,D+=1))):(S=Math.floor(_/s),D=_-S*s);w.row=S,w.column=D,w.style.height=`calc((100% - ${(y-1)*g}px) / ${y})`,w.style[e.getDirectionLabel("margin-top")]=S!==0?g&&`${g}px`:"",w.swiperSlideGridSet=!0},h=(_,w)=>{let{centeredSlides:b,roundLengths:v}=e.params,g=o(),{rows:y}=e.params.grid;if(e.virtualSize=(_+g)*a,e.virtualSize=Math.ceil(e.virtualSize/y)-g,e.params.cssMode||(e.wrapperEl.style[e.getDirectionLabel("width")]=`${e.virtualSize+g}px`),b){let E=[];for(let N=0;N<w.length;N+=1){let L=w[N];v&&(L=Math.floor(L)),w[N]<e.virtualSize+w[0]&&E.push(L)}w.splice(0,w.length),w.push(...E)}},f=()=>{l=e.params.grid&&e.params.grid.rows>1},m=()=>{let{params:_,el:w}=e,b=_.grid&&_.grid.rows>1;l&&!b?(w.classList.remove(`${_.containerModifierClass}grid`,`${_.containerModifierClass}grid-column`),r=1,e.emitContainerClasses()):!l&&b&&(w.classList.add(`${_.containerModifierClass}grid`),_.grid.fill==="column"&&w.classList.add(`${_.containerModifierClass}grid-column`),e.emitContainerClasses()),l=b};n("init",f),n("update",m),e.grid={initSlides:u,unsetSlides:c,updateSlide:d,updateWrapperSize:h}}function gl(i){let e=this,{params:t,slidesEl:n}=e;t.loop&&e.loopDestroy();let a=s=>{if(typeof s=="string"){let r=document.createElement("div");r.innerHTML=s,n.append(r.children[0]),r.innerHTML=""}else n.append(s)};if(typeof i=="object"&&"length"in i)for(let s=0;s<i.length;s+=1)i[s]&&a(i[s]);else a(i);e.recalcSlides(),t.loop&&e.loopCreate(),(!t.observer||e.isElement)&&e.update()}function _l(i){let e=this,{params:t,activeIndex:n,slidesEl:a}=e;t.loop&&e.loopDestroy();let s=n+1,r=l=>{if(typeof l=="string"){let o=document.createElement("div");o.innerHTML=l,a.prepend(o.children[0]),o.innerHTML=""}else a.prepend(l)};if(typeof i=="object"&&"length"in i){for(let l=0;l<i.length;l+=1)i[l]&&r(i[l]);s=n+i.length}else r(i);e.recalcSlides(),t.loop&&e.loopCreate(),(!t.observer||e.isElement)&&e.update(),e.slideTo(s,0,!1)}function vl(i,e){let t=this,{params:n,activeIndex:a,slidesEl:s}=t,r=a;n.loop&&(r-=t.loopedSlides,t.loopDestroy(),t.recalcSlides());let l=t.slides.length;if(i<=0){t.prependSlide(e);return}if(i>=l){t.appendSlide(e);return}let o=r>i?r+1:r,u=[];for(let c=l-1;c>=i;c-=1){let d=t.slides[c];d.remove(),u.unshift(d)}if(typeof e=="object"&&"length"in e){for(let c=0;c<e.length;c+=1)e[c]&&s.append(e[c]);o=r>i?r+e.length:r}else s.append(e);for(let c=0;c<u.length;c+=1)s.append(u[c]);t.recalcSlides(),n.loop&&t.loopCreate(),(!n.observer||t.isElement)&&t.update(),n.loop?t.slideTo(o+t.loopedSlides,0,!1):t.slideTo(o,0,!1)}function wl(i){let e=this,{params:t,activeIndex:n}=e,a=n;t.loop&&(a-=e.loopedSlides,e.loopDestroy());let s=a,r;if(typeof i=="object"&&"length"in i){for(let l=0;l<i.length;l+=1)r=i[l],e.slides[r]&&e.slides[r].remove(),r<s&&(s-=1);s=Math.max(s,0)}else r=i,e.slides[r]&&e.slides[r].remove(),r<s&&(s-=1),s=Math.max(s,0);e.recalcSlides(),t.loop&&e.loopCreate(),(!t.observer||e.isElement)&&e.update(),t.loop?e.slideTo(s+e.loopedSlides,0,!1):e.slideTo(s,0,!1)}function bl(){let i=this,e=[];for(let t=0;t<i.slides.length;t+=1)e.push(t);i.removeSlide(e)}function xs(i){let{swiper:e}=i;Object.assign(e,{appendSlide:gl.bind(e),prependSlide:_l.bind(e),addSlide:vl.bind(e),removeSlide:wl.bind(e),removeAllSlides:bl.bind(e)})}function He(i){let{effect:e,swiper:t,on:n,setTranslate:a,setTransition:s,overwriteParams:r,perspective:l,recreateShadows:o,getEffectParams:u}=i;n("beforeInit",()=>{if(t.params.effect!==e)return;t.classNames.push(`${t.params.containerModifierClass}${e}`),l&&l()&&t.classNames.push(`${t.params.containerModifierClass}3d`);let d=r?r():{};Object.assign(t.params,d),Object.assign(t.originalParams,d)}),n("setTranslate",()=>{t.params.effect===e&&a()}),n("setTransition",(d,h)=>{t.params.effect===e&&s(h)}),n("transitionEnd",()=>{if(t.params.effect===e&&o){if(!u||!u().slideShadows)return;t.slides.forEach(d=>{d.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(h=>h.remove())}),o()}});let c;n("virtualUpdate",()=>{t.params.effect===e&&(t.slides.length||(c=!0),requestAnimationFrame(()=>{c&&t.slides&&t.slides.length&&(a(),c=!1)}))})}function Ke(i,e){let t=Ee(e);return t!==e&&(t.style.backfaceVisibility="hidden",t.style["-webkit-backface-visibility"]="hidden"),t}function bt(i){let{swiper:e,duration:t,transformElements:n,allSlides:a}=i,{activeIndex:s}=e,r=l=>l.parentElement?l.parentElement:e.slides.filter(u=>u.shadowRoot&&u.shadowRoot===l.parentNode)[0];if(e.params.virtualTranslate&&t!==0){let l=!1,o;a?o=n:o=n.filter(u=>{let c=u.classList.contains("swiper-slide-transform")?r(u):u;return e.getSlideIndex(c)===s}),o.forEach(u=>{_t(u,()=>{if(l||!e||e.destroyed)return;l=!0,e.animating=!1;let c=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0});e.wrapperEl.dispatchEvent(c)})})}}function Cs(i){let{swiper:e,extendParams:t,on:n}=i;t({fadeEffect:{crossFade:!1}}),He({effect:"fade",swiper:e,on:n,setTranslate:()=>{let{slides:r}=e,l=e.params.fadeEffect;for(let o=0;o<r.length;o+=1){let u=e.slides[o],d=-u.swiperSlideOffset;e.params.virtualTranslate||(d-=e.translate);let h=0;e.isHorizontal()||(h=d,d=0);let f=e.params.fadeEffect.crossFade?Math.max(1-Math.abs(u.progress),0):1+Math.min(Math.max(u.progress,-1),0),m=Ke(l,u);m.style.opacity=f,m.style.transform=`translate3d(${d}px, ${h}px, 0px)`}},setTransition:r=>{let l=e.slides.map(o=>Ee(o));l.forEach(o=>{o.style.transitionDuration=`${r}ms`}),bt({swiper:e,duration:r,transformElements:l,allSlides:!0})},overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!e.params.cssMode})})}function ks(i){let{swiper:e,extendParams:t,on:n}=i;t({cubeEffect:{slideShadows:!0,shadow:!0,shadowOffset:20,shadowScale:.94}});let a=(o,u,c)=>{let d=c?o.querySelector(".swiper-slide-shadow-left"):o.querySelector(".swiper-slide-shadow-top"),h=c?o.querySelector(".swiper-slide-shadow-right"):o.querySelector(".swiper-slide-shadow-bottom");d||(d=he("div",`swiper-slide-shadow-cube swiper-slide-shadow-${c?"left":"top"}`.split(" ")),o.append(d)),h||(h=he("div",`swiper-slide-shadow-cube swiper-slide-shadow-${c?"right":"bottom"}`.split(" ")),o.append(h)),d&&(d.style.opacity=Math.max(-u,0)),h&&(h.style.opacity=Math.max(u,0))};He({effect:"cube",swiper:e,on:n,setTranslate:()=>{let{el:o,wrapperEl:u,slides:c,width:d,height:h,rtlTranslate:f,size:m,browser:_}=e,w=vt(e),b=e.params.cubeEffect,v=e.isHorizontal(),g=e.virtual&&e.params.virtual.enabled,y=0,E;b.shadow&&(v?(E=e.wrapperEl.querySelector(".swiper-cube-shadow"),E||(E=he("div","swiper-cube-shadow"),e.wrapperEl.append(E)),E.style.height=`${d}px`):(E=o.querySelector(".swiper-cube-shadow"),E||(E=he("div","swiper-cube-shadow"),o.append(E))));for(let L=0;L<c.length;L+=1){let D=c[L],S=L;g&&(S=parseInt(D.getAttribute("data-swiper-slide-index"),10));let C=S*90,P=Math.floor(C/360);f&&(C=-C,P=Math.floor(-C/360));let A=Math.max(Math.min(D.progress,1),-1),T=0,I=0,O=0;S%4===0?(T=-P*4*m,O=0):(S-1)%4===0?(T=0,O=-P*4*m):(S-2)%4===0?(T=m+P*4*m,O=m):(S-3)%4===0&&(T=-m,O=3*m+m*4*P),f&&(T=-T),v||(I=T,T=0);let V=`rotateX(${w(v?0:-C)}deg) rotateY(${w(v?C:0)}deg) translate3d(${T}px, ${I}px, ${O}px)`;A<=1&&A>-1&&(y=S*90+A*90,f&&(y=-S*90-A*90)),D.style.transform=V,b.slideShadows&&a(D,A,v)}if(u.style.transformOrigin=`50% 50% -${m/2}px`,u.style["-webkit-transform-origin"]=`50% 50% -${m/2}px`,b.shadow)if(v)E.style.transform=`translate3d(0px, ${d/2+b.shadowOffset}px, ${-d/2}px) rotateX(89.99deg) rotateZ(0deg) scale(${b.shadowScale})`;else{let L=Math.abs(y)-Math.floor(Math.abs(y)/90)*90,D=1.5-(Math.sin(L*2*Math.PI/360)/2+Math.cos(L*2*Math.PI/360)/2),S=b.shadowScale,C=b.shadowScale/D,P=b.shadowOffset;E.style.transform=`scale3d(${S}, 1, ${C}) translate3d(0px, ${h/2+P}px, ${-h/2/C}px) rotateX(-89.99deg)`}let N=(_.isSafari||_.isWebView)&&_.needPerspectiveFix?-m/2:0;u.style.transform=`translate3d(0px,0,${N}px) rotateX(${w(e.isHorizontal()?0:y)}deg) rotateY(${w(e.isHorizontal()?-y:0)}deg)`,u.style.setProperty("--swiper-cube-translate-z",`${N}px`)},setTransition:o=>{let{el:u,slides:c}=e;if(c.forEach(d=>{d.style.transitionDuration=`${o}ms`,d.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(h=>{h.style.transitionDuration=`${o}ms`})}),e.params.cubeEffect.shadow&&!e.isHorizontal()){let d=u.querySelector(".swiper-cube-shadow");d&&(d.style.transitionDuration=`${o}ms`)}},recreateShadows:()=>{let o=e.isHorizontal();e.slides.forEach(u=>{let c=Math.max(Math.min(u.progress,1),-1);a(u,c,o)})},getEffectParams:()=>e.params.cubeEffect,perspective:()=>!0,overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,resistanceRatio:0,spaceBetween:0,centeredSlides:!1,virtualTranslate:!0})})}function Qe(i,e,t){let n=`swiper-slide-shadow${t?`-${t}`:""}${i?` swiper-slide-shadow-${i}`:""}`,a=Ee(e),s=a.querySelector(`.${n.split(" ").join(".")}`);return s||(s=he("div",n.split(" ")),a.append(s)),s}function Is(i){let{swiper:e,extendParams:t,on:n}=i;t({flipEffect:{slideShadows:!0,limitRotation:!0}});let a=(o,u)=>{let c=e.isHorizontal()?o.querySelector(".swiper-slide-shadow-left"):o.querySelector(".swiper-slide-shadow-top"),d=e.isHorizontal()?o.querySelector(".swiper-slide-shadow-right"):o.querySelector(".swiper-slide-shadow-bottom");c||(c=Qe("flip",o,e.isHorizontal()?"left":"top")),d||(d=Qe("flip",o,e.isHorizontal()?"right":"bottom")),c&&(c.style.opacity=Math.max(-u,0)),d&&(d.style.opacity=Math.max(u,0))};He({effect:"flip",swiper:e,on:n,setTranslate:()=>{let{slides:o,rtlTranslate:u}=e,c=e.params.flipEffect,d=vt(e);for(let h=0;h<o.length;h+=1){let f=o[h],m=f.progress;e.params.flipEffect.limitRotation&&(m=Math.max(Math.min(f.progress,1),-1));let _=f.swiperSlideOffset,b=-180*m,v=0,g=e.params.cssMode?-_-e.translate:-_,y=0;e.isHorizontal()?u&&(b=-b):(y=g,g=0,v=-b,b=0),f.style.zIndex=-Math.abs(Math.round(m))+o.length,c.slideShadows&&a(f,m);let E=`translate3d(${g}px, ${y}px, 0px) rotateX(${d(v)}deg) rotateY(${d(b)}deg)`,N=Ke(c,f);N.style.transform=E}},setTransition:o=>{let u=e.slides.map(c=>Ee(c));u.forEach(c=>{c.style.transitionDuration=`${o}ms`,c.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(d=>{d.style.transitionDuration=`${o}ms`})}),bt({swiper:e,duration:o,transformElements:u})},recreateShadows:()=>{e.params.flipEffect,e.slides.forEach(o=>{let u=o.progress;e.params.flipEffect.limitRotation&&(u=Math.max(Math.min(o.progress,1),-1)),a(o,u)})},getEffectParams:()=>e.params.flipEffect,perspective:()=>!0,overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!e.params.cssMode})})}function As(i){let{swiper:e,extendParams:t,on:n}=i;t({coverflowEffect:{rotate:50,stretch:0,depth:100,scale:1,modifier:1,slideShadows:!0}}),He({effect:"coverflow",swiper:e,on:n,setTranslate:()=>{let{width:r,height:l,slides:o,slidesSizesGrid:u}=e,c=e.params.coverflowEffect,d=e.isHorizontal(),h=e.translate,f=d?-h+r/2:-h+l/2,m=d?c.rotate:-c.rotate,_=c.depth,w=vt(e);for(let b=0,v=o.length;b<v;b+=1){let g=o[b],y=u[b],E=g.swiperSlideOffset,N=(f-E-y/2)/y,L=typeof c.modifier=="function"?c.modifier(N):N*c.modifier,D=d?m*L:0,S=d?0:m*L,C=-_*Math.abs(L),P=c.stretch;typeof P=="string"&&P.indexOf("%")!==-1&&(P=parseFloat(c.stretch)/100*y);let A=d?0:P*L,T=d?P*L:0,I=1-(1-c.scale)*Math.abs(L);Math.abs(T)<.001&&(T=0),Math.abs(A)<.001&&(A=0),Math.abs(C)<.001&&(C=0),Math.abs(D)<.001&&(D=0),Math.abs(S)<.001&&(S=0),Math.abs(I)<.001&&(I=0);let O=`translate3d(${T}px,${A}px,${C}px)  rotateX(${w(S)}deg) rotateY(${w(D)}deg) scale(${I})`,V=Ke(c,g);if(V.style.transform=O,g.style.zIndex=-Math.abs(Math.round(L))+1,c.slideShadows){let k=d?g.querySelector(".swiper-slide-shadow-left"):g.querySelector(".swiper-slide-shadow-top"),M=d?g.querySelector(".swiper-slide-shadow-right"):g.querySelector(".swiper-slide-shadow-bottom");k||(k=Qe("coverflow",g,d?"left":"top")),M||(M=Qe("coverflow",g,d?"right":"bottom")),k&&(k.style.opacity=L>0?L:0),M&&(M.style.opacity=-L>0?-L:0)}}},setTransition:r=>{e.slides.map(o=>Ee(o)).forEach(o=>{o.style.transitionDuration=`${r}ms`,o.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(u=>{u.style.transitionDuration=`${r}ms`})})},perspective:()=>!0,overwriteParams:()=>({watchSlidesProgress:!0})})}function Ms(i){let{swiper:e,extendParams:t,on:n}=i;t({creativeEffect:{limitProgress:1,shadowPerProgress:!1,progressMultiplier:1,perspective:!0,prev:{translate:[0,0,0],rotate:[0,0,0],opacity:1,scale:1},next:{translate:[0,0,0],rotate:[0,0,0],opacity:1,scale:1}}});let a=l=>typeof l=="string"?l:`${l}px`;He({effect:"creative",swiper:e,on:n,setTranslate:()=>{let{slides:l,wrapperEl:o,slidesSizesGrid:u}=e,c=e.params.creativeEffect,{progressMultiplier:d}=c,h=e.params.centeredSlides,f=vt(e);if(h){let m=u[0]/2-e.params.slidesOffsetBefore||0;o.style.transform=`translateX(calc(50% - ${m}px))`}for(let m=0;m<l.length;m+=1){let _=l[m],w=_.progress,b=Math.min(Math.max(_.progress,-c.limitProgress),c.limitProgress),v=b;h||(v=Math.min(Math.max(_.originalProgress,-c.limitProgress),c.limitProgress));let g=_.swiperSlideOffset,y=[e.params.cssMode?-g-e.translate:-g,0,0],E=[0,0,0],N=!1;e.isHorizontal()||(y[1]=y[0],y[0]=0);let L={translate:[0,0,0],rotate:[0,0,0],scale:1,opacity:1};b<0?(L=c.next,N=!0):b>0&&(L=c.prev,N=!0),y.forEach((I,O)=>{y[O]=`calc(${I}px + (${a(L.translate[O])} * ${Math.abs(b*d)}))`}),E.forEach((I,O)=>{let V=L.rotate[O]*Math.abs(b*d);E[O]=V}),_.style.zIndex=-Math.abs(Math.round(w))+l.length;let D=y.join(", "),S=`rotateX(${f(E[0])}deg) rotateY(${f(E[1])}deg) rotateZ(${f(E[2])}deg)`,C=v<0?`scale(${1+(1-L.scale)*v*d})`:`scale(${1-(1-L.scale)*v*d})`,P=v<0?1+(1-L.opacity)*v*d:1-(1-L.opacity)*v*d,A=`translate3d(${D}) ${S} ${C}`;if(N&&L.shadow||!N){let I=_.querySelector(".swiper-slide-shadow");if(!I&&L.shadow&&(I=Qe("creative",_)),I){let O=c.shadowPerProgress?b*(1/c.limitProgress):b;I.style.opacity=Math.min(Math.max(Math.abs(O),0),1)}}let T=Ke(c,_);T.style.transform=A,T.style.opacity=P,L.origin&&(T.style.transformOrigin=L.origin)}},setTransition:l=>{let o=e.slides.map(u=>Ee(u));o.forEach(u=>{u.style.transitionDuration=`${l}ms`,u.querySelectorAll(".swiper-slide-shadow").forEach(c=>{c.style.transitionDuration=`${l}ms`})}),bt({swiper:e,duration:l,transformElements:o,allSlides:!0})},perspective:()=>e.params.creativeEffect.perspective,overwriteParams:()=>({watchSlidesProgress:!0,virtualTranslate:!e.params.cssMode})})}function Ps(i){let{swiper:e,extendParams:t,on:n}=i;t({cardsEffect:{slideShadows:!0,rotate:!0,perSlideRotate:2,perSlideOffset:8}}),He({effect:"cards",swiper:e,on:n,setTranslate:()=>{let{slides:r,activeIndex:l,rtlTranslate:o}=e,u=e.params.cardsEffect,{startTranslate:c,isTouched:d}=e.touchEventsData,h=o?-e.translate:e.translate;for(let f=0;f<r.length;f+=1){let m=r[f],_=m.progress,w=Math.min(Math.max(_,-4),4),b=m.swiperSlideOffset;e.params.centeredSlides&&!e.params.cssMode&&(e.wrapperEl.style.transform=`translateX(${e.minTranslate()}px)`),e.params.centeredSlides&&e.params.cssMode&&(b-=r[0].swiperSlideOffset);let v=e.params.cssMode?-b-e.translate:-b,g=0,y=-100*Math.abs(w),E=1,N=-u.perSlideRotate*w,L=u.perSlideOffset-Math.abs(w)*.75,D=e.virtual&&e.params.virtual.enabled?e.virtual.from+f:f,S=(D===l||D===l-1)&&w>0&&w<1&&(d||e.params.cssMode)&&h<c,C=(D===l||D===l+1)&&w<0&&w>-1&&(d||e.params.cssMode)&&h>c;if(S||C){let I=(1-Math.abs((Math.abs(w)-.5)/.5))**.5;N+=-28*w*I,E+=-.5*I,L+=96*I,g=`${-25*I*Math.abs(w)}%`}if(w<0?v=`calc(${v}px ${o?"-":"+"} (${L*Math.abs(w)}%))`:w>0?v=`calc(${v}px ${o?"-":"+"} (-${L*Math.abs(w)}%))`:v=`${v}px`,!e.isHorizontal()){let I=g;g=v,v=I}let P=w<0?`${1+(1-E)*w}`:`${1-(1-E)*w}`,A=`
        translate3d(${v}, ${g}, ${y}px)
        rotateZ(${u.rotate?o?-N:N:0}deg)
        scale(${P})
      `;if(u.slideShadows){let I=m.querySelector(".swiper-slide-shadow");I||(I=Qe("cards",m)),I&&(I.style.opacity=Math.min(Math.max((Math.abs(w)-.5)/.5,0),1))}m.style.zIndex=-Math.abs(Math.round(_))+r.length;let T=Ke(u,m);T.style.transform=A}},setTransition:r=>{let l=e.slides.map(o=>Ee(o));l.forEach(o=>{o.style.transitionDuration=`${r}ms`,o.querySelectorAll(".swiper-slide-shadow").forEach(u=>{u.style.transitionDuration=`${r}ms`})}),bt({swiper:e,duration:r,transformElements:l})},perspective:()=>!0,overwriteParams:()=>({watchSlidesProgress:!0,virtualTranslate:!e.params.cssMode})})}var yl=[cs,ds,us,ps,hs,fs,ms,gs,_s,vs,ws,bs,ys,Ss,Ts,Es,xs,Cs,ks,Is,As,Ms,Ps];wt.use(yl);var jt=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","swiperElementNodeName","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopAdditionalSlides","loopAddBlankSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideFullyVisibleClass","slideNextClass","slidePrevClass","slideBlankClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function Nt(i){return typeof i=="object"&&i!==null&&i.constructor&&Object.prototype.toString.call(i).slice(8,-1)==="Object"&&!i.__swiper__}function Qi(i,e){let t=["__proto__","constructor","prototype"];Object.keys(e).filter(n=>t.indexOf(n)<0).forEach(n=>{typeof i[n]>"u"?i[n]=e[n]:Nt(e[n])&&Nt(i[n])&&Object.keys(e[n]).length>0?e[n].__swiper__?i[n]=e[n]:Qi(i[n],e[n]):i[n]=e[n]})}function Ns(i){return i===void 0&&(i={}),i.navigation&&typeof i.navigation.nextEl>"u"&&typeof i.navigation.prevEl>"u"}function Ds(i){return i===void 0&&(i={}),i.pagination&&typeof i.pagination.el>"u"}function Os(i){return i===void 0&&(i={}),i.scrollbar&&typeof i.scrollbar.el>"u"}function Wt(i){return i===void 0&&(i=""),i.replace(/-[a-z]/g,e=>e.toUpperCase().replace("-",""))}function Ls(i){let{swiper:e,slides:t,passedParams:n,changedParams:a,nextEl:s,prevEl:r,scrollbarEl:l,paginationEl:o}=i,u=a.filter(S=>S!=="children"&&S!=="direction"&&S!=="wrapperClass"),{params:c,pagination:d,navigation:h,scrollbar:f,virtual:m,thumbs:_}=e,w,b,v,g,y,E,N,L;a.includes("thumbs")&&n.thumbs&&n.thumbs.swiper&&c.thumbs&&!c.thumbs.swiper&&(w=!0),a.includes("controller")&&n.controller&&n.controller.control&&c.controller&&!c.controller.control&&(b=!0),a.includes("pagination")&&n.pagination&&(n.pagination.el||o)&&(c.pagination||c.pagination===!1)&&d&&!d.el&&(v=!0),a.includes("scrollbar")&&n.scrollbar&&(n.scrollbar.el||l)&&(c.scrollbar||c.scrollbar===!1)&&f&&!f.el&&(g=!0),a.includes("navigation")&&n.navigation&&(n.navigation.prevEl||r)&&(n.navigation.nextEl||s)&&(c.navigation||c.navigation===!1)&&h&&!h.prevEl&&!h.nextEl&&(y=!0);let D=S=>{e[S]&&(e[S].destroy(),S==="navigation"?(e.isElement&&(e[S].prevEl.remove(),e[S].nextEl.remove()),c[S].prevEl=void 0,c[S].nextEl=void 0,e[S].prevEl=void 0,e[S].nextEl=void 0):(e.isElement&&e[S].el.remove(),c[S].el=void 0,e[S].el=void 0))};a.includes("loop")&&e.isElement&&(c.loop&&!n.loop?E=!0:!c.loop&&n.loop?N=!0:L=!0),u.forEach(S=>{if(Nt(c[S])&&Nt(n[S]))Object.assign(c[S],n[S]),(S==="navigation"||S==="pagination"||S==="scrollbar")&&"enabled"in n[S]&&!n[S].enabled&&D(S);else{let C=n[S];(C===!0||C===!1)&&(S==="navigation"||S==="pagination"||S==="scrollbar")?C===!1&&D(S):c[S]=n[S]}}),u.includes("controller")&&!b&&e.controller&&e.controller.control&&c.controller&&c.controller.control&&(e.controller.control=c.controller.control),a.includes("children")&&t&&m&&c.virtual.enabled?(m.slides=t,m.update(!0)):a.includes("virtual")&&m&&c.virtual.enabled&&(t&&(m.slides=t),m.update(!0)),a.includes("children")&&t&&c.loop&&(L=!0),w&&_.init()&&_.update(!0),b&&(e.controller.control=c.controller.control),v&&(e.isElement&&(!o||typeof o=="string")&&(o=document.createElement("div"),o.classList.add("swiper-pagination"),o.part.add("pagination"),e.el.appendChild(o)),o&&(c.pagination.el=o),d.init(),d.render(),d.update()),g&&(e.isElement&&(!l||typeof l=="string")&&(l=document.createElement("div"),l.classList.add("swiper-scrollbar"),l.part.add("scrollbar"),e.el.appendChild(l)),l&&(c.scrollbar.el=l),f.init(),f.updateSize(),f.setTranslate()),y&&(e.isElement&&((!s||typeof s=="string")&&(s=document.createElement("div"),s.classList.add("swiper-button-next"),s.innerHTML=e.hostEl.constructor.nextButtonSvg,s.part.add("button-next"),e.el.appendChild(s)),(!r||typeof r=="string")&&(r=document.createElement("div"),r.classList.add("swiper-button-prev"),r.innerHTML=e.hostEl.constructor.prevButtonSvg,r.part.add("button-prev"),e.el.appendChild(r))),s&&(c.navigation.nextEl=s),r&&(c.navigation.prevEl=r),h.init(),h.update()),a.includes("allowSlideNext")&&(e.allowSlideNext=n.allowSlideNext),a.includes("allowSlidePrev")&&(e.allowSlidePrev=n.allowSlidePrev),a.includes("direction")&&e.changeDirection(n.direction,!1),(E||L)&&e.loopDestroy(),(N||L)&&e.loopCreate(),e.update()}var Hs=i=>{if(parseFloat(i)===Number(i))return Number(i);if(i==="true"||i==="")return!0;if(i==="false")return!1;if(i==="null")return null;if(i!=="undefined"){if(typeof i=="string"&&i.includes("{")&&i.includes("}")&&i.includes('"')){let e;try{e=JSON.parse(i)}catch{e=i}return e}return i}},Rs=["a11y","autoplay","controller","cards-effect","coverflow-effect","creative-effect","cube-effect","fade-effect","flip-effect","free-mode","grid","hash-navigation","history","keyboard","mousewheel","navigation","pagination","parallax","scrollbar","thumbs","virtual","zoom"];function Bn(i,e,t){let n={},a={};Qi(n,Ki);let s=[...jt,"on"],r=s.map(o=>o.replace(/_/,""));s.forEach(o=>{o=o.replace("_",""),typeof i[o]<"u"&&(a[o]=i[o])});let l=[...i.attributes];return typeof e=="string"&&typeof t<"u"&&l.push({name:e,value:Nt(t)?X({},t):t}),l.forEach(o=>{let u=Rs.filter(c=>o.name.indexOf(`${c}-`)===0)[0];if(u){let c=Wt(u),d=Wt(o.name.split(`${u}-`)[1]);typeof a[c]>"u"&&(a[c]={}),a[c]===!0&&(a[c]={enabled:!0}),a[c][d]=Hs(o.value)}else{let c=Wt(o.name);if(!r.includes(c))return;let d=Hs(o.value);a[c]&&Rs.includes(o.name)&&!Nt(d)?(a[c].constructor!==Object&&(a[c]={}),a[c].enabled=!!d):a[c]=d}}),Qi(n,a),n.navigation?n.navigation=X({prevEl:".swiper-button-prev",nextEl:".swiper-button-next"},n.navigation!==!0?n.navigation:{}):n.navigation===!1&&delete n.navigation,n.scrollbar?n.scrollbar=X({el:".swiper-scrollbar"},n.scrollbar!==!0?n.scrollbar:{}):n.scrollbar===!1&&delete n.scrollbar,n.pagination?n.pagination=X({el:".swiper-pagination"},n.pagination!==!0?n.pagination:{}):n.pagination===!1&&delete n.pagination,{params:n,passedParams:a}}var Sl=":host{--swiper-theme-color:#007aff}:host{position:relative;display:block;margin-left:auto;margin-right:auto;z-index:1}.swiper{width:100%;height:100%;margin-left:auto;margin-right:auto;position:relative;overflow:hidden;list-style:none;padding:0;z-index:1;display:block}.swiper-vertical>.swiper-wrapper{flex-direction:column}.swiper-wrapper{position:relative;width:100%;height:100%;z-index:1;display:flex;transition-property:transform;transition-timing-function:var(--swiper-wrapper-transition-timing-function,initial);box-sizing:content-box}.swiper-android ::slotted(swiper-slide),.swiper-ios ::slotted(swiper-slide),.swiper-wrapper{transform:translate3d(0px,0,0)}.swiper-horizontal{touch-action:pan-y}.swiper-vertical{touch-action:pan-x}::slotted(swiper-slide){flex-shrink:0;width:100%;height:100%;position:relative;transition-property:transform;display:block}::slotted(.swiper-slide-invisible-blank){visibility:hidden}.swiper-autoheight,.swiper-autoheight ::slotted(swiper-slide){height:auto}.swiper-autoheight .swiper-wrapper{align-items:flex-start;transition-property:transform,height}.swiper-backface-hidden ::slotted(swiper-slide){transform:translateZ(0);-webkit-backface-visibility:hidden;backface-visibility:hidden}.swiper-3d.swiper-css-mode .swiper-wrapper{perspective:1200px}.swiper-3d .swiper-wrapper{transform-style:preserve-3d}.swiper-3d{perspective:1200px}.swiper-3d .swiper-cube-shadow,.swiper-3d ::slotted(swiper-slide){transform-style:preserve-3d}.swiper-css-mode>.swiper-wrapper{overflow:auto;scrollbar-width:none;-ms-overflow-style:none}.swiper-css-mode>.swiper-wrapper::-webkit-scrollbar{display:none}.swiper-css-mode ::slotted(swiper-slide){scroll-snap-align:start start}.swiper-css-mode.swiper-horizontal>.swiper-wrapper{scroll-snap-type:x mandatory}.swiper-css-mode.swiper-vertical>.swiper-wrapper{scroll-snap-type:y mandatory}.swiper-css-mode.swiper-free-mode>.swiper-wrapper{scroll-snap-type:none}.swiper-css-mode.swiper-free-mode ::slotted(swiper-slide){scroll-snap-align:none}.swiper-css-mode.swiper-centered>.swiper-wrapper::before{content:'';flex-shrink:0;order:9999}.swiper-css-mode.swiper-centered ::slotted(swiper-slide){scroll-snap-align:center center;scroll-snap-stop:always}.swiper-css-mode.swiper-centered.swiper-horizontal ::slotted(swiper-slide):first-child{margin-inline-start:var(--swiper-centered-offset-before)}.swiper-css-mode.swiper-centered.swiper-horizontal>.swiper-wrapper::before{height:100%;min-height:1px;width:var(--swiper-centered-offset-after)}.swiper-css-mode.swiper-centered.swiper-vertical ::slotted(swiper-slide):first-child{margin-block-start:var(--swiper-centered-offset-before)}.swiper-css-mode.swiper-centered.swiper-vertical>.swiper-wrapper::before{width:100%;min-width:1px;height:var(--swiper-centered-offset-after)}.swiper-virtual ::slotted(swiper-slide){-webkit-backface-visibility:hidden;transform:translateZ(0)}.swiper-virtual.swiper-css-mode .swiper-wrapper::after{content:'';position:absolute;left:0;top:0;pointer-events:none}.swiper-virtual.swiper-css-mode.swiper-horizontal .swiper-wrapper::after{height:1px;width:var(--swiper-virtual-size)}.swiper-virtual.swiper-css-mode.swiper-vertical .swiper-wrapper::after{width:1px;height:var(--swiper-virtual-size)}:host{--swiper-navigation-size:44px}.swiper-button-next,.swiper-button-prev{position:absolute;top:var(--swiper-navigation-top-offset,50%);width:calc(var(--swiper-navigation-size)/ 44 * 27);height:var(--swiper-navigation-size);margin-top:calc(0px - (var(--swiper-navigation-size)/ 2));z-index:10;cursor:pointer;display:flex;align-items:center;justify-content:center;color:var(--swiper-navigation-color,var(--swiper-theme-color))}.swiper-button-next.swiper-button-disabled,.swiper-button-prev.swiper-button-disabled{opacity:.35;cursor:auto;pointer-events:none}.swiper-button-next.swiper-button-hidden,.swiper-button-prev.swiper-button-hidden{opacity:0;cursor:auto;pointer-events:none}.swiper-navigation-disabled .swiper-button-next,.swiper-navigation-disabled .swiper-button-prev{display:none!important}.swiper-button-next svg,.swiper-button-prev svg{width:100%;height:100%;object-fit:contain;transform-origin:center}.swiper-rtl .swiper-button-next svg,.swiper-rtl .swiper-button-prev svg{transform:rotate(180deg)}.swiper-button-prev,.swiper-rtl .swiper-button-next{left:var(--swiper-navigation-sides-offset,10px);right:auto}.swiper-button-next,.swiper-rtl .swiper-button-prev{right:var(--swiper-navigation-sides-offset,10px);left:auto}.swiper-button-lock{display:none}.swiper-pagination{position:absolute;text-align:center;transition:.3s opacity;transform:translate3d(0,0,0);z-index:10}.swiper-pagination.swiper-pagination-hidden{opacity:0}.swiper-pagination-disabled>.swiper-pagination,.swiper-pagination.swiper-pagination-disabled{display:none!important}.swiper-horizontal>.swiper-pagination-bullets,.swiper-pagination-bullets.swiper-pagination-horizontal,.swiper-pagination-custom,.swiper-pagination-fraction{bottom:var(--swiper-pagination-bottom,8px);top:var(--swiper-pagination-top,auto);left:0;width:100%}.swiper-pagination-bullets-dynamic{overflow:hidden;font-size:0}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet{transform:scale(.33);position:relative}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active{transform:scale(1)}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-main{transform:scale(1)}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev{transform:scale(.66)}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev{transform:scale(.33)}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next{transform:scale(.66)}.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next-next{transform:scale(.33)}.swiper-pagination-bullet{width:var(--swiper-pagination-bullet-width,var(--swiper-pagination-bullet-size,8px));height:var(--swiper-pagination-bullet-height,var(--swiper-pagination-bullet-size,8px));display:inline-block;border-radius:var(--swiper-pagination-bullet-border-radius,50%);background:var(--swiper-pagination-bullet-inactive-color,#000);opacity:var(--swiper-pagination-bullet-inactive-opacity, .2)}button.swiper-pagination-bullet{border:none;margin:0;padding:0;box-shadow:none;-webkit-appearance:none;appearance:none}.swiper-pagination-clickable .swiper-pagination-bullet{cursor:pointer}.swiper-pagination-bullet:only-child{display:none!important}.swiper-pagination-bullet-active{opacity:var(--swiper-pagination-bullet-opacity, 1);background:var(--swiper-pagination-color,var(--swiper-theme-color))}.swiper-pagination-vertical.swiper-pagination-bullets,.swiper-vertical>.swiper-pagination-bullets{right:var(--swiper-pagination-right,8px);left:var(--swiper-pagination-left,auto);top:50%;transform:translate3d(0px,-50%,0)}.swiper-pagination-vertical.swiper-pagination-bullets .swiper-pagination-bullet,.swiper-vertical>.swiper-pagination-bullets .swiper-pagination-bullet{margin:var(--swiper-pagination-bullet-vertical-gap,6px) 0;display:block}.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic,.swiper-vertical>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic{top:50%;transform:translateY(-50%);width:8px}.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,.swiper-vertical>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet{display:inline-block;transition:.2s transform,.2s top}.swiper-horizontal>.swiper-pagination-bullets .swiper-pagination-bullet,.swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet{margin:0 var(--swiper-pagination-bullet-horizontal-gap,4px)}.swiper-horizontal>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic,.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic{left:50%;transform:translateX(-50%);white-space:nowrap}.swiper-horizontal>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet{transition:.2s transform,.2s left}.swiper-horizontal.swiper-rtl>.swiper-pagination-bullets-dynamic .swiper-pagination-bullet{transition:.2s transform,.2s right}.swiper-pagination-fraction{color:var(--swiper-pagination-fraction-color,inherit)}.swiper-pagination-progressbar{background:var(--swiper-pagination-progressbar-bg-color,rgba(0,0,0,.25));position:absolute}.swiper-pagination-progressbar .swiper-pagination-progressbar-fill{background:var(--swiper-pagination-color,var(--swiper-theme-color));position:absolute;left:0;top:0;width:100%;height:100%;transform:scale(0);transform-origin:left top}.swiper-rtl .swiper-pagination-progressbar .swiper-pagination-progressbar-fill{transform-origin:right top}.swiper-horizontal>.swiper-pagination-progressbar,.swiper-pagination-progressbar.swiper-pagination-horizontal,.swiper-pagination-progressbar.swiper-pagination-vertical.swiper-pagination-progressbar-opposite,.swiper-vertical>.swiper-pagination-progressbar.swiper-pagination-progressbar-opposite{width:100%;height:var(--swiper-pagination-progressbar-size,4px);left:0;top:0}.swiper-horizontal>.swiper-pagination-progressbar.swiper-pagination-progressbar-opposite,.swiper-pagination-progressbar.swiper-pagination-horizontal.swiper-pagination-progressbar-opposite,.swiper-pagination-progressbar.swiper-pagination-vertical,.swiper-vertical>.swiper-pagination-progressbar{width:var(--swiper-pagination-progressbar-size,4px);height:100%;left:0;top:0}.swiper-pagination-lock{display:none}.swiper-scrollbar{border-radius:var(--swiper-scrollbar-border-radius,10px);position:relative;touch-action:none;background:var(--swiper-scrollbar-bg-color,rgba(0,0,0,.1))}.swiper-scrollbar-disabled>.swiper-scrollbar,.swiper-scrollbar.swiper-scrollbar-disabled{display:none!important}.swiper-horizontal>.swiper-scrollbar,.swiper-scrollbar.swiper-scrollbar-horizontal{position:absolute;left:var(--swiper-scrollbar-sides-offset,1%);bottom:var(--swiper-scrollbar-bottom,4px);top:var(--swiper-scrollbar-top,auto);z-index:50;height:var(--swiper-scrollbar-size,4px);width:calc(100% - 2 * var(--swiper-scrollbar-sides-offset,1%))}.swiper-scrollbar.swiper-scrollbar-vertical,.swiper-vertical>.swiper-scrollbar{position:absolute;left:var(--swiper-scrollbar-left,auto);right:var(--swiper-scrollbar-right,4px);top:var(--swiper-scrollbar-sides-offset,1%);z-index:50;width:var(--swiper-scrollbar-size,4px);height:calc(100% - 2 * var(--swiper-scrollbar-sides-offset,1%))}.swiper-scrollbar-drag{height:100%;width:100%;position:relative;background:var(--swiper-scrollbar-drag-bg-color,rgba(0,0,0,.5));border-radius:var(--swiper-scrollbar-border-radius,10px);left:0;top:0}.swiper-scrollbar-cursor-drag{cursor:move}.swiper-scrollbar-lock{display:none}::slotted(.swiper-slide-zoomed){cursor:move;touch-action:none}.swiper .swiper-notification{position:absolute;left:0;top:0;pointer-events:none;opacity:0;z-index:-1000}.swiper-free-mode>.swiper-wrapper{transition-timing-function:ease-out;margin:0 auto}.swiper-grid>.swiper-wrapper{flex-wrap:wrap}.swiper-grid-column>.swiper-wrapper{flex-wrap:wrap;flex-direction:column}.swiper-fade.swiper-free-mode ::slotted(swiper-slide){transition-timing-function:ease-out}.swiper-fade ::slotted(swiper-slide){pointer-events:none;transition-property:opacity}.swiper-fade ::slotted(swiper-slide) ::slotted(swiper-slide){pointer-events:none}.swiper-fade ::slotted(.swiper-slide-active){pointer-events:auto}.swiper-fade ::slotted(.swiper-slide-active) ::slotted(.swiper-slide-active){pointer-events:auto}.swiper-cube{overflow:visible}.swiper-cube ::slotted(swiper-slide){pointer-events:none;-webkit-backface-visibility:hidden;backface-visibility:hidden;z-index:1;visibility:hidden;transform-origin:0 0;width:100%;height:100%}.swiper-cube ::slotted(swiper-slide) ::slotted(swiper-slide){pointer-events:none}.swiper-cube.swiper-rtl ::slotted(swiper-slide){transform-origin:100% 0}.swiper-cube ::slotted(.swiper-slide-active),.swiper-cube ::slotted(.swiper-slide-active) ::slotted(.swiper-slide-active){pointer-events:auto}.swiper-cube ::slotted(.swiper-slide-active),.swiper-cube ::slotted(.swiper-slide-next),.swiper-cube ::slotted(.swiper-slide-prev){pointer-events:auto;visibility:visible}.swiper-cube .swiper-cube-shadow{position:absolute;left:0;bottom:0px;width:100%;height:100%;opacity:.6;z-index:0}.swiper-cube .swiper-cube-shadow:before{content:'';background:#000;position:absolute;left:0;top:0;bottom:0;right:0;filter:blur(50px)}.swiper-cube ::slotted(.swiper-slide-next)+::slotted(swiper-slide){pointer-events:auto;visibility:visible}.swiper-flip{overflow:visible}.swiper-flip ::slotted(swiper-slide){pointer-events:none;-webkit-backface-visibility:hidden;backface-visibility:hidden;z-index:1}.swiper-flip ::slotted(swiper-slide) ::slotted(swiper-slide){pointer-events:none}.swiper-flip ::slotted(.swiper-slide-active),.swiper-flip ::slotted(.swiper-slide-active) ::slotted(.swiper-slide-active){pointer-events:auto}.swiper-creative ::slotted(swiper-slide){-webkit-backface-visibility:hidden;backface-visibility:hidden;overflow:hidden;transition-property:transform,opacity,height}.swiper-cards{overflow:visible}.swiper-cards ::slotted(swiper-slide){transform-origin:center bottom;-webkit-backface-visibility:hidden;backface-visibility:hidden;overflow:hidden}",Tl="::slotted(.swiper-slide-shadow),::slotted(.swiper-slide-shadow-bottom),::slotted(.swiper-slide-shadow-left),::slotted(.swiper-slide-shadow-right),::slotted(.swiper-slide-shadow-top){position:absolute;left:0;top:0;width:100%;height:100%;pointer-events:none;z-index:10}::slotted(.swiper-slide-shadow){background:rgba(0,0,0,.15)}::slotted(.swiper-slide-shadow-left){background-image:linear-gradient(to left,rgba(0,0,0,.5),rgba(0,0,0,0))}::slotted(.swiper-slide-shadow-right){background-image:linear-gradient(to right,rgba(0,0,0,.5),rgba(0,0,0,0))}::slotted(.swiper-slide-shadow-top){background-image:linear-gradient(to top,rgba(0,0,0,.5),rgba(0,0,0,0))}::slotted(.swiper-slide-shadow-bottom){background-image:linear-gradient(to bottom,rgba(0,0,0,.5),rgba(0,0,0,0))}.swiper-lazy-preloader{animation:swiper-preloader-spin 1s infinite linear;width:42px;height:42px;position:absolute;left:50%;top:50%;margin-left:-21px;margin-top:-21px;z-index:10;transform-origin:50%;box-sizing:border-box;border:4px solid var(--swiper-preloader-color,var(--swiper-theme-color));border-radius:50%;border-top-color:transparent}@keyframes swiper-preloader-spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}::slotted(.swiper-slide-shadow-cube.swiper-slide-shadow-bottom),::slotted(.swiper-slide-shadow-cube.swiper-slide-shadow-left),::slotted(.swiper-slide-shadow-cube.swiper-slide-shadow-right),::slotted(.swiper-slide-shadow-cube.swiper-slide-shadow-top){z-index:0;-webkit-backface-visibility:hidden;backface-visibility:hidden}::slotted(.swiper-slide-shadow-flip.swiper-slide-shadow-bottom),::slotted(.swiper-slide-shadow-flip.swiper-slide-shadow-left),::slotted(.swiper-slide-shadow-flip.swiper-slide-shadow-right),::slotted(.swiper-slide-shadow-flip.swiper-slide-shadow-top){z-index:0;-webkit-backface-visibility:hidden;backface-visibility:hidden}::slotted(.swiper-zoom-container){width:100%;height:100%;display:flex;justify-content:center;align-items:center;text-align:center}::slotted(.swiper-zoom-container)>canvas,::slotted(.swiper-zoom-container)>img,::slotted(.swiper-zoom-container)>svg{max-width:100%;max-height:100%;object-fit:contain}",zn=class{},zs=typeof window>"u"||typeof HTMLElement>"u"?zn:HTMLElement,Bs=`<svg width="11" height="20" viewBox="0 0 11 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0.38296 20.0762C0.111788 19.805 0.111788 19.3654 0.38296 19.0942L9.19758 10.2796L0.38296 1.46497C0.111788 1.19379 0.111788 0.754138 0.38296 0.482966C0.654131 0.211794 1.09379 0.211794 1.36496 0.482966L10.4341 9.55214C10.8359 9.9539 10.8359 10.6053 10.4341 11.007L1.36496 20.0762C1.09379 20.3474 0.654131 20.3474 0.38296 20.0762Z" fill="currentColor"/></svg>
    `,$s=(i,e)=>{if(typeof CSSStyleSheet<"u"&&i.adoptedStyleSheets){let t=new CSSStyleSheet;t.replaceSync(e),i.adoptedStyleSheets=[t]}else{let t=document.createElement("style");t.rel="stylesheet",t.textContent=e,i.appendChild(t)}},Zi=class extends zs{constructor(){super(),this.attachShadow({mode:"open"})}static get nextButtonSvg(){return Bs}static get prevButtonSvg(){return Bs.replace("/></svg>",' transform-origin="center" transform="rotate(180)"/></svg>')}cssStyles(){return[Sl,...this.injectStyles&&Array.isArray(this.injectStyles)?this.injectStyles:[]].join(`
`)}cssLinks(){return this.injectStylesUrls||[]}calcSlideSlots(){let e=this.slideSlots||0,t=[...this.querySelectorAll("[slot^=slide-]")].map(n=>parseInt(n.getAttribute("slot").split("slide-")[1],10));if(this.slideSlots=t.length?Math.max(...t)+1:0,!!this.rendered){if(this.slideSlots>e)for(let n=e;n<this.slideSlots;n+=1){let a=document.createElement("swiper-slide");a.setAttribute("part",`slide slide-${n+1}`);let s=document.createElement("slot");s.setAttribute("name",`slide-${n+1}`),a.appendChild(s),this.shadowRoot.querySelector(".swiper-wrapper").appendChild(a)}else if(this.slideSlots<e){let n=this.swiper.slides;for(let a=n.length-1;a>=0;a-=1)a>this.slideSlots&&n[a].remove()}}}render(){if(this.rendered)return;this.calcSlideSlots();let e=this.cssStyles();this.slideSlots>0&&(e=e.replace(/::slotted\(([a-z-0-9.]*)\)/g,"$1")),e.length&&$s(this.shadowRoot,e),this.cssLinks().forEach(n=>{if(this.shadowRoot.querySelector(`link[href="${n}"]`))return;let s=document.createElement("link");s.rel="stylesheet",s.href=n,this.shadowRoot.appendChild(s)});let t=document.createElement("div");t.classList.add("swiper"),t.part="container",t.innerHTML=`
      <slot name="container-start"></slot>
      <div class="swiper-wrapper" part="wrapper">
        <slot></slot>
        ${Array.from({length:this.slideSlots}).map((n,a)=>`
        <swiper-slide part="slide slide-${a}">
          <slot name="slide-${a}"></slot>
        </swiper-slide>
        `).join("")}
      </div>
      <slot name="container-end"></slot>
      ${Ns(this.passedParams)?`
        <div part="button-prev" class="swiper-button-prev">${this.constructor.prevButtonSvg}</div>
        <div part="button-next" class="swiper-button-next">${this.constructor.nextButtonSvg}</div>
      `:""}
      ${Ds(this.passedParams)?`
        <div part="pagination" class="swiper-pagination"></div>
      `:""}
      ${Os(this.passedParams)?`
        <div part="scrollbar" class="swiper-scrollbar"></div>
      `:""}
    `,this.shadowRoot.appendChild(t),this.rendered=!0}initialize(){var e=this;if(this.initialized)return;this.initialized=!0;let{params:t,passedParams:n}=Bn(this);this.swiperParams=t,this.passedParams=n,delete this.swiperParams.init,this.render(),this.swiper=new wt(this.shadowRoot.querySelector(".swiper"),ie(X(X({},t.virtual?{}:{observer:!0}),t),{touchEventsTarget:"container",onAny:function(a){a==="observerUpdate"&&e.calcSlideSlots();let s=t.eventsPrefix?`${t.eventsPrefix}${a.toLowerCase()}`:a.toLowerCase();for(var r=arguments.length,l=new Array(r>1?r-1:0),o=1;o<r;o++)l[o-1]=arguments[o];let u=new CustomEvent(s,{detail:l,bubbles:a!=="hashChange",cancelable:!0});e.dispatchEvent(u)}}))}connectedCallback(){this.initialized&&this.nested&&this.closest("swiper-slide")&&this.closest("swiper-slide").swiperLoopMoveDOM||this.init===!1||this.getAttribute("init")==="false"||this.initialize()}disconnectedCallback(){this.nested&&this.closest("swiper-slide")&&this.closest("swiper-slide").swiperLoopMoveDOM||(this.swiper&&this.swiper.destroy&&this.swiper.destroy(),this.initialized=!1)}updateSwiperOnPropChange(e,t){let{params:n,passedParams:a}=Bn(this,e,t);this.passedParams=a,this.swiperParams=n,!(this.swiper&&this.swiper.params[e]===t)&&Ls(X(X(X({swiper:this.swiper,passedParams:this.passedParams,changedParams:[Wt(e)]},e==="navigation"&&a[e]?{prevEl:".swiper-button-prev",nextEl:".swiper-button-next"}:{}),e==="pagination"&&a[e]?{paginationEl:".swiper-pagination"}:{}),e==="scrollbar"&&a[e]?{scrollbarEl:".swiper-scrollbar"}:{}))}attributeChangedCallback(e,t,n){this.initialized&&(t==="true"&&n===null&&(n=!1),this.updateSwiperOnPropChange(e,n))}static get observedAttributes(){return jt.filter(t=>t.includes("_")).map(t=>t.replace(/[A-Z]/g,n=>`-${n}`).replace("_","").toLowerCase())}};jt.forEach(i=>{i!=="init"&&(i=i.replace("_",""),Object.defineProperty(Zi.prototype,i,{configurable:!0,get(){return(this.passedParams||{})[i]},set(e){this.passedParams||(this.passedParams={}),this.passedParams[i]=e,this.initialized&&this.updateSwiperOnPropChange(i,e)}}))});var $n=class extends zs{constructor(){super(),this.attachShadow({mode:"open"})}render(){let e=this.lazy||this.getAttribute("lazy")===""||this.getAttribute("lazy")==="true";if($s(this.shadowRoot,Tl),this.shadowRoot.appendChild(document.createElement("slot")),e){let t=document.createElement("div");t.classList.add("swiper-lazy-preloader"),t.part.add("preloader"),this.shadowRoot.appendChild(t)}}initialize(){this.render()}connectedCallback(){this.initialize()}},qs=()=>{typeof window>"u"||(window.customElements.get("swiper-container")||window.customElements.define("swiper-container",Zi),window.customElements.get("swiper-slide")||window.customElements.define("swiper-slide",$n))};typeof window<"u"&&(window.SwiperElementRegisterParams=i=>{jt.push(...i)});var Fs={[p.dang_nhap]:null,[p.quen_mat_khau]:[x.Setting,x.ChangePassword],[p.trang_chu]:null,[p.cai_dat_giao_dien]:null,[p.danh_sach_tim_kiem]:null,[p.lai_suat]:null,[p.ty_gia]:null,[p.atm_chi_nhanh]:null,[p.dang_ky_truc_tuyen]:null,[p.dang_ki_goi_dv]:null,[p.email_thong_bao]:null,[p.doi_han_muc]:[x.Setting,x.SettingTransactionLimit,x.SettingChangeTransactionLimit],[p.han_muc_giao_dich]:[x.Setting,x.SettingTransactionLimit],[p.tra_soat_khieu_nai]:[x.SupportComplaintTracking,x.SupportComplaintCreateTracking],[p.danh_sach_tra_soat_khieu_nai]:[x.SupportComplaintTracking],[p.gop_y_dich_vu]:[x.SupportFeedback],[p.bao_cao_giao_dich]:[x.TransactionReport],[p.lich_su_giao_dich]:null,[p.quan_ly_ds_tai_khoan_giao_thong]:null,[p.lien_ket_tai_khoan_giao_thong]:null,[p.danh_sach_tuy_chinh]:[x.Setting,x.SettingCustomize],[p.huong_dan_su_dung_dich_vu]:null,[p.cau_hoi_thuong_gap]:null,[p.danh_ba_thu_huong]:[x.Setting,x.Directories],[p.mau_chuyen_tien]:[x.Setting,x.Directories],[p.danh_ba_thu_huong_chuyen_tien_quoc_te]:[x.Setting,x.Directories],[p.chuyen_tien_noi_bo]:[x.Transfer],[p.chuyen_tien_ngoai_stk]:[x.Transfer],[p.chuyen_tien_ngoai_so_the]:[x.Transfer],[p.chuyen_tien]:[x.Transfer],[p.chuyen_tien_dinh_ky]:[x.Transfer,x.TransferPeriodic],[p.dat_lenh_chuyen_tien_dinh_ky]:[x.Transfer,x.TransferPeriodic,x.CreatePeriodicTransfer],[p.chuyen_tien_quoc_te]:[x.InternationalTransfer],[p.tao_dien_chuyen_tien_quoc_te]:[x.InternationalTransfer,x.InternationalTransaction],[p.tham_khao_ti_gia_va_phi]:[x.InternationalTransfer,x.ReferenceExchangeRateAndFee],[p.ctqt_danh_ba_thu_huong]:[x.InternationalTransfer,x.InternationalTransferContact],[p.thanh_toan_hoa_don_quoc_te]:[x.InternationalTransfer,x.InternationalPaymentBill],[p.danh_sach_tai_khoan]:[x.Account],[p.tai_khoan_thanh_toan]:[x.Account],[p.tai_khoan_tien_vay]:[x.Account],[p.tai_khoan_tiet_kiem]:[x.Account],[p.dich_vu_the]:[x.CardService],[p.chuyen_doi_the_chip]:null,[p.phat_hanh_the]:[x.CardService,x.IssuanceOnline,x.DebitDomestic],[p.phat_hanh_the_truc_tuyen_the_ghi_no_quoc_te]:[x.CardService,x.IssuanceOnline,x.DebitInternational],[p.phat_hanh_the_truc_tuyen_the_tin_dung_quoc_te]:[x.CardService,x.IssuanceOnline,x.CreditInternational],[p.phat_hanh_the_truc_tuyen_the_tra_truoc_quoc_te]:[x.CardService,x.IssuanceOnline,x.CardPrepaidInternationalIssuance],[p.lien_ket_the_de_thanh_toan_vien_phi]:null,[p.them_lien_ket_the]:null,[p.quan_ly_the_da_lien_ket_va_huy_lien_ket_the_y_te_phi_vat_ly]:null,[p.phat_hanh_the_tin_dung_tu_dong]:null,[p.tra_gop_the_tin_dung]:[x.CardService],[p.thanh_toan_the_tin_dung]:[x.CardService,x.CardPaymentCredit],[p.thanh_toan_sao_ke]:[x.CardService,x.CardPaymentCredit],[p.tai_khoan_so_dep]:[x.Account,x.Desired,x.SearchNiceNumberAccount],[p.tai_khoan_dinh_danh]:[x.Account,x.Desired,x.ListAccountsNickname],[p.mo_tai_khoan_dinh_danh]:[x.Account,x.Desired,x.OpenChooseNameAccount],[p.tai_khoan_nhu_y]:[x.Account,x.Desired],[p.cai_dat]:null,[p.doi_mat_khau]:[x.Setting,x.ChangePassword],[p.BIDV_home]:null,[p.vay_cam_co]:null,[p.vay_cam_co_online]:null,[p.vay_nhanh]:null,[p.theo_doi_no_vay]:null,[p.tiet_kiem]:[x.Saving],[p.danh_muc_sp_tiet_kiem]:[x.Saving],[p.thay_doi_thong_tin_tk_tich_luy]:[x.Account],[p.gui_tien_online]:[x.Saving],[p.mo_tk_tich_luy]:[x.Saving,x.SavingAccumulateTarget],[p.tiet_kiem_tu_dong]:[x.Saving,x.SavingAutomatic],[p.cham_dut_san_pham_tiet_kiem_tu_dong]:[x.Saving,x.SavingAutomatic],[p.van_tin_san_pham_tiet_kiem_tu_dong]:[x.Saving,x.SavingAutomatic],[p.rut_tich_luy_online]:[x.Account,x.SavingWithdrawAccumulateOnline],[p.gui_tich_luy_online]:[x.Account,x.SavingAccumulateAdd],[p.rut_tiet_kiem_online]:[x.Saving,x.SavingOnline,x.SavingWithdrawOnline],[p.bao_hiem]:[x.InsuranceInvestment,x.InsuranceService],[p.chung_chi_quy_mo]:null,[p.chung_khoan]:[x.InsuranceInvestment,x.Stock],[p.trai_phieu]:null,[p.ve_may_bay]:null,[p.khach_san]:null,[p.ve_xe]:null,[p.ve_tau]:null,[p.vnpay_taxi]:null,[p.giao_hang]:null,[p.dat_hoa]:null,[p.vnshop]:null,[p.ve_xem_phim]:null,[p.mua_sam_hoan_tien]:null,[p.ve_su_kien]:null,[p.data_4g]:null,[p.voucher_deal_today]:null,[p.vietlott_sms]:null,[p.dich_vu_golf]:null,[p.uu_dai_cua_ban]:null,[p.the_game]:null,[p.ban_ngoai_te]:null,[p.mua_ngoai_te_tien_mat]:null,[p.nop_phi_truoc_ba]:[x.RegistrationFeePayment],[p.nop_phi_ha_tang_cang_bien]:[x.PayFeeSeaport],[p.tao_lenh_nop_phi_ha_tang_cang_bien]:[x.PayFeeSeaport],[p.nop_thue]:[x.PayTax],[p.nop_thue_theo_mst]:[x.PayTax],[p.chia_se_bien_dong_so_du]:null,[p.quan_ly_cua_hang]:[x.StoreManagement],[p.quan_ly_qr_shop]:[x.StoreManagement],[p.tao_moi_qr_shop]:[x.StoreManagement,x.StoreCreateQR],[p.tao_moi_qr_don_hang]:[x.StoreManagement,x.StoreCreateOrder],[p.nap_tien_dt]:[x.Topup],[p.vi_dien_tu]:null,[p.danh_sach_dich_vu]:null,[p.smart_kids]:null,[p.chuyen_tien_ung_ho]:[x.CharityGift],[p.chuyen_tien_tu_thien]:[x.CharityGift],[p.tang_qua]:null,[p.ung_ho_quy_vac_xin]:null,[p.quan_ly_tai_chinh_ca_nhan]:null,[p.quan_ly_dac_quyen]:null,[p.game_green_mission]:null,[p.gioi_thieu_ban]:null,[p.thanh_toan_hoa_don]:[x.Payment],[p.quan_ly_mau_thanh_toan]:[x.Payment,x.PaymentTemplate],[p.danh_sach_thanh_toan_hoa_don_nhom_thanh_toan_hoa_don]:[x.Payment],[p.thanh_toan_vien_phi]:null,[p.tai_khoan]:[x.Account],[p.thanh_toan_truyen_hinh]:null,[p.thanh_toan_hoa_don_dinh_ky]:[x.Payment,x.PaymentRecurring],[p.thanh_toan_tu_dong]:[x.Payment,x.PaymentRecurring],[p.tao_thanh_toan_hoa_don_dinh_ky]:[x.Payment,x.PaymentRecurring,x.PaymentRecurringSubcribe],[p.reward]:[x.Rewards],[p.doi_qua]:[x.Rewards,x.RewardsMyGift]},Xp=[p.dang_nhap,p.cai_dat,p.quen_mat_khau,p.doi_mat_khau,p.trang_chu,p.danh_sach_dich_vu,p.tai_khoan,p.lich_su_giao_dich,p.tao_ma_qr,p.lich_su_giao_dich_qr,p.them_lien_ket_the,p.thanh_toan_vien_phi,p.quan_ly_the_da_lien_ket_va_huy_lien_ket_the_y_te_phi_vat_ly,p.dang_ky_dich_vu_ott,p.mua_ngoai_te_tien_mat,p.van_tin_giao_dich_mua_ngoai_te,p.danh_sach_tim_kiem,p.lich_su_gioi_thieu,p.tien_vay_tra_vo_khoan_vay,p.ban_ngoai_te,p.thanh_toan_hoa_don,p.quet_ma_qr,p.tang_qua,p.ve_tau,p.ve_xe,p.ve_xem_phim,p.khach_san,p.vnshop,p.ve_may_bay,p.lich_su_qua_da_tang_da_nhan,p.dang_ky_truc_tuyen,p.rut_tien_mat_tai_atm,p.dang_ki_goi_dv,p.email_thong_bao,p.gioi_thieu_ban,p.quan_ly_tai_chinh_ca_nhan,p.chuyen_doi_the_chip,p.lien_ket_the_de_thanh_toan_vien_phi,p.vay_cam_co_online,p.cham_dut_han_muc_thau_chi,p.cai_dat_dang_nhap,p.vay_nhanh,p.vay_cam_co,p.ung_ho_quy_vac_xin,p.nap_tien_the_thanh_toan_vien_phi,p.tien_vay_danh_sach_chuc_nang,p.dat_san_golf,p.vnpay_taxi,p.giao_hang,p.dat_hoa,p.dang_ky_qua_ekyc,p.dang_ky_cho_KH_da_co_tk_thanh_toan,p.smart_watch,p.han_muc_giao_dich,p.thong_bao_ott,p.cau_hoi_thuong_gap,p.bo_sung_chung_tu_mua_ngoai_te,p.cap_nhat_giao_dich_mua_ngoai_te,p.vi_dien_tu,p.tao_lien_ket_vi_dien_tu,p.cap_nhat_lien_ket_vi_dien_tu,p.nap_tien_vi_dien_tu,p.lien_ket_tai_khoan_giao_thong,p.cai_dat,p.cai_dat_van_tay,p.smart_kids,p.dang_ky_smart_kids,p.thong_tin_smart_kids,p.lich_su_giao_dich_smart_kids,p.dong_mo_cif,p.cai_dat_sinh_trac_hoc,p.ve_su_kien,p.chat_bot,p.game_green_mission,p.BIDV_home,p.phat_hanh_the_tin_dung_tu_dong,p.mua_sam_hoan_tien,p.chia_se_bien_dong_so_du,p.dau_an_nam,p.chung_chi_quy_mo,p.apple_pay,p.quan_ly_dac_quyen,p.tong_dai_cskh,p.xac_thuc_giao_dich_tren_cong,p.trai_phieu,p.dat_mua_trai_phieu,p.cap_nhap_thong_tin_nha_dau_tu,p.cai_dat_widget,p.vay_ung_luong,p.theo_doi_no_vay,p.data_4g,p.voucher_deal_today,p.uu_dai_cua_ban,p.ung_ho_khac_phuc_hau_qua_thien_tai,p.cai_dat_smart_otp,p.vietlott_sms,p.dich_vu_golf,p.the_game,p.dinh_danh_the,p.chuyen_nhuong_online,p.tao_yc_chuyen_nhuong_dich_danh,p.tao_yc_chuyen_nhuong_tu_do,p.mua_tai_khoan_tien_gui,p.quan_ly_giao_dich,p.mua_the_dien_thoai_imedia,p.bidv_securities,p.my_rm,p.lien_ket_bidv_securities,p.huy_lien_ket_bidv_securities,p.mo_tai_khoan_chung_khoan,p.trang_chu_tien_ich_cuoc_song,p.danh_muc_san_pham,p.menu_theo_doi_no_vay,p.menu_tra_no_vay,p.webview_hst,p.quan_ly_ngan_sach,p.quan_ly_vi,p.danh_muc_sp_tiet_kiem,p.danh_muc_sp_vay_von,p.danh_muc_sp_qltc,p.danh_muc_sp_tien_ich_cs,p.danh_muc_sp_dv_cong,p.danh_muc_sp_thanh_toan,p.danh_muc_sp_dv_tieu_thuong,p.danh_muc_sp_dv_ngoai_te,p.danh_muc_sp_bao_hiem_dau_tu,p.danh_muc_sp_thien_nguyen,p.danh_muc_sp_dac_quyen,p.danh_muc_sp_smart_kids,p.quan_ly_ds_tai_khoan_giao_thong,p.lich_su_dat_mua_trai_phieu],Vs=[p.dang_nhap,p.cai_dat,p.quen_mat_khau,p.doi_mat_khau,p.trang_chu,p.danh_sach_dich_vu,p.tai_khoan,p.lich_su_giao_dich,p.danh_sach_tim_kiem,p.dang_ky_truc_tuyen,p.email_thong_bao,p.ung_ho_quy_vac_xin,p.dat_san_golf,p.dang_ky_qua_ekyc,p.tao_ma_qr,p.cai_dat_widget,p.chuyen_doi_the_chip,p.chia_se_bien_dong_so_du,p.dinh_danh_the,p.the_game,p.danh_muc_san_pham,p.danh_muc_sp_tiet_kiem,p.lich_su_dat_mua_trai_phieu];var ta=mn(gr());var El="2.0.0",Jt="",Gs="?",an="function",Tt="undefined",ei="object",jn="string",Ei="major",$="model",R="name",H="type",q="vendor",z="version",xe="architecture",Kt="console",J="mobile",ue="tablet",_e="smarttv",Si="wearable",qn="xr",Wn="embedded",mi="inapp",Us="user-agent",Xn=500,Zn="brands",Ot="formFactors",Jn="fullVersionList",Qt="platform",ea="platformVersion",rn="bitness",Et="sec-ch-ua",xl=Et+"-full-version-list",Cl=Et+"-arch",kl=Et+"-"+rn,Il=Et+"-form-factors",Al=Et+"-"+J,Ml=Et+"-"+$,rr=Et+"-"+Qt,Pl=rr+"-version",or=[Zn,Jn,J,$,Qt,ea,xe,Ot,rn],Me="browser",st="cpu",Ze="device",rt="engine",Re="os",Zt="result",Ji="Amazon",gi="Apple",Ys="ASUS",js="BlackBerry",Dt="Google",Ws="Huawei",Xs="Lenovo",Nl="Honor",Fn="LG",nn="Microsoft",Ks="Motorola",_i="Samsung",Qs="Sharp",en="Sony",Vn="Xiaomi",Gn="Zebra",Xt="Mobile ",vi=" Browser",Zs="Chrome",yt="Chromecast",Dl="Edge",wi="Firefox",bi="Opera",Js="Facebook",er="Sogou",Kn="Windows",Ol=typeof window!==Tt,Ce=Ol&&window.navigator?window.navigator:void 0,St=Ce&&Ce.userAgentData?Ce.userAgentData:void 0,Ll=function(i,e){var t={},n=e;if(!sn(e)){n={};for(var a in e)for(var s in e[a])n[s]=e[a][s].concat(n[s]?n[s]:[])}for(var r in i)t[r]=n[r]&&n[r].length%2===0?n[r].concat(i[r]):i[r];return t},on=function(i){for(var e={},t=0;t<i.length;t++)e[i[t].toUpperCase()]=i[t];return e},Qn=function(i,e){if(typeof i===ei&&i.length>0){for(var t in i)if(ot(i[t])==ot(e))return!0;return!1}return ti(i)?ot(e).indexOf(ot(i))!==-1:!1},sn=function(i,e){for(var t in i)return/^(browser|cpu|device|engine|os)$/.test(t)||(e?sn(i[t]):!1)},ti=function(i){return typeof i===jn},Un=function(i){if(i){for(var e=[],t=Lt(/\\?\"/g,i).split(","),n=0;n<t.length;n++)if(t[n].indexOf(";")>-1){var a=xi(t[n]).split(";v=");e[n]={brand:a[0],version:a[1]}}else e[n]=xi(t[n]);return e}},ot=function(i){return ti(i)?i.toLowerCase():i},Yn=function(i){return ti(i)?Lt(/[^\d\.]/g,i).split(".")[0]:void 0},lt=function(i){for(var e in i){var t=i[e];typeof t==ei&&t.length==2?this[t[0]]=t[1]:this[t]=void 0}return this},Lt=function(i,e){return ti(e)?e.replace(i,Jt):e},yi=function(i){return Lt(/\\?\"/g,i)},xi=function(i,e){if(ti(i))return i=Lt(/^\s\s*/,i),typeof e===Tt?i:i.substring(0,Xn)},tr=function(i,e){if(!(!i||!e))for(var t=0,n,a,s,r,l,o;t<e.length&&!l;){var u=e[t],c=e[t+1];for(n=a=0;n<u.length&&!l&&u[n];)if(l=u[n++].exec(i),l)for(s=0;s<c.length;s++)o=l[++a],r=c[s],typeof r===ei&&r.length>0?r.length===2?typeof r[1]==an?this[r[0]]=r[1].call(this,o):this[r[0]]=r[1]:r.length===3?typeof r[1]===an&&!(r[1].exec&&r[1].test)?this[r[0]]=o?r[1].call(this,o,r[2]):void 0:this[r[0]]=o?o.replace(r[1],r[2]):void 0:r.length===4&&(this[r[0]]=o?r[3].call(this,o.replace(r[1],r[2])):void 0):this[r]=o||void 0;t+=2}},Ti=function(i,e){for(var t in e)if(typeof e[t]===ei&&e[t].length>0){for(var n=0;n<e[t].length;n++)if(Qn(e[t][n],i))return t===Gs?void 0:t}else if(Qn(e[t],i))return t===Gs?void 0:t;return e.hasOwnProperty("*")?e["*"]:i},ir={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2","8.1":"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},nr={embedded:"Automotive",mobile:"Mobile",tablet:["Tablet","EInk"],smarttv:"TV",wearable:"Watch",xr:["VR","XR"],"?":["Desktop","Unknown"],"*":void 0},ar={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[z,[R,Xt+"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[z,[R,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[R,z],[/opios[\/ ]+([\w\.]+)/i],[z,[R,bi+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[z,[R,bi+" GX"]],[/\bopr\/([\w\.]+)/i],[z,[R,bi]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[z,[R,"Baidu"]],[/\b(?:mxbrowser|mxios|myie2)\/?([-\w\.]*)\b/i],[z,[R,"Maxthon"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer|sleipnir)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim(?:browser|boat|jet))[\/ ]?([\d\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|duckduckgo|klar|helio|(?=comodo_)?dragon)\/([-\w\.]+)/i,/(heytap|ovi|115)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[R,z],[/quark(?:pc)?\/([-\w\.]+)/i],[z,[R,"Quark"]],[/\bddg\/([\w\.]+)/i],[z,[R,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[z,[R,"UCBrowser"]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[z,[R,"WeChat"]],[/konqueror\/([\w\.]+)/i],[z,[R,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[z,[R,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[z,[R,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[z,[R,"Smart "+Xs+vi]],[/(avast|avg)\/([\w\.]+)/i],[[R,/(.+)/,"$1 Secure"+vi],z],[/\bfocus\/([\w\.]+)/i],[z,[R,wi+" Focus"]],[/\bopt\/([\w\.]+)/i],[z,[R,bi+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[z,[R,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[z,[R,"Dolphin"]],[/coast\/([\w\.]+)/i],[z,[R,bi+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[z,[R,"MIUI"+vi]],[/fxios\/([\w\.-]+)/i],[z,[R,Xt+wi]],[/\bqihoobrowser\/?([\w\.]*)/i],[z,[R,"360"]],[/\b(qq)\/([\w\.]+)/i],[[R,/(.+)/,"$1Browser"],z],[/(oculus|sailfish|huawei|vivo|pico)browser\/([\w\.]+)/i],[[R,/(.+)/,"$1"+vi],z],[/samsungbrowser\/([\w\.]+)/i],[z,[R,_i+" Internet"]],[/metasr[\/ ]?([\d\.]+)/i],[z,[R,er+" Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[R,er+" Mobile"],z],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345(?=browser|chrome|explorer))\w*[\/ ]?v?([\w\.]+)/i],[R,z],[/(lbbrowser|rekonq)/i],[R],[/ome\/([\w\.]+) \w* ?(iron) saf/i,/ome\/([\w\.]+).+qihu (360)[es]e/i],[z,R],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[R,Js],z,[H,mi]],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(instagram|snapchat)[\/ ]([-\w\.]+)/i],[R,z,[H,mi]],[/\bgsa\/([\w\.]+) .*safari\//i],[z,[R,"GSA"],[H,mi]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[z,[R,"TikTok"],[H,mi]],[/\[(linkedin)app\]/i],[R,[H,mi]],[/(chromium)[\/ ]([-\w\.]+)/i],[R,z],[/headlesschrome(?:\/([\w\.]+)| )/i],[z,[R,Zs+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[R,Zs+" WebView"],z],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[z,[R,"Android"+vi]],[/chrome\/([\w\.]+) mobile/i],[z,[R,Xt+"Chrome"]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[R,z],[/version\/([\w\.\,]+) .*mobile(?:\/\w+ | ?)safari/i],[z,[R,Xt+"Safari"]],[/iphone .*mobile(?:\/\w+ | ?)safari/i],[[R,Xt+"Safari"]],[/version\/([\w\.\,]+) .*(safari)/i],[z,R],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[R,[z,"1"]],[/(webkit|khtml)\/([\w\.]+)/i],[R,z],[/(?:mobile|tablet);.*(firefox)\/([\w\.-]+)/i],[[R,Xt+wi],z],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[R,"Netscape"],z],[/(wolvic|librewolf)\/([\w\.]+)/i],[R,z],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[z,[R,wi+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/\b(links) \(([\w\.]+)/i],[R,[z,/_/g,"."]],[/(cobalt)\/([\w\.]+)/i],[R,[z,/[^\d\.]+./,Jt]]],cpu:[[/\b(?:(amd|x|x86[-_]?|wow|win)64)\b/i],[[xe,"amd64"]],[/(ia32(?=;))/i,/((?:i[346]|x)86)[;\)]/i],[[xe,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[xe,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[xe,"armhf"]],[/windows (ce|mobile); ppc;/i],[[xe,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[xe,/ower/,Jt,ot]],[/(sun4\w)[;\)]/i],[[xe,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[xe,ot]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[$,[q,_i],[H,ue]],[/\b((?:s[cgp]h|gt|sm)-(?![lr])\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]((?!sm-[lr])[-\w]+)/i,/sec-(sgh\w+)/i],[$,[q,_i],[H,J]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[$,[q,gi],[H,J]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[$,[q,gi],[H,ue]],[/(macintosh);/i],[$,[q,gi]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[$,[q,Qs],[H,J]],[/(?:honor)([-\w ]+)[;\)]/i],[$,[q,Nl],[H,J]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[$,[q,Ws],[H,ue]],[/(?:huawei)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[$,[q,Ws],[H,J]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite|pro)?)(?: bui|\))/i],[[$,/_/g," "],[q,Vn],[H,J]],[/oid[^\)]+; (2\d{4}(283|rpbf)[cgl])( bui|\))/i,/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[$,/_/g," "],[q,Vn],[H,ue]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[$,[q,"OPPO"],[H,J]],[/\b(opd2\d{3}a?) bui/i],[$,[q,"OPPO"],[H,ue]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[$,[q,"Vivo"],[H,J]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[$,[q,"Realme"],[H,J]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[$,[q,Ks],[H,J]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[$,[q,Ks],[H,ue]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[$,[q,Fn],[H,ue]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[$,[q,Fn],[H,J]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[$,[q,Xs],[H,ue]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[$,/_/g," "],[q,"Nokia"],[H,J]],[/(pixel c)\b/i],[$,[q,Dt],[H,ue]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[$,[q,Dt],[H,J]],[/droid.+; (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[$,[q,en],[H,J]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[$,"Xperia Tablet"],[q,en],[H,ue]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[$,[q,"OnePlus"],[H,J]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo(?!bc)\w\w)( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[$,[q,Ji],[H,ue]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[$,/(.+)/g,"Fire Phone $1"],[q,Ji],[H,J]],[/(playbook);[-\w\),; ]+(rim)/i],[$,q,[H,ue]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[$,[q,js],[H,J]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[$,[q,Ys],[H,ue]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[$,[q,Ys],[H,J]],[/(nexus 9)/i],[$,[q,"HTC"],[H,ue]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[q,[$,/_/g," "],[H,J]],[/tcl (xess p17aa)/i,/droid [\w\.]+; ((?:8[14]9[16]|9(?:0(?:48|60|8[01])|1(?:3[27]|66)|2(?:6[69]|9[56])|466))[gqswx])(_\w(\w|\w\w))?(\)| bui)/i],[$,[q,"TCL"],[H,ue]],[/droid [\w\.]+; (418(?:7d|8v)|5087z|5102l|61(?:02[dh]|25[adfh]|27[ai]|56[dh]|59k|65[ah])|a509dl|t(?:43(?:0w|1[adepqu])|50(?:6d|7[adju])|6(?:09dl|10k|12b|71[efho]|76[hjk])|7(?:66[ahju]|67[hw]|7[045][bh]|71[hk]|73o|76[ho]|79w|81[hks]?|82h|90[bhsy]|99b)|810[hs]))(_\w(\w|\w\w))?(\)| bui)/i],[$,[q,"TCL"],[H,J]],[/(itel) ((\w+))/i],[[q,ot],$,[H,Ti,{tablet:["p10001l","w7001"],"*":"mobile"}]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[$,[q,"Acer"],[H,ue]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[$,[q,"Meizu"],[H,J]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[$,[q,"Ulefone"],[H,J]],[/; (energy ?\w+)(?: bui|\))/i,/; energizer ([\w ]+)(?: bui|\))/i],[$,[q,"Energizer"],[H,J]],[/; cat (b35);/i,/; (b15q?|s22 flip|s48c|s62 pro)(?: bui|\))/i],[$,[q,"Cat"],[H,J]],[/((?:new )?andromax[\w- ]+)(?: bui|\))/i],[$,[q,"Smartfren"],[H,J]],[/droid.+; (a(?:015|06[35]|142p?))/i],[$,[q,"Nothing"],[H,J]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno|micromax|advan)[-_ ]?([-\w]*)/i,/; (imo) ((?!tab)[\w ]+?)(?: bui|\))/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[q,$,[H,J]],[/(imo) (tab \w+)/i,/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i],[q,$,[H,ue]],[/(surface duo)/i],[$,[q,nn],[H,ue]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[$,[q,"Fairphone"],[H,J]],[/(shield[\w ]+) b/i],[$,[q,"Nvidia"],[H,ue]],[/(sprint) (\w+)/i],[q,$,[H,J]],[/(kin\.[onetw]{3})/i],[[$,/\./g," "],[q,nn],[H,J]],[/droid.+; ([c6]+|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[$,[q,Gn],[H,ue]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[$,[q,Gn],[H,J]],[/smart-tv.+(samsung)/i],[q,[H,_e]],[/hbbtv.+maple;(\d+)/i],[[$,/^/,"SmartTV"],[q,_i],[H,_e]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[q,Fn],[H,_e]],[/(apple) ?tv/i],[q,[$,gi+" TV"],[H,_e]],[/crkey.*devicetype\/chromecast/i],[[$,yt+" Third Generation"],[q,Dt],[H,_e]],[/crkey.*devicetype\/([^/]*)/i],[[$,/^/,"Chromecast "],[q,Dt],[H,_e]],[/fuchsia.*crkey/i],[[$,yt+" Nest Hub"],[q,Dt],[H,_e]],[/crkey/i],[[$,yt],[q,Dt],[H,_e]],[/droid.+aft(\w+)( bui|\))/i],[$,[q,Ji],[H,_e]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[$,[q,Qs],[H,_e]],[/(bravia[\w ]+)( bui|\))/i],[$,[q,en],[H,_e]],[/(mitv-\w{5}) bui/i],[$,[q,Vn],[H,_e]],[/Hbbtv.*(technisat) (.*);/i],[q,$,[H,_e]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[q,xi],[$,xi],[H,_e]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[H,_e]],[/(ouya)/i,/(nintendo) (\w+)/i],[q,$,[H,Kt]],[/droid.+; (shield) bui/i],[$,[q,"Nvidia"],[H,Kt]],[/(playstation \w+)/i],[$,[q,en],[H,Kt]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[$,[q,nn],[H,Kt]],[/\b(sm-[lr]\d\d[05][fnuw]?s?)\b/i],[$,[q,_i],[H,Si]],[/((pebble))app/i],[q,$,[H,Si]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[$,[q,gi],[H,Si]],[/droid.+; (wt63?0{2,3})\)/i],[$,[q,Gn],[H,Si]],[/droid.+; (glass) \d/i],[$,[q,Dt],[H,qn]],[/(pico) (4|neo3(?: link|pro)?)/i],[q,$,[H,qn]],[/; (quest( \d| pro)?)/i],[$,[q,Js],[H,qn]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[q,[H,Wn]],[/(aeobc)\b/i],[$,[q,Ji],[H,Wn]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+? mobile safari/i],[$,[H,J]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[$,[H,ue]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[H,ue]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[H,J]],[/(android[-\w\. ]{0,9});.+buil/i],[$,[q,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[z,[R,Dl+"HTML"]],[/(arkweb)\/([\w\.]+)/i],[R,z],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[z,[R,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna|servo)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[R,z],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[z,R]],os:[[/microsoft (windows) (vista|xp)/i],[R,z],[/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i],[R,[z,Ti,ir]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[z,Ti,ir],[R,Kn]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[z,/_/g,"."],[R,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[R,"macOS"],[z,/_/g,"."]],[/android ([\d\.]+).*crkey/i],[z,[R,yt+" Android"]],[/fuchsia.*crkey\/([\d\.]+)/i],[z,[R,yt+" Fuchsia"]],[/crkey\/([\d\.]+).*devicetype\/smartspeaker/i],[z,[R,yt+" SmartSpeaker"]],[/linux.*crkey\/([\d\.]+)/i],[z,[R,yt+" Linux"]],[/crkey\/([\d\.]+)/i],[z,[R,yt]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[z,R],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish|openharmony)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[R,z],[/\(bb(10);/i],[z,[R,js]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[z,[R,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[z,[R,wi+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[z,[R,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[z,[R,"watchOS"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[R,"Chrome OS"],z],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) (\w+)/i,/(xbox); +xbox ([^\);]+)/i,/(pico) .+os([\w\.]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[R,z],[/(sunos) ?([\w\.\d]*)/i],[[R,"Solaris"],z],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[R,z]]},tn=function(){var i={init:{},isIgnore:{},isIgnoreRgx:{},toString:{}};return lt.call(i.init,[[Me,[R,z,Ei,H]],[st,[xe]],[Ze,[H,$,q]],[rt,[R,z]],[Re,[R,z]]]),lt.call(i.isIgnore,[[Me,[z,Ei]],[rt,[z]],[Re,[z]]]),lt.call(i.isIgnoreRgx,[[Me,/ ?browser$/i],[Re,/ ?os$/i]]),lt.call(i.toString,[[Me,[R,z]],[st,[xe]],[Ze,[q,$]],[rt,[R,z]],[Re,[R,z]]]),i}(),Hl=function(i,e){var t=tn.init[e],n=tn.isIgnore[e]||0,a=tn.isIgnoreRgx[e]||0,s=tn.toString[e]||0;function r(){lt.call(this,t)}return r.prototype.getItem=function(){return i},r.prototype.withClientHints=function(){return St?St.getHighEntropyValues(or).then(function(l){return i.setCH(new lr(l,!1)).parseCH().get()}):i.parseCH().get()},r.prototype.withFeatureCheck=function(){return i.detectFeature().get()},e!=Zt&&(r.prototype.is=function(l){var o=!1;for(var u in this)if(this.hasOwnProperty(u)&&!Qn(n,u)&&ot(a?Lt(a,this[u]):this[u])==ot(a?Lt(a,l):l)){if(o=!0,l!=Tt)break}else if(l==Tt&&o){o=!o;break}return o},r.prototype.toString=function(){var l=Jt;for(var o in s)typeof this[s[o]]!==Tt&&(l+=(l?" ":Jt)+this[s[o]]);return l||Tt}),St||(r.prototype.then=function(l){var o=this,u=function(){for(var d in o)o.hasOwnProperty(d)&&(this[d]=o[d])};u.prototype={is:r.prototype.is,toString:r.prototype.toString};var c=new u;return l(c),c}),new r};function lr(i,e){if(i=i||{},lt.call(this,or),e)lt.call(this,[[Zn,Un(i[Et])],[Jn,Un(i[xl])],[J,/\?1/.test(i[Al])],[$,yi(i[Ml])],[Qt,yi(i[rr])],[ea,yi(i[Pl])],[xe,yi(i[Cl])],[Ot,Un(i[Il])],[rn,yi(i[kl])]]);else for(var t in i)this.hasOwnProperty(t)&&typeof i[t]!==Tt&&(this[t]=i[t])}function sr(i,e,t,n){return this.get=function(a){return a?this.data.hasOwnProperty(a)?this.data[a]:void 0:this.data},this.set=function(a,s){return this.data[a]=s,this},this.setCH=function(a){return this.uaCH=a,this},this.detectFeature=function(){if(Ce&&Ce.userAgent==this.ua)switch(this.itemType){case Me:Ce.brave&&typeof Ce.brave.isBrave==an&&this.set(R,"Brave");break;case Ze:!this.get(H)&&St&&St[J]&&this.set(H,J),this.get($)=="Macintosh"&&Ce&&typeof Ce.standalone!==Tt&&Ce.maxTouchPoints&&Ce.maxTouchPoints>2&&this.set($,"iPad").set(H,ue);break;case Re:!this.get(R)&&St&&St[Qt]&&this.set(R,St[Qt]);break;case Zt:var a=this.data,s=function(r){return a[r].getItem().detectFeature().get()};this.set(Me,s(Me)).set(st,s(st)).set(Ze,s(Ze)).set(rt,s(rt)).set(Re,s(Re))}return this},this.parseUA=function(){return this.itemType!=Zt&&tr.call(this.data,this.ua,this.rgxMap),this.itemType==Me&&this.set(Ei,Yn(this.get(z))),this},this.parseCH=function(){var a=this.uaCH,s=this.rgxMap;switch(this.itemType){case Me:var r=a[Jn]||a[Zn],l;if(r)for(var o in r){var u=Lt(/(Google|Microsoft) /,r[o].brand||r[o]),c=r[o].version;!/not.a.brand/i.test(u)&&(!l||/chrom/i.test(l)&&!/chromi/i.test(u))&&(this.set(R,u).set(z,c).set(Ei,Yn(c)),l=u)}break;case st:var d=a[xe];d&&(d&&a[rn]=="64"&&(d+="64"),tr.call(this.data,d+";",s));break;case Ze:if(a[J]&&this.set(H,J),a[$]&&this.set($,a[$]),a[$]=="Xbox"&&this.set(H,Kt).set(q,nn),a[Ot]){var h;if(typeof a[Ot]!="string")for(var f=0;!h&&f<a[Ot].length;)h=Ti(a[Ot][f++],nr);else h=Ti(a[Ot],nr);this.set(H,h)}break;case Re:var m=a[Qt];if(m){var _=a[ea];m==Kn&&(_=parseInt(Yn(_),10)>=13?"11":"10"),this.set(R,m).set(z,_)}this.get(R)==Kn&&a[$]=="Xbox"&&this.set(R,"Xbox").set(z,void 0);break;case Zt:var w=this.data,b=function(v){return w[v].getItem().setCH(a).parseCH().get()};this.set(Me,b(Me)).set(st,b(st)).set(Ze,b(Ze)).set(rt,b(rt)).set(Re,b(Re))}return this},lt.call(this,[["itemType",i],["ua",e],["uaCH",n],["rgxMap",t],["data",Hl(this,i)]]),this}function Je(i,e,t){if(typeof i===ei?(sn(i,!0)?(typeof e===ei&&(t=e),e=i):(t=i,e=void 0),i=void 0):typeof i===jn&&!sn(e,!0)&&(t=e,e=void 0),t&&typeof t.append===an){var n={};t.forEach(function(o,u){n[u]=o}),t=n}if(!(this instanceof Je))return new Je(i,e,t).getResult();var a=typeof i===jn?i:t&&t[Us]?t[Us]:Ce&&Ce.userAgent?Ce.userAgent:Jt,s=new lr(t,!0),r=e?Ll(ar,e):ar,l=function(o){return o==Zt?function(){return new sr(o,a,r,s).set("ua",a).set(Me,this.getBrowser()).set(st,this.getCPU()).set(Ze,this.getDevice()).set(rt,this.getEngine()).set(Re,this.getOS()).get()}:function(){return new sr(o,a,r[o],s).parseUA().get()}};return lt.call(this,[["getBrowser",l(Me)],["getCPU",l(st)],["getDevice",l(Ze)],["getEngine",l(rt)],["getOS",l(Re)],["getResult",l(Zt)],["getUA",function(){return a}],["setUA",function(o){return ti(o)&&(a=o.length>Xn?xi(o,Xn):o),this}]]).setUA(a),this}Je.VERSION=El;Je.BROWSER=on([R,z,Ei,H]);Je.CPU=on([xe]);Je.DEVICE=on([$,q,H,Kt,J,_e,ue,Si,Wn]);Je.ENGINE=Je.OS=on([R,z]);var Bl=["contentToCanvas"];function zl(i,e){if(i&1&&(re(0,"div",11),li(1,12),se()),i&2){let t=pe();te(),ce("ngTemplateOutlet",t.contentExtra)}}function $l(i,e){if(i&1){let t=Fe();re(0,"button",15),Ve("click",function(){ze(t);let a=pe(2);return $e(a.copyImage())}),Ge(1),Ie(2,"translate"),se()}if(i&2){let t=pe(2);ce("color",t.UI.ButtonColor.Outline),te(),Ue(" ",Ae(2,2,"sao_chep_anh")," ")}}function ql(i,e){i&1&&ye(0,"div")}function Fl(i,e){if(i&1){let t=Fe();ge(0,$l,3,4,"button",13)(1,ql,1,0,"div"),re(2,"a",14),Ve("click",function(){ze(t);let a=pe();return $e(a.handleSave())}),Ge(3),Ie(4,"translate"),se()}if(i&2){let t=pe();qe(t.showBtnCopy?0:1),te(3),Ue(" ",Ae(4,2,"tai_anh_xuong")," ")}}var cr=(()=>{class i{contentToCanvas;data;showBtnCopy=!1;dataModal=Y(bn)?.data;UI=Y(Ft);modalData=Y(bn);modalRef=Y(Fi);translate=Y(Oe);toastService=Y(mt);modalService=Y(ut);loadingService=Y(ht);commonService=Y(Ha);ngContent=this.dataModal?.ngContent;fileName=this.dataModal?.fileName;contentExtra=this.dataModal?.contentExtra;ngOnInit(){this.modalData||(this.modalService.error({message:"Kh\xF4ng c\xF3 d\u1EEF li\u1EC7u"}),this.modalRef.close()),this.detectBrowser()}ngAfterViewInit(){this.commonService.screenShot2(this.contentToCanvas?.nativeElement,{imageName:this.fileName,imageFormat:"png"}).then(t=>{this.data=t})}copyImage(){return ri(this,null,function*(){try{if(this.data?.image){let n=yield(yield fetch(this.data?.image)).blob(),a=[new ClipboardItem({[n.type]:n})];yield navigator.clipboard.write(a),this.toastService.success({title:this.translate.instant("message.copy_thanh_cong")}),this.modalRef.close()}else throw new Error("kh\xF4ng c\xF3 \u1EA3nh \u0111\u01B0\u1EE3c ch\u1ECDn")}catch(t){t.name==="ReferenceError"?this.toastService.error({title:this.translate.instant("errors.trinh_duyet_khong_ho_tro_copy")}):this.toastService.error({title:this.translate.instant("errors.copy_that_bai")})}})}handleSave(){return ri(this,null,function*(){if(this.contentToCanvas){this.loadingService.showLoading();try{this.data||(this.data=yield this.commonService.screenShot2(this.contentToCanvas.nativeElement,{imageName:this.fileName,imageFormat:"png"})),this.commonService.downloadImageToLocal(this.data.image,this.data.imageName)}catch{console.error("L\u1ED7i khi l\u01B0u \u1EA3nh")}finally{this.loadingService.hideLoading()}}})}detectBrowser(){let t=`2345Explorer, 360 Browser, Amaya, Android Browser, Arora, Avant, Avast, AVG,
    BIDUBrowser, Baidu, Basilisk, Blazer, Bolt, Brave, Bowser, Camino, Chimera,
    Chrome Headless, Chrome WebView, Chrome, Chromium, Comodo Dragon, Dillo,
    Dolphin, Doris, Edge, Epiphany, Facebook, Falkon, Fennec, Firebird,
    Flock, GSA, GoBrowser, ICE Browser, IceApe, IceCat, IceDragon,
    Iceape, Iceweasel, Iridium, Iron, Jasmine, K-Meleon, Kindle, Konqueror,
    LBBROWSER Line, Links, Lunascape, Lynx, MIUI Browser, Maemo Browser, Maemo,
    Maxthon, MetaSr Midori, Minimo, Mosaic, Mozilla, NetFront,
    NetSurf, Netfront, Netscape, NokiaBrowser, Oculus Browser, OmniWeb,
    Opera Coast, Opera Mini, Opera Mobi, Opera Tablet, Opera, PaleMoon, PhantomJS,
    Phoenix, Polaris, Puffin, QQ, QQBrowser, QQBrowserLite, Quark, QupZilla,
    RockMelt, Sailfish Browser, Samsung Browser, SeaMonkey, Silk, Skyfire,
    Sleipnir, Slim, SlimBrowser, Swiftfox, Tizen Browser, UCBrowser, Vivaldi,
    Waterfox, WeChat, Yandex, baidu, iCab, w3m, Whale Browser`,n=new Je().getResult(),{browser:a}=n;a?.name&&(this.showBtnCopy=t.includes(a.name),console.log("checkkkkkk->>>>>",this.showBtnCopy,a.name))}SERVICE_CODE=Q;TransferType=En;DEFAULT_BANK_TARGET=$t;CAPTURE_IGNORE_CLASS=Pa;static \u0275fac=function(n){return new(n||i)};static \u0275cmp=Pe({type:i,selectors:[["bidv-omni-transfer-modal-save-image-transaction"]],viewQuery:function(n,a){if(n&1&&Ni(Bl,5),n&2){let s;Di(s=Oi())&&(a.contentToCanvas=s.first)}},standalone:!0,features:[Ne],decls:14,vars:6,consts:[["contentToCanvas",""],["actionTpl",""],[3,"title","actions"],[1,"layout-base-result","result-in-modal"],[1,"result-wrap"],[1,"mask"],[1,"result-inner"],[1,"modal-result-logo"],[3,"src","colorChange"],[1,"line"],[3,"innerHTML"],[1,"result-extra-bottom"],[3,"ngTemplateOutlet"],["app-button","","type","button",3,"color"],["app-button","","type","button",3,"click"],["app-button","","type","button",3,"click","color"]],template:function(n,a){if(n&1&&(re(0,"app-modal-base",2)(1,"div",3,0)(3,"div",4,0),ye(5,"div",5),re(6,"div",6)(7,"div",7),ye(8,"app-svg",8)(9,"div",9),se(),ye(10,"div",10),ge(11,zl,2,1,"div",11),se()()(),ge(12,Fl,5,4,"ng-template",null,1,kt),se()),n&2){let s=Ct(13);ce("title","L\u01B0u \u1EA3nh")("actions",s),te(8),ce("src","media/logo/bidv.svg")("colorChange",!1),te(2),ce("innerHTML",a.ngContent,da),te(),qe(a.contentExtra?11:-1)}},dependencies:[Ui,Vi,qi,$i,qt,Hi]})}return i})();var ii=mn(_r()),dr=mn(Fa());ii.default.extend(dr.default);var ur=(()=>{class i{rendererFactory=Y(ua);modalService=Y(ut);storage=Y(De);toastService=Y(mt);translate=Y(Oe);router=Y(zt);cryptoService=Y(La);location=Y(Sa);homeService=Y(ln);apiService=Y(pt);timerDebounce=void 0;handleT8$=new Bt;renderer;constructor(){this.renderer=this.rendererFactory.createRenderer(null,null)}convertVietnameseCharacters(t){let n=t;return n=n?.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g,"a"),n=n?.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g,"A"),n=n?.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g,"e"),n=n?.replace(/E|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g,"E"),n=n?.replace(/ì|í|ị|ỉ|ĩ/g,"i"),n=n?.replace(/Ì|Í|Ị|Ỉ|Ĩ/g,"I"),n=n?.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g,"o"),n=n?.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g,"O"),n=n?.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g,"u"),n=n?.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g,"U"),n=n?.replace(/ỳ|ý|ỵ|ỷ|ỹ/g,"y"),n=n?.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g,"Y"),n=n?.replace(/đ/g,"d"),n=n?.replace(/Đ/g,"D"),n}convertVietnameseToNormalize(t){return t?t.normalize("NFD").replace(/[\u0300-\u036f]/g,"")?.replace(/đ/g,"d")?.replace(/Đ/g,"D"):""}markFormGroupTouched(t){Object.values(t.controls).forEach(n=>{n.markAsTouched(),n.controls&&this.markFormGroupTouched(n)})}convert2Map(t,n,a,s,r){let l=new Map;return a||(a=n),t.forEach(o=>{o&&o[n]&&!l.has(o[n])&&l.set(o[n].toString(),X({code:o[n],name:o[a],des:o[s],image:o[r]},o))}),l}array2Map({arr:t,code:n,callback:a}){let s=new Map;return Array.isArray(t)&&t.forEach(r=>{r&&r[n]&&!s.has(r[n])&&s.set(r[n].toString(),a(r))}),s}screenShot(t,n="screenshot"){return ri(this,null,function*(){try{if(ta.default){window.scrollTo(0,0);let a=0;t.querySelectorAll('[data-html2canvas-ignore="true"]').forEach(u=>{a+=u.clientHeight});let s=getComputedStyle(t),r=parseFloat(s.paddingLeft)+parseFloat(s.paddingRight),l={width:t.scrollWidth-r,height:t.scrollHeight,scrollX:-7,scrollY:0},o=ie(X({backgroundColor:"none"},l),{useCORS:!0});return(0,ta.default)(t,o).then(u=>{let c=u.toDataURL(),d=u.toDataURL(),h=`${n}.png`;this.modalService.open(cr,{data:{src:d,href:c,download:h},size:Ri.Large,maskClosable:!1})})}else throw console.error("Th\xEAm th\u01B0 vi\u1EC7n `./assets/js/vendors/html2canvas/html2canvas.min.js` v\xE0o module s\u1EED d\u1EE5ng function n\xE0y"),new Error("thi\u1EBFu th\u01B0 vi\u1EC7n html2 canvas")}catch(a){return console.error(a),Promise.reject(a)}})}exportCSV(t,n,a){if(!t)throw new Error("Please insert data to func");a=a||"download",n||(n=t[0]&&Object.keys(t[0]).map(c=>({key:c,name:c})));let s=n.map(c=>c.name).join(",")+`\r
`,r=t.map(c=>n.map(d=>c[d.key]).join(",")+`\r
`).join(""),o=`data:text/csv;charset=utf-8,${encodeURIComponent("\uFEFF"+s+r)}`,u=document.createElement("a");u.href=o,u.download=`${a}.${Date.now()}.csv`,u.click()}dataUrlToFile(t,n){let a=t.split(","),s=a[0].match(/:(.*?);/),r,l=atob(a[1]);s&&s[1]&&(r=s[1]);let o=l.length,u=new Uint8Array(o);for(;o--;)u[o]=l.charCodeAt(o);return new File([u],n,{type:r})}debounce(t,n){typeof t=="function"&&(clearTimeout(this.timerDebounce),this.timerDebounce=setTimeout(()=>{clearTimeout(this.timerDebounce),typeof t=="function"?t():console.error("fn not is function, check again")},n))}scrollTo(t){t&&t.scrollIntoView({behavior:"smooth",block:"center"})}scrollToError(t){for(let n of Object.keys(t.controls))if(t.controls[n].invalid){let a=document.querySelector('[formcontrolname="'+n+'"]');if(a){a.focus();break}}setTimeout(()=>{let n=document.querySelector(".parsley-errors-list");n&&this.scrollTo(n)},100)}generateKeyword(t){let n=a=>`(^${a.split("").map((l,o)=>o===0?`(^${l}.*?)`:`(\\s[${l}].*?)`).join("")}$)|${a}`;if(t.search(/\s/gi)>-1){let a=t.split(/\s/);return new RegExp(`${t}|(${a.join(".+?")})`,"gi")}else return new RegExp(n(t),"gi")}handleMessage(t){t?.code!=Xe.success&&t?.des&&this.modalService.warning({message:t?.des})}diffObjects(t,n){let a=Array.prototype.map?function(r,...l){return Array.prototype.map.apply(r,l)}:function(r,l){let o=new Array(r.length);for(let u=0,c=r.length;u<c;u++)o[u]=l(r[u],u,r);return o},s=Array.prototype.push;if(t==null||n==null)return t!=n?[["","null",t!=null,n!=null]]:void 0;if(t.constructor!=n.constructor||typeof t!=typeof n)return[["","type",Object.prototype.toString.call(t),Object.prototype.toString.call(n)]];if(Object.prototype.toString.call(t)=="[object Array]"){if(t.length!=n.length)return[["","length",t.length,n.length]];let r=[];for(let l=0;l<t.length;l++){let o=this.diffObjects(t[l],n[l]);o&&s.apply(r,a(o,function(u,c){return u[0]="["+l+"]"+u[0],u}))}return r.length?r:void 0}if(Object.prototype.toString.call(t)=="[object Object]"){let r=[];for(let l in t)if(typeof n[l]>"u"&&typeof t[l]<"u")r.push(["["+l+"]","undefined",t[l],void 0]);else{let o=this.diffObjects(t[l],n[l]);o&&s.apply(r,a(o,function(u,c){return u[0]="["+l+"]"+u[0],u}))}for(let l in n)typeof t[l]>"u"&&typeof n[l]<"u"&&r.push(["["+l+"]","undefined",void 0,n[l]]);return r.length?r:void 0}if(t!=n)return[["","value",t,n]]}initMenuFavorites(t=wn){t&&t.length<Da&&wn.forEach((n,a)=>{typeof t[a]>"u"&&(t[a]=n)}),t?this.storage.systemMenu=Sn.map(n=>{let a=t.indexOf(n.id);return a>=0?(n.isFav=!0,n.sortFav=a):n.isFav=!1,n}):this.storage.systemMenu=Sn}validURL(t){return!!new RegExp("^(https?:\\/\\/)?((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|((\\d{1,3}\\.){3}\\d{1,3}))(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*(\\?[;&a-z\\d%_.~+=-]*)?(\\#[-a-z\\d_]*)?$","i").test(t)}getFormErrors(t){Object.keys(t.controls).forEach(n=>{let a=t.controls[n].errors;a!=null&&Object.keys(a).forEach(s=>{console.log("Key control: "+n+", keyError: "+s+", err value: ",a[s])})})}randomString(t=10,n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"){let a=new Uint8Array(t);return window.crypto.getRandomValues(a),Array.from(a,s=>n[s%n.length]).join("")}randomNumberString(t,n="0123456789"){return this.randomString(t,n)}replaceAt(t,n,a){return t.substring(0,n)+a+t.substring(n+1)}copyText(t,n="copy_sucess"){let a=document.createElement("textarea");a.style.position="fixed",a.style.left="0",a.style.top="0",a.style.opacity="0",a.value=t,document.body.appendChild(a),a.focus(),a.select(),document.execCommand("copy"),document.body.removeChild(a),this.toastService.success({title:this.translate.instant(n)})}initTooltipData(t,n){let a="",s={cashback:{code:"cashback",key:"reward_core.refund_point"},mileage:{code:"mileage",key:"reward_core.change_miles_point"},reward:{code:"reward",key:"reward_core.change_gift_point"}};if(Array.isArray(t))if(t.length>0){if(t=t.filter(r=>r.id).sort((r,l)=>Number(r.id)-Number(l.id)),t.length===0)return"";t.forEach((r,l)=>{r.id&&s[r.code]&&(t.length===1?a=`<b>${n.instant(s[r.code].key)}</b>`:t.length>1&&l===t.length-1?a+=`<b>${n.instant(s[r.code].key)}</b>`:t.length>1&&l===t.length-2?a+=`<b>${n.instant(s[r.code].key)}</b> ${n.instant("va")} `:a+=`<b>${n.instant(s[r.code].key)}</b>, `)})}else return"";return a}contact2ToAcc(t){let n=ie(X({},t),{accName:t.toAccName,accNo:t.toAcc,cardNo:t.toCard,khacChuTK:t.serviceCode!==Q.ck_noi_bo_cung_chu_tk,totalAmount:t.totalAmount});return t?.toAcc&&t?.toCard&&t.serviceCode&&[Q.ck_noi_bo_cung_chu_tk,Q.ck_noi_bo_khac_chu_tk].includes(t.serviceCode)&&(t?.toCard?.startsWith("970418")||t?.toCard?.startsWith("********")?delete n.accNo:delete n.cardNo),t.virtualAccount&&(n.idAcc=t.virtualAccount,n.type=t.virtualAccountType),t.serviceCode===Q.ck_nhanh_lien_ngan_hang?n.loaiCK=ft.Quick:t.serviceCode===Q.ck_thuong_lien_ngan_hang&&(n.loaiCK=ft.Normal),n}directory2ToAcc(t){let n=t?.data?.serviceCode,a=ie(X({},t),{urlLogo:t?.data?.urlLogo,type:t?.type?.toString(),accName:t.accountName,accNo:t.accountNumber,accountNo:t.accountNumber});return n===Q.ck_noi_bo_so_the&&(a.urlLogo=$t.urlLogo,a.sortName=$t.sortName),a}template2ToAcc(t){let n=ie(X({},t),{accName:t.toAccName,accNo:t.toAcc,cardNo:t.toCard,khacChuTK:t.serviceCode!==Q.ck_noi_bo_cung_chu_tk});return t.virtualAccount&&(n.idAcc=t.virtualAccount,n.type=t.virtualAccountType),t.serviceCode===Q.ck_nhanh_lien_ngan_hang?n.loaiCK=ft.Quick:t.serviceCode===Q.ck_thuong_lien_ngan_hang&&(n.loaiCK=ft.Normal),n}transferIn2ToAcc(t){let n=ie(X({},t),{accName:t.toAccName,accNo:t.toAcc,cardNo:t.toCard,khacChuTK:t.serviceCode!==Q.ck_noi_bo_cung_chu_tk});return t.virtualAccount&&(n.idAcc=t.virtualAccount,n.type=t.virtualAccountType),t.serviceCode===Q.ck_nhanh_lien_ngan_hang?n.loaiCK=ft.Quick:t.serviceCode===Q.ck_thuong_lien_ngan_hang&&(n.loaiCK=ft.Normal),n}getAccountDefault(t){return!t||t.length===0?void 0:t.find(a=>a.showDefault)||t.reduce((a,s)=>Number(a.balanceAval||0)>Number(s.balanceAval||0)?a:s)}getDefaultAccountWithCCY(t,n="VND"){if(!t||t.length===0)return;let a=t.filter(r=>r.ccy===n)||[],s=r=>r.reduce((l,o)=>Number(l.balanceAval||0)>Number(o.balanceAval||0)?l:o);return a.length?s(a):s(t)}sortAccountList(t){return t.sort((n,a)=>Number(a.balanceAval)-Number(n.balanceAval))}getAccNoType(t){return t.startsWith(ji.cardLength19)||t.startsWith(ji.cardLengthMin16)?Gi.card_no:t.length===14&&this.containsOnlyNumbers(t[0])&&t.substring(5,7)==="00"&&!t.startsWith("96")||t.length<=12&&t.length!==9&&!t.startsWith("0")&&!t.startsWith("96")&&this.containsOnlyNumbers(t)?Gi.account_no:Gi.elite_account}containsOnlyNumbers(t){return/^\d+$/.test(t)}checkGroup(t,n,a){let r=this.cryptoService.encryptAccountNo(t.serviceTypeId+"**********");if(n==="1")t.accNo?this.router.navigate(["dich-vu-ngan-hang-so","thanh-toan-hoa-don",r],{state:{serviceTypeId:t.serviceTypeId,serviceTypeName:t.serviceTypeName,providerId:t.providerId,serviceId:t.serviceId,accNo:t.accNo,channel:t.channel,napTaiKhoanGiaoThong:t?.napTaiKhoanGiaoThong,napViDienTu:t?.napViDienTu?t?.napViDienTu:!1}}):this.router.navigate(["dich-vu-ngan-hang-so","thanh-toan-hoa-don",r],{state:{serviceTypeId:t.serviceTypeId,serviceTypeName:t.serviceTypeName,providerId:t.providerId,serviceId:t.serviceId,rewardPoints:t.rewardPoints,channel:t.channel,napTaiKhoanGiaoThong:t?.napTaiKhoanGiaoThong,napViDienTu:t?.napViDienTu?t?.napViDienTu:!1}});else{let l={serviceTypeId:t.serviceTypeId,serviceTypeName:t.serviceTypeName,providerId:t.providerId,serviceId:t.serviceId,maKhachHang:t.invoice||t.metaData.invoice,sdtDiaChi:t.metaData.cusAddress,soTien:t.metaData.amount,tenKhachHang:t.metaData.cusName,thongTin:t.metaData.additionData,menhGia:t.metaData.amount,channel:t.channel,napTaiKhoanGiaoThong:t?.napTaiKhoanGiaoThong,napViDienTu:t?.napViDienTu?t?.napViDienTu:!1};a?this.router.navigate([a,r],{state:X({},l)}):this.router.navigate(["dich-vu-ngan-hang-so","thanh-toan-hoa-don",r],{state:X({},l)})}}allowMetaKey(t){return["Delete","Backspace","Tab","Escape","Enter","NumLock","ArrowLeft","ArrowRight","End","Home"].indexOf(t.key)!==-1||t.key==="a"&&(t.ctrlKey||t.metaKey)||t.key==="c"&&(t.ctrlKey||t.metaKey)||t.key==="v"&&(t.ctrlKey||t.metaKey)||t.key==="x"&&(t.ctrlKey||t.metaKey)}getErrorT8(){return this.handleT8$.asObservable()}setErrorT8(t){this.handleT8$.next(t)}mapObjectToISelect(t,n,a,s,r,l,o){return[...t?.map(c=>({label:c[n],value:c[a],code:s?c[s]:"",image:r?c[r]:"",sub:l?c[l]:"",disabled:o?c[o]:!1,extend:X({},c)}))||[]]}sortListBank(t=[],n=!0){n&&t.unshift($t);let a=Oa.map(s=>s.toLowerCase());return t.sort((s,r)=>{let l=s.sortName?s.sortName.toLowerCase():"",o=r.sortName?r.sortName.toLowerCase():"";if(l==="bidv")return-1;if(o==="bidv")return 1;let u=s.bankCode247?s.bankCode247.toLowerCase():"",c=r.bankCode247?r.bankCode247.toLowerCase():"",d=a.indexOf(u),h=a.indexOf(c);return d!==-1&&h!==-1?d-h:d!==-1?-1:h!==-1||!s.sortName?1:r.sortName?l.localeCompare(o):-1})}groupItemOfArrayByField(t,n,a){let s=t.reduce((l,o)=>{let u=o[n];return a&&(u=(0,ii.default)(u,a).format(a).toString()),l[u]||(l[u]=[]),l[u].push(o),l},{}),r=[];if(s)for(let[l,o]of Object.entries(s)){let u={key:l.toString(),value:o};r.push(u)}return[...r]}mergeGroupItem(t,n){let a=new Map;return t.forEach(s=>{a.set(s.key,s.value)}),n.forEach(s=>{a.has(s.key)?a.set(s.key,[...a.get(s.key),...s.value]):a.set(s.key,s.value)}),Array.from(a,([s,r])=>({key:s,value:r}))}convertDateToRequestForm(t){if(!t)return;let[n,a,s]=t.split("/"),r=n.padStart(2,"0"),l=a.padStart(2,"0");return`${s}${l}${r}`}convertBannerToSlide(t){return t.filter(n=>!!n?.urlPicture).map(n=>ie(X({},n),{image:n?.urlPicture,functionCode:n?.navigationFunction}))}convertGiftCategoryToServiceType(t){return t?.map(n=>({serviceTypeId:n.giftCatalogId,serviceTypeName:n.giftCatalogName,serviceTypeNameEn:n.giftCatalogNameEn,icon:n?.icon,iconVip:n?.iconVip,badge:n?.tag===Vt.Yes?"moi":""}))||[]}convertTermToString(t){let n=t.match(/^(\d+)([DWMYP])$/i);if(!n)return"";let a=n[1],s=n[2].toUpperCase(),r=this.getTermUnitText(s);return`${a} ${r}`}getTermUnitText(t){switch(t){case"D":return this.translate.instant("days");case"W":return this.translate.instant("weeks");case"M":return this.translate.instant("months");case"P":return this.translate.instant("quarters");case"Y":return this.translate.instant("years")}}isBusinessTypeAccount(t){return t==="1"||t==="2"||t==="7"}isYourOwnAccount(t,n){return t?.some(a=>a.accNo===n)}checkRangeTime(t,n){let a="hh:mm:ss",s=(0,ii.default)(),r=(0,ii.default)(t,a),l=(0,ii.default)(n,a);return s.isBetween(r,l)}checkFeatureCOT(t,n,a="",s=!1){if(!t||!n)return!1;try{let l=function(d){if(!/^\d{2,4}$/.test(d))return"Invalid format";let h=d.slice(0,2),f=d.length===4?d.slice(2,4):"00";return`${h}:${f}`};var r=l;let o=l(t),u=l(n),c=this.checkRangeTime(`${o}:00`,`${u}:00`);return c||this.failedCOT(a,o,u,s),c}catch(l){return console.error(l),!1}}checkRequiredSignatureAuth(t,n,a){return t===Vt.Yes?a!=="VN"||n===Gt.HouseHoldBusiness?(this.modalService.notice({message:this.translate.instant("errors.chuc_nang_yeu_cau_xac_thuc_chu_ky_so")}),!1):(this.modalService.notice({message:this.translate.instant("errors.vui_long_xac_thuc_chu_ky_so_tren_MB")}),!1):!0}checkBusinessBiometrics(t){return t===ui.ActiveForBusiness?!0:(this.modalService.notice({message:this.translate.instant("errors.thong_tin_sth_hkd_khong_hop_le")}),!1)}failedBiometrics(t=!1,n="VN"){this.modalService.notice({message:this.translate.instant(`errors.${n==="VN"?"trang_thai_sth_khong_hop_le_de_su_dung_dich_vu":"trang_thai_sth_khong_hop_le_de_su_dung_dich_vu_nguoi_nuoc_ngoai"}`),confirm:()=>{t&&this.location.back()}})}failBiometricsStatus(t=!1){this.modalService.notice({message:this.translate.instant("errors.trang_thai_sth_khong_hoat_dong_de_su_dung_dich_vu"),confirm:()=>{t&&this.location.back()}})}failedCOT(t,n,a,s=!1){this.modalService.notice({message:this.translate.instant("errors.loi_COT_khong_su_dung_duoc_tinh_nang",{functionName:t,startTime:n,endTime:a}),confirm:()=>{s&&this.location.back()}})}checkAbilityToAccessFeature(t,n=!1){console.log("processCheckToAccessFeatureCode",t);let a=this.storage.features;if(!a?.length)return xt(!0);let s=a.find(b=>b.functionCode===t.toString());if(console.log("processCheckToAccessFeature",s),!s)return xt(!0);let{allowCheckCot:r,beginCot:l,finishCot:o,allowCheckSth:u,allowCheckCks:c,functionName:d,functionNameEn:h}=s,{cusType:f,nationality:m}=this.storage.userInfo||{},_=this.storage.language,w={allowCheckCot:r,allowCheckSth:u,allowCheckCks:c,functionName:_===dt.En?h:d,beginCot:l,finishCot:o,cusType:f,nationality:m};return this.processCheckToAccessFeature(w,n)}processCheckToAccessFeature(t,n=!1){let{allowCheckCot:a,allowCheckSth:s,allowCheckCks:r,functionName:l,beginCot:o,finishCot:u,cusType:c,nationality:d}=t,h=f=>{let{sthStatus:m,bcaStatus:_}=f||{};if(m){if(c===Gt.Individual)if(d==="VN")if(m===ui.Active)if(_===Tn.Auth){if(!this.checkRequiredSignatureAuth(r,c,d))return!1}else return this.failedBiometrics(),!1;else return this.failBiometricsStatus(),!1;else{if(m===ui.Locked)return this.failedBiometrics(n,d),!1;if(!this.checkRequiredSignatureAuth(r,c,d))return!1}return c===Gt.HouseHoldBusiness?this.checkBusinessBiometrics(m):!0}else return c===Gt.HouseHoldBusiness?this.checkBusinessBiometrics():(this.failedBiometrics(n,d),!1)};if(a===Vt.Yes&&!this.checkFeatureCOT(o,u,l,n))return xt(!1);if(s===Vt.Yes){let{sthStatus:f,bcaStatus:m}=this.storage.userInfo||{},_,w=this.storage.userInfo||{};(Object.hasOwn(w,"sthStatus")||Object.hasOwn(w,"bcaStatus"))&&(_={sthStatus:f,bcaStatus:m});let b=_||this.storage.biometrics;return b?(this.storage.biometrics||(this.storage.biometrics=b),xt(h(b))):new aa(v=>{this.apiService.getBiometrics().subscribe({next:g=>{g?.code===Xe.success&&(this.storage.biometrics=g?.data,v.next(h(g?.data)))},error:g=>{this.modalService.error(g?.error?.des),v.next(!1)}})})}else if(s===Vt.No){let f=this.checkRequiredSignatureAuth(r,c);return xt(f)}return xt(!0)}getHomePaymentChildren(t=5){let n=new Map,a=this.storage.searchFullData?.billFeatures;if(!a?.length)return[];let s=a?.sort((r,l)=>(r?.orderServiceType??9999)-(l?.orderServiceType??9999));for(let r=0;r<s.length&&!(n.size>=t);r++){let l=s[r]?.orderServiceType;l&&(n.has(l)||n.set(l,s[r]))}return Array.from(n.values())}create247TransferQRBankCode(t,n,a=!1,s,r){let l="**********[QRType]38[AccountInforLength][AccountInfor]*********[amountLength][amount]5802VN62[contentLength][Content]6304";s||(l=l.replace("54[amountLength][amount]","")),r||(l=l.replace("62[contentLength][Content]","")),l=l.replace("[QRType]",s?"12":"11");let o="0010A00000072701[BankInforLength][BankInfor]0208QRIBFTTA";if(a&&(o=o.replace("QRIBFTTA","QRIBFTTC")),t.length!==6)return null;let u=n.length<10?`0${n.length}`:`${n.length}`,c="0006[Bankcode]01[AccountNoLength][AccountNo]";c=c.replace("[Bankcode]",t),c=c.replace("[AccountNoLength]",u),c=c.replace("[AccountNo]",n);let d=c.length;if(o=o.replace("[BankInforLength]",`${d}`).replace("[BankInfor]",c),l=l.replace("[AccountInforLength]",`${o.length}`).replace("[AccountInfor]",o),s){let f=s.length<10?`0${s.length}`:`${s.length}`;l=l.replace("[amountLength]",f).replace("[amount]",s)}if(r){let f="08[contentLength][content]",m=r.length<10?`0${r.length}`:`${r.length}`;f=f.replace("[contentLength]",m).replace("[content]",r);let _=f.length<10?`0${f.length}`:`${f.length}`;l=l.replace("[contentLength]",_).replace("[Content]",f)}let h=this.crc16CheckSum(l);return l+=h,l}crc16CheckSum(t){let a=new TextEncoder().encode(t),s=65535;for(let r=0;r<a.length;r++){let l=s>>8^a[r];l^=l>>4,s=s<<8&65535^l<<12&65535^l<<5&65535^l&65535}return s.toString(16).toUpperCase().padStart(4,"0")}static \u0275fac=function(n){return new(n||i)};static \u0275prov=be({token:i,factory:i.\u0275fac,providedIn:"root"})}return i})();var pr=(()=>{class i{router=Y(zt);toastService=Y(mt);helperService=Y(ur);storageService=Y(De);apiPaymentService=Y(qa);translateService=Y(Oe);homeService=Y(ln);processSelectBanner(t){if(t?.navigationUrlVn||t?.navigationUrlEn){let a=this.storageService.language===dt.En?t?.navigationUrlEn:t?.navigationUrlVn;window.open(a,"_blank");return}this.processNavigation(t)}processSelectBillPaymentService(t){return this.router.navigate([x.Payment,x.PaymentBill],{state:{isService:!0,selectedPaymentService:t}})}processSelectBillPaymentTemplate(t){return this.router.navigate([x.Payment,x.PaymentTemplate,x.PaymentTemplateDetail],{state:{templateData:t}})}processNavigation(t,n){if(console.log("resulttttttttttt",t),t?.isFixed){window.open(tt.fixedBannerNavigationLink,"_blank");return}if(n===Wi.BILL_FEATURES){this.processSelectBillPaymentService(t);return}if(n===Wi.TEMPLATES){this.processSelectBillPaymentTemplate(t);return}if(t?.functionCode){if(t?.functionCode?.startsWith(String(p.thanh_toan_hoa_don)+"_")){this.apiPaymentService.checkRedirectBillPayment(t.functionCode);return}let a=Number(t?.functionCode??0),s=t?.metadata;if(s)try{let r=s.replace(/([{,]\s*)(\w+)(\s*:)/g,'$1"$2"$3'),{serviceTypeId:l}=JSON.parse(r)||{};if(l){this.apiPaymentService.checkRedirectBillPayment(`${a}_${l}`);return}}catch(r){console.error(r),this.failNavigation();return}switch(a){case p.lai_suat:window.open(tt.interestRates,"_blank");return;case p.ty_gia:window.open(tt.exchangeRates,"_blank");return;case p.atm_chi_nhanh:window.open(Na,"_blank");return;case p.faq:window.open(tt.QAInformation,"_blank");return;case p.huong_dan_giao_dich_an_toan:window.open(tt.safeTransactionInstruction,"_blank");return;case p.huong_dan_su_dung_dich_vu:window.open(tt.usingServiceInstruction,"_blank");return;case p.tai_khoan_nhu_y:return this.router.navigate([x.Account],{state:{paymentAccountType:Ra.DESIRED}});case p.mo_tk_tich_luy:return this.router.navigate([x.Saving],{state:{navigateToAccumulateTarget:!0}});case p.cai_dat_giao_dien:return this.homeService.openModalSettingTheme();default:this.navigateWithServiceId(a);return}}if((t.bankId||t?.serviceCode)&&!t?.templateId)return t.serviceCode??=t.bankCode247?Q.ck_nhanh_lien_ngan_hang:Q.ck_thuong_lien_ngan_hang,t.fromAcc??={},t.toAcc??={},this.navigateToTransfer(t);if(t?.templateId&&([Q.ck_noi_bo_SDT,Q.ck_noi_bo_cung_chu_tk,Q.ck_noi_bo_khac_chu_tk,Q.ck_noi_bo_so_the,Q.ck_nhanh_lien_ngan_hang,Q.ck_thuong_lien_ngan_hang,Q.ck_nhanh_lien_ngan_hang_den_so_the].includes(t.serviceCode)?this.navigateToTransfer(t):this.navigateToTransferTemplate(t)),t?.topupId)return this.router.navigate([x.Topup],{state:{topUpData:t}});this.failNavigation()}navigateWithServiceId(t){let n=Fs[t],a=Vs.includes(t);if(!n||a){this.toastService.info({title:this.translateService.instant("errors.dich_vu_khong_the_thuc_hien_luc_nay")});return}return this.router.navigate(n)}navigateToTransferTemplate(t){this.toastService.info({title:this.translateService.instant("errors.dich_vu_khong_the_thuc_hien_luc_nay")})}navigateToTransfer(t){let n=this.helperService.contact2ToAcc(t);if(t.serviceCode){let a=[],s={toAcc:n,fromContact:!0,transferFast:!!n?.bankCode247};switch(t.serviceCode){case Q.ck_noi_bo_SDT:case Q.ck_noi_bo_cung_chu_tk:case Q.ck_noi_bo_khac_chu_tk:s.sameOwner=t.serviceCode===Q.ck_noi_bo_cung_chu_tk,a=[x.Transfer,x.WithinTransfer];break;case Q.ck_noi_bo_so_the:s.cardNo=t.toCard,a=[x.Transfer,x.WithinTransfer];break;case Q.ck_nhanh_lien_ngan_hang:s.bankTarget=t,s.transferFast=!0,t?.toAcc?(s.toAcc=ie(X({},t),{accNo:t.toAcc}),a=[x.Transfer,x.InterbankTransferToAccount]):a=[x.Transfer];break;case Q.ck_thuong_lien_ngan_hang:s.bankTarget=t,s.transferFast=!1,t?.toAcc?(s.toAcc=ie(X({},t),{accNo:t.toAcc}),a=[x.Transfer,x.InterbankTransferToAccount]):a=[x.Transfer];break;case Q.ck_nhanh_lien_ngan_hang_den_so_the:s.cardNo=t.toCard,a=[x.Transfer,x.InterbankTransferToCard];break;default:this.toastService.info({title:this.translateService.instant("errors.dich_vu_khong_the_thuc_hien_luc_nay")});return}this.router.navigate(a,{state:s})}}failNavigation(){this.toastService.info({title:this.translateService.instant("errors.dich_vu_khong_the_thuc_hien_luc_nay")})}static \u0275fac=function(n){return new(n||i)};static \u0275prov=be({token:i,factory:i.\u0275fac,providedIn:"root"})}return i})();var Vl=["swiper"],Gl=i=>({item:i});function Ul(i,e){if(i&1&&(re(0,"swiper-slide",6),li(1,7),se()),i&2){let t=e.$implicit,n=pe(2),a=Ct(6);te(),ce("ngTemplateOutlet",n.swiperTpl||a)("ngTemplateOutletContext",Li(2,Gl,t))}}function Yl(i,e){if(i&1&&(ma(0),ge(1,Ul,2,4,"swiper-slide",5),ga()),i&2){let t=pe();te(),ce("ngForOf",t.slides)}}function jl(i,e){if(i&1&&li(0,8),i&2){let t=pe();ce("ngTemplateOutlet",t.slidesTpl)}}function Wl(i,e){if(i&1){let t=Fe();re(0,"div",9),Ve("click",function(){let a=ze(t).item,s=pe();return $e(s.handleNavigate(a))}),ye(1,"img",10),se()}if(i&2){let t=e.item;te(),ce("src",t.image,Mi)}}qs();var hr=(()=>{class i{swipers=!0;slides;config;swiperTpl;slidesTpl;allowNavigate=!0;allowFollowIndex=!1;slideChanged=new Ai;swiper;navigationService=Y(pr);ngAfterViewInit(){let t=this.allowFollowIndex?ie(X({},this.config),{on:{slideChange:this.handleSlideChange.bind(this)}}):this.config;Object.assign(this.swiper.nativeElement,t),this.swiper.nativeElement.initialize()}ngOnDestroy(){this.swiper.nativeElement.swiper.destroy()}handleSlideChange(){let t=this.swiper?.nativeElement?.swiper;this.slideChanged.emit(t?.realIndex)}handleNavigate(t){this.allowNavigate&&this.navigationService.processSelectBanner(t)}goToLastSlide(){let t=this.swiper.nativeElement.swiper;t&&t.slideTo(t.slides.length-1)}goToFirstSlide(){let t=this.swiper.nativeElement.swiper;t&&t.slideTo(0)}static \u0275fac=function(n){return new(n||i)};static \u0275cmp=Pe({type:i,selectors:[["app-swiper"]],viewQuery:function(n,a){if(n&1&&Ni(Vl,5),n&2){let s;Di(s=Oi())&&(a.swiper=s.first)}},hostVars:2,hostBindings:function(n,a){n&2&&fa("app-swiper",a.swipers)},inputs:{slides:"slides",config:"config",swiperTpl:"swiperTpl",slidesTpl:"slidesTpl",allowNavigate:"allowNavigate",allowFollowIndex:"allowFollowIndex"},outputs:{slideChanged:"slideChanged"},standalone:!0,features:[Ne],decls:7,vars:2,consts:[["swiper",""],["slidesFreeTpl",""],["swiperDefaultTpl",""],["init","false",1,"swiper-container"],[4,"ngIf","ngIfElse"],["class","swiper-slide",4,"ngFor","ngForOf"],[1,"swiper-slide"],[3,"ngTemplateOutlet","ngTemplateOutletContext"],[3,"ngTemplateOutlet"],[1,"image",3,"click"],["alt","",3,"src"]],template:function(n,a){if(n&1&&(re(0,"swiper-container",3,0),ge(2,Yl,2,1,"ng-container",4)(3,jl,1,1,"ng-template",null,1,kt),se(),ge(5,Wl,2,1,"ng-template",null,2,kt)),n&2){let s=Ct(4);te(2),ce("ngIf",!a.slidesTpl)("ngIfElse",s)}},dependencies:[ct,Ea,xa,Hi,ka]})}return i})();var Xl=function(i){return i.Premier="11",i.PremierElite="12",i.Private="13",i}(Xl||{}),cn=function(i){return i.Normal="NORMAL",i.Vip="VIP",i}(cn||{}),ni=function(i){return i.Normal="NORMAL",i.Vip="VIP",i}(ni||{});function Kl(i,e){if(i&1&&(re(0,"p",15),Ge(1),Ie(2,"translate"),se()),i&2){let t=pe().item;te(),Ue(" ",Ae(2,1,t.description)," ")}}function Ql(i,e){if(i&1&&(re(0,"div",16)(1,"div",17),Ge(2),Ie(3,"translate"),se()()),i&2){let t=pe(2);te(),ce("color",t.UI.BadgeColor.SubSuccess),te(),Ue(" ",Ae(3,2,"msg.applying_theme")," ")}}function Zl(i,e){if(i&1&&(re(0,"div",9)(1,"div",10)(2,"div",11),ye(3,"img",12),se()(),re(4,"div",13)(5,"p",14),Ge(6),Ie(7,"translate"),se(),ge(8,Kl,3,3,"p",15)(9,Ql,4,4,"div",16),se()()),i&2){let t=e.item;te(3),ce("src",t.image,Mi),te(3),Ue(" ",Ae(7,4,t.themeName)," "),te(2),qe(t.description?8:-1),te(),qe(t.isApplied?9:-1)}}function Jl(i,e){if(i&1){let t=Fe();re(0,"div",18)(1,"button",19),Ve("click",function(){ze(t);let a=pe(2);return $e(a.handleSettingTheme())}),Ge(2),Ie(3,"translate"),se()()}if(i&2){let t,n=pe(2);te(),ce("disabled",(t=n.slides[n.currentSlideIndex]==null?null:n.slides[n.currentSlideIndex].isApplied)!==null&&t!==void 0?t:!0),te(),Ue(" ",Ae(3,2,"msg.apply")," ")}}function ec(i,e){if(i&1){let t=Fe();re(0,"div",18)(1,"button",20),Ve("click",function(){ze(t);let a=pe(2);return $e(a.handleExploreUpRank())}),Ge(2),Ie(3,"translate"),se()()}i&2&&(te(),ce("suffixIcon","media/icons/outline/arrow-right.svg"),te(),Ue(" ",Ae(3,2,"msg.explore_how_to_up_rank")," "))}function tc(i,e){if(i&1&&ge(0,Jl,4,4,"div",18)(1,ec,4,4,"div",18),i&2){let t=pe();qe((t.userInfo==null?null:t.userInfo.isVip)===t.IsVip.Vip?0:1)}}var fr=(()=>{class i extends Ua{slides;currentTheme$;currentSlideIndex=0;config={speed:850,loop:!0,navigation:{nextEl:".swiper-setting-theme .swiper-button-next",prevEl:".swiper-setting-theme .swiper-button-prev"},slidesPerView:1,spaceBetween:16};ThemeMode=At;IsVip=cn;themeService=Y(zi);loadingService=Y(ht);toastService=Y(mt);apiSettingService=Y(Ya);destroyRef=Y(ca);UI=Y(Ft);nzModalRef=Y(Fi);ngOnInit(){this.currentTheme$=this.themeService.getTheme(),this.currentTheme$.subscribe(t=>{this.slides=this.getSlides(t)})}handleSettingTheme(){this.nzModalRef.close(),this.loadingService.showLoading();let n=this.userInfo?.currentTheme===ni.Vip?ni.Normal:ni.Vip;this.apiSettingService.updateTheme({currentTheme:n}).subscribe({next:a=>{a?.code===Xe.success&&(this.storeService.userInfo=ie(X({},this.storeService.userInfo),{currentTheme:n}),this.themeService.switchTheme(n===ni.Vip?At.Premier:At.Mass),this.toastService.success({title:this.translate.instant("msg.switch_theme_successfully")}))},error:a=>{this.toastService.error({title:a?.error?.des})}})}getCurrentSlideIndex(t){this.currentSlideIndex=t}handleExploreUpRank(){window.open(tt.exploreHowToUpRank,"_blank")}getSlides(t){switch(this.userInfo?.isVip){case cn.Vip:let n=[{id:"1124",image:"media/img/demo/theme-mass-demo.png",themeName:"msg.mass_theme",description:null,isApplied:t===At.Mass},{id:"124",image:"media/img/demo/theme-premier-demo.png",themeName:"msg.premier_theme",description:null,isApplied:t===At.Premier}];return t===At.Premier&&n.reverse(),n;default:return[{id:"12445",image:"media/img/demo/theme-premier-demo.png",themeName:"msg.premier_theme",description:"msg.up_rank_to_use_premier_theme",isApplied:!1},{id:"1124",image:"media/img/demo/theme-premier-outstanding-demo.png",themeName:"msg.premier_theme",description:"msg.up_rank_to_use_premier_theme",isApplied:!1}]}}static \u0275fac=(()=>{let t;return function(a){return(t||(t=la(i)))(a||i)}})();static \u0275cmp=Pe({type:i,selectors:[["bidv-omni-modal-setting-theme"]],standalone:!0,features:[pa,Ne],decls:14,vars:14,consts:[["swiperTpl",""],[3,"title"],[1,"setting--wrap"],[1,"swiper-setting-theme","swiper-navigation-wrap"],[3,"slideChanged","slides","config","swiperTpl","allowFollowIndex"],[1,"swiper-button","swiper-button-prev"],[3,"src","size"],[1,"swiper-button","swiper-button-next"],[1,"setting--actions"],[1,"slide-content"],[1,"slide-content--img-wrap"],[1,"slide-content--img-inner"],["alt","",1,"slide-content--img",3,"src"],[1,"slide-content--info"],[1,"slide-content--name"],[1,"slide-content--desc"],[1,"slide-content--badge"],["app-badge","",3,"color"],[1,"flex","justify-center"],["app-button","","type","button",1,"btn-theme","btn-set-theme",3,"click","disabled"],["app-button","","type","button",1,"btn-theme",3,"click","suffixIcon"]],template:function(n,a){if(n&1){let s=Fe();re(0,"app-modal-base",1),Ie(1,"translate"),ge(2,Zl,10,6,"ng-template",null,0,kt),re(4,"div",2)(5,"div",3)(6,"app-swiper",4),Ve("slideChanged",function(l){return ze(s),$e(a.getCurrentSlideIndex(l))}),se(),re(7,"div",5),ye(8,"app-svg",6),se(),re(9,"div",7),ye(10,"app-svg",6),se()(),re(11,"div",8),ge(12,tc,2,1),Ie(13,"async"),se()()()}if(n&2){let s,r=Ct(3);ce("title",Ae(1,10,"msg.setting_theme")),te(6),ce("slides",a.slides)("config",a.config)("swiperTpl",r)("allowFollowIndex",!0),te(2),ce("src","media/icons/outline/chevron-left.svg")("size",5),te(2),ce("src","media/icons/outline/chevron-right.svg")("size",5),te(2),qe((s=Ae(13,12,a.currentTheme$))?12:-1,s)}},dependencies:[ct,Ca,Ui,Vi,hr,Ga,qt,qi,$i]})}return i})();var ln=(()=>{class i{defaultParams={};featuresSubject$=new ve([]);suggestedSystemFeaturesSubject$=new ve([]);homeBannersSubject$=new ve([]);resultBannersSubject$=new ve([]);loyaltyBannersSubject$=new ve([]);cardBannersSubject$=new ve([]);introductionFeatureBanners$=new ve([]);promotionBanners$=new ve([]);insuranceGroups$=new ve([]);loadingService=Y(ht);storageService=Y(De);apiService=Y(pt);modalService=Y(ut);themeService=Y(zi);customizeFavouriteFeaturesService=Y(Va);constructor(){this.defaultParams={user:this.storageService?.userInfo?.user,lang:this.storageService?.language};let t=this.storageService.features||[];this.setFeatures(t)}processInitApp(){this.customizeFavouriteFeaturesService.processData();let t=this.storageService.features||[],n=this.storageService.homeBanners||[],a=this.storageService.suggestedSystemFeatures||[];if(this.featuresSubject$.next(t),this.homeBannersSubject$.next(n),this.suggestedSystemFeaturesSubject$.next(a),!this.featuresSubject$.getValue()?.length){this.loadingService.showLoading();let s={function:Wa,type:ja};this.apiService.getValueFavoriteFeature(s).subscribe({next:r=>{r?.code===Xe.success&&(this.setFeatures(r?.data||[]),this.storageService.features=r?.data||[])},error:r=>{console.error(r)}})}if(n?.length)this.setHomeBanners(n);else{let s={screenType:it.Home,channel:di};this.apiService.getListBanner(s).subscribe({next:r=>{r?.code===Xe.success&&(this.setHomeBanners(r?.data||[]),this.storageService.homeBanners=r?.data||[])},error:r=>{console.error(r),this.setHomeBanners([])}})}a?.length||this.fetchSuggestedSystemFeatures()}fetchSuggestedSystemFeatures(){let t={user:this.storageService.userInfo.user,lang:this.storageService.language};this.apiService.getSuggestedFeatures(t).pipe().subscribe({next:n=>{n?.code===Xe.success&&(this.setSuggestedSystemFeatures(n?.data||[]),this.storageService.suggestedSystemFeatures=n?.data||[])}})}clearCachedBanners(){this.storageService.homeBanners=[],this.storageService.resultBanners=[],this.storageService.loyaltyBanners=[],this.storageService.cardBanners=[]}fetchBanners(t,n,a,s){let r=this.storageService[t]??[];if(r?.length){a(r);return}let l={screenType:n,channel:di};this.apiService.getListBanner(X(X({},l),s)).subscribe({next:o=>{if(o?.code===Xe.success){let u=o?.data||[];switch(a(u),n){case it.Home:this.storageService.homeBanners=u;break;case it.Result:this.storageService.resultBanners=u;break;case it.Loyalty:this.storageService.loyaltyBanners=u;break;case it.Card:this.storageService.cardBanners=u;break;default:break}}else this.clearCachedBanners()},error:o=>this.clearCachedBanners()})}fetchResultBanners(){this.fetchBanners("resultBanners",it.Result,this.setResultBanners.bind(this),{})}fetchLoyaltyBanners(){this.fetchBanners("loyaltyBanners",it.Loyalty,this.setLoyaltyBanners.bind(this),{})}fetchCardBanners(){this.fetchBanners("cardBanners",it.Card,this.setCardBanners.bind(this),{function:p.dich_vu_the})}getFeatures(){return this.featuresSubject$.asObservable()}setFeatures(t){this.featuresSubject$.next(t)}getSuggestedSystemFeatures(){return this.suggestedSystemFeaturesSubject$.asObservable()}setSuggestedSystemFeatures(t){this.suggestedSystemFeaturesSubject$.next(t)}getHomeBanners(){return this.homeBannersSubject$.asObservable()}setHomeBanners(t){this.classifyBanner(t),this.homeBannersSubject$.next(t)}getResultBanners(){return this.resultBannersSubject$.asObservable()}setResultBanners(t){this.resultBannersSubject$.next(t)}getLoyaltyBanners(){return this.loyaltyBannersSubject$.asObservable()}setLoyaltyBanners(t){this.loyaltyBannersSubject$.next(t)}getCardBanners(){return this.cardBannersSubject$.asObservable()}setCardBanners(t){this.cardBannersSubject$.next(t)}getIntroductionFeatureBanners(){return this.introductionFeatureBanners$.asObservable()}getPromotionBanners(){return this.promotionBanners$.asObservable()}setIntroductionFeatureBanners(t){this.introductionFeatureBanners$.next(t)}setPromotionBanners(t){this.promotionBanners$.next(t)}classifyBanner(t){let n=a=>t.filter(s=>s.displayArea===a).slice(0,Ba).sort((s,r)=>(s.order||0)-(r.order||0));this.setIntroductionFeatureBanners(n(yn.NewFeature)||[]),this.setPromotionBanners(n(yn.Promotion)||[])}getInsuranceGroups(){return this.insuranceGroups$.asObservable()}setInsuranceGroups(t=[]){this.insuranceGroups$.next(t)}openModalSettingTheme(){this.modalService.open(fr,{size:Ri.XLarge,class:"bidv-omni-modal-setting-theme"})}static \u0275fac=function(n){return new(n||i)};static \u0275prov=be({token:i,factory:i.\u0275fac,providedIn:"root"})}return i})();export{p as a,oe as b,Sn as c,bc as d,me as e,Tr as f,Er as g,xr as h,Cr as i,qa as j,kr as k,ft as l,Ir as m,Ar as n,Mr as o,Pr as p,Nr as q,zc as r,Dr as s,Or as t,Lr as u,Hr as v,Rr as w,Br as x,zr as y,$r as z,ji as A,qr as B,Fr as C,En as D,Vr as E,Gr as F,Ur as G,Yr as H,jr as I,Wr as J,Xr as K,Kr as L,Qr as M,Zr as N,Jr as O,Wi as P,eo as Q,Xp as R,mt as S,Je as T,Kc as U,cr as V,Fa as W,Wa as X,Jc as Y,Va as Z,Ga as _,Ua as $,Xl as aa,ni as ba,Ya as ca,fr as da,ln as ea,ur as fa,pr as ga,hr as ha};
